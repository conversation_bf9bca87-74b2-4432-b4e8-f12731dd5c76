<?php

return [
    /*
    |--------------------------------------------------------------------------
    | MQTT服务器配置
    |--------------------------------------------------------------------------
    |
    | 这里配置MQTT服务器的连接参数
    |
    */

    // MQTT服务器地址
    'host' => env('MQTT_HOST', 'localhost'),

    // MQTT服务器端口
    'port' => env('MQTT_PORT', 1883),

    // 用户名
    'username' => env('MQTT_USERNAME'),

    // 密码
    'password' => env('MQTT_PASSWORD'),

    // 客户端ID（如果为空则自动生成）
    'client_id' => env('MQTT_CLIENT_ID', 'laravel_' . uniqid()),

    // 清理会话
    'clean_session' => env('MQTT_CLEAN_SESSION', true),

    // 保持连接间隔（秒）
    'keep_alive' => env('MQTT_KEEP_ALIVE', 60),

    // 连接超时时间（秒）
    'timeout' => env('MQTT_TIMEOUT', 5),

    /*
    |--------------------------------------------------------------------------
    | SSL/TLS配置
    |--------------------------------------------------------------------------
    |
    | 如果需要使用SSL/TLS连接，请配置以下参数
    |
    */

    // CA证书文件路径
    'ca_file' => env('MQTT_CA_FILE'),

    // 客户端证书文件路径
    'cert_file' => env('MQTT_CERT_FILE'),

    // 客户端私钥文件路径
    'key_file' => env('MQTT_KEY_FILE'),

    // 是否验证对等证书
    'verify_peer' => env('MQTT_VERIFY_PEER', false),

    // 是否验证对等证书名称
    'verify_peer_name' => env('MQTT_VERIFY_PEER_NAME', false),

    /*
    |--------------------------------------------------------------------------
    | 默认QoS级别
    |--------------------------------------------------------------------------
    |
    | 0: 最多一次传递（At most once delivery）
    | 1: 至少一次传递（At least once delivery）
    | 2: 恰好一次传递（Exactly once delivery）
    |
    */

    'default_qos' => env('MQTT_DEFAULT_QOS', 0),

    /*
    |--------------------------------------------------------------------------
    | 默认主题前缀
    |--------------------------------------------------------------------------
    |
    | 可以为所有主题添加统一前缀
    |
    */

    'topic_prefix' => env('MQTT_TOPIC_PREFIX', ''),

    /*
    |--------------------------------------------------------------------------
    | 重连配置
    |--------------------------------------------------------------------------
    |
    | 连接断开时的重连策略
    |
    */

    // 是否启用自动重连
    'auto_reconnect' => env('MQTT_AUTO_RECONNECT', true),

    // 重连间隔（秒）
    'reconnect_interval' => env('MQTT_RECONNECT_INTERVAL', 5),

    // 最大重连次数
    'max_reconnect_attempts' => env('MQTT_MAX_RECONNECT_ATTEMPTS', 3),

    /*
    |--------------------------------------------------------------------------
    | 日志配置
    |--------------------------------------------------------------------------
    |
    | MQTT相关日志配置
    |
    */

    // 是否启用调试日志
    'debug_log' => env('MQTT_DEBUG_LOG', false),

    // 日志通道
    'log_channel' => env('MQTT_LOG_CHANNEL', 'default'),
];
