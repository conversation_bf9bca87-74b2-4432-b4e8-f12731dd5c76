<?php

return [
    'system'  => [
        'platform_type'    => env('SYSTEM_PLATFORM_TYPE', 'api'),
        'platform_type_id' => env('SYSTEM_PLATFORM_TYPE_ID', 0),
    ],
    //用户类相关配置
    'user'    => [
        //用户相关业务用redis
        'redis_connection'          => env('SYSTEM_USER_REDIS_CONNECTION', 'default'),
        //短信验证码发送限制周期（秒）
        'verify_sms_limit_circle'   => 86400,
        //短信验证码长度 类型:1登录短信,2绑定短信,3修改密码短信
        'verify_sms_length'         => [
            1 => 6,
            2 => 6,
            3 => 6,
        ],
        //短信验证码有效时间（秒）
        'verify_sms_expired'        => [
            1 => 300,
            2 => 300,
            3 => 300,
        ],
        //短信验证码发送限制次数 类型同上
        'verify_sms_limit_time'     => [
            1 => 100,
            2 => 100,
            3 => 100,
        ],
        //短信验证码验证失败限制次数 类型同上
        'verify_sms_error_times'    => [
            1 => 10,
            2 => 10,
            3 => 10,
        ],
        //短信验证码验证失败记录保存时间（秒） 类型同上
        'verify_sms_error_duration' => 3600,
        //验证码短信模板ID
        'verify_sms_template'       => [
            1 => [
                'id'      => 'xxxxxx',
                'content' => '{{code}}',
            ],
        ],
        //短信验证码内容
        'verify_sms_content'        => [
            1 => '登录验证码为{{code}}，5分钟内有效，请勿泄露给他人。',
            2 => '您正在进行账号绑定，验证码为{{code}}，5分钟内有效，请勿泄露给他人。',
            3 => '您正在修改密码，验证码为{{code}}，5分钟内有效，请勿泄露给他人。',
        ],
        //短信发送源
        'verify_sms_origin'         => [
            1 => 'api.user.login',
            2 => 'api.user.bind',
            3 => 'api.user.reset-password',
        ],
        //密码输入错误次数限制
        'password_error_times'      => 10,
        //密码输入错误记录保存时间（秒）
        'password_error_duration'   => 3600,
    ],
    //用户会话相关配置
    'session' => [
        'redis_connection'       => env('SYSTEM_SESSION_REDIS_CONNECTION', 'default'),
        'redis_token_key_prefix' => env('SYSTEM_SESSION_REDIS_TOKEN_KEY_PREFIX', 'token:'),
        'redis_login_key_prefix' => env('SYSTEM_SESSION_REDIS_LOGIN_KEY_PREFIX', 'login:'),
        'lifetime'               => env('SYSTEM_SESSION_LIFETIME', 86400),
        'single_device'          => env('SYSTEM_SESSION_SINGLE_DEVICE', false),
    ],
];
