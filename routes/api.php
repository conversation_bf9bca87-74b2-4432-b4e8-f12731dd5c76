<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

Route::get('/user', function (Request $request) {
    return $request->user();
})->middleware('auth:sanctum');

Route::middleware(['api.sign'])
     ->group(base_path('routes/api/v1.php'));

// 内网api
Route::domain(env('INTERNAL_API_DOMAIN', 'api.localhost.com'))
     ->group(function () {
         Route::prefix('/internal')
              ->namespace('App\Http\Controllers\Internal')
              ->group(function () {
                  Route::post('/mqtt/login', 'MqttController@Login');
                  Route::post('/mqtt/auth', 'MqttController@Auth');
              });
     });

// 备用路由，捕获所有未匹配的 /api/* 请求
Route::any('{any}', function () {
    return outputJsonError(statusCode: 404);
})->where('any', '.*');
