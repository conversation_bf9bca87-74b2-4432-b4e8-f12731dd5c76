<?php

return [
    200    => '成功',
    400    => '请求参数错误',
    401    => '签名错误',
    402    => '客户端时差太大',
    429    => '请求太频繁',
    500    => '失败',
    1000   => '用户未登录',
    1001   => '登录未完成，未选择医院',

    // 10000-19499 用户相关
    10000  => '用户手机号格式错误',
    10010  => '用户性别选择错误',
    10011  => '用户生日无效',
    10100  => '用户不存在',
    10101  => '用户已存在，不可重复注册',
    10102  => '用户已禁用',
    10160  => '密码错误次数太多，请稍后再试',
    10180  => '用户注册失败',
    10190  => '登录失败',
    10191  => '登录失败，密码错误',
    10196  => '登录选择医院失败',
    10199  => '退出登录失败',

    // 10500-10599 用户验证码
    10500  => '验证码类型错误',
    10501  => '验证码发送方式错误',
    10520  => '验证码发送次数已超过限制',
    10521  => '验证码错误次数太多，请稍后再试',
    10551  => '验证码发送错误',
    10552  => '验证码发送失败',
    10570  => '验证码错误',
    10571  => '验证码已过期',
    10580  => '验证码失效，请重新获取',
    10590  => '未找到验证码验证记录',

    // 10600-10699 用户权限类
    10600  => '您在当前医院无账号',
    10602  => '您在当前医院账号已被禁用',

    // 20000-28999 医院相关
    20100  => '医院不存在',
    20101  => '医院已存在，不可重复注册',
    20102  => '医院已禁用',

    // 29000-29999 设置相关
    29000  => '云打印机不存在',
    29001  => '云打印机已存在',
    29005  => '添加打印机失败',
    29006  => '编辑打印机失败',
    29010  => '云打印机接口异常',
    29090  => '云打印指令构建异常',
    29091  => '云打印失败',

    // 30000-31999 会员相关
    30000  => '会员已存在当前医院',
    30001  => '获取会员信息失败',
    30002  => '会员不存在',
    30003  => '会员手机号不可修改',
    30004  => '会员信息更新失败',
    30005  => '会员画像更新失败',
    30006  => '暂无权限操作当前会员',

    // 32000-32999 宠物相关
    32000  => '宠物不存在',
    32001  => '添加宠物失败',
    32002  => '更新宠物信息失败',
    32003  => '添加宠物免疫失败',
    32004  => '添加宠物驱虫失败',

    // 33000-33999 商品相关
    33000  => '商品不存在',
    33001  => '商品条码不存在',

    // 34000-34999 预约相关
    34000  => '预约不存在',
    34001  => '预约参数错误',
    34002  => '预约异常，请重试',
    34003  => '预约已取消或无效，不可操作',

    // 35000-35999 挂号相关
    35000  => '挂号来源不存在',
    35001  => '挂号原因不存在',
    35002  => '挂号类型不存在',
    35003  => '挂号接诊医生不存在',
    35004  => '挂号方式无效',
    35005  => '当前挂号商品未知',
    35006  => '当前用户与医院组织不同，不可挂号',
    35007  => '当前宠物存在未使用挂号，不可挂号',
    35008  => '当前宠物存在未完结门诊，不可挂号',
    35009  => '当前宠物存在未完结住院，不可挂号',
    35010  => '挂号失败',
    35011  => '挂号单不存在',
    35012  => '挂号单已删除',
    35100  => '挂号单当前状态不允许删除操作',
    35101  => '挂号单删除失败',

    // 36000-36999 门诊相关
    36000  => '门诊不存在',
    36001  => '就诊状态发生变化，不可接诊',
    36002  => '无法接诊其它医生的门诊',
    36003  => '就诊状态发生变化，不可暂停诊断',
    36004  => '暂无操作此门诊权限',
    36005  => '存在未支付处方，不可结束诊断',
    36006  => '诊断结果不存在',
    36007  => '操作诊断状态异常',
    36008  => '转诊接诊医生不能与原诊断医生一致',

    // 37000-37999 住院相关
    37000  => '病房不存在',
    37001  => '床位不存在',
    37002  => '床位已使用，不可选择',
    37003  => '当前宠物门诊状态未就诊中，不可住院',
    37004  => '当前宠物存在未完结住院，不可住院',
    37005  => '办理住院异常，请重试',
    37006  => '住院记录不存在',
    37007  => '当前宠物已出院，不可接诊',
    37008  => '暂停诊断异常，请重试',
    37009  => '住院状态非住院中，不可操作',
    37010  => '住院部名称已存在',

    // 38000-38999 病历相关
    38000  => '病历不存在',
    38001  => '非本院病历，不可操作',
    38002  => '非本医生病历，不可操作',
    38003  => '病历已完结，不可操作',
    38004  => '病历已关闭，不可操作',
    38005  => '更新病历诊断信息失败',
    38006  => '病历宠物关联体征信息不存在',
    38007  => '编辑体况信息失败，请重试',

    // 39000-39099 处方相关
    39000  => '处方不存在',
    39001  => '病历已完结，处方不可操作',
    39002  => '非本医生处方，不可操作',
    39003  => '处方保存，药品验证错误',
    39004  => '处方保存，化验验证错误',
    39005  => '处方保存，影像验证错误',
    39006  => '处方保存，处置验证错误',
    39007  => '处方保存，组合验证错误',
    39008  => '处方保存失败',
    39009  => '处方明细不存在',
    39010  => '处方商品验证失败',

    // 39100-39200 处方模版相关
    39100  => '处方模版不存在',
    39101  => '处方模版商品不存在',
    39102  => '处方模版添加异常，请重试',
    39103  => '暂无权限操作当前处方模版',
    39104  => '处方模版删除异常，请重试',

    // 40000-40100 化验相关
    40000  => '化验项不存在',
    40001  => '化验检测员不存在',
    40002  => '化验已开始检测，不可操作',
    40003  => '化验已存在结果，不可操作',
    40004  => '开始检测化验异常',
    40005  => '化验未开始检测，不可录入结果',
    40006  => '化验结果模版已失效，不可使用',
    40007  => '化验结果模版，检查部位必填',
    40008  => '化验结果模版，采样方式必填',
    40009  => '化验结果模版，维度参数错误',
    40010  => '化验结果模版，指标参数错误',
    40011  => '化验结果模版报告，指标结果为空',
    40012  => '化验结果模版报告，数据有误',
    40013  => '化验报告单模板维度项有变化，请刷新重试',
    40014  => '化验结果保存异常，请重试',

    // 40101-40200 影像相关
    40101  => '影像不存在',
    40102  => '影像检测员不存在',
    40103  => '影像已开始检测，不可操作',
    40104  => '影像已存在结果，不可操作',
    40105  => '影像未开始检测，不可录入结果',
    40106  => '病历已结束诊断，不可录入结果',
    40107  => '影像结果模版已失效，不可使用',
    40108  => '影像结果模版，检查部位必填',
    40109  => '影像结果模版，检查超声描述/表现/症状必填',
    40110  => '影像结果模版，检查超声提示/意见/建议必填',
    40111  => '影像结果模版，检查超声图片必传',
    40112  => '影像结果模版报告，数据有误',
    40113  => '影像结果保存异常，请重试',
    40114  => '影像开始检测异常',

    // 40201-40300 处置相关
    40201  => '处置不存在',
    40202  => '执行人不存在',
    40203  => '处置已开始执行，不可重复开始',
    40204  => '处置已存在结果，不可重复开始',

    // 40301-40400 转诊相关
    40301  => '转诊单不存在',
    40302  => '院内转诊只支持门诊操作',
    40303  => '转诊门诊与病历关联门诊不一致',
    40304  => '转诊病历未结束诊断，不可转诊',
    40305  => '院内转诊，病历宠物关联体征信息不存在',
    40306  => '转诊的门诊非结束就诊，不可转诊',
    40307  => '转诊门诊关联挂号不存在',
    40308  => '转诊接诊医生不存在',
    40309  => '院内转诊异常，请重试',
    40310  => '转出与转入医院一致，不可转院',
    40311  => '转入的医院不存在或不可接受转院',
    40312  => '转诊单非本院接收，不可操作',
    40313  => '转诊单状态非等待办理，不可操作',
    40314  => '转诊单用户与当前用户不一致，不可操作',
    40315  => '转诊单宠物与当前宠物不一致，不可操作',
    40316  => '内部转院异常，请重试',

    // 41000-41999 仓库相关
    41000  => '仓库不存在',
    41001  => '仓库数量已达上限，不可在添加',
    41002  => '增加货位，行数和列数至少需要一个大于0',
    41003  => '增加货位，超限',
    41004  => '当前仓库存在有效的货位，不可删除',

    // 42000-42999 仓储-采购相关
    42000  => '采购类型不存在',
    42001  => '请输入项目名称检索',
    42002  => '经销商不存在或已失效',
    42003  => '采购说明不可为空',
    42004  => '保存类型错误',
    42005  => '采购商品不可为空',
    42006  => '调拨源门店无效或不可接受调拨',
    42007  => '采购商品错误，请检查',
    42008  => '采购总价不可小于0',
    42009  => '创建采购单异常，请重试',
    42010  => '采购单不存在或已失效',
    42011  => '采购单采购商品不存在',
    42012  => '采购单不可编辑',
    42013  => '采购单采购类型不可编辑',
    42014  => '采购单供应商或调拨源门店不可编辑',
    42015  => '删除采购单异常，请重试',
    42016  => '到货单不存在或已失效',
    42017  => '到货单商品明细不存在',
    42018  => '采购单状态未审核通过，不可签收',
    42019  => '采购单已全部签收，不可重复签收',
    42020  => '采购单已全部入库，不可签收',
    42021  => '采购单签收异常，请重试',
    42022  => '到货单已全部入库，不可重复入库',
    42023  => '入库商品明细不存在',
    42024  => '入库商品已全部入库，不可重复入库',
    42025  => '入库商品条形码与到货单商品条形码不一致',
    42026  => '整装入库数量超过到货数量',
    42027  => '散装入库数量超过到货数量',
    42028  => '采购单状态非审核通过，无法入库',
    42029  => '采购单未签收，无法入库',
    42030  => '采购单已全部入库，无法再次入库',
    42031  => '入库异常，请重试',
    42032  => '更新采购单入库状态错误',

    // 43000-43999 仓储-入库相关
    43000  => '入库商品不存在',
    43001  => '入库货位不存在',
    43002  => '入库商品参数错误',
    43003  => '商品库存入库异常，请重试',
    43004  => '入库记录不存在',
    43005  => '入库记录错误，数量、价格无效',
    43006  => '加权价更新异常，请重新操作',

    // 44000-44019 仓储-出库相关-拣货出库、调拨出库
    44000  => '出库单不存在',
    44001  => '出库单详情不存在',
    44002  => '出库单已出库完成，不可重复出库',
    44003  => '出库单创建异常',

    // 44020-44039 仓储-出库相关-耗材出库
    44020  => '耗材领用说明不可为空',
    44021  => '耗材领用商品不可为空',
    44022  => '验证领用耗材商品无效',
    44023  => '耗材领用异常，请重试',
    44024  => '耗材领用单状态发生变化，不可操作',
    44025  => '删除耗材领用单异常，请重试',

    // 44040 => 44059 仓储-出库相关-退货出库
    44040  => '退货单说明不可为空',
    44041  => '保存类型错误',
    44042  => '退货商品不可为空',
    44043  => '退货单异常，请重试',
    44044  => '删除退货单异常，请重试',

    // 45000-45999 仓储-库存出库
    45000  => '库存商品不存在或信息不完整',
    45001  => '出库数量无效',
    45002  => '商品暂无库存',
    45003  => '商品库存不足',
    45004  => '商品库存出库异常',
    45005  => '退货单不可操作',

    // 46000-46999 仓储-盘点相关
    46000  => '盘点单不存在',
    46001  => '盘点单未开始，不可操作',
    46002  => '盘点单已结束，不可操作',
    46003  => '盘点商品不在盘点单内，不可操作',
    46004  => '盘点商品参数错误，不可操作',
    46005  => '盘点异常，请重试',

    // 500000-500199 余额充值相关
    500000 => '余额充值活动为必选',
    500001 => '余额充值数量为必选',
    500010 => '余额充值活动不存在',
    500011 => '余额充值活动未开始',
    500012 => '余额充值活动已结束',
    500013 => '充值活动在当前城市不可用',
    500014 => '充值活动在当前医院不可用',
    500015 => '充值活动次数已用完',
    500016 => '充值活动个人次数已用完',
    500017 => '充值活动剩余次数不足',
    500018 => '充值活动个人剩余次数不足',
    500030 => '充值金额不正确',
    500040 => '创建充值单失败',
    500070 => '充值单不存在',
    500071 => '充值单明细不存在',
    500120 => '充值会员余额，充值订单不存在',
    500121 => '充值会员余额，充值订单状态异常',
    500122 => '充值会员余额，创建充值批次记录失败',
    500123 => '充值会员余额，创建余额变动记录失败',
    500124 => '充值会员余额，更新会员余额失败',
    500125 => '充值会员余额，创建会员余额失败',
    500129 => '充值会员余额异常',

    // 500200-500599 洗美相关
    500200 => '洗美商品验证失败',
    500201 => '洗美服务单中无有效的洗美商品',
    500202 => '洗美服务单中单品商品不存在',
    500203 => '洗美服务单中组合商品不存在',
    500204 => '洗美服务单中商品全部无效',
    500210 => '洗美商品价格获取失败',
    500211 => '洗美组合商品价格获取失败',
    500212 => '商品价格获取失败',
    500290 => '洗美服务单保存：单品验证错误',
    500291 => '洗美服务单保存：组合验证错误',
    500292 => '洗美服务单保存：价格验证错误',
    500298 => '创建洗美服务单失败',
    500310 => '洗美服务单不存在',
    500311 => '洗美服务单明细不存在',
    500312 => '洗美服务单已删除',
    500340 => '洗美服务单当前状态不允许此操作',
    500380 => '编辑洗美服务单失败',
    500390 => '删除洗美服务单失败',
    500420 => '洗美服务单已超过执行时间限制',
    500421 => '洗美服务单未支付，不可执行',
    500425 => '执行洗美服务单失败',

    // 500600-500899 零售相关
    500600 => '零售商品验证失败',
    500601 => '零售购买单中有无效的商品',
    500604 => '零售购买单中商品全部无效',
    500610 => '零售商品价格获取失败',
    500690 => '零售购买单保存：商品验证错误',
    500692 => '零售购买单保存：价格验证错误',
    500698 => '创建零售购买单失败',
    500710 => '零售购买单不存在',
    500711 => '零售购买单明细不存在',
    500712 => '零售购买单已删除',
    500740 => '零售购买单当前状态不允许此操作',
    500780 => '编辑零售购买单失败',
    500790 => '删除零售购买单失败',

    // 500900-500999 购买单通用
    500900 => '购买单不存在',
    500901 => '购买单明细不存在',
    500902 => '购买单信息不完整',
    500930 => '购买单当前状态不允许编辑操作',
    500931 => '购买单当前状态不允许删除操作',
    500940 => '购买单当前状态不允许锁定操作',
    500941 => '要锁定的购买单会员不一致，不允许锁定',
    500942 => '要锁定的购买单已过期，不允许锁定',
    500946 => '锁定失败',
    500947 => '锁定异常',
    500950 => '购买单当前状态不允许解锁操作',
    500951 => '要解锁的购买单会员不一致，不允许解锁',
    500956 => '解锁失败',
    500957 => '解锁异常',
    500970 => '购买单支付失败',
    500971 => '购买单支付异常',

    // 501000-501999 业务订单相关
    501000 => '结算子单类型与当前业务订单不符合',
    501001 => '结算子单数据异常',
    501010 => '结算单未支付，无法创建业务订单',
    501011 => '结算子单未支付，无法创建业务订单',
    501490 => '业务订单创建失败',
    501491 => '业务订单商品创建失败',
    501492 => '业务订单商品明细创建失败',
    501496 => '结算子单更新失败',
    501499 => '业务订单创建异常',
    501500 => '业务订单不存在',
    501501 => '业务订单商品不存在',
    501502 => '业务订单商品明细不存在',
    501503 => '业务订单获取失败',
    501504 => '业务订单获取异常',

    // 600000-600999 结算相关
    600000 => '要结算的购买单不存在',
    600001 => '要结算的购买单明细不存在',
    600002 => '要结算的购买单信息不完整',
    600030 => '要结算的购买单已过期，不允许结算',
    600080 => '单次结算购买单数量超过限制',
    600100 => '结算逻辑处理器注册异常',
    600101 => '要结算的购买单状态不允许结算',
    600102 => '要结算的购买单会员不一致，不允许结算',
    600103 => '要结算的购买单部分金额有变动，不允许结算',
    600130 => '要结算的购买单存在必须单独结算的业务，不允许合并结算',
    600200 => '结算数据准备失败',
    600250 => '结算基本数据校验失败',
    600400 => '结算折扣金额计算错误',
    600401 => '结算折扣率计算错误',
    600410 => '结算第三方支付方式不存在',
    600411 => '结算第三方支付金额计算错误',
    600420 => '结算余额支付金额计算错误',
    600430 => '结算现付款支付金额计算错误',
    600431 => '现付款支付渠道不存在',
    600432 => '现付款支付方式不存在',
    600440 => '结算时会员余额不足',

    // 601000-601999 结算单相关
    601000 => '结算单原购买单数据异常',
    601001 => '结算单数据构造异常',
    601002 => '结算单数据异常',
    601003 => '结算子单数据异常',
    601004 => '结算子单扩展数据异常',
    601090 => '结算单创建失败',
    601091 => '结算单写入失败',
    601092 => '锁定业务购买单失败',
    601093 => '解锁业务购买单失败',
    601100 => '结算单不存在',
    601101 => '结算单已删除',
    601106 => '结算单未支付',
    601110 => '结算单当前状态不允许发起支付',
    601111 => '结算单当前状态不允许取消',
    601130 => '取消结算单失败',
    601131 => '取消结算单异常',
    601300 => '结算单详情获取失败',
    601301 => '结算单详情获取异常',
    601401 => '结算单逻辑处理器注册异常',

    // 602000-602999 支付相关
    602000 => '会员余额不存在或已冻结',
    602001 => '会员余额不足',
    602002 => '会员余额充值批次余额不足',
    602020 => '使用会员余额更新余额批次失败',
    602021 => '使用会员余额记录操作日志失败',
    602022 => '使用会员余额更新会员余额失败',
    602090 => '使用会员余额扣减余额异常',
    602091 => '使用会员余额异常',
    602650 => '支付请求异常',
    602651 => '支付请求失败',
    602652 => '确认支付结果异常',
    602653 => '确认支付结果失败',
    602900 => '无效的支付回调信息',
    602901 => '支付回调记录不存在',
    602902 => '支付回调记录无效',
    602910 => '支付回调无匹配的支付请求记录',
    602920 => '支付回调的结算单状态错误',
    602930 => '支付成功，更新结算单失败',
    602931 => '支付成功，更新结算子单失败',
    602932 => '支付成功，更新结算单异常',
    602940 => '支付结果未知或正在处理中',
    602941 => '支付失败',
];
