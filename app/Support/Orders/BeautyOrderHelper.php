<?php

namespace App\Support\Orders;

use App\Logics\Logic;
use App\Models\ItemBeautyModel;
use App\Models\ItemSuitModel;
use App\Enums\PaySubOrderTypeEnum;
use App\Support\Item\ItemHelper;

class BeautyOrderHelper extends Logic
{
    /**
     * 格式化构建洗美订单详情
     *
     * @param array       $orders
     * @param array       $orderItems
     * @param array       $orderItemDetails
     * @param string|null $keyByCode
     *
     * @return array
     */
    public static function FormatOrderDetailsStructure(
        array $orders, array $orderItems, array $orderItemDetails, ?string $keyByCode = null
    ): array
    {
        if (empty($orders) || empty($orderItems))
        {
            return [];
        }

        //获取商品和组合ID，根据item_type分割
        $itemIds               = [];
        $orderItemsKeyRelation = [];
        foreach ($orderItems as $key => $item)
        {
            $orderItemsKeyRelation[$item['id']] = $key;
            $itemIds[]                          = $item['item_id'];
        }

        $itemIds = array_unique($itemIds);

        if (!empty($orderItemDetails))
        {
            $itemIds = array_merge($itemIds, array_unique(array_column($orderItemDetails, 'item_id')));
            $itemIds = array_unique($itemIds);
        }
        $itemSuitIds = array_unique(array_column($orderItems, 'item_suit_id'));

        //获取商品信息
        $itemInfos     = [];
        $suitItemInfos = [];

        if (!empty($itemIds))
        {
            $itemInfos = ItemBeautyModel::getManyByIds($itemIds)
                                        ->keyBy('id')
                                        ->toArray();
        }
        if (!empty($itemSuitIds))
        {
            $suitItemInfos = ItemSuitModel::getManyByIds($itemSuitIds)
                                          ->keyBy('id')
                                          ->toArray();
        }

        //先组合商品详情信息
        foreach ($orderItemDetails as $detail)
        {
            $orderItemKey = $orderItemsKeyRelation[$detail['order_item_id']] ?? null;
            if ($orderItemKey === null)
            {
                continue;
            }

            if (!isset($orderItems[$orderItemKey]['details']))
            {
                $orderItems[$orderItemKey]['details'] = [];
            }

            $orderItems[$orderItemKey]['details'][] = $detail;
        }

        /**
         * 构建订单详情
         *
         * @param array       $orders
         * @param array       $items
         * @param string      $type //订单详情类型：item detail
         * @param string|null $keyByCode
         *
         * @return array
         */
        $buildItem = function (array $orders, array $items, string $type, ?string $keyByCode = null) use (
            &$buildItem, $itemInfos, $suitItemInfos
        ) {
            $orderRelation = array_column($orders, null, 'id');

            $result = [];

            foreach ($items as $item)
            {
                $isSuit      = $item['is_suit'] ?? 0;
                $curOrder    = $orderRelation[$item['order_id']] ?? [];
                $curItemInfo = null;

                if ($isSuit)
                {
                    $curItemInfo = $suitItemInfos[$item['item_suit_id']] ?? [];
                }
                else
                {
                    $curItemInfo = $itemInfos[$item['item_id']] ?? [];
                }

                if (empty($curOrder) || empty($curItemInfo))
                {
                    continue;
                }

                $orderCode       = $curOrder['order_code'];
                $paySubOrderCode = $curOrder['pay_sub_order_code'];
                // 商品出库单位规格
                $curItemUseUnit = $curItemInfo['use_unit'] ?? '次';
                $curItemSpec    = 1 . $curItemUseUnit;

                //用于订单详情退款勾选
                $rowKey = PaySubOrderTypeEnum::Beauty->value . "|{$orderCode}|{$type}|{$item['uid']}|{$item['suit_unique_uid']}";
                //用于合并同一个品
                $groupKey = "{$item['order_id']}|{$type}|{$item['suit_unique_uid']}|{$item['item_suit_id']}|{$item['item_id']}";

                if (!isset($result[$groupKey]))
                {
                    $result[$groupKey] = [
                        'rowKey'               => $rowKey,
                        'uid'                  => $item['uid'],
                        'isSuit'               => $item['is_suit'] ?? 0,
                        'suitUniqueId'         => $item['suit_unique_uid'],
                        'itemSuitUid'          => $isSuit ? $curItemInfo['uid'] : '',
                        'itemUid'              => !$isSuit ? $curItemInfo['uid'] : '',
                        'itemName'             => ItemHelper::ItemDisplayName($curItemInfo),
                        'spec'                 => $curItemSpec,
                        'unit'                 => $curItemUseUnit,
                        'unitType'             => 0,
                        'isPreciseMetering'    => false,
                        'isPrintItems'         => !(($curItemInfo['is_print_items'] ?? 1) == 2),
                        'quantity'             => formatDisplayNumber($item['quantity']),
                        'originPrice'          => formatDisplayNumber($item['origin_price'], 2, false),
                        'salePrice'            => formatDisplayNumber($item['sale_price'], 2, false),
                        'reducePrice'          => formatDisplayNumber($item['reduce_price'], 2, false),
                        'dealPrice'            => formatDisplayNumber($item['deal_price'], 2, false),
                        'couponPrice'          => formatDisplayNumber($item['coupon_price'], 2, false),
                        'cashCouponPrice'      => formatDisplayNumber($item['cash_coupon_price'], 2, false),
                        'discountPrice'        => formatDisplayNumber($item['discount_price'], 2, false),
                        'thirdPartyPrice'      => formatDisplayNumber($item['third_party_price'], 2, false),
                        'balancePrice'         => formatDisplayNumber($item['balance_price'], 2, false),
                        'depositPrice'         => formatDisplayNumber($item['deposit_price'], 2, false),
                        'balanceRechargePrice' => formatDisplayNumber($item['balance_recharge_price'], 2, false),
                        'balanceGiftPrice'     => formatDisplayNumber($item['balance_gift_price'], 2, false),
                        'payPrice'             => formatDisplayNumber($item['pay_price'], 2, false),
                    ];

                    if ($type == 'item')
                    {
                        $result[$groupKey]['orderCode']       = $orderCode;
                        $result[$groupKey]['paySubOrderCode'] = $paySubOrderCode;
                    }
                }
                else
                {
                    $result[$groupKey] = array_merge($result[$groupKey], [
                        'quantity'             => formatDisplayNumber(bcadd($result[$groupKey]['quantity'],
                                                                            $item['quantity'],
                                                                            2)),
                        'salePrice'            => bcadd($result[$groupKey]['salePrice'], $item['sale_price'], 2),
                        'reducePrice'          => bcadd($result[$groupKey]['reducePrice'], $item['reduce_price'], 2),
                        'dealPrice'            => bcadd($result[$groupKey]['dealPrice'], $item['deal_price'], 2),
                        'couponPrice'          => bcadd($result[$groupKey]['couponPrice'], $item['coupon_price'], 2),
                        'cashCouponPrice'      => bcadd($result[$groupKey]['cashCouponPrice'],
                                                        $item['cash_coupon_price'],
                                                        2),
                        'discountPrice'        => bcadd($result[$groupKey]['discountPrice'],
                                                        $item['discount_price'],
                                                        2),
                        'thirdPartyPrice'      => bcadd($result[$groupKey]['thirdPartyPrice'],
                                                        $item['third_party_price'],
                                                        2),
                        'balancePrice'         => bcadd($result[$groupKey]['balancePrice'], $item['balance_price'], 2),
                        'depositPrice'         => bcadd($result[$groupKey]['depositPrice'], $item['deposit_price'], 2),
                        'balanceRechargePrice' => bcadd($result[$groupKey]['balanceRechargePrice'],
                                                        $item['balance_recharge_price'],
                                                        2),
                        'balanceGiftPrice'     => bcadd($result[$groupKey]['balanceGiftPrice'],
                                                        $item['balance_gift_price'],
                                                        2),
                        'payPrice'             => bcadd($result[$groupKey]['payPrice'], $item['pay_price'], 2),
                    ]);
                }

                if (!empty($item['details']))
                {
                    $result[$groupKey]['suitItems'] = array_values($buildItem($orders, $item['details'], 'detail'));
                }
            }

            if ($type == 'item')
            {
                $return = [];
                foreach ($result as $value)
                {
                    $key = $value[$keyByCode] ?? $value['orderCode'];
                    unset($value['orderCode']);
                    unset($value['paySubOrderCode']);

                    $return[$key][] = $value;
                }

                return $keyByCode ? $return : array_values($return);
            }
            else
            {
                return $result;
            }
        };


        //组合商品信息
        return $buildItem($orders, $orderItems, 'item', $keyByCode);
    }
}
