<?php

namespace App\Support\Orders;

use App\Logics\Logic;
use App\Enums\PaySubOrderTypeEnum;
use App\Models\BalanceRechargeActivitiesModel;

class RechargeOrderHelper extends Logic
{
    /**
     * 格式化构建充值订单详情
     *
     * @param array       $orders
     * @param string|null $keyByCode
     *
     * @return array
     */
    public static function FormatOrderDetailsStructure(array $orders, ?string $keyByCode = null): array
    {
        if (empty($orders))
        {
            return [];
        }

        //获取商品和组合ID，根据item_type分割
        $itemIds = array_unique(array_column($orders, 'activity_id'));

        //获取商品信息
        $itemInfos = BalanceRechargeActivitiesModel::getManyByIds($itemIds)
                                                   ->keyBy('id')
                                                   ->toArray();

        /**
         * 构建订单详情
         *
         * @param array       $orders
         * @param string|null $keyByCode
         *
         * @return array
         */
        $buildItem = function (array $orders, ?string $keyByCode = null) use (&$buildItem, $itemInfos) {
            $result = [];

            foreach ($orders as $order)
            {
                $curItemInfo = $itemInfos[$order['activity_id']] ?? [];

                if (empty($curItemInfo))
                {
                    continue;
                }

                $orderCode       = $order['order_code'];
                $paySubOrderCode = $order['pay_sub_order_code'];
                // 商品出库单位规格
                $curItemUseUnit = '次';
                $curItemSpec    = 1 . $curItemUseUnit;

                //用于订单详情退款勾选
                $rowKey = PaySubOrderTypeEnum::Recharge->value . "|{$orderCode}|item|{$orderCode}|";

                $result[] = [
                    'orderCode'            => $orderCode,
                    'paySubOrderCode'      => $paySubOrderCode,
                    'rowKey'               => $rowKey,
                    'uid'                  => $orderCode,
                    'isSuit'               => 0,
                    'suitUniqueId'         => '',
                    'itemSuitUid'          => '',
                    'itemUid'              => $curItemInfo['uid'],
                    'itemName'             => $curItemInfo['title'],
                    'spec'                 => $curItemSpec,
                    'unit'                 => $curItemUseUnit,
                    'unitType'             => 0,
                    'isPreciseMetering'    => false,
                    'isPrintItems'         => true,
                    'quantity'             => formatDisplayNumber($order['quantity']),
                    'originPrice'          => formatDisplayNumber($order['origin_price'], 2, false),
                    'salePrice'            => formatDisplayNumber($order['sale_price'], 2, false),
                    'reducePrice'          => formatDisplayNumber($order['reduce_price'], 2, false),
                    'dealPrice'            => formatDisplayNumber($order['deal_price'], 2, false),
                    'couponPrice'          => formatDisplayNumber($order['coupon_price'], 2, false),
                    'cashCouponPrice'      => formatDisplayNumber($order['cash_coupon_price'], 2, false),
                    'discountPrice'        => formatDisplayNumber($order['discount_price'], 2, false),
                    'thirdPartyPrice'      => formatDisplayNumber($order['third_party_price'], 2, false),
                    'balancePrice'         => formatDisplayNumber($order['balance_price'], 2, false),
                    'depositPrice'         => formatDisplayNumber($order['deposit_price'], 2, false),
                    'balanceRechargePrice' => formatDisplayNumber($order['balance_recharge_price'], 2, false),
                    'balanceGiftPrice'     => formatDisplayNumber($order['balance_gift_price'], 2, false),
                    'payPrice'             => formatDisplayNumber($order['pay_price'], 2, false),
                ];
            }

            $return = [];
            foreach ($result as $value)
            {
                $key = $value[$keyByCode] ?? $value['orderCode'];
                unset($value['orderCode']);
                unset($value['paySubOrderCode']);

                $return[$key][] = $value;
            }

            return $keyByCode ? $return : array_values($return);
        };


        //组合商品信息
        return $buildItem($orders, $keyByCode);
    }
}
