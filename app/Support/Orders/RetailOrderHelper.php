<?php

namespace App\Support\Orders;

use App\Enums\ItemSaleTyeEnum;
use App\Enums\ItemUnitTypeEnum;
use App\Enums\PaySubOrderTypeEnum;
use App\Logics\Logic;
use App\Models\ItemModel;
use App\Support\Item\ItemHelper;

class RetailOrderHelper extends Logic
{
    /**
     * 格式化构建零售订单详情
     *
     * @param array       $orders
     * @param array       $orderItems
     * @param string|null $keyByCode
     *
     * @return array
     */
    public static function FormatOrderDetailsStructure(
        array $orders, array $orderItems, ?string $keyByCode = null
    ): array
    {
        if (empty($orders) || empty($orderItems))
        {
            return [];
        }

        //获取商品和组合ID，根据item_type分割
        $itemIds = array_unique(array_column($orderItems, 'item_id'));

        //获取商品信息
        $itemInfos = ItemModel::getManyByIds($itemIds)
                              ->keyBy('id')
                              ->toArray();


        /**
         * 构建订单详情
         *
         * @param array       $orders
         * @param array       $items
         * @param string|null $keyByCode
         *
         * @return array
         */
        $buildItem = function (array $orders, array $items, ?string $keyByCode = null) use (
            &$buildItem, $itemInfos
        ) {
            $orderRelation = array_column($orders, null, 'id');

            $result = [];

            foreach ($items as $item)
            {
                $curOrder         = $orderRelation[$item['order_id']] ?? [];
                $curItemInfo      = $itemInfos[$item['item_id']] ?? [];
                $saleTypeFiledKey = 'first_sale_type_id';

                if (empty($curOrder) || empty($curItemInfo))
                {
                    continue;
                }

                $orderCode       = $curOrder['order_code'];
                $paySubOrderCode = $curOrder['pay_sub_order_code'];
                // 商品出库单位规格
                $curItemUseUnit   = $curItemInfo['use_unit'] ?? '次';
                $curItemUseRatio  = $curItemInfo['use_ratio'] ?? 0;
                $curItemBulkUnit  = $curItemInfo['bulk_unit'] ?? '';
                $curItemBulkRatio = $curItemInfo['bulk_ratio'] ?? 0;
                $curItemPackUnit  = $curItemInfo['pack_unit'] ?? '';

                // 如果没有整装单位、散装单位使，用计量单位
                if (empty($curItemBulkUnit))
                {
                    $curItemBulkUnit = $curItemUseUnit;
                }
                if (empty($curItemPackUnit))
                {
                    $curItemPackUnit = $curItemUseUnit;
                }

                // 商品出库单位，1ml/ml、1次、1瓶。（计量比 + 计量单位 + 散装单位 ）
                if ($saleTypeFiledKey != '' && $curItemInfo[$saleTypeFiledKey] == ItemSaleTyeEnum::FirstDrug->value)
                {
                    // 处方内使用单位-散装
                    if ($item['unit_type'] == ItemUnitTypeEnum::UNIT_TYPE_BULK->value)
                    {
                        $curItemSpec = formatDisplayNumber($curItemUseRatio) . $curItemUseUnit . '/' . $curItemBulkUnit;
                    }
                    // 处方内使用单位-整装
                    else
                    {
                        $curItemSpec = formatDisplayNumber($curItemBulkRatio) . $curItemBulkUnit . '/' . $curItemPackUnit;
                    }
                }
                else
                {
                    $curItemSpec = 1 . $curItemUseUnit;
                }

                //用于订单详情退款勾选
                $rowKey = PaySubOrderTypeEnum::Retail->value . "|{$orderCode}|item|{$item['uid']}|";
                //用于合并同一个品
                $groupKey = "{$item['order_id']}|item|{$item['item_id']}";

                if (!isset($result[$groupKey]))
                {
                    $result[$groupKey] = [
                        'orderCode'            => $orderCode,
                        'paySubOrderCode'      => $paySubOrderCode,
                        'rowKey'               => $rowKey,
                        'uid'                  => $item['uid'],
                        'isSuit'               => 0,
                        'suitUniqueId'         => '',
                        'itemSuitUid'          => '',
                        'itemUid'              => $curItemInfo['uid'],
                        'itemName'             => ItemHelper::ItemDisplayName($curItemInfo),
                        'spec'                 => $curItemSpec,
                        'unit'                 => $item['unit_type'] == ItemUnitTypeEnum::UNIT_TYPE_BULK->value ? $curItemBulkUnit : $curItemPackUnit,
                        'unitType'             => $item['unit_type'],
                        'isPreciseMetering'    => $item['is_precise_metering'] == 1,
                        'isPrintItems'         => true,
                        'quantity'             => formatDisplayNumber($item['quantity']),
                        'originPrice'          => formatDisplayNumber($item['origin_price'], 2, false),
                        'salePrice'            => formatDisplayNumber($item['sale_price'], 2, false),
                        'reducePrice'          => formatDisplayNumber($item['reduce_price'], 2, false),
                        'dealPrice'            => formatDisplayNumber($item['deal_price'], 2, false),
                        'couponPrice'          => formatDisplayNumber($item['coupon_price'], 2, false),
                        'cashCouponPrice'      => formatDisplayNumber($item['cash_coupon_price'], 2, false),
                        'discountPrice'        => formatDisplayNumber($item['discount_price'], 2, false),
                        'thirdPartyPrice'      => formatDisplayNumber($item['third_party_price'], 2, false),
                        'balancePrice'         => formatDisplayNumber($item['balance_price'], 2, false),
                        'depositPrice'         => formatDisplayNumber($item['deposit_price'], 2, false),
                        'balanceRechargePrice' => formatDisplayNumber($item['balance_recharge_price'], 2, false),
                        'balanceGiftPrice'     => formatDisplayNumber($item['balance_gift_price'], 2, false),
                        'payPrice'             => formatDisplayNumber($item['pay_price'], 2, false),
                    ];
                }
                else
                {
                    $result[$groupKey] = array_merge($result[$groupKey], [
                        'quantity'             => formatDisplayNumber(bcadd($result[$groupKey]['quantity'],
                                                                            $item['quantity'],
                                                                            2)),
                        'salePrice'            => bcadd($result[$groupKey]['salePrice'], $item['sale_price'], 2),
                        'reducePrice'          => bcadd($result[$groupKey]['reducePrice'], $item['reduce_price'], 2),
                        'dealPrice'            => bcadd($result[$groupKey]['dealPrice'], $item['deal_price'], 2),
                        'couponPrice'          => bcadd($result[$groupKey]['couponPrice'], $item['coupon_price'], 2),
                        'cashCouponPrice'      => bcadd($result[$groupKey]['cashCouponPrice'],
                                                        $item['cash_coupon_price'],
                                                        2),
                        'discountPrice'        => bcadd($result[$groupKey]['discountPrice'],
                                                        $item['discount_price'],
                                                        2),
                        'thirdPartyPrice'      => bcadd($result[$groupKey]['thirdPartyPrice'],
                                                        $item['third_party_price'],
                                                        2),
                        'balancePrice'         => bcadd($result[$groupKey]['balancePrice'], $item['balance_price'], 2),
                        'depositPrice'         => bcadd($result[$groupKey]['depositPrice'], $item['deposit_price'], 2),
                        'balanceRechargePrice' => bcadd($result[$groupKey]['balanceRechargePrice'],
                                                        $item['balance_recharge_price'],
                                                        2),
                        'balanceGiftPrice'     => bcadd($result[$groupKey]['balanceGiftPrice'],
                                                        $item['balance_gift_price'],
                                                        2),
                        'payPrice'             => bcadd($result[$groupKey]['payPrice'], $item['pay_price'], 2),
                    ]);
                }
            }

            $return = [];
            foreach ($result as $value)
            {
                $key = $value[$keyByCode] ?? $value['orderCode'];
                unset($value['orderCode']);
                unset($value['paySubOrderCode']);

                $return[$key][] = $value;
            }

            return $keyByCode ? $return : array_values($return);
        };


        //组合商品信息
        return $buildItem($orders, $orderItems, $keyByCode);
    }
}
