<?php

namespace App\Support\Registration;

use ArrayAccess;
use Illuminate\Support\Arr;
use App\Logics\Logic;
use App\Logics\LogicResult;
use App\Enums\RegistrationStatusEnum;
use App\Enums\PayStatusEnum;
use App\Models\RegistrationsModel;
use App\Models\RegistrationSourceModel;
use App\Models\RegistrationReasonModel;
use App\Models\RegistrationTypeModel;
use App\Models\ItemRegistrationModel;
use App\Support\Item\ItemHelper;
use App\Support\User\HospitalUserHelper;
use App\Logics\V1\PetLogic;

class RegistrationHelper extends Logic
{
    /**
     * 获取有效的挂号单
     *
     * @param string $registrationCode
     * @param string $registrationUid
     * @param int    $registrationId
     * @param int    $hospitalId
     *
     * @return LogicResult
     */
    public static function GetValidRegistration(
        string $registrationCode = '', string $registrationUid = '', int $registrationId = 0, int $hospitalId = 0
    ): LogicResult
    {
        $registration = RegistrationsModel::GetRegistrationByCodeOrUidOrId($registrationCode,
                                                                           $registrationUid,
                                                                           $registrationId,
                                                                           $hospitalId);
        if (empty($registration))
        {
            return self::Fail('挂号单不存在', 35011);
        }

        if ($registration->status == RegistrationStatusEnum::Invalid->value)
        {
            return self::Fail('挂号单已删除', 35012);
        }

        return self::Success($registration->toArray());
    }


    /**
     * 格式化挂号单详情
     *
     * @param array       $registrations 挂号单信息[支持批量构建]
     * @param array       $publicParams  公共参数
     * @param bool        $withId        返回原始ID
     * @param bool        $withItems     模仿商品详情
     * @param string|null $keyBy         以某个Key为键
     *
     * @return LogicResult
     */
    public static function FormatDetailStructure(
        array $registrations, array $publicParams, bool $withId = false, bool $withItems = false, ?string $keyBy = null
    ): LogicResult
    {
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        $orgId      = intval(Arr::get($publicParams, '_hospitalOrgId'));

        if (empty($registrations))
        {
            return self::Fail('挂号单信息错误', 400);
        }
        if (empty($publicParams) || empty($hospitalId) || empty($orgId))
        {
            return self::Fail('公共参数错误', 400);
        }

        $petIds          = array_unique(array_column($registrations, 'pet_id'));
        $sourceIds       = array_unique(array_column($registrations, 'source_id'));
        $reasonIds       = array_unique(array_column($registrations, 'reason_id'));
        $typeIds         = array_unique(array_column($registrations, 'type_id'));
        $doctorIds       = array_unique(array_column($registrations, 'doctor_id'));
        $beautyDoctorIds = array_unique(array_column($registrations, 'beauty_doctor_id'));
        $createUserIds   = array_unique(array_column($registrations, 'created_by'));
        $itemIds         = array_unique(array_column($registrations, 'item_id'));

        //获取宠物和会员信息
        $getPetRes = PetLogic::GetPetBaseInfoByPetIds($petIds);
        if ($getPetRes->isFail())
        {
            return $getPetRes;
        }
        $petRes = $getPetRes->getData();

        //获取挂号来源信息
        $registrationSource = RegistrationSourceModel::getData(whereIn: ['id' => $sourceIds], keyBy: 'id');

        //获取挂号原因信息
        $registrationReason = RegistrationReasonModel::getData(whereIn: ['id' => $reasonIds], keyBy: 'id');

        //获取挂号类型信息
        $registrationType = RegistrationTypeModel::getData(whereIn: ['id' => $typeIds], keyBy: 'id');

        //获取创建人信息
        $getUserRes = HospitalUserHelper::GetUserOptionsByUserIds(array_merge($doctorIds,
                                                                              $beautyDoctorIds,
                                                                              $createUserIds),
                                                                  $hospitalId,
                                                                  true);
        $itemsInfo  = [];
        if ($withItems && !empty($itemIds))
        {
            $itemsInfo = ItemRegistrationModel::getData(
                where:   ['org_id' => $orgId],
                whereIn: ['id' => $itemIds],
                keyBy:   'id'
            );
        }

        $result = [];
        foreach ($registrations as $value)
        {
            $curPet = $petRes[$value['pet_id']] ?? [];
            if (empty($curPet))
            {
                return self::Fail('宠物不存在', 32000);
            }
            $curPetInfo    = $curPet['petInfo'] ?? null;
            $curMemberInfo = $curPet['memberInfo'] ?? null;


            $curSource = $registrationSource[$value['source_id']] ?? [];
            $curReason = $registrationReason[$value['reason_id']] ?? [];
            $curType   = $registrationType[$value['type_id']] ?? [];

            $curDoctor       = $getUserRes[$value['doctor_id']] ?? null;
            $curBeautyDoctor = $getUserRes[$value['beauty_doctor_id']] ?? null;
            $curCreateUser   = $getUserRes[$value['created_by']] ?? null;

            $checkDeleteRes = self::CheckRegistrationDelete($value, $publicParams);
            $deleteAble     = $checkDeleteRes->isSuccess();

            $tempData = [
                'sheetCode'          => $value['registration_code'],
                'registrationCode'   => $value['registration_code'],
                'memberInfo'         => $curMemberInfo,
                'petInfo'            => $curPetInfo,
                'source'             => $curSource ? ['id' => $curSource['id'], 'name' => $curSource['name']] : null,
                'reason'             => $curReason ? ['id' => $curReason['id'], 'name' => $curReason['name']] : null,
                'type'               => $curType ? ['id' => $curType['id'], 'name' => $curType['name']] : null,
                'doctor'             => $curDoctor ? ['uid' => $curDoctor['uid'], 'name' => $curDoctor['name']] : null,
                'beautyDoctor'       => $curBeautyDoctor ? [
                    'uid'  => $curBeautyDoctor['uid'],
                    'name' => $curBeautyDoctor['name']
                ] : null,
                'registrationDate'   => $value['registration_date'],
                'registrationNumber' => $value['registration_number'],
                'salePrice'          => formatDisplayNumber($value['pay_price'], 2, false),
                'price'              => formatDisplayNumber($value['pay_price'], 2, false),
                'status'             => [
                    'id'   => $value['status'],
                    'name' => RegistrationStatusEnum::getDescription($value['status']),
                ],
                'payStatus'          => [
                    'id'   => $value['pay_status'],
                    'name' => PayStatusEnum::getDescription($value['pay_status']),
                ],
                'createUser'         => $curCreateUser,
                'createTime'         => formatDisplayDateTime($value['created_at']),
                'updateTime'         => formatDisplayDateTime($value['updated_at']),
                'deleteAble'         => $deleteAble,
            ];

            if ($withId)
            {
                $tempData['id'] = $value['id'];
            }
            if ($withItems)
            {
                $curItemInfo       = $itemsInfo[$value['item_id']] ?? [];
                $tempData['items'] = [
                    [
                        'uid'          => $value['uid'],
                        'itemUid'      => $curItemInfo['uid'] ?? '',
                        'type'         => ['id' => 0, 'name' => '挂号'],
                        'name'         => ItemHelper::ItemDisplayName($curItemInfo),
                        'spec'         => '1次',
                        'unit'         => '次',
                        'quantity'     => 1,
                        'price'        => formatDisplayNumber($value['pay_price']),
                        'totalPrice'   => formatDisplayNumber(numberMul([$value['pay_price'], 1])),
                        'itemInfo'     => [
                            'uid'  => $curItemInfo['uid'] ?? '',
                            'type' => ['id' => 0, 'name' => '挂号'],
                            'name' => ItemHelper::ItemDisplayName($curItemInfo),
                        ],
                        'isPrintItems' => null,
                        'suitItems'    => [],
                    ]
                ];
            }

            if (!empty($keyBy) && isset($value[$keyBy]))
            {
                $result[$value[$keyBy]] = $tempData;
            }
            else
            {
                $result[] = $tempData;
            }
        }

        return self::Success($result);
    }

    /**
     * 验证挂号单是否可以删除
     *
     * @param array|ArrayAccess $registration
     * @param array             $publicParams
     *
     * @return LogicResult
     */
    public static function CheckRegistrationDelete(array|ArrayAccess $registration, array $publicParams): LogicResult
    {
        $userId = intval(Arr::get($publicParams, '_userId'));

        if (empty($registration) || empty($publicParams) || empty($userId))
        {
            return self::Fail('验证挂号单是否可操作，缺少必选参数', 400);
        }

        if (!RegistrationStatusEnum::DeleteAble($registration['status']))
        {
            return self::Fail('挂号单当前状态不允许删除操作', 35100);
        }

        if (!PayStatusEnum::DeleteAble($registration['pay_status']))
        {
            return self::Fail('挂号单当前支付状态不允许删除操作', 35100);
        }

        //TODO:根据是否本人和是否院长角色来判断

        return self::Success();
    }
}
