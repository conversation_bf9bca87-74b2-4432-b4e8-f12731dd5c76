<?php

namespace App\Support\Common;

use Illuminate\Support\Facades\Log;
use App\Logics\Logic;
use App\Logics\LogicResult;

class PriceSplitHelper extends Logic
{
    /**
     * 均摊价格【尽可能的分0.01】
     *
     * 子项目价格比例依据deal_price
     *
     * third_party_price
     * balance_price            在均摊balance_price需要先均摊balance_recharge_price
     * deposit_price
     * balance_recharge_price   无需单独均摊
     * balance_gift_price
     * pay_price                均摊前必须有值
     *
     * @param array  $orderItems
     * @param string $splitPriceType
     * @param string $splitPrice
     *
     * @return LogicResult
     */
    public static function CommonSplitPrice(array $orderItems, string $splitPriceType, string $splitPrice): LogicResult
    {
        self::DebugLog('info', ['$splitPriceType' => $splitPriceType, '$splitPrice' => $splitPrice]);

        if (empty($orderItems) || bccomp($splitPrice, 0, 2) == 0)
        {
            return self::Success([]);
        }

        if ($splitPriceType == "" || !array_key_exists($splitPriceType, $orderItems[0]))
        {
            return self::Fail('均摊价格失败：不存在的价格类型', 400);
        }

        if ($splitPriceType == 'balance_gift_price')
        {
            return self::Fail('均摊价格失败："balance_gift_price"类型不需要单独均摊', 400);
        }

        $orderItemCount             = count($orderItems); //子项目数量
        $minSplitPriceOrderItems    = [];
        $minSplitPriceOrderItemsIds = [];
        $totalDealPrice             = 0;



        foreach ($orderItems as $key => $orderItem)
        {
            $totalDealPrice   = bcadd($totalDealPrice, $orderItem['pay_price'], 2);
            $orderItem['_id'] = $key;
            $orderItems[$key] = $orderItem;
        }

        foreach ($orderItems as $key => $orderItem)
        {
            //0元的pay_price跳过
            if (bccomp($orderItem['pay_price'], 0, 2) == 0)
            {
                continue;
            }

            self::DebugLog(__CLASS__ . '::' . __METHOD__ . ' 找出均摊后小于等于0.01的项目',
                           [
                               'pay_price' => $orderItem['pay_price'],
                               'min_price' => sprintf('%.4f', ($totalDealPrice / $splitPrice / 100)),
                               'orderItem' => $orderItem
                           ]);

            //找出均摊后小于等于0.01的项目
            if (bccomp($orderItem['pay_price'], ($totalDealPrice / $splitPrice / 100), 4) <= 0)
            {
                $minSplitPriceOrderItems[]    = $orderItem;
                $minSplitPriceOrderItemsIds[] = $key;
            }
        }

        /*
         * 均摊模式
         * 0:完全按加权均摊
         * 1:按价格从大到小分配0.01,直到分完,
         * 2:先给0.01到只能均摊<=0.01的项目，其他的再均摊剩下的
         */
        $splitMode = 0;
        if (bccomp($splitPrice, bcmul($orderItemCount, 0.01, 2), 2) <= 0)
        {
            //模式1
            $splitMode = 1;
        }
        elseif (!empty($minSplitPriceOrderItems))
        {
            //模式2
            $splitMode = 2;
        }

        $tmpSplitPrice     = $splitPrice;
        $result            = [];
        $arrSplitItemPrice = [];
        if ($splitMode == 2)
        {
            foreach ($minSplitPriceOrderItems as $key => $orderItem)
            {
                try
                {
                    //取两位小数后进一法取整
                    $swapCurSplitPriceF = $splitPrice * ($orderItem['pay_price'] / $totalDealPrice) * 100;
                    $swapCurSplitPrice  = floor("{$swapCurSplitPriceF}");
                    $curSplitPrice      = $swapCurSplitPrice / 100; //变回两位小数
                    if (bccomp($curSplitPrice, 0.01, 2) > 0)
                    {
                        self::DebugLog(__CLASS__ . '::' . __METHOD__ . ' 均摊模式2：均摊金额大于0.01',
                                       [
                                           'swapCurSplitPriceF' => $swapCurSplitPriceF,
                                           'curSplitPrice'      => $curSplitPrice,
                                           'orderItem'          => $orderItem
                                       ]);
                    }
                } catch (\Exception $e)
                {
                    Log::error(__CLASS__ . '::' . __METHOD__ . ' split_mode:2 exception.', ['orderItem' => $orderItem]);
                }

                $curSplitPrice = 0.01;
                self::DebugLog(__CLASS__ . '::' . __METHOD__ . ' 均摊模式2：debug',
                               [
                                   sprintf('%.4f', $swapCurSplitPriceF),
                                   sprintf('%.4f', $swapCurSplitPrice),
                                   sprintf('%.4f', $curSplitPrice)
                               ]);

                $tmpSplitPrice              = bcsub($tmpSplitPrice, $curSplitPrice, 2);
                $orderItem[$splitPriceType] = $curSplitPrice;
                $result[]                   = $orderItem;

                $totalDealPrice = bcsub($totalDealPrice, $orderItem['pay_price'], 2);
            }
        }

        //按照价格从大到小排序,按照顺序自然排序
        usort($orderItems, function ($prev, $next) {
            switch (bccomp($prev['pay_price'], $next['pay_price'], 2))
            {
                case 1;
                    return - 1;
                case - 1:
                    return 1;
                case 0:
                    return $prev['_id'] > $next['_id'] ? 1 : - 1;
            }
        });

        $tmpSplitPrice2 = $tmpSplitPrice;
        foreach ($orderItems as $key => $orderItem)
        {
            if ($splitMode == 2 && in_array($orderItem['_id'], $minSplitPriceOrderItemsIds))
            {
                unset($orderItems[$key]);
                continue;
            }

            //取两位小数后进一法取整
            $swapCurSplitPriceF = $tmpSplitPrice * ($orderItem['pay_price'] / $totalDealPrice) * 100;
            $swapCurSplitPrice  = floor("{$swapCurSplitPriceF}");
            $curSplitPrice      = $swapCurSplitPrice / 100; //变回两位小数
            if ($splitMode == 1)
            {
                self::DebugLog(__CLASS__ . '::' . __METHOD__ . ' 均摊模式1：debug',
                               ['tmpSplitPrice2' => $tmpSplitPrice2]);

                $curSplitPrice = 0.01;
            }

            self::DebugLog(__CLASS__ . '::' . __METHOD__ . " 均摊模式{$splitMode}：info",
                           [
                               sprintf('%.4f', $swapCurSplitPriceF),
                               sprintf('%.4f', $swapCurSplitPrice),
                               sprintf('%.4f', $curSplitPrice)
                           ]);

            //当剩余值小于比例分摊时，比例分摊用当前剩余值
            if ($tmpSplitPrice2 < $curSplitPrice)
            {
                $curSplitPrice = $tmpSplitPrice2;
            }
            //当前需支付金额小于比例分摊时,比例分摊用当前需支付金额
            if ($orderItem['pay_price'] < $curSplitPrice)
            {
                $curSplitPrice = $orderItem['pay_price'];
            }

            $tmpSplitPrice2             = bcsub($tmpSplitPrice2, $curSplitPrice, 2);
            $orderItem[$splitPriceType] = $curSplitPrice;
            $arrSplitItemPrice[]        = $orderItem;
        }

        while ($tmpSplitPrice2 > 0)
        {
            foreach ($arrSplitItemPrice as $key => $orderItem)
            {
                $tiniSub = bccomp($tmpSplitPrice2, 0.01, 2) == 1 ? 0.01 : $tmpSplitPrice2;
                if (bccomp(bcsub($orderItem['pay_price'], $orderItem[$splitPriceType], 2), $tiniSub, 2) >= 0)
                {
                    $arrSplitItemPrice[$key][$splitPriceType] = bcadd($orderItem[$splitPriceType], $tiniSub, 2);
                }
                $tmpSplitPrice2 = bcsub($tmpSplitPrice2, $tiniSub, 2);
            }
        }


        $result = array_merge($result, $arrSplitItemPrice);

        if (!in_array($splitPriceType, ['balance_gift_price', 'balance_recharge_price']))
        {
            //均摊赠送余额 不减pay_price
            foreach ($result as $key => $rowSplitItemPrice)
            {
                $result[$key]['pay_price'] = bcsub($rowSplitItemPrice['pay_price'],
                                                   $rowSplitItemPrice[$splitPriceType],
                                                   2);
                if ($rowSplitItemPrice['balance_price'] > 0)
                {
                    self::DebugLog(__CLASS__ . '::' . __METHOD__ . " 价格：info",
                                   [
                                       'balance_price'          => $rowSplitItemPrice['balance_price'],
                                       'balance_recharge_price' => $rowSplitItemPrice['balance_recharge_price']
                                   ]);

                    $result[$key]['balance_gift_price'] = bcsub($rowSplitItemPrice['balance_price'],
                                                                $rowSplitItemPrice['balance_recharge_price'],
                                                                2);
                }
            }
        }
        //按照价格从大到小排序,按照顺序自然排序
        usort($result, function ($prev, $next) {
            return $prev['_id'] > $next['_id'] ? 1 : - 1;
        });

        foreach ($result as $key => $orderItem)
        {
            unset($result[$key]['_id']);
        }

        return self::Success($result);
    }

    /**
     * 调试日志
     *
     * env PRICE_SPLIT_LOG_ENABLE=true 控制
     *
     * @param string $info
     * @param array  $data
     *
     * @return void
     */
    private static function DebugLog(string $info, array $data = []): void
    {
        if (!env('PRICE_SPLIT_LOG_ENABLE', false))
        {
            return;
        }

        Log::debug($info, $data);
    }
}
