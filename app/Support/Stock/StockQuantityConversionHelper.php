<?php

namespace App\Support\Stock;

/**
 * 库存数量转化计算辅助类
 *
 * 主要用于处理整装和散装之间的换算验证
 * 通过将所有数量统一转换为散装单位进行计算，简化整散比验证逻辑
 */
class StockQuantityConversionHelper
{
    /**
     * 将整装+散装转换为总散装数量
     *
     * @param string $packQuantity 整装数量
     * @param string $bulkQuantity 散装数量
     * @param int    $bulkRatio    整散比（1个整装=多少个散装）
     *
     * @return string 总散装数量
     */
    public static function convertToTotalBulkQuantity(string $packQuantity, string $bulkQuantity, int $bulkRatio): string
    {
        if ($bulkRatio <= 0)
        {
            return $bulkQuantity;
        }

        return numberAdd([numberMul([$packQuantity, $bulkRatio]), $bulkQuantity]);
    }

    /**
     * 将总散装数量转换为整装+散装
     *
     * @param string $totalBulkQuantity 总散装数
     * @param string $bulkRatio         整散比
     *
     * @return array ['packQuantity' => 0, 'bulkQuantity' => 0]
     */
    public static function convertToPackAndBulkQuantity(string $totalBulkQuantity, string $bulkRatio): array
    {
        if (bccomp($totalBulkQuantity, '0', 2) <= 0)
        {
            return ['packQuantity' => '0', 'bulkQuantity' => '0'];
        }
        if (bccomp($bulkRatio, '0', 2) <= 0)
        {
            return ['packQuantity' => '0', 'bulkQuantity' => $totalBulkQuantity];
        }

        // 整装数量
        $packQuantity = numberDiv([$totalBulkQuantity, $bulkRatio], 0); // 取整数部分

        // 散装数量
        $bulkQuantity = numberSub([$totalBulkQuantity, numberMul([$packQuantity, $bulkRatio])]);

        return [
            'packQuantity' => $packQuantity,
            'bulkQuantity' => $bulkQuantity
        ];
    }

    /**
     * 验证操作的整散数量是满足
     *
     * @param string $actualPackQuantity  实际操作整装数量
     * @param string $actualBulkQuantity  实际操作散装数量
     * @param string $allowedPackQuantity 库存整装数量
     * @param string $allowedBulkQuantity 库存散装数量
     * @param int    $bulkRatio           整散比
     *
     * @return bool 是否验证通过（true: 满足，库存足够，false: 不满足，超限）
     */
    public static function checkQuantityIsSufficient(string $actualPackQuantity, string $actualBulkQuantity, string $allowedPackQuantity, string $allowedBulkQuantity, int $bulkRatio = 1): bool
    {
        // 参数验证
        if ($actualPackQuantity < 0 || $actualBulkQuantity < 0 || $allowedPackQuantity < 0 || $allowedBulkQuantity < 0 || $bulkRatio < 1)
        {
            return false;
        }

        $actualTotal  = self::convertToTotalBulkQuantity($actualPackQuantity, $actualBulkQuantity, $bulkRatio);
        $allowedTotal = self::convertToTotalBulkQuantity($allowedPackQuantity, $allowedBulkQuantity, $bulkRatio);

        return $actualTotal <= $allowedTotal;
    }

    /**
     * 获取剩余可操作库存，按整装 + 散装方式返回（优先整装）
     *
     * @param string $allowedPackQuantity 允许的整装数量
     * @param string $allowedBulkQuantity 允许的散装数量
     * @param string $usedPackQuantity    已使用整装数量
     * @param string $usedBulkQuantity    已使用散装数量
     * @param int    $bulkRatio           整散比
     *
     * @return array ['remainPackQuantity' => int, 'remainBulkQuantity' => int, 'totalRemainBulk' => int]
     */
    public static function getRemainPackAndBulkQuantity(string $allowedPackQuantity, string $allowedBulkQuantity, string $usedPackQuantity, string $usedBulkQuantity, int $bulkRatio): array
    {
        // 参数验证
        if ($allowedPackQuantity < 0 || $allowedBulkQuantity < 0 || $usedPackQuantity < 0 || $usedBulkQuantity < 0 || $bulkRatio < 0)
        {
            return [
                'remainPackQuantity' => 0,
                'remainBulkQuantity' => 0,
                'totalRemainBulk'    => 0,
                'bulkRatio'          => 0
            ];
        }

        $allowedTotal = self::convertToTotalBulkQuantity($allowedPackQuantity, $allowedBulkQuantity, $bulkRatio);
        $usedTotal    = self::convertToTotalBulkQuantity($usedPackQuantity, $usedBulkQuantity, $bulkRatio);

        $remainTotal = max(0, $allowedTotal - $usedTotal);

        // 剩余数量按整装优先分配
        if ($bulkRatio > 0)
        {
            $remainPackQuantity = floor(numberDiv([$remainTotal, $bulkRatio]));
            $remainBulkQuantity = numberSub([$remainTotal, numberMul([$remainPackQuantity, $bulkRatio])]);
        }
        else
        {
            $remainPackQuantity = 0;
            $remainBulkQuantity = $remainTotal;
        }

        return [
            'remainPackQuantity' => $remainPackQuantity,
            'remainBulkQuantity' => $remainBulkQuantity,
            'totalRemainBulk'    => $remainTotal,
            'bulkRatio'          => $bulkRatio
        ];
    }

    /**
     * 生成库存数量结构体
     *
     * @param array $stockRecord    库存记录数据（优先从此获取单位信息）
     * @param array $extendData     扩展数据（备用单位信息来源）
     * @param array $quantityFields 数量字段映射 ['pack' => 'pack_quantity', 'bulk' => 'bulk_quantity']
     * @param array $unitFields     单位字段映射 ['pack' => 'pack_unit', 'bulk' => 'bulk_unit']
     *
     * @return array
     */
    // @formatter:off
    public static function getStockQuantityStructure(array $stockRecord = [],
                                                     array $extendData = [],
                                                     array $quantityFields = ['pack' => 'pack_quantity', 'bulk' => 'bulk_quantity'],
                                                     array $unitFields = ['pack' => 'pack_unit', 'bulk' => 'bulk_unit']
    ): array
    // @formatter:on
    {
        // 获取数量值
        $packQuantity = $stockRecord[$quantityFields['pack']] ?? 0;
        $bulkQuantity = $stockRecord[$quantityFields['bulk']] ?? 0;

        // 获取单位信息（优先从库存记录中获取，其次从扩展数据中获取）
        $packUnit = $stockRecord[$unitFields['pack']] ?? $extendData[$unitFields['pack']] ?? '';
        $bulkUnit = $stockRecord[$unitFields['bulk']] ?? $extendData[$unitFields['bulk']] ?? '';

        // 生成总数量描述
        $totalQuantityDesc = '';
        if ($packQuantity > 0)
        {
            $totalQuantityDesc .= $packQuantity . $packUnit;
        }
        if ($bulkQuantity > 0)
        {
            $totalQuantityDesc .= $bulkQuantity . $bulkUnit;
        }

        return [
            'packQuantity'      => $packQuantity,
            'bulkQuantity'      => $bulkQuantity,
            'totalQuantityDesc' => $totalQuantityDesc,
            'hasStock'          => ($packQuantity > 0 || $bulkQuantity > 0) // 只要整装、散装存在一个库存，即认为是有库存。可以用于一些前置不需要精确库存的操作
        ];
    }

    /**
     * 生成无需库存默认数量结构体
     * @return int[]
     */
    public static function getStockQuantityDefaultStructure(): array
    {
        return [
            'packQuantity'      => 9999,
            'bulkQuantity'      => 0,
            'totalQuantityDesc' => '无需库存管理',
            'hasStock'          => true, // 只要整装、散装存在一个库存，即认为是有库存。可以用于一些前置不需要精确库存的操作
        ];
    }
}
