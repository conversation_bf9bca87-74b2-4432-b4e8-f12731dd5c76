<?php

namespace App\Support\Stock;

use Illuminate\Support\Arr;
use App\Support\Item\ItemHelper;
use App\Logics\Logic;
use App\Logics\LogicResult;
use App\Logics\V1\ItemLogic;

/**
 * 库存验证器
 * 提供通用的库存充足性验证功能
 */
class StockVerifierHelper extends Logic
{
    /**
     * 验证库存需求是否充足
     *
     * @param array $stockRequirements 库存需求 [itemId => ['pack_quantity' => x, 'bulk_quantity' => y]]
     * @param array $publicParams      公共参数
     * @param array $occupiedStock     占用库存 [itemId => ['pack_quantity' => x, 'bulk_quantity' => y]]
     *
     * @return LogicResult
     */
    public static function ValidateStockRequirements(array $stockRequirements, array $publicParams, array $occupiedStock = []): LogicResult
    {
        if (empty($stockRequirements))
        {
            return self::Success();
        }

        // 验证公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        if (empty($hospitalId))
        {
            return self::Fail('验证库存，缺少公共参数', 400);
        }

        // 获取商品信息
        $itemIds        = array_keys($stockRequirements);
        $getItemInfoRes = ItemLogic::GetItemFullInfo(itemIds: $itemIds, publicParams: $publicParams, withItemStock: true);
        if ($getItemInfoRes->isFail())
        {
            return $getItemInfoRes;
        }

        $getItemInfoRes = $getItemInfoRes->getData();
        $getItemInfoRes = array_column($getItemInfoRes, null, 'id');
        if (empty($getItemInfoRes))
        {
            return self::Fail('验证库存，商品信息不存在', 33000);
        }

        // 验证库存充足性
        $errorMsg = [];
        foreach ($stockRequirements as $itemId => &$requirement)
        {
            $curItemInfo = $getItemInfoRes[$itemId] ?? [];
            if (empty($curItemInfo))
            {
                return self::Fail('验证库存，商品信息不存在', 33000);
            }

            // 商品库存信息
            $itemDisplayName  = ItemHelper::ItemDisplayName($curItemInfo);
            $curItemStockInfo = $curItemInfo['stock_info'] ?? [];

            // 检查是否有库存
            if (empty($curItemStockInfo['hasStock']))
            {
                $errorMsg[] = "商品【{$itemDisplayName}】 暂无库存";
                continue;
            }

            // 商品整散比
            $bulkRatio                 = $curItemInfo['bulk_ratio'];
            $requirement['bulk_ratio'] = $bulkRatio;

            // 计算需求的总散装数量
            $needTotalBulk = StockQuantityConversionHelper::convertToTotalBulkQuantity($requirement['pack_quantity'] ?? 0, $requirement['bulk_quantity'] ?? 0, $bulkRatio);

            // 计算实际库存的总散装数量
            $actualTotalBulk = StockQuantityConversionHelper::convertToTotalBulkQuantity($curItemStockInfo['packQuantity'], $curItemStockInfo['bulkQuantity'], $bulkRatio);

            // 减去占用库存
            $curOccupiedInfo = $occupiedStock[$itemId] ?? 0;
            if (!empty($curOccupiedInfo))
            {
                $occupiedTotalBulk = StockQuantityConversionHelper::convertToTotalBulkQuantity($curOccupiedInfo['occupy_pack_quantity'],
                                                                                               $curOccupiedInfo['occupy_bulk_quantity'],
                                                                                               $bulkRatio);
                $actualTotalBulk   = numberSub([$actualTotalBulk, $occupiedTotalBulk]);
            }

            // 验证是否充足
            if (bccomp($actualTotalBulk, $needTotalBulk, 2) < 0)
            {
                $errorMsg[] = "商品【{$itemDisplayName}】 库存不足";
            }
        }

        if (!empty($errorMsg))
        {
            return self::Fail(implode("；\n", $errorMsg), 45003);
        }

        return self::Success($stockRequirements);
    }
}
