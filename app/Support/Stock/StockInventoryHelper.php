<?php

namespace App\Support\Stock;

use App\Logics\Logic;
use App\Logics\LogicResult;
use App\Logics\V1\StockItemShelfAddLogic;

class StockInventoryHelper extends Logic
{
    /**
     * 验证盘点提交是否正确，并返回完整信息
     *
     * @param array $itemInfo
     * @param array $stockItemShelf
     *
     * @return LogicResult
     */
    public static function GetValidInventoryParams(array $itemInfo, array $stockItemShelf): LogicResult
    {
        if (empty($itemInfo))
        {
            return self::Fail('验证盘点库存，缺少商品信息', 400);
        }
        if (empty($stockItemShelf))
        {
            return self::Fail('验证盘点库存，缺少必选参数', 400);
        }

        // 商品信息
        $itemBarcode = $itemInfo['item_barcode_info']['item_barcode'];

        // 验证盘点库存
        $arrUnionShelf = [];
        foreach ($stockItemShelf as $curItemShelf)
        {
            $curItemUid = $curItemShelf['itemUid'] ?? '';
            if (empty($curItemUid))
            {
                return self::Fail('验证盘点库存，缺少商品UID', 46004);
            }
            if (empty($curItemShelf['itemBarcode']))
            {
                return self::Fail('验证盘点库存，缺少商品条码', 46004);
            }
            if ($curItemShelf['itemBarcode'] != $itemBarcode)
            {
                return self::Fail('验证盘点库存，商品条码不一致', 46004);
            }
            if (empty($curItemShelf['shelfCode']))
            {
                return self::Fail('验证盘点库存，缺少货位编码', 46004);
            }
            if (!empty($curItemShelf['expiredDate']) && !checkDateIsValid($curItemShelf['expiredDate']))
            {
                return self::Fail('验证盘点库存，效期无效', 46004);
            }

            // 验证退货数量
            $curPackQuantity = $curItemShelf['packStock'] ?: 0;
            $curBulkQuantity = $curItemShelf['bulkStock'] ?: 0;
            if ($curPackQuantity < 0 && $curBulkQuantity < 0)
            {
                return self::Fail('验证盘点库存，整装、散装数量不可同时小于0', 46004);
            }

            // 验证商品效期
            $curStockExpirationDateRes = StockItemShelfAddLogic::GetAddStockItemProduceDateAndExpireDate($curItemShelf, $itemInfo);
            if ($curStockExpirationDateRes->isFail())
            {
                return $curStockExpirationDateRes;
            }

            // 相同效期的商品,相同货位,不可重复盘点
            $curUniqueKey = self::getInventoryItemUnionKey($curItemShelf);
            if (in_array($curUniqueKey, $arrUnionShelf))
            {
                return self::Fail("货位:【{$curItemShelf['shelfCode']}】效期:【{$curItemShelf['expiredDate']}】，相同货位效期不可重复盘点", 46004);
            }
            $arrUnionShelf[] = $curUniqueKey;
        }

        return self::Success();
    }

    /**
     * 获取实际盘点库存数
     *
     * @param array $addStockInfo
     * @param array $reduceStockInfo
     * @param int   $itemBulkRatio
     *
     * @return array[]
     */
    public static function getActualInventoryStock(array $addStockInfo, array $reduceStockInfo, int $itemBulkRatio): array
    {
        // 汇总所有盘盈操作，按照货位+效期
        $stockShelfGroup = [];
        foreach ($addStockInfo as $curAdd)
        {
            $curUnionKey = self::getInventoryItemUnionKey($curAdd);
            if (!isset($stockShelfGroup[$curUnionKey]))
            {
                $stockShelfGroup[$curUnionKey] = [
                    'packQuantity' => 0,
                    'bulkQuantity' => 0,
                    'shelfCode'    => $curAdd['shelfCode'],
                    'expiredDate'  => $curAdd['expiredDate'],
                ];
            }

            $stockShelfGroup[$curUnionKey]['packQuantity'] += $curAdd['packQuantity'];
            $stockShelfGroup[$curUnionKey]['bulkQuantity'] += $curAdd['bulkQuantity'];
        }

        // 盘盈中如果存在盘亏，则进行冲销
        foreach ($reduceStockInfo as $curReduce)
        {
            $curUnionKey = self::getInventoryItemUnionKey($curReduce);
            if (!isset($stockShelfGroup[$curUnionKey]))
            {
                $stockShelfGroup[$curUnionKey] = [
                    'packQuantity' => 0,
                    'bulkQuantity' => 0,
                    'shelfCode'    => $curReduce['shelfCode'],
                    'expiredDate'  => $curReduce['expiredDate'],
                ];
            }
            $stockShelfGroup[$curUnionKey]['packQuantity'] -= $curReduce['packQuantity'];
            $stockShelfGroup[$curUnionKey]['bulkQuantity'] -= $curReduce['bulkQuantity'];
        }

        // 最终库存是实际盘盈、盘亏数
        $actualAddStock    = [];
        $actualReduceStock = [];
        foreach ($stockShelfGroup as $data)
        {
            // 库存最终变动数
            $curDiffQuantity = numberAdd([numberMul([$data['packQuantity'], $itemBulkRatio]), $data['bulkQuantity']]);

            // 盘盈
            if ($curDiffQuantity > 0)
            {
                $addAmount        = StockQuantityConversionHelper::convertToPackAndBulkQuantity($curDiffQuantity, $itemBulkRatio);
                $actualAddStock[] = [
                    'packQuantity' => $addAmount['packQuantity'],
                    'bulkQuantity' => $addAmount['bulkQuantity'],
                    'shelfCode'    => $data['shelfCode'],
                    'expiredDate'  => $data['expiredDate'],
                ];
            }

            // 盘亏
            if ($curDiffQuantity < 0)
            {
                // 最终结果为盘亏
                $reduceAmount        = StockQuantityConversionHelper::convertToPackAndBulkQuantity(abs($curDiffQuantity), $itemBulkRatio);
                $actualReduceStock[] = [
                    'packQuantity' => $reduceAmount['packQuantity'],
                    'bulkQuantity' => $reduceAmount['bulkQuantity'],
                    'shelfCode'    => $data['shelfCode'],
                    'expiredDate'  => $data['expiredDate'],
                ];
            }
        }

        return [
            'addStockInfo'    => $actualAddStock,
            'reduceStockInfo' => $actualReduceStock,
        ];
    }

    /**
     * 获取盘点库存的唯一键
     *
     * @param array $stockItemShelf
     *
     * @return string
     */
    private static function getInventoryItemUnionKey(array $stockItemShelf): string
    {
        return $stockItemShelf['itemBarcode'] . '-' . $stockItemShelf['shelfCode'] . '-' . ($stockItemShelf['expiredDate'] ?? 'NO_EXPIRE_DATE');
    }
}
