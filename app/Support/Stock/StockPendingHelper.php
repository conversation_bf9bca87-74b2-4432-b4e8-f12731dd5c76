<?php

namespace App\Support\Stock;

use Arr;
use App\Enums\ItemSaleTyeEnum;
use App\Enums\ItemUnitTypeEnum;
use App\Enums\StockPendingOrderDetailTypeEnum;
use App\Logics\Logic;
use App\Logics\LogicResult;
use App\Logics\V1\ItemLogic;
use App\Models\TestsConsumablesTemplatesItemModel;

class StockPendingHelper extends Logic
{
    /**
     * 构建待出库详情数据
     *
     * @param array $orderItems
     * @param array $orderItemDetails
     * @param array $publicParams
     *
     * @return LogicResult
     */
    public static function BuildPendingDetailsData(array $orderItems, array $orderItemDetails, array $publicParams): LogicResult
    {
        if (empty($orderItems))
        {
            return self::Fail('构建待出库详情数据，缺少订单明细', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('构建待出库详情数据，缺少公共参数', 400);
        }

        // 获取订单商品信息
        $itemIds = array_unique(array_column($orderItems, 'item_id'));
        if (!empty($orderItemDetails))
        {
            $itemIds = array_merge($itemIds, array_unique(array_column($orderItemDetails, 'item_id')));
            $itemIds = array_unique($itemIds);
        }

        $getItemInfoRes = ItemLogic::GetItemFullInfo(itemIds: $itemIds, publicParams: $publicParams);
        if ($getItemInfoRes->isFail())
        {
            return $getItemInfoRes;
        }

        $getItemInfoRes = $getItemInfoRes->getData();
        $getItemInfoRes = array_column($getItemInfoRes, null, 'id');
        if (empty($getItemInfoRes))
        {
            return self::Fail('构建待出库详情数据，商品信息不存在', 33000);
        }

        // 如果存在组合明细，则按照订单商品ID分组，[组合1 => [商品1，商品2]]
        $orderItemDetailGrouped = [];
        if (!empty($orderItemDetails))
        {
            foreach ($orderItemDetails as $detail)
            {
                $orderItemDetailGrouped[$detail['order_item_id']][] = $detail;
            }
        }

        // 处理订单商品
        $pendingDetailsData = [];
        foreach ($orderItems as $curOrderItem)
        {
            // 单品
            if (empty($curOrderItem['is_suit']))
            {
                // 当前商品信息
                $curItemInfo = $getItemInfoRes[$curOrderItem['item_id']] ?? [];
                if (empty($curItemInfo))
                {
                    continue;
                }

                $curFirstSaleType = $curItemInfo['first_sale_type_id'];
                if ($curFirstSaleType == ItemSaleTyeEnum::FirstDrug->value)
                {
                    $curIsMultiple       = false;
                    $getPendingDetailRes = self::BuildPendingDetailRecord($curOrderItem, StockPendingOrderDetailTypeEnum::Drug->value, $publicParams);
                }
                elseif ($curFirstSaleType == ItemSaleTyeEnum::FirstTest->value)
                {
                    // 化验项目获取绑定的耗材
                    $curIsMultiple       = true;
                    $getPendingDetailRes = self::GetTestRelationConsumables($curOrderItem, $publicParams);
                }
                else
                {
                    continue;
                }

                $curPendingDetailRecord = $getPendingDetailRes->getData();
                if (empty($curPendingDetailRecord))
                {
                    continue;
                }

                // 化验可能存在多个耗材
                if (!empty($curIsMultiple))
                {
                    $pendingDetailsData = array_merge($pendingDetailsData, $curPendingDetailRecord);
                }
                else
                {
                    $pendingDetailsData[] = $curPendingDetailRecord;
                }
            }
            // 组合
            else
            {
                $curSuitItemDetailList = $orderItemDetailGrouped[$curOrderItem['id']] ?? [];
                foreach ($curSuitItemDetailList as $curSuitSubItem)
                {
                    $curSubItemId   = $curSuitSubItem['item_id'] ?? 0;
                    $curSubItemInfo = $getItemInfoRes[$curSubItemId] ?? [];
                    if (empty($curSubItemId) || empty($curSubItemInfo))
                    {
                        continue;
                    }

                    $curSubItemFirstSaleType = $curSubItemInfo['first_sale_type_id'];
                    if ($curSubItemFirstSaleType == ItemSaleTyeEnum::FirstDrug->value)
                    {
                        // 组合中的药品
                        $curIsMultiple       = false;
                        $getPendingDetailRes = self::BuildPendingDetailRecord($curSuitSubItem, StockPendingOrderDetailTypeEnum::SuitDrug->value, $publicParams);
                    }
                    elseif ($curSubItemFirstSaleType == ItemSaleTyeEnum::FirstTest->value)
                    {
                        // 组合中的化验项目
                        $curIsMultiple       = true;
                        $getPendingDetailRes = self::GetTestRelationConsumables($curSuitSubItem, $publicParams);
                    }
                    else
                    {
                        continue;
                    }

                    $curPendingDetailRecord = $getPendingDetailRes->getData();
                    if (empty($curPendingDetailRecord))
                    {
                        continue;
                    }

                    // 化验可能存在多个耗材
                    if (!empty($curIsMultiple))
                    {
                        $pendingDetailsData = array_merge($pendingDetailsData, $curPendingDetailRecord);
                    }
                    else
                    {
                        $pendingDetailsData[] = $curPendingDetailRecord;
                    }
                }
            }
        }

        return self::Success($pendingDetailsData);
    }

    /**
     * 获取化验项目耗材
     *
     * @param array $orderItem
     * @param array $publicParams
     *
     * @return LogicResult
     */
    private static function GetTestRelationConsumables(array $orderItem, array $publicParams): LogicResult
    {
        if (empty($orderItem))
        {
            return self::Fail('构建待出库详情数据-获取化验项目耗材，缺少订单商品信息', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('构建待出库详情数据-获取化验项目耗材，缺少公共参数', 400);
        }

        // 公共参数
        $hospitalId      = intval(Arr::get($publicParams, '_hospitalId'));
        $hospitalBrandId = intval(Arr::get($publicParams, '_hospitalBrandId'));
        $hospitalOrgId   = intval(Arr::get($publicParams, '_hospitalOrgId'));
        if (empty($hospitalId) || empty($hospitalBrandId) || empty($hospitalOrgId))
        {
            return self::Fail('构建待出库详情数据-获取化验项目耗材，公共参数不存在', 400);
        }

        $getConsumablesRes = TestsConsumablesTemplatesItemModel::getConsumablesByTestItemIds([$orderItem['item_id']], $hospitalId, $hospitalBrandId, $hospitalOrgId);
        $getConsumablesRes = $getConsumablesRes[$orderItem['item_id']] ?? [];
        if (empty($getConsumablesRes))
        {
            return self::Success();
        }

        $pendingDetailsData = [];
        foreach ($getConsumablesRes as $consumable)
        {
            // 预处理耗材数据
            $consumableItem = array_merge($orderItem, [
                'item_id'   => $consumable['item_id'],
                'unit_type' => $consumable['unit_type'],
                'quantity'  => numberMul([$orderItem['quantity'], $consumable['once_use']])
            ]);

            $getPendingDetailRes = self::BuildPendingDetailRecord($consumableItem, StockPendingOrderDetailTypeEnum::Consumables->value, $publicParams);
            if ($getPendingDetailRes->isFail())
            {
                return $getPendingDetailRes;
            }

            $curPendingDetailRecord = $getPendingDetailRes->getData();
            if (!empty($curPendingDetailRecord))
            {
                $pendingDetailsData[] = $curPendingDetailRecord;
            }
        }

        return self::Success($pendingDetailsData);
    }

    /**
     * 构建待出库记录数据
     *
     * @param array $orderItem
     * @param int   $type
     * @param array $publicParams
     *
     * @return LogicResult
     */
    private static function BuildPendingDetailRecord(array $orderItem, int $type, array $publicParams): LogicResult
    {
        if (empty($orderItem) || empty($type))
        {
            return self::Fail('构建待出库记录数据，缺少订单商品信息', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('构建待出库记录数据，缺少公共参数', 400);
        }

        // 公共参数
        $hospitalId      = intval(Arr::get($publicParams, '_hospitalId'));
        $hospitalBrandId = intval(Arr::get($publicParams, '_hospitalBrandId'));
        $hospitalOrgId   = intval(Arr::get($publicParams, '_hospitalOrgId'));
        if (empty($hospitalId) || empty($hospitalBrandId) || empty($hospitalOrgId))
        {
            return self::Fail('构建待出库记录数据，公共参数不存在', 400);
        }

        // 如果组合ID不为空，则说明是组合子项。orderItemId需要关联上一层order_item_id
        if (!empty($orderItem['item_suit_id']) && !empty($orderItem['order_item_id']))
        {
            $orderItemId       = $orderItem['order_item_id'];
            $orderItemDetailId = $orderItem['id'];
        }
        else
        {
            $orderItemId       = $orderItem['id'];
            $orderItemDetailId = 0;
        }

        $bulkQuantity = 0;
        $packQuantity = 0;
        if ($orderItem['unit_type'] == ItemUnitTypeEnum::UNIT_TYPE_BULK->value)
        {
            $bulkQuantity = $orderItem['quantity'];
        }
        if ($orderItem['unit_type'] == ItemUnitTypeEnum::UNIT_TYPE_PACK->value)
        {
            $packQuantity = $orderItem['quantity'];
        }

        return self::Success([
                                 'uid'                  => generateUUID(),
                                 'pending_order_id'     => 0,
                                 'org_id'               => $hospitalOrgId,
                                 'brand_id'             => $hospitalBrandId,
                                 'hospital_id'          => $hospitalId,
                                 'order_item_id'        => $orderItemId,
                                 'order_item_detail_id' => $orderItemDetailId,
                                 'type'                 => $type,
                                 'recipe_id'            => 0,
                                 'recipe_item_id'       => $orderItem['relation_id'],
                                 'item_id'              => $orderItem['item_id'],
                                 'pack_quantity'        => $packQuantity,
                                 'bulk_quantity'        => $bulkQuantity,
                             ]);
    }
}
