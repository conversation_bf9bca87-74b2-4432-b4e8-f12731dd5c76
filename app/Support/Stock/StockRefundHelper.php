<?php

namespace App\Support\Stock;

use Arr;
use App\Enums\ItemStatusEnum;
use App\Enums\ItemSaleTyeEnum;
use App\Enums\StockRefundStatusEnum;
use App\Logics\Logic;
use App\Logics\LogicResult;
use App\Logics\V1\ItemLogic;
use App\Logics\V1\StockItemShelfLogic;
use App\Logics\V1\StockItemDailyPriceLogic;

class StockRefundHelper extends Logic
{
    /**
     * 验证采购单提交是否正确，并返回完整信息
     *
     * @param array $addStockRefundParams
     * @param array $publicParams
     * @param array $oldRefundItemInfo
     *
     * @return LogicResult
     */
    public static function GetValidRefundParams(array $addStockRefundParams, array $publicParams, array $oldRefundItemInfo = []): LogicResult
    {
        if (empty($addStockRefundParams))
        {
            return self::Fail('验证采购单，缺少必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('验证采购单，缺少公共必选参数', 400);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        if (empty($hospitalId))
        {
            return self::Fail('验证采购单，缺少医院相关必选参数', 400);
        }

        // 业务参数
        $refundRemark = trimWhitespace(Arr::get($addStockRefundParams, 'remark', ''));
        $submitType   = intval(Arr::get($addStockRefundParams, 'submitType', 0));
        $refundItems  = Arr::get($addStockRefundParams, 'items', []);
        if (empty($refundRemark))
        {
            return self::Fail('退货说明不可为空', 44040);
        }
        if (!in_array($submitType, [StockRefundStatusEnum::Draft->value, StockRefundStatusEnum::Pending->value]))
        {
            return self::Fail('提交类型错误', 44041);
        }
        if (empty($refundItems))
        {
            return self::Fail('退货商品不可为空', 44042);
        }

        // 旧的退货单上存在的商品
        if (!empty($oldRefundItemInfo))
        {
            $oldRefundItemInfo = array_column($oldRefundItemInfo, null, 'uid');
        }

        // 验证退货商品
        foreach ($refundItems as $curRefundItem)
        {
            $curUid      = $curRefundItem['uid'] ?? '';
            $curItemName = $curRefundItem['itemName'] ?? '';
            $curItemUid  = $curRefundItem['itemUid'] ?? '';
            if (empty($curItemUid))
            {
                return self::Fail('验证退货商品，缺少商品UID', 44042);
            }
            if (empty($curRefundItem['itemBarcode']))
            {
                $errorMsg[] = '名称【' . $curItemName . '】，缺少商品条码';
                continue;
            }

            // 编辑提交时，是否商品一致
            $curOldRefundItem = $oldRefundItemInfo[$curUid] ?? [];
            if (!empty($curUid) && empty($curOldRefundItem))
            {
                $errorMsg[] = '名称【' . $curItemName . '】，原退货商品已不存在或已失效，请刷新重试';
                continue;
            }
            if (!empty($curOldRefundItem) && $curRefundItem['itemBarcode'] != $curOldRefundItem['item_barcode'])
            {
                $errorMsg[] = '名称【' . $curItemName . '】，提交退货商品与原商品条码不一致';
                continue;
            }

            // 验证效期
            if (!empty($curRefundItem['expiredDate']) && !checkDateIsValid($curRefundItem['expiredDate']))
            {
                $errorMsg[] = '名称【' . $curItemName . '】，效期错误';
                continue;
            }

            // 验证退货数量
            $curPackQuantity = $curRefundItem['packQuantity'] ?: 0;
            $curBulkQuantity = $curRefundItem['bulkQuantity'] ?: 0;
            if ($curPackQuantity <= 0 && $curBulkQuantity <= 0)
            {
                $errorMsg[] = '名称【' . $curItemName . '】，散装数量、整装数量不可同时为0';
            }
        }

        // 退货商品基本校验
        if (!empty($errorMsg))
        {
            return self::Fail(implode("；\n", $errorMsg), 44042);
        }

        // 获取退货商品信息
        $arrUniqueItemUids = array_unique(array_column($refundItems, 'itemUid'));
        $getItemInfoRes    = ItemLogic::GetItemFullInfo(itemUids: $arrUniqueItemUids, publicParams: $publicParams);
        if ($getItemInfoRes->isFail())
        {
            return $getItemInfoRes;
        }

        $getItemInfoRes = $getItemInfoRes->getData();
        $getItemInfoRes = array_column($getItemInfoRes, null, 'uid');
        if (empty($getItemInfoRes))
        {
            return self::Fail('退货商品基本信息不存在', 44042);
        }

        // 获取退货商品库存（此处需要获取的是按照效期为维度）
        $arrItemIds                    = array_column($getItemInfoRes, 'id');
        $getStockItemQuantityDetailRes = StockItemShelfLogic::GetEffectiveStockGroupedByExpire($arrItemIds, $publicParams);
        if ($getStockItemQuantityDetailRes->isFail())
        {
            return $getStockItemQuantityDetailRes;
        }

        // 获取退货商品加权价格
        $getItemDailyPriceRes = StockItemDailyPriceLogic::GetItemNowDailyPrice($arrItemIds, $publicParams);
        if ($getItemDailyPriceRes->isFail())
        {
            return $getItemDailyPriceRes;
        }

        // 计算退货单退货总金额（申请发起时的库存加权成本价之和）
        $refundTotalPrice = 0;
        foreach ($refundItems as &$newRefundItem)
        {
            $curItemUid  = $newRefundItem['itemUid'] ?? '';
            $curItemName = $newRefundItem['itemName'] ?? '';

            // 商品、商品条码信息
            $curItemBaseInfo    = $getItemInfoRes[$curItemUid] ?? [];
            $curItemBarcodeInfo = $curItemBaseInfo['item_barcode_info'] ?? '';
            if (empty($curItemBaseInfo))
            {
                $errorMsg[] = '名称【' . $curItemName . '】，商品基本信息不存在';
                continue;
            }
            if (empty($curItemBarcodeInfo))
            {
                $errorMsg[] = '名称【' . $curItemName . '】，商品条码信息不存在';
                continue;
            }
            if ($curItemBarcodeInfo['item_barcode'] != $newRefundItem['itemBarcode'])
            {
                $errorMsg[] = '名称【' . $curItemName . '】，商品条码不一致';
                continue;
            }
            if (!empty($curItemBaseInfo['is_shelf_life']) && (empty($newRefundItem['expiredDate']) || !checkDateIsValid($newRefundItem['expiredDate'])))
            {
                $errorMsg[] = '名称【' . $curItemName . '】，效期管理商品，必须带有效期';
                continue;
            }
            if ($curItemBaseInfo['status'] != ItemStatusEnum::Online->value)
            {
                $errorMsg[] = '名称【' . $curItemName . '】，商品非上线状态';
                continue;
            }
            if ($curItemBaseInfo['first_sale_type_id'] != ItemSaleTyeEnum::FirstDrug->value)
            {
                $errorMsg[] = '名称【' . $curItemName . '】，商品非普通商品、药品';
                continue;
            }

            // 商品ID
            $curItemId = $curItemBaseInfo['id'];

            // 匹配同效期库存
            $curItemAllExpireStock = $getStockItemQuantityDetailRes->getData($curItemId, []);
            $curItemStockInfo      = [];
            foreach ($curItemAllExpireStock as $curExpireStock)
            {
                // 如果商品无有效效期，同时当前库存行也是无效期
                if ((is_null($newRefundItem['expiredDate']) || $newRefundItem['expiredDate'] == '0000-00-00') && (is_null($curExpireStock['expiredDate']) || $curExpireStock['expiredDate'] == '0000-00-00'))
                {
                    $curItemStockInfo = $curExpireStock;
                    break;
                }

                // 退货商品效期与当前库存效期一致
                if ($newRefundItem['expiredDate'] == $curExpireStock['expiredDate'])
                {
                    $curItemStockInfo = $curExpireStock;
                    break;
                }
            }

            // 验证库存是否满足
            if (empty($curItemStockInfo))
            {
                $errorMsg[] = '名称【' . $curItemName . '】，无对应效期库存，请重试';
                continue;
            }

            $curPackQuantity       = $newRefundItem['packQuantity'] ?: 0;
            $curBulkQuantity       = $newRefundItem['bulkQuantity'] ?: 0;
            $getCheckSufficientRes = StockQuantityConversionHelper::checkQuantityIsSufficient($curPackQuantity,
                                                                                              $curBulkQuantity,
                                                                                              $curItemStockInfo['stock']['packQuantity'],
                                                                                              $curItemStockInfo['stock']['bulkQuantity'],
                                                                                              $curItemBaseInfo['bulk_ratio']);
            if (empty($getCheckSufficientRes))
            {
                $errorMsg[] = '名称【' . $curItemName . '】，商品库存不足';
                continue;
            }

            // 商品加权价
            $curItemDailyPrice = $getItemDailyPriceRes->getData($curItemId, []);
            if (empty($curItemDailyPrice))
            {
                $errorMsg[] = '名称【' . $curItemName . '】，商品加权价不存在';
                continue;
            }

            // 整、散加权价
            $curPackDailyPrice = $curItemDailyPrice['packPrice'] ?? 0;
            $curBulkDailyPrice = $curItemDailyPrice['bulkPrice'] ?? 0;
            if ($curPackDailyPrice <= 0 || $curBulkDailyPrice <= 0)
            {
                $errorMsg[] = '名称【' . $curItemName . '】，商品加权价无效';
                continue;
            }

            // 计算申请退货商品整、散总价
            $curTotalBulkPrice = 0;
            $curTotalPackPrice = 0;
            if ($curPackQuantity > 0)
            {
                $curTotalPackPrice = numberMul([$curPackQuantity, $curPackDailyPrice], 4);
            }
            if ($curBulkQuantity > 0)
            {
                $curTotalBulkPrice = numberMul([$curBulkQuantity, $curBulkDailyPrice], 4);
            }
            if ($curTotalPackPrice <= 0 && $curTotalBulkPrice <= 0)
            {
                $errorMsg[] = '名称【' . $curItemName . '】，退货总金额错误';
                continue;
            }

            $newRefundItem['packPrice'] = $curPackQuantity > 0 ? $curPackDailyPrice : 0;
            $newRefundItem['bulkPrice'] = $curBulkQuantity > 0 ? $curBulkDailyPrice : 0;
            $newRefundItem['itemId']    = $curItemId;
            $newRefundItem['itemInfo']  = $curItemBaseInfo;

            $refundTotalPrice = numberAdd([$refundTotalPrice, $curTotalPackPrice, $curTotalBulkPrice]);
        }

        if (!empty($errorMsg))
        {
            return self::Fail(implode("；\n", $errorMsg), 44042);
        }

        // 验证后的退货商品
        $addStockRefundParams['remark']           = $refundRemark;
        $addStockRefundParams['items']            = $refundItems;
        $addStockRefundParams['refundTotalPrice'] = $refundTotalPrice;

        return self::Success($addStockRefundParams);
    }

    /**
     * 对比退货商品明细差异，构建更新数据和日志
     *
     * @param array $oldRefundItemInfo stock_refund_item中的一行数据
     * @param array $newRefundItemInfo 提交的退货商品数据
     *
     * @return array [$updateData, $logContent]
     */
    public static function CompareRefundItemChange(array $oldRefundItemInfo, array $newRefundItemInfo): array
    {
        $fieldMap = [
            'packQuantity' => ['label' => '整装退货数量', 'unit' => '个', 'db_field' => 'pack_quantity', 'type' => 'int'],
            'bulkQuantity' => ['label' => '散装退货数量', 'unit' => '个', 'db_field' => 'bulk_quantity', 'type' => 'int'],
        ];

        $updateData = [];
        $logLines   = [];
        foreach ($fieldMap as $camelField => $meta)
        {
            $oldVal = $oldRefundItemInfo[$meta['db_field']] ?? null;
            $newVal = $newRefundItemInfo[$camelField] ?? null;

            $isDifferent = match ($meta['type'])
            {
                'decimal' => bccomp($oldVal, $newVal, 4) !== 0,
                'int' => (int) $oldVal !== (int) $newVal,
                default => (string) $oldVal !== (string) $newVal,
            };

            if ($isDifferent)
            {
                $updateData[$meta['db_field']] = $newVal;
                $logLines[]                    = "  {$meta['label']}由$oldVal{$meta['unit']}变为$newVal{$meta['unit']}";
            }
        }

        if (!empty($updateData))
        {
            $logContent = "修改商品:【{$newRefundItemInfo['itemInfo']['id']}】{$newRefundItemInfo['itemName']}" . implode(";", $logLines);

            return [$updateData, $logContent];
        }

        return [];
    }
}
