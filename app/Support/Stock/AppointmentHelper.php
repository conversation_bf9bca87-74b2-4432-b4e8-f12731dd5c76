<?php

namespace App\Support\Stock;

use Arr;
use Exception;
use App\Logics\Logic;
use App\Logics\LogicResult;
use App\Logics\V1\MemberLogic;
use App\Models\UsersModel;
use App\Models\HospitalUserModel;
use App\Models\AppointmentReasonModel;

class AppointmentHelper extends Logic
{
    /**
     * 验证预约参数
     *
     * @param array $appointmentParams
     * @param array $publicParams
     *
     * @return LogicResult
     * @throws Exception
     */
    public static function GetValidatedAppointmentParams(array $appointmentParams, array $publicParams): LogicResult
    {
        if (empty($appointmentParams))
        {
            return self::Fail('验证预约，缺少必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('验证预约，缺少公共必选参数', 400);
        }

        // 公共参数
        $hospitalOrgId   = intval(Arr::get($publicParams, '_hospitalOrgId'));
        $hospitalBrandId = intval(Arr::get($publicParams, '_hospitalBrandId'));
        $hospitalId      = intval(Arr::get($publicParams, '_hospitalId'));
        $userId          = intval(Arr::get($publicParams, '_userId'));
        if (empty($hospitalOrgId) || empty($hospitalBrandId) || empty($hospitalId) || empty($userId))
        {
            return self::Fail('验证预约，缺少公共必选参数', 400);
        }

        // 业务参数
        $typeId     = intval(Arr::get($appointmentParams, 'type', 0));
        $categoryId = intval(Arr::get($appointmentParams, 'category', 0));
        $date       = trimWhitespace(Arr::get($appointmentParams, 'date'));
        $startTime  = trimWhitespace(Arr::get($appointmentParams, 'startTime'));
        $endTime    = trimWhitespace(Arr::get($appointmentParams, 'endTime'));
        $salutation = trimWhitespace(Arr::get($appointmentParams, 'salutation'));
        $phone      = trimWhitespace(Arr::get($appointmentParams, 'phone'));
        $doctorUid  = trimWhitespace(Arr::get($appointmentParams, 'doctorUid'));
        $petUids    = Arr::get($appointmentParams, 'petUid', []);
        $remark     = trimWhitespace(Arr::get($appointmentParams, 'remark'));
        if (empty($typeId) || empty($categoryId))
        {
            return self::Fail('验证预约，类型错误', 34001);
        }
        if (empty($date) || !checkDateIsValid($date))
        {
            return self::Fail('验证预约，时间错误', 34001);
        }
        if (empty($startTime) || empty($endTime))
        {
            return self::Fail('验证预约，时间错误', 34001);
        }
        if (empty($salutation))
        {
            return self::Fail('验证预约，称呼错误', 34001);
        }
        if (empty($phone) || !checkValidCellphone($phone))
        {
            return self::Fail('验证预约，客户电话错误', 34001);
        }
        if (empty($doctorUid))
        {
            return self::Fail('验证预约，医生/美容师错误', 34001);
        }

        // 验证时间格式
        if (!preg_match('/^\d{2}:\d{2}$/', $startTime) || !preg_match('/^\d{2}:\d{2}$/', $endTime))
        {
            return self::Fail('时间格式错误，请使用HH:MM格式', 34001);
        }

        // 验证日期不能小于当前日期
        $appointmentDate = strtotime($date);
        $currentDate     = strtotime(date('Y-m-d'));
        if ($appointmentDate < $currentDate)
        {
            return self::Fail('预约日期不能小于当前日期', 34001);
        }

        // 验证时间段逻辑
        if ($startTime >= $endTime)
        {
            return self::Fail('开始时间不能大于或等于结束时间', 34001);
        }

        // 如果是当天预约，验证时间不能小于当前时间
        if ($appointmentDate == $currentDate)
        {
            $currentDateTime          = time();
            $appointmentStartDateTime = strtotime($date . ' ' . $startTime);

            if ($appointmentStartDateTime <= $currentDateTime)
            {
                return self::Fail('当天预约时间不能小于或等于当前时间', 34001);
            }
        }

        // 验证时间是否为有效时间
        $startTimeValid = strtotime($date . ' ' . $startTime);
        $endTimeValid   = strtotime($date . ' ' . $endTime);
        if ($startTimeValid === false || $endTimeValid === false)
        {
            return self::Fail('预约时间无效', 34001);
        }

        // 验证宠物UID是否包含重复
        if (!empty($petUids))
        {
            // 过滤空值
            $petUids = array_filter($petUids, function ($petUid) {
                return !empty(trimWhitespace($petUid));
            });

            // 检查是否有重复
            $uniquePetUids = array_unique($petUids);
            if (count($petUids) !== count($uniquePetUids))
            {
                return self::Fail('预约宠物重复，请检查', 34001);
            }
        }

        // 预约医生是否存在
        $getDoctorInfoRes = UsersModel::getOneByUid($doctorUid);
        if (empty($getDoctorInfoRes))
        {
            return self::Fail('预约医生/美容师不存在', 34001);
        }

        $getHospitalDoctorRes = HospitalUserModel::getHospitalUsers($hospitalId, [$getDoctorInfoRes['id']]);
        if ($getHospitalDoctorRes->isEmpty())
        {
            return self::Fail('预约医生/美容师不存在', 34001);
        }

        // 预约原因是否存在
        $getReasonRes = AppointmentReasonModel::getData(where: ['id' => $categoryId, 'status' => 1]);
        $getReasonRes = Arr::first($getReasonRes);
        if (empty($getReasonRes))
        {
            return self::Fail('预约原因不存在', 34001);
        }
        if ($getReasonRes['appointment_type_id'] != $typeId)
        {
            return self::Fail('预约原因类型不匹配', 34001);
        }

        // 获取预约用户，可能为空
        $getMemberRes = MemberLogic::GetMemberForByPhone($phone, $publicParams, true);
        if ($getMemberRes->isFail())
        {
            return $getMemberRes;
        }

        // 预约用户信息、宠物列表
        $memberInfo        = $getMemberRes->getData('memberInfo', []);
        $memberPetListInfo = $getMemberRes->getData('petList', []);

        // 用户不存在，选择了宠物
        if (empty($memberInfo) && !empty($petUids))
        {
            return self::Fail('预约用户不存在，不可选择宠物', 34001);
        }
        if (empty($memberPetListInfo) && !empty($petUids))
        {
            return self::Fail('预约用户无宠物，不可选择宠物', 34001);
        }

        // 预约存在的用户
        $memberId = 0;
        $petIds   = [];
        if (!empty($memberInfo))
        {
            $memberId = $memberInfo['id'];

            // 选择预约的宠物
            if (!empty($petUids))
            {
                $memberPetListInfo = array_column($memberPetListInfo, null, 'uid');
                $diffPetUids       = array_diff($petUids, array_keys($memberPetListInfo));
                if (!empty($diffPetUids))
                {
                    return self::Fail('预约用户宠物不存在', 34001);
                }

                array_walk($petUids, function ($petUid) use (&$petIds, $memberPetListInfo) {
                    $petIds[] = $memberPetListInfo[$petUid]['id'];
                });
            }
        }

        return self::Success([
                                 'member_id'  => $memberId,
                                 'salutation' => $salutation,
                                 'phone'      => $phone,
                                 'type_id'    => $typeId,
                                 'reason_id'  => $categoryId,
                                 'start_time' => $date . ' ' . $startTime,
                                 'end_time'   => $date . ' ' . $endTime,
                                 'exec_id'    => $getDoctorInfoRes['id'],
                                 'remark'     => $remark,
                                 'petIds'     => $petIds,
                             ]);
    }
}
