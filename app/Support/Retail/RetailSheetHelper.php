<?php

namespace App\Support\Retail;

use ArrayAccess;
use Illuminate\Support\Arr;
use App\Logics\Logic;
use App\Logics\LogicResult;
use App\Enums\ItemSaleTyeEnum;
use App\Enums\ItemTypeEnum;
use App\Enums\ItemUnitTypeEnum;
use App\Enums\SheetStatusEnum;
use App\Models\ItemModel;
use App\Models\ItemBarcodeModel;
use App\Models\ItemSalePriceModel;
use App\Models\ItemSaleTypeModel;
use App\Models\RetailSheetModel;
use App\Models\MemberModel;
use App\Logics\V1\HospitalLogic;
use App\Support\User\HospitalUserHelper;
use App\Support\Item\ItemHelper;

/**
 * 零售购买单助手
 * Class RetailSheetHelper
 * @package App\Support\Retail
 */
class RetailSheetHelper extends Logic
{
    /**
     * 获取有效的零售购买单
     *
     * @param string $sheetCode
     * @param int    $sheetId
     * @param int    $hospitalId
     *
     * @return LogicResult
     */
    public static function GetValidSheet(string $sheetCode = '', int $sheetId = 0, int $hospitalId = 0): LogicResult
    {
        $sheet = RetailSheetModel::GetSheetByCodeOrId($sheetCode, $sheetId, $hospitalId);
        if (empty($sheet))
        {
            return self::Fail('零售购买单不存在', 500710);
        }

        if ($sheet->status < SheetStatusEnum::Unpaid->value)
        {
            return self::Fail('零售购买单已删除', 500712);
        }

        return self::Success($sheet->toArray());
    }

    /**
     * 验证购买单内商品基本信息（商品状态、整装是否可售卖、商品价格、开具数量）
     *
     * @param array $items
     * @param array $publicParams
     *
     * @return LogicResult
     */
    public static function GetValidSheetItems(array $items, array $publicParams): LogicResult
    {
        if (empty($items))
        {
            return self::Fail('缺少商品明细', 400);
        }

        //公共参数
        $hospitalOrgId   = intval(Arr::get($publicParams, '_hospitalOrgId'));
        $hospitalBrandId = intval(Arr::get($publicParams, '_hospitalBrandId'));
        $hospitalId      = intval(Arr::get($publicParams, '_hospitalId'));
        if (empty($hospitalOrgId) || empty($hospitalBrandId) || empty($hospitalId))
        {
            return self::Fail('公共参数缺少医院信息', 400);
        }

        // 商品明细基础验证
        $errorMsg   = [];
        $itemInfos  = [];
        $itemUids   = [];
        $totalPrice = 0;
        foreach ($items as $value)
        {
            $curItemName = $value['name'] ?? '';
            if (empty($value['itemUid']))
            {
                $errorMsg[] = '名称【' . $curItemName . '】，缺少商品UID';
                continue;
            }
            if (in_array($value['itemUid'], $itemUids))
            {
                $errorMsg[] = '名称【' . $curItemName . '】，重复';
                continue;
            }
            if (empty($value['itemBarcode']))
            {
                $errorMsg[] = '名称【' . $curItemName . '】，缺少商品条码';
                continue;
            }
            if (!empty($value['isSuit']))
            {
                $errorMsg[] = '名称【' . $curItemName . '】为组合商品，零售业务暂不支持';
                continue;
            }
            if (!empty($value['type']) && !empty($value['type']['id']) && !in_array($value['type']['id'],
                                                                                    ItemSaleTyeEnum::RETAIL_SALE_TYPE))
            {
                $errorMsg[] = '名称【' . $curItemName . '】非商品、药品的项目，零售业务暂不支持';
                continue;
            }
            if (!is_numeric($value['quantity']) || $value['quantity'] <= 0)
            {
                $errorMsg[] = '名称【' . $curItemName . '】，数量参数错误';
                continue;
            }
            // 开具单位，1:散；2:整。
            $curUnitType = $value['unitType'];
            if (!in_array($curUnitType, ItemUnitTypeEnum::RETAIL_UNIT_TYPE))
            {
                $errorMsg[] = '名称【' . $curItemName . '】，单位类型参数错误';
                continue;
            }
            if (bccomp($value['price'], '0', 2) != 1)
            {
                $errorMsg[] = '名称【' . $curItemName . '】，单价参数错误';
                continue;
            }
            if (bccomp($value['totalPrice'], '0', 2) != 1)
            {
                $errorMsg[] = '名称【' . $curItemName . '】，金额参数错误';
                continue;
            }

            $itemInfos[] = $value;
            $itemUids[]  = $value['itemUid'];
        }

        // 商品基本校验
        if (!empty($errorMsg))
        {
            return self::Fail(implode("；\n", $errorMsg), 500600);
        }

        // 无有效商品
        if (empty($itemInfos) || empty($itemUids))
        {
            return self::Fail('零售购买单中无有效的商品', 500601);
        }

        // 购买单中单品
        $itemsBaseInfo = ItemModel::getData(where: ['org_id' => $hospitalOrgId],
            whereIn:                               ['uid' => $itemUids],
            keyBy:                                 'uid');

        if (empty($itemsBaseInfo))
        {
            return self::Fail('洗美购买单中单品商品不存在', 500202);
        }

        // 购买单中单品ID
        $itemIds = array_column($itemsBaseInfo, 'id');

        // 购买单中商品条码
        $itemsBarcodes = ItemBarcodeModel::getData(where: ['org_id' => $hospitalOrgId, 'status' => 1],
            whereIn:                                      ['item_id' => $itemIds],
            keyBy:                                        'item_id');

        // 获取医院信息，为了获取商品价格使用。
        $getHospitalRes = HospitalLogic::GetHospitalBaseInfo('', $hospitalId, true, true);
        if ($getHospitalRes->isFail())
        {
            return $getHospitalRes;
        }

        // 医院所属城市
        $hospitalProvinceId = $getHospitalRes->getData('addressInfo.provinceId', 0);
        $hospitalCityId     = $getHospitalRes->getData('addressInfo.cityId', 0);

        // 获取商品价格
        $getItemsPriceRes = ItemSalePriceModel::getItemSalePrice(ItemTypeEnum::Sku->value,
                                                                 $itemIds,
                                                                 $hospitalOrgId,
                                                                 $hospitalBrandId,
                                                                 $hospitalProvinceId,
                                                                 $hospitalCityId,
                                                                 $hospitalId);
        if (empty($getItemsPriceRes))
        {
            return self::Fail('零售商品价格获取失败', 500610);
        }

        $returnResult = [
            'item'       => [],
            'totalPrice' => 0,
            'itemsInfo'  => [],
        ];
        foreach ($items as $value)
        {
            $curItemUid  = $value['itemUid'] ?? '';
            $curItemName = $value['name'] ?? '';

            $curItemBaseInfo = $itemsBaseInfo[$curItemUid] ?? [];

            // 商品不存在
            $curItemId = $curItemBaseInfo['id'] ?? 0;
            if (empty($curItemBaseInfo) || empty($curItemId))
            {
                $errorMsg[] = '名称【' . $curItemName . '】，商品不存在';
                continue;
            }

            // 商品条码
            $curItemBarcodeInfo = $itemsBarcodes[$curItemId] ?? [];
            $curItemBarcode     = $curItemBarcodeInfo['item_barcode'] ?? '';
            if ($curItemBarcode != $value['itemBarcode'])
            {
                $errorMsg[] = '名称【' . $curItemName . '】，商品条码不一致';
                continue;
            }

            // 商品非上线状态
            if ($curItemBaseInfo['status'] != 1)
            {
                $errorMsg[] = '名称【' . $curItemName . '】，商品非上线状态，不能保存购买单';
                continue;
            }

            // 商品一级类型是否可开具
            if (!in_array($curItemBaseInfo['first_sale_type_id'], ItemSaleTyeEnum::RETAIL_SALE_TYPE))
            {
                $errorMsg[] = '名称【' . $curItemName . '】，商品一级类型非零售可开具，不能保存购买单';
                continue;
            }

            // 商品是否零售可开具
            if (isset($curItemBaseInfo['is_retail_allow']) && $curItemBaseInfo['is_retail_allow'] != 1)
            {
                $errorMsg[] = '名称【' . $curItemName . '】，商品非零售可开具，不能保存购买单';
                continue;
            }

            // 商品整装是否可售卖
            $curUnitType = $value['unitType'] ?? ItemUnitTypeEnum::getRetailDefaultUnit();
            if ($curUnitType == ItemUnitTypeEnum::UNIT_TYPE_PACK->value && $curItemBaseInfo['is_pack_sale_allow'] != 1)
            {
                $errorMsg[] = '名称【' . $curItemName . '】，商品整装不可售卖';
                continue;
            }

            // 获取商品价格
            $curItemPriceInfo = $getItemsPriceRes[$curItemId]['sale_price'] ?? [];

            // 价格可以为0，但是不可以不存在价格记录
            if (empty($curItemPriceInfo))
            {
                $errorMsg[] = '名称【' . $curItemName . '】，价格不存在';
                continue;
            }

            // 商品价格
            $curItemPrice = $curItemPriceInfo[ItemUnitTypeEnum::UNIT_TYPE_PRICE_FIELD[$curUnitType]] ?? 0;
            if (bccomp($curItemPrice, '0', 2) == - 1)
            {
                $errorMsg[] = '名称【' . $curItemName . '】，价格不能小于0';
                continue;
            }

            if (bccomp($value['price'], $curItemPrice, 2) != 0)
            {
                $errorMsg[] = '名称【' . $curItemName . '】，价格有变动，请是刷新重试';
            }

            if (bccomp($curItemPrice * $value['quantity'], $value['totalPrice'], 2) != 0)
            {
                $errorMsg[] = '名称【' . $curItemName . '】，价格有变动，请是刷新重试';
            }

            // 增加商品ID、价格
            $value['itemId']    = $curItemId;
            $value['itemPrice'] = $curItemPrice;

            $totalPrice = numberAdd([$totalPrice, $curItemPrice * $value['quantity']]);

            $returnResult['item'][] = $value;
        }

        if (!empty($errorMsg))
        {
            return self::Fail(implode("；\n", $errorMsg), 500690);
        }

        $returnResult['itemsInfo']  = array_column($itemsBaseInfo, null, 'id');
        $returnResult['totalPrice'] = $totalPrice;

        return self::Success($returnResult);
    }

    /**
     * 获取添加购买单内单品
     *
     * @param array $addItems
     * @param array $itemsInfo
     * @param array $publicParams
     *
     * @return LogicResult
     */
    public static function GetAddItems(array $addItems, array $itemsInfo, array $publicParams): LogicResult
    {
        if (empty($addItems) || empty($itemsInfo) || empty($publicParams))
        {
            return self::Fail('保存购买单，商品参数错误', 400);
        }

        // 公共参数
        $userId = intval(Arr::get($publicParams, '_userId', 0));

        $statusErrorMsg = [];
        $addTotalPrice  = 0;
        $returnAddItems = [];
        foreach ($addItems as $value)
        {
            $itemId      = $value['itemId'] ?? '';
            $curItemName = $value['name'] ?? '';
            $curItemInfo = $itemsInfo[$itemId] ?? [];
            if (empty($curItemInfo))
            {
                $statusErrorMsg[] = '商品【' . $curItemName . '】，信息不存在';
                continue;
            }

            // 当前商品库存管理非精确计量，采用进一法
            $curQuantity = bcmul($value['quantity'], 1, 2);
            if (empty($curItemInfo['is_precise_metering']))
            {
                $curQuantity = ceil($curQuantity);
            }

            // 商品整装是否可售卖
            $curUnitType = $value['unitType'] ?? ItemUnitTypeEnum::getRetailDefaultUnit();
            if ($curUnitType == ItemUnitTypeEnum::UNIT_TYPE_PACK->value && $curItemInfo['is_pack_sale_allow'] != 1)
            {
                $statusErrorMsg[] = '名称【' . $curItemName . '】，商品整装不可售卖';
                continue;
            }

            // 商品是否零售可开具
            if (isset($curItemInfo['is_retail_allow']) && $curItemInfo['is_retail_allow'] != 1)
            {
                $statusErrorMsg[] = '名称【' . $curItemName . '】，商品非零售可开具，不能保存购买单';
                continue;
            }

            // 价格可以为0，但是不可以不存在价格记录
            if (!isset($value['itemPrice']))
            {
                $statusErrorMsg[] = '商品【' . $curItemName . '】，价格不存在';

                return self::Fail(implode("；\n", $statusErrorMsg), 500690);
            }

            if (bccomp($value['price'], $value['itemPrice'], 2) != 0)
            {
                $statusErrorMsg[] = '名称【' . $curItemName . '】，价格有变动，请是刷新重试';

                return self::Fail(implode("；\n", $statusErrorMsg), 500690);
            }

            if (bccomp($value['itemPrice'] * $curQuantity, $value['totalPrice'], 2) != 0)
            {
                $statusErrorMsg[] = '名称【' . $curItemName . '】，价格有变动，请是刷新重试';

                return self::Fail(implode("；\n", $statusErrorMsg), 500690);
            }

            // 如果是编辑会存在uid，新增则为null
            if (!empty($value['uid']))
            {
                $curUid = $value['uid'];
            }
            else
            {
                $curUid = generateUUID();
            }

            $tmpData = [
                'item_uid'     => $value['itemUid'],
                'uid'          => $curUid,
                'sheet_id'     => 0,
                'item_id'      => $itemId,
                'item_barcode' => $value['itemBarcode'],
                'unit_type'    => $curUnitType,
                'price'        => $value['itemPrice'],
                'quantity'     => $curQuantity,
                'status'       => 1,
                'created_by'   => $userId,
            ];

            $addTotalPrice    = numberAdd([
                                              $addTotalPrice,
                                              numberMul([$curQuantity, $value['itemPrice']])
                                          ]);
            $returnAddItems[] = $tmpData;
        }

        if (!empty($statusErrorMsg))
        {
            return self::Fail(implode("；\n", $statusErrorMsg), 500690);
        }

        return self::Success(['totalPrice' => $addTotalPrice, 'items' => $returnAddItems]);
    }

    /**
     * 格式化构购买单主信息
     *
     * @param array       $sheets
     * @param array       $publicParams
     * @param bool        $withId
     * @param string|null $keyBy
     *
     * @return LogicResult
     */
    public static function FormatDetailStructure(
        array $sheets, array $publicParams, bool $withId = false, ?string $keyBy = null
    ): LogicResult
    {
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));

        if (empty($sheets))
        {
            return self::Fail('零售购买单信息错误', 400);
        }
        if (empty($publicParams) || empty($hospitalId))
        {
            return self::Fail('公共参数错误', 400);
        }


        //获取关联的数据
        $memberIds     = array_unique(array_column($sheets, 'member_id'));
        $createUserIds = array_unique(array_column($sheets, 'created_by'));

        //获取会员信息
        $getMemberRes = MemberModel::getManyByIds($memberIds)
                                   ->keyBy('id');

        //获取创建人信息
        $getCreateUserRes = HospitalUserHelper::GetUserOptionsByUserIds($createUserIds, $hospitalId, true);

        $result = [];
        foreach ($sheets as $sheet)
        {
            $curMemberInfo = !empty($getMemberRes[$sheet['member_id']]) ? [
                'uid'   => $getMemberRes[$sheet['member_id']]['uid'],
                'name'  => $getMemberRes[$sheet['member_id']]['name'],
                'phone' => secretCellphone($getMemberRes[$sheet['member_id']]['phone']),
            ] : null;
            $curCreateUser = $getCreateUserRes[$sheet['created_by']] ?? null;

            $tmp = [
                'sheetCode'  => $sheet['sheet_code'],
                'memberInfo' => $curMemberInfo,
                'price'      => formatDisplayNumber($sheet['price'], 2, false),
                'status'     => [
                    'id'   => $sheet['status'],
                    'name' => SheetStatusEnum::getDescription($sheet['status']),
                ],
                'createUser' => $curCreateUser,
                'orderTime'  => formatDisplayDateTime($sheet['order_time']),
                'createTime' => formatDisplayDateTime($sheet['created_at']),
                'updateTime' => formatDisplayDateTime($sheet['updated_at']),
            ];

            if ($withId)
            {
                $tmp['id'] = $sheet['id'];
            }

            if (!empty($keyBy) && isset($sheet[$keyBy]))
            {
                $result[$sheet[$keyBy]] = $tmp;
            }
            else
            {
                $result[] = $tmp;
            }
        }

        return self::Success($result);
    }

    /**
     * 格式化构购买单内商品明细
     *
     * @param array       $sheetItems
     * @param array       $publicParams
     * @param bool        $withItemPrice
     * @param string|null $keyBy
     *
     * @return LogicResult
     */
    public static function FormatSheetItemStructure(
        array $sheetItems, array $publicParams, bool $withItemPrice = false, ?string $keyBy = null
    ): LogicResult
    {
        if (empty($sheetItems))
        {
            return self::Fail('格式化零售购买单详情，明细不存在', 500711);
        }

        // 公共参数
        $orgId      = intval(Arr::get($publicParams, '_hospitalOrgId', 0));
        $brandId    = intval(Arr::get($publicParams, '_hospitalBrandId', 0));
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId', 0));
        if (empty($orgId) || empty($brandId) || empty($hospitalId))
        {
            return self::Fail('缺少医院必选参数', 400);
        }

        // 购买单内商品ID
        $itemIds = array_column($sheetItems, 'item_id');

        // 获取购买单内商品
        $getItemInfoRes = [];
        if (!empty($itemIds))
        {
            $getItemInfoRes = ItemModel::getManyByIds($itemIds);
            $getItemInfoRes = $getItemInfoRes->isNotEmpty() ? $getItemInfoRes->keyBy('id')
                                                                             ->toArray() : [];
        }
        if (empty($getItemInfoRes))
        {
            return self::Fail('零售购买单中商品全部无效', 500604);
        }

        // 获取商品项目类型
        $getItemSaleTypeRes = ItemSaleTypeModel::getAllSaleType([0, 1]);
        $getItemSaleTypeRes = $getItemSaleTypeRes->isNotEmpty() ? $getItemSaleTypeRes->keyBy('id')
                                                                                     ->toArray() : [];

        // 获取商品价格
        $getItemPriceRes = [];
        if ($withItemPrice)
        {
            // 获取医院信息
            $getHospitalRes = HospitalLogic::GetHospitalBaseInfo('', $hospitalId, true, true);
            if ($getHospitalRes->isFail())
            {
                return $getHospitalRes;
            }

            // 医院所属城市
            $hospitalProvinceId = $getHospitalRes->getData('addressInfo.provinceId', 0);
            $hospitalCityId     = $getHospitalRes->getData('addressInfo.cityId', 0);

            $getItemPriceRes = ItemSalePriceModel::getItemSalePrice(ItemTypeEnum::Sku->value,
                                                                    $itemIds,
                                                                    $orgId,
                                                                    $brandId,
                                                                    $hospitalProvinceId,
                                                                    $hospitalCityId,
                                                                    $hospitalId);
        }

        // TODO 获取商品库存

        // 当前方法内定义递归构建逻辑
        $buildItem = function ($sheetItem) use (
            &$buildItem, $getItemInfoRes, $getItemPriceRes, $getItemSaleTypeRes
        ) {
            // 商品信息、价格
            $curItemInfo      = $getItemInfoRes[$sheetItem['item_id']] ?? [];
            $curItemPriceInfo = $getItemPriceRes[$sheetItem['item_id']]['sale_price'] ?? [];
            $saleTypeFiledKey = 'first_sale_type_id';

            // 商品出库单位规格
            $curItemUseUnit   = $curItemInfo['use_unit'] ?? '';
            $curItemUseRatio  = $curItemInfo['use_ratio'] ?? 0;
            $curItemBulkUnit  = $curItemInfo['bulk_unit'] ?? '';
            $curItemBulkRatio = $curItemInfo['bulk_ratio'] ?? 0;
            $curItemPackUnit  = $curItemInfo['pack_unit'] ?? '';

            // 如果没有整装单位、散装单位使，用计量单位
            if (empty($curItemBulkUnit))
            {
                $curItemBulkUnit = $curItemUseUnit;
            }
            if (empty($curItemPackUnit))
            {
                $curItemPackUnit = $curItemUseUnit;
            }

            // 商品出库单位，1ml/ml、1次、1瓶。（计量比 + 计量单位 + 散装单位 ）
            if ($curItemInfo[$saleTypeFiledKey] == ItemSaleTyeEnum::FirstDrug->value)
            {
                // 处方内使用单位-散装
                if ($sheetItem['unit_type'] == ItemUnitTypeEnum::UNIT_TYPE_BULK->value)
                {
                    $curItemSpec = formatDisplayNumber($curItemUseRatio) . $curItemUseUnit . '/' . $curItemBulkUnit;
                }
                // 处方内使用单位-整装
                else
                {
                    $curItemSpec = formatDisplayNumber($curItemBulkRatio) . $curItemBulkUnit . '/' . $curItemPackUnit;
                }
            }
            else
            {
                $curItemSpec = 1 . $curItemUseUnit;
            }

            // 商品类型
            $curItemTypeInfo = [
                'id'   => $curItemInfo[$saleTypeFiledKey] ?? 0,
                'name' => $getItemSaleTypeRes[$curItemInfo[$saleTypeFiledKey]]['alias_name'] ?? '',
            ];

            $tmpItemData = [
                'uid'          => $sheetItem['uid'] ?? '',
                'itemUid'      => $curItemInfo['uid'] ?? '',
                'itemBarcode'  => $sheetItem['item_barcode'] ?? '',
                'type'         => $curItemTypeInfo,
                'name'         => ItemHelper::ItemDisplayName($curItemInfo),
                'spec'         => $curItemSpec,
                'unit'         => $sheetItem['unit_type'] == ItemUnitTypeEnum::UNIT_TYPE_BULK->value ? $curItemBulkUnit : $curItemPackUnit,
                'unitType'     => $sheetItem['unit_type'],
                'quantity'     => formatDisplayNumber($sheetItem['quantity']),
                'price'        => isset($sheetItem['price']) ? formatDisplayNumber($sheetItem['price']) : 0,
                'totalPrice'   => isset($sheetItem['price']) ? formatDisplayNumber(numberMul([
                                                                                                 $sheetItem['price'],
                                                                                                 $sheetItem['quantity']
                                                                                             ])) : 0,
                'createTime'   => formatDisplayDateTime($sheetItem['created_at']),
                'itemInfo'     => [
                    'uid'               => $curItemInfo['uid'] ?? '',
                    'type'              => $curItemTypeInfo,
                    'name'              => ItemHelper::ItemDisplayName($curItemInfo),
                    'useUnit'           => $curItemUseUnit,
                    'useRatio'          => $curItemUseRatio,
                    'spec'              => $curItemSpec,
                    'packUnit'          => $curItemPackUnit,
                    'packPrice'         => formatDisplayNumber($curItemPriceInfo['pack_sale_price'] ?? 0),
                    'bulkUnit'          => $curItemBulkUnit,
                    'bulkPrice'         => formatDisplayNumber($curItemPriceInfo['bulk_sale_price'] ?? 0),
                    'bulkRatio'         => $curItemBulkRatio,
                    'stock'             => [],
                    'isPreciseMetering' => $curItemInfo['is_precise_metering'] ?? false,
                    'isPackSaleAllow'   => $curItemInfo['is_pack_sale_allow'] ?? false,
                ],
                'isPrintItems' => null,
                'suitItems'    => [],
            ];

            return $tmpItemData;
        };

        // 执行主构建逻辑（排除掉子项）
        $returnItemData = [];
        foreach ($sheetItems as $item)
        {
            if (!empty($keyBy) && isset($item[$keyBy]))
            {
                $returnItemData[$item[$keyBy]] = $buildItem($item);
            }
            else
            {
                $returnItemData[] = $buildItem($item);
            }
        }

        return self::Success($returnItemData);
    }

    /**
     * 验证购买单是否可以编辑或删除
     *
     * @param array|ArrayAccess $sheet
     * @param array             $publicParams
     *
     * @return LogicResult
     */
    public static function CheckSheetEditOrDelete(array|ArrayAccess $sheet, array $publicParams): LogicResult
    {
        $userId = intval(Arr::get($publicParams, '_userId'));

        if (empty($sheet) || empty($publicParams) || empty($userId))
        {
            return self::Fail('验证零售购买单是否可操作，缺少必选参数', 400);
        }

        if (!SheetStatusEnum::EditAble($sheet['status']))
        {
            return self::Fail('零售购买单当前状态不允许此操作', 500740);
        }

        //TODO:根据是否本人和是否院长角色来判断

        return self::Success();
    }
}
