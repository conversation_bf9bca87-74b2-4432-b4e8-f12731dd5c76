<?php

namespace App\Support\Checkout;

use App\Logics\Logic;
use App\Logics\LogicResult;
use App\Enums\SheetBusinessTypeEnum;

class CheckoutHelper extends Logic
{
    /**
     * 验证结算参数-购买单参数
     *
     * 验证参数必选
     * 验证必须单独结算的业务混合错误
     *
     * @param array $sheetsParams
     *
     * @return LogicResult
     */
    public static function GetValidSheetParams(array $sheetsParams): LogicResult
    {
        if (empty($sheetsParams))
        {
            return self::Fail('结算参数验证，缺少购买单必选参数', 400);
        }

        $sheetMap = [];
        foreach ($sheetsParams as $sheet)
        {
            if (!isset($sheet['type']) || !SheetBusinessTypeEnum::exists($sheet['type']))
            {
                return self::Fail('结算参数验证，购物单类型参数错误', 400);
            }
            if (!isset($sheet['sheetCode']))
            {
                return self::Fail('结算参数验证，购物单编码参数错误', 400);
            }
            if (!isset($sheet['price']))
            {
                return self::Fail('结算参数验证，购物单金额参数错误', 400);
            }

            $type = $sheet['type'];

            if (!isset($sheetMap[$type]))
            {
                $sheetMap[$type] = [
                    'codes' => [],
                    'total' => '0.00',
                ];
            }

            $sheetMap[$type]['codes'][] = $sheet['sheetCode'];
            $sheetMap[$type]['total']   = numberAdd([$sheetMap[$type]['total'], $sheet['price']], 2);
        }

        //验证是否存在必须单独结算的业务
        if (count($sheetMap) > 1 && array_any($sheetMap, fn($value, $key) => SheetBusinessTypeEnum::IsSinglePay($key)))
        {
            return self::Fail('结算参数验证，存在必须单独结算的业务', 600130);
        }

        return self::Success($sheetMap);
    }
}
