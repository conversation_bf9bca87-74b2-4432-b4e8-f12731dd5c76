<?php

namespace App\Support\Checkout;

use Illuminate\Support\Arr;
use App\Logics\Logic;
use App\Logics\LogicResult;
use App\Enums\DiscountTypeEnum;
use App\Models\PayModeThirdModel;
use App\Support\PayMode\HospitalPayModeHelper;

class CheckoutPriceHelper extends Logic
{

    /**
     * 计算并验证结算收到的价格是否正确
     *
     * @param array $prepareData
     * @param array $params
     * @param array $publicParams
     *
     * @return LogicResult
     */
    public static function CalculateCheckoutPrice(array $prepareData, array $params, array $publicParams): LogicResult
    {
        //业务参数
        $totalPrice         = trim(Arr::get($params, 'totalPrice', '0.00'));
        $activityPrice      = trim(Arr::get($params, 'activityPrice', '0.00'));
        $couponPrice        = trim(Arr::get($params, 'couponPrice', '0.00'));
        $discountPrice      = trim(Arr::get($params, 'discountPrice', '0.00'));
        $thirdPartyPayPrice = trim(Arr::get($params, 'thirdPartyPayPrice', '0.00'));
        $balancePayPrice    = trim(Arr::get($params, 'balancePayPrice', '0.00'));
        $cashPayPrice       = trim(Arr::get($params, 'cashPayPrice', '0.00'));
        $activity           = Arr::get($params, 'activity', []);
        $coupon             = Arr::get($params, 'coupon', []);
        $discount           = Arr::get($params, 'discount', []);
        $thirdParty         = Arr::get($params, 'thirdParty', []);
        $balance            = Arr::get($params, 'balance', []);
        $cash               = Arr::get($params, 'cash', []);

        $originTotalPrice = Arr::get($prepareData, 'totalPrice');
        $memberBalance    = Arr::get($prepareData, 'memberBalance', []);

        //公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId', 0));
        if (empty($hospitalId))
        {
            return self::Fail('结算校验，缺少医院ID必选参数', 400);
        }
        if (!is_numeric($totalPrice))
        {
            return self::Fail('结算校验，总金额参数错误', 400);
        }

        $pricesRes = self::CheckPriceDataOrInit($originTotalPrice);
        if ($pricesRes->isFail())
        {
            return $pricesRes;
        }
        $prices = $pricesRes->getData();

        //逐个验证并计算应付金额
        if (!empty($activity))
        {
            $pricesRes = self::CheckActivityPrice($prices, $activity);
            if ($pricesRes->isFail())
            {
                return $pricesRes;
            }
            $prices = $pricesRes->getData();
        }
        if (!empty($coupon))
        {
            $pricesRes = self::CheckCouponPrice($prices, $coupon);
            if ($pricesRes->isFail())
            {
                return $pricesRes;
            }
            $prices = $pricesRes->getData();
        }
        if (!empty($discount))
        {
            $pricesRes = self::CheckDiscountPrice($prices, $discount);
            if ($pricesRes->isFail())
            {
                return $pricesRes;
            }
            $prices = $pricesRes->getData();
        }
        if (!empty($thirdParty))
        {
            $pricesRes = self::CheckThirdPartyPayPrice($prices, $thirdParty);
            if ($pricesRes->isFail())
            {
                return $pricesRes;
            }
            $prices = $pricesRes->getData();
        }
        if (!empty($balance))
        {
            $pricesRes = self::CheckBalancePayPrice($prices, $balance, $memberBalance);
            if ($pricesRes->isFail())
            {
                return $pricesRes;
            }
            $prices = $pricesRes->getData();
        }
        //最后的先付款，无论是否选择都需要调用
        $pricesRes = self::CheckCashPayPrice($prices, $cash, $hospitalId);
        if ($pricesRes->isFail())
        {
            return $pricesRes;
        }
        $prices = $pricesRes->getData();

        //和接收到的参数中的价格逐一对比
        if (bccomp($prices['totalPrice'], $totalPrice, 2) !== 0)
        {
            return self::Fail('结算校验，总金额计算错误', 600250);
        }
        if (bccomp($prices['activityPrice'], $activityPrice, 2) !== 0)
        {
            return self::Fail('结算校验，活动优惠金额计算错误', 600250);
        }
        if (bccomp($prices['couponPrice'], $couponPrice, 2) !== 0)
        {
            return self::Fail('结算校验，代金券/现金券优惠金额计算错误', 600250);
        }
        if (bccomp($prices['discountPrice'], $discountPrice, 2) !== 0)
        {
            return self::Fail('结算校验，折扣优惠金额计算错误', 600250);
        }
        if (bccomp($prices['thirdPartyPayPrice'], $thirdPartyPayPrice, 2) !== 0)
        {
            return self::Fail('结算校验，第三方支付金额计算错误', 600250);
        }
        if (bccomp($prices['balancePayPrice'], $balancePayPrice, 2) !== 0)
        {
            return self::Fail('结算校验，余额支付金额计算错误', 600250);
        }
        if (bccomp($prices['cashPayPrice'], $cashPayPrice, 2) !== 0)
        {
            return self::Fail('结算校验，现付款支付金额计算错误', 600250);
        }

        //将所有价格加入公式，计算最后的金额是否是0
        $finalPrice = numberSub([
                                    $prices['totalPrice'],
                                    $prices['activityPrice'],
                                    $prices['couponPrice'],
                                    $prices['discountPrice'],
                                    $prices['thirdPartyPayPrice'],
                                    $prices['balancePayPrice'],
                                    $prices['cashPayPrice']
                                ],
                                2);
        if (bccomp($finalPrice, '0.00', 2) !== 0)
        {
            return self::Fail('结算校验，最终金额计算错误', 600250);
        }

        return self::Success($prices);
    }

    /**
     * 检查价格数据，如果为空则初始化
     *
     * @param string|int|float $totalPrice
     * @param array            $prices
     *
     * @return LogicResult
     */
    private static function CheckPriceDataOrInit(string|int|float $totalPrice, array $prices = []): LogicResult
    {
        $totalPrice = formatDisplayNumber($totalPrice, 2, false);
        if (empty($prices))
        {
            $prices = [
                'totalPrice'            => $totalPrice,
                'activityPrice'         => '0.00',
                'couponPrice'           => '0.00',
                'discountPrice'         => '0.00',
                'thirdPartyPayPrice'    => '0.00',
                'balancePayPrice'       => '0.00',
                'cashPayPrice'          => $totalPrice,
                //增加中间计算用金额,只有在对应的类型进过计算后才会被动赋值
                'maxDiscountPrice'      => null,
                'maxThirdPartyPayPrice' => null,
                'maxBalancePayPrice'    => null,
                'maxCashPayPrice'       => null,
            ];
        }

        //判断以下的价格字段是否存在
        if (
            !isset($prices['totalPrice']) ||
            !isset($prices['activityPrice']) ||
            !isset($prices['couponPrice']) ||
            !isset($prices['discountPrice']) ||
            !isset($prices['thirdPartyPayPrice']) ||
            !isset($prices['balancePayPrice']) ||
            !isset($prices['cashPayPrice'])
        )
        {
            return self::Fail('检查价格数据，缺少必选价格参数', 400);
        }

        $prices = [
            'totalPrice'         => formatDisplayNumber($prices['totalPrice'], 2, false),
            'activityPrice'      => formatDisplayNumber($prices['activityPrice'], 2, false),
            'couponPrice'        => formatDisplayNumber($prices['couponPrice'], 2, false),
            'discountPrice'      => formatDisplayNumber($prices['discountPrice'], 2, false),
            'thirdPartyPayPrice' => formatDisplayNumber($prices['thirdPartyPayPrice'], 2, false),
            'balancePayPrice'    => formatDisplayNumber($prices['balancePayPrice'], 2, false),
            'cashPayPrice'       => formatDisplayNumber($prices['cashPayPrice'], 2, false),
        ];

        return self::Success($prices);
    }

    /**
     * 检查活动优惠价格
     *
     * @param array $prices
     * @param array $activity
     *
     * @return LogicResult
     */
    private static function CheckActivityPrice(array $prices, array $activity): LogicResult
    {
        //TODO:目前不支持活动优惠，直接返回
        $prices['activityPrice'] = '0.00';

        return self::Success($prices);
    }


    /**
     * 检查代金券/现金券优惠价格
     *
     * @param array $prices
     * @param array $coupon
     *
     * @return LogicResult
     */
    private static function CheckCouponPrice(array $prices, array $coupon): LogicResult
    {
        //TODO:目前不支持代金券/现金券优惠，直接返回
        $prices['couponPrice'] = '0.00';


        return self::Success($prices);
    }

    /**
     * 检查折扣金额
     *
     * @param array $prices
     * @param array $discount
     *
     * @return LogicResult
     */
    private static function CheckDiscountPrice(array $prices, array $discount): LogicResult
    {
        $maxDiscountAmount = numberSub([$prices['totalPrice'], $prices['activityPrice'], $prices['couponPrice']], 2);

        $discountType  = Arr::get($discount, 'type', null);
        $discountRate  = Arr::get($discount, 'rate', null);
        $discountPrice = Arr::get($discount, 'price', null);
        if (!DiscountTypeEnum::exists($discountType))
        {
            return self::Fail('计算折扣优惠价格，折扣信息参数错误', 400);
        }
        if (!is_numeric($discountRate) || !preg_match('/^(?:[0-9](?:\.[0-9])?|9(?:\.0)?)$/', $discountRate))
        {
            return self::Fail('计算折扣优惠价格，折扣率参数错误', 400);
        }
        if (bccomp($discountRate, '0', 2) == - 1 || bccomp($discountRate, '9.9', 2) == 1)
        {
            return self::Fail('计算折扣优惠价格，折扣率参数范围错误', 400);
        }
        if (
            !is_numeric($discountPrice) ||
            bccomp($discountPrice, '0', 2) == - 1 ||
            bccomp($discountPrice, $maxDiscountAmount, 2) == 1)
        {
            return self::Fail('计算折扣优惠价格，折扣金额参数错误', 400);
        }

        $realDiscountRate = $discountRate / 10;

        //如果折扣方式是打折，那么尝试计算折扣后的金额，进一法计算金额
        if ($discountType == DiscountTypeEnum::Discount->value)
        {
            $calcDiscountPrice = formatDisplayNumber(
                ceil($maxDiscountAmount - $maxDiscountAmount * $realDiscountRate),
                2,
                false
            );
            if (bccomp($calcDiscountPrice, $maxDiscountAmount, 2) == 1)
            {
                $calcDiscountPrice = $maxDiscountAmount;
            }
            if (bccomp($calcDiscountPrice, $discountPrice, 2) !== 0)
            {
                return self::Fail('结算折扣金额计算错误', 600400);
            }
        }
        else
        {
            $calcDiscountPrice = formatDisplayNumber($discountPrice, 2, false);
            $calcDiscountRate  = round(($maxDiscountAmount - $discountPrice) / $maxDiscountAmount, 2);

            if (bccomp($calcDiscountRate, $realDiscountRate, 2) !== 0)
            {
                return self::Fail('结算折扣率计算错误', 600401);
            }
        }

        $prices['maxDiscountPrice'] = $maxDiscountAmount;
        $prices['discountPrice']    = $calcDiscountPrice;

        return self::Success($prices);
    }

    /**
     * 检查第三方支付价格
     *
     * @param array $prices
     * @param array $thirdPartyPay
     *
     * @return LogicResult
     */
    private static function CheckThirdPartyPayPrice(array $prices, array $thirdPartyPay): LogicResult
    {
        $maxThirdPartyPayPrice = numberSub([
                                               $prices['totalPrice'],
                                               $prices['activityPrice'],
                                               $prices['couponPrice'],
                                               $prices['discountPrice']
                                           ],
                                           2);

        $thirdPartyPayType   = Arr::get($thirdPartyPay, 'type', null);
        $thirdPartyPayPrice  = Arr::get($thirdPartyPay, 'price', null);
        $thirdPartyPayRemark = Arr::get($thirdPartyPay, 'remark', null);
        if (!is_integer($thirdPartyPayType))
        {
            return self::Fail('计算第三方支付价格，第三方支付方式参数错误', 400);
        }
        if (!is_numeric($thirdPartyPayPrice) || bccomp($thirdPartyPayPrice, '0', 2) <= 0)
        {
            return self::Fail('计算第三方支付价格，第三方支付金额参数错误', 400);
        }
        if (empty($thirdPartyPayRemark))
        {
            return self::Fail('计算第三方支付价格，第三方支付备注参数错误', 400);
        }
        if (!PayModeThirdModel::isExists($thirdPartyPayType))
        {
            return self::Fail('计算第三方支付价格，第三方支付方式不存在', 600410);
        }

        if (bccomp($thirdPartyPayPrice, $maxThirdPartyPayPrice, 2) == 1)
        {
            return self::Fail('计算第三方支付价格，第三方支付金额计算错误', 600411);
        }

        $prices['maxThirdPartyPayPrice'] = $maxThirdPartyPayPrice;
        $prices['thirdPartyPayPrice']    = formatDisplayNumber($thirdPartyPayPrice, 2, false);

        return self::Success($prices);
    }

    /**
     * 检查余额支付价格
     *
     * @param array $prices
     * @param array $balancePay
     * @param array $memberBalance
     *
     * @return LogicResult
     */
    private static function CheckBalancePayPrice(array $prices, array $balancePay, array $memberBalance): LogicResult
    {
        $maxBalancePayPrice = numberSub([
                                            $prices['totalPrice'],
                                            $prices['activityPrice'],
                                            $prices['couponPrice'],
                                            $prices['discountPrice'],
                                            $prices['thirdPartyPayPrice']
                                        ],
                                        2);
        $memberTotalBalance = Arr::get($memberBalance, 'balanceTotal', '0.00');
        $maxBalancePayPrice = bccomp($maxBalancePayPrice,
                                     $memberTotalBalance,
                                     2) == 1 ? $memberTotalBalance : $maxBalancePayPrice;


        $useBalance      = Arr::get($balancePay, 'use', null);
        $balancePayPrice = Arr::get($balancePay, 'price', null);
        if (!is_bool($useBalance))
        {
            return self::Fail('计算余额支付价格，是否使用余额参数错误', 400);
        }

        if (!$useBalance)
        {
            if (is_numeric($balancePayPrice) && bccomp($balancePayPrice, '0', 2) != 0)
            {
                return self::Fail('计算余额支付价格，未使用余额，余额支付金额参数错误', 400);
            }

            $prices['balancePayPrice'] = '0.00';

            return self::Success($prices);
        }

        if (!is_numeric($balancePayPrice) || bccomp($balancePayPrice, '0', 2) <= 0)
        {
            return self::Fail('计算余额支付价格，余额支付金额参数错误', 400);
        }
        //余额应该最大化使用，不可以随意输入
        if (bccomp($balancePayPrice, $maxBalancePayPrice, 2) != 0)
        {
            return self::Fail('计算余额支付价格，余额支付金额计算错误', 600420);
        }

        $prices['maxBalancePayPrice'] = $maxBalancePayPrice;
        $prices['balancePayPrice']    = formatDisplayNumber($balancePayPrice, 2, false);

        return self::Success($prices);
    }

    /**
     * 检查现付款价格
     *
     * @param array      $prices
     * @param array|null $cashPay
     * @param int        $hospitalId
     *
     * @return LogicResult
     */
    private static function CheckCashPayPrice(array $prices, ?array $cashPay, int $hospitalId): LogicResult
    {
        $maxCashPayPrice = numberSub([
                                         $prices['totalPrice'],
                                         $prices['activityPrice'],
                                         $prices['couponPrice'],
                                         $prices['discountPrice'],
                                         $prices['thirdPartyPayPrice'],
                                         $prices['balancePayPrice']
                                     ],
                                     2);

        if (empty($cashPay))
        {
            $prices['maxCashPayPrice'] = $maxCashPayPrice;
            $prices['cashPayPrice']    = $maxCashPayPrice;

            return self::Success($prices);
        }

        $cashPayChannel = Arr::get($cashPay, 'channel', null);
        $cashPayMode    = Arr::get($cashPay, 'mode', null);
        $cashPayPrice   = Arr::get($cashPay, 'price', null);
        if (!is_integer($cashPayChannel))
        {
            return self::Fail('计算现付款价格，现付款支付渠道参数错误', 400);
        }
        if (!is_integer($cashPayMode))
        {
            return self::Fail('计算现付款价格，现付款支付方式参数错误', 400);
        }
        if (!is_numeric($cashPayPrice) || bccomp($cashPayPrice, '0', 2) == - 1)
        {
            return self::Fail('计算现付款价格，现付款支付金额参数错误', 400);
        }
        if (bccomp($cashPayPrice, $maxCashPayPrice, 2) != 0)
        {
            return self::Fail('计算现付款价格，现付款支付金额计算错误', 600430);
        }

        $hospitalPayChannelAndModelRes = HospitalPayModeHelper::GetHospitalPayChannelAndModel($hospitalId);
        if ($hospitalPayChannelAndModelRes->isFail())
        {
            return $hospitalPayChannelAndModelRes;
        }

        $payChannelsData = $hospitalPayChannelAndModelRes->getData('payChannelsData', []);
        if (empty($payChannelsData))
        {
            return self::Fail('计算现付款价格，医院支付渠道和支付方式不存在', 600431);
        }
        $payChannelData = array_find($payChannelsData, function ($value) use ($cashPayChannel) {
            return $value['id'] == $cashPayChannel;
        });
        if (empty($payChannelData))
        {
            return self::Fail('计算现付款价格，医院支付渠道不存在', 600431);
        }
        if (!isset($payChannelData['children']))
        {
            return self::Fail('计算现付款价格，医院支付方式不存在', 600432);
        }
        $payModeData = array_find($payChannelData['children'], function ($value) use ($cashPayMode) {
            return $value['id'] == $cashPayMode;
        });
        if (empty($payModeData))
        {
            return self::Fail('计算现付款价格，医院支付方式不存在', 600432);
        }

        $prices['maxCashPayPrice'] = $maxCashPayPrice;
        $prices['cashPayPrice']    = formatDisplayNumber($cashPayPrice, 2, false);

        return self::Success($prices);
    }
}
