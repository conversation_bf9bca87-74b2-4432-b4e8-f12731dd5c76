<?php

namespace App\Support\Hospital;

use App\Logics\Logic;
use App\Logics\LogicResult;
use App\Models\HospitalModel;
use App\Models\AddressCityModel;
use App\Models\AddressAreaModel;
use App\Models\AddressStreetModel;

class HospitalHelper extends Logic
{
    /**
     * 获取医院完整信息
     *
     * @param int  $hospitalId
     * @param bool $withAddressCity
     *
     * @return LogicResult
     */
    public static function GetHospitalFullInfo(int $hospitalId, bool $withAddressCity = false): LogicResult
    {
        if (empty($hospitalId))
        {
            return self::Fail('获取医院基本信息，缺少必选参数', 400);
        }

        $hospital = HospitalModel::getOne($hospitalId);
        if (empty($hospital))
        {
            return self::Fail('医院不存在', 20100);
        }

        $result = [
            'uid'       => $hospital->uid,
            'name'      => $hospital->name,
            'aliasName' => $hospital->alias_name,
            'showName'  => $hospital->alias_name != '' ? $hospital->alias_name : $hospital->name,
            'logo'      => realPicturePath($hospital->logo),
            'wechatUrl' => $hospital->wechat_url,
            'phone'     => $hospital->phone,
        ];

        if ($withAddressCity)
        {
            $city = AddressCityModel::getOne($hospital->city_id);
            if (empty($city))
            {
                return self::Fail('城市信息不存在', 400);
            }

            $area = AddressAreaModel::getOne($hospital->area_id);
            if (empty($area))
            {
                return self::Fail('区域信息不存在', 400);
            }

            $street = AddressStreetModel::getOne($hospital->town_id);
            if (empty($street))
            {
                return self::Fail('街道信息不存在', 400);
            }

            $result['address'] = [
                'city'   => $city->ext_name,
                'area'   => $area->ext_name,
                'street' => $street->ext_name,
                'detail' => $hospital->address,
                'full'   => implode('', [$city->ext_name, $area->ext_name, $street->ext_name, $hospital->address]),
            ];
        }

        return self::Success($result);
    }
}
