<?php

namespace App\Support\Balance;

use App\Logics\Logic;
use App\Models\MemberBalanceModel;

class MemberBalanceHelper extends Logic
{
    /**
     * 获取单个会员的余额
     *
     * 可安全获取，不存在的记录会返回默认值
     *
     * @param int       $memberId
     * @param bool|null $isFrozen
     * @param bool      $onWritePdo
     *
     * @return array|null
     */
    public static function MemberBalance(int $memberId, ?bool $isFrozen = false, bool $onWritePdo = false): array|null
    {
        if (empty($memberId))
        {
            return null;
        }

        $memberBalance = MemberBalanceModel::GetMemberBalance($memberId, $isFrozen, $onWritePdo);

        $balanceRecharge = $memberBalance['balance_recharge'] ?? '0.00';
        $balanceGift     = $memberBalance['balance_gift'] ?? '0.00';

        return [
            'balanceTotal'    => numberAdd([$balanceRecharge, $balanceGift], 2),
            'balanceRecharge' => formatDisplayNumber($balanceRecharge, 2, false),
            'balanceGift'     => formatDisplayNumber($balanceGift, 2, false),
        ];
    }

    /**
     * 批量获取用户的余额
     *
     * 可安全获取，不存在的记录会返回默认值
     *
     * @param array     $memberIds
     * @param bool|null $isFrozen
     * @param bool      $onWritePdo
     * @param bool      $keyByMemberId
     *
     * @return array
     */
    public static function MembersBalance(
        array $memberIds, ?bool $isFrozen = false, bool $onWritePdo = false, bool $keyByMemberId = true
    ): array
    {
        if (empty($memberIds))
        {
            return [];
        }

        $memberBalances         = MemberBalanceModel::GetMembersBalance($memberIds, $isFrozen, $onWritePdo);
        $memberBalancesRelation = $memberBalances->keyBy('member_id');

        $result = [];
        foreach ($memberIds as $memberId)
        {
            $memberBalance   = $memberBalancesRelation[$memberId] ?? null;
            $balanceRecharge = $memberBalance['balance_recharge'] ?? '0.00';
            $balanceGift     = $memberBalance['balance_gift'] ?? '0.00';

            $tmp = [
                'balanceTotal'    => numberAdd([$balanceRecharge, $balanceGift], 2),
                'balanceRecharge' => formatDisplayNumber($balanceRecharge, 2, false),
                'balanceGift'     => formatDisplayNumber($balanceGift, 2, false),
            ];

            if ($keyByMemberId)
            {
                $result[$memberId] = $tmp;
            }
            else
            {
                $result[] = $tmp;
            }
        }

        return $result;
    }
}
