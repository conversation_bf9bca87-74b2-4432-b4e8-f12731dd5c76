<?php

namespace App\Support\SheetUnion;

use Illuminate\Support\Arr;
use ArrayAccess;
use App\Logics\Logic;
use App\Logics\LogicResult;
use App\Enums\SheetStatusEnum;
use App\Enums\SheetBusinessTypeEnum;
use App\Models\MemberModel;
use App\Models\MemberPetsModel;
use App\Support\User\HospitalUserHelper;

/**
 * 统一购买单助手
 * Class SheetUnionHelper
 * @package App\Support\SheetUnion
 */
class SheetUnionHelper extends Logic
{
    /**
     * 格式化构建统一购买单列表
     *
     * @param array $sheetList
     * @param array $publicParams
     *
     * @return array
     */
    public static function FormatSheetListStructure(array $sheetList, array $publicParams): array
    {
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));

        if (empty($sheetList) || empty($hospitalId))
        {
            return [];
        }

        //获取关联数据ID
        $memberIds     = array_column($sheetList, 'member_id');
        $petIds        = array_column($sheetList, 'pet_id');
        $createUserIds = array_column($sheetList, 'created_by');
        //批量获取关联数据
        $memberRes        = [];
        $petRes           = [];
        $getCreateUserRes = [];
        if (!empty($memberIds))
        {
            $memberRes = MemberModel::getData(
                whereIn: ['id' => $memberIds],
                keyBy:   'id',
            );
        }
        if (!empty($petIds))
        {
            $petRes = MemberPetsModel::getData(
                whereIn: ['id' => $petIds],
                keyBy:   'id',
            );
        }
        if (!empty($createUserIds))
        {
            $getCreateUserRes = HospitalUserHelper::GetUserOptionsByUserIds($createUserIds, $hospitalId, true);
        }

        $result = [];
        foreach ($sheetList as $sheet)
        {
            //获取会员和宠物信息
            $curMemberInfo = $memberRes[$sheet['member_id']] ?? null;
            $curPetInfo    = $petRes[$sheet['pet_id']] ?? null;

            // 创建人
            $curCreateUser = $getCreateUserRes[$sheet['created_by']] ?? null;

            $tmp = [
                'sheetCode'  => $sheet['sheet_code'],
                'type'       => [
                    'uid'  => $sheet['business_type'],
                    'name' => SheetBusinessTypeEnum::getDescription($sheet['business_type']) ?? null,
                ],
                'memberInfo' => !empty($curMemberInfo) ? [
                    'uid'   => $curMemberInfo['uid'] ?? null,
                    'name'  => $curMemberInfo['name'] ?? null,
                    'phone' => secretCellphone($curMemberInfo['phone'] ?? ''),
                ] : null,
                'petInfo'    => !empty($curPetInfo) ? [
                    'uid'  => $curPetInfo['uid'] ?? null,
                    'name' => $curPetInfo['name'] ?? null,
                ] : null,
                'price'      => formatDisplayNumber($sheet['price'], 2, false),
                'status'     => [
                    'id'   => $sheet['status'],
                    'name' => SheetStatusEnum::getDescription($sheet['status']),
                ],
                'createUser' => $curCreateUser,
                'orderTime'  => formatDisplayDateTime($sheet['order_time']),
                'createTime' => formatDisplayDateTime($sheet['created_at']),
                'updateTime' => formatDisplayDateTime($sheet['updated_at']),
                'payTime'    => formatDisplayDateTime($sheet['paid_at']),
                'editAble'   => self::CheckSheetEdit($sheet, $publicParams)
                                    ->isSuccess(),
                'deleteAble' => self::CheckSheetDelete($sheet, $publicParams)
                                    ->isSuccess(),
            ];

            $result[] = $tmp;
        }

        return $result;
    }

    /**
     * 验证购买单单是否可以编辑
     *
     * @param array|ArrayAccess $sheet
     * @param array             $publicParams
     *
     * @return LogicResult
     */
    public static function CheckSheetEdit(array|ArrayAccess $sheet, array $publicParams): LogicResult
    {
        $userId = intval(Arr::get($publicParams, '_userId'));

        if (empty($sheet) || empty($publicParams) || empty($userId))
        {
            return self::Fail('验证购买单是否可编辑，缺少必选参数', 400);
        }

        if (!SheetStatusEnum::EditAble($sheet['status']))
        {
            return self::Fail('购买单当前状态不允许编辑操作', 500930);
        }

        if (!SheetBusinessTypeEnum::EditAble($sheet['business_type']))
        {
            return self::Fail('购买单业务类型不允许编辑操作', 500930);
        }

        //TODO:根据是否本人和是否院长角色来判断

        return self::Success();
    }

    /**
     * 验证购买单单是否可以删除
     *
     * @param array|ArrayAccess $sheet
     * @param array             $publicParams
     *
     * @return LogicResult
     */
    public static function CheckSheetDelete(array|ArrayAccess $sheet, array $publicParams): LogicResult
    {
        $userId = intval(Arr::get($publicParams, '_userId'));

        if (empty($sheet) || empty($publicParams) || empty($userId))
        {
            return self::Fail('验证购买单是否可操作，缺少必选参数', 400);
        }

        if (!SheetStatusEnum::EditAble($sheet['status']))
        {
            return self::Fail('购买单当前状态不允许删除操作', 500931);
        }

        if (!SheetBusinessTypeEnum::DeleteAble($sheet['business_type']))
        {
            return self::Fail('购买单业务类型不允许删除操作', 500931);
        }

        //TODO:根据是否本人和是否院长角色来判断

        return self::Success();
    }
}
