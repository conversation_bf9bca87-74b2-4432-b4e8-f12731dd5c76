<?php

namespace App\Support\PayOrder;

use ArrayAccess;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Arr;
use App\Logics\Logic;
use App\Logics\LogicResult;
use App\Enums\SheetStatusEnum;
use App\Models\PayOrderModel;
use App\Support\Member\MemberHelper;
use App\Support\User\HospitalUserHelper;
use App\Support\PayMode\PayModeThirdHelper;
use App\Support\PayMode\PayModeHelper;
use App\Support\PayMode\PayChannelHelper;

class PayOrderHelper extends Logic
{
    /**
     * 验证结算单价格是否平衡
     *
     * @param array $paySubOrderData
     * @param array $payOrderData
     *
     * @return bool
     */
    public static function VerifyPayOrderPrice(array $paySubOrderData, array $payOrderData = []): bool
    {
        $mainFields    = [
            'sale_price',
            'reduce_price',
            'coupon_price',
            'cash_coupon_price',
            'discount_price',
            'third_party_price',
            'balance_price',
            'deposit_price',
            'pay_price',
        ];
        $priceFields   = [
            'sale_price',
            'reduce_price',
            'deal_price',
        ];
        $balanceFields = [
            'balance_price',
            'balance_recharge_price',
            'balance_gift_price',
        ];
        $allFields     = array_merge($mainFields, $priceFields, $balanceFields);

        foreach ($paySubOrderData as $value)
        {
            if (bccomp(numberSub(Arr::only($value, $mainFields), 2), 0, 2) != 0)
            {
                Log::warning('$mainFields 不平衡', [
                    '$paySubOrderData' => $paySubOrderData,
                    '$payOrderData'    => $payOrderData,
                    'mainFields'       => Arr::only($value, $mainFields)
                ]);

                return false;
            };

            if (bccomp(numberSub(Arr::only($value, $priceFields), 2), 0, 2) != 0)
            {
                Log::warning('$priceFields 不平衡', [
                    '$paySubOrderData' => $paySubOrderData,
                    '$payOrderData'    => $payOrderData,
                    'priceFields'      => Arr::only($value, $priceFields)
                ]);

                return false;
            };

            if (bccomp(numberSub(Arr::only($value, $balanceFields), 2), 0, 2) != 0)
            {
                Log::warning('$balanceFields 不平衡', [
                    '$paySubOrderData' => $paySubOrderData,
                    '$payOrderData'    => $payOrderData,
                    'balanceFields'    => Arr::only($value, $balanceFields)
                ]);

                return false;
            };

            return true;
        }

        if (empty($payOrderData))
        {
            return true;
        }

        foreach ($allFields as $field)
        {
            $orderPrice = $payOrderData[$field];
            $subPrice   = numberAdd(Arr::pluck($paySubOrderData, $field), 2);
            if (bccomp($orderPrice, $subPrice, 2) != 0)
            {
                Log::warning('父子 不平衡', [
                    '$paySubOrderData' => $paySubOrderData,
                    '$payOrderData'    => $payOrderData,
                    '$field'           => $field,
                    '$orderPrice'      => $orderPrice,
                    '$subPrice'        => $subPrice,
                ]);

                return false;
            }
        }

        return true;
    }

    /**
     * 获取有效的结算单
     *
     * @param string $payOrderCode
     * @param int    $payOrderId
     * @param int    $hospitalId
     * @param bool   $useWritePdo
     *
     * @return LogicResult
     */
    public static function GetValidPayOrder(
        string $payOrderCode = '', int $payOrderId = 0, int $hospitalId = 0, bool $useWritePdo = false
    ): LogicResult
    {
        $payOrder = PayOrderModel::GetPayOrderByCodeOrId($payOrderCode, $payOrderId, $hospitalId, $useWritePdo);
        if (empty($payOrder))
        {
            return self::Fail('结算单不存在', 601100);
        }

        if ($payOrder->status < SheetStatusEnum::Unpaid->value)
        {
            return self::Fail('结算单已删除', 601101);
        }

        return self::Success($payOrder->toArray());
    }

    /**
     * 设置结算单为支付中
     *
     * @param int $payOrderId
     *
     * @return bool
     */
    public static function SetPayOrderToPaying(int $payOrderId): bool
    {
        $setRes = PayOrderModel::on()
                               ->where([
                                           'id'     => $payOrderId,
                                           'status' => SheetStatusEnum::Unpaid->value,
                                       ])
                               ->update(['status' => SheetStatusEnum::Paying->value]);

        return $setRes > 0;
    }

    /**
     * 设置结算单为待支付
     *
     * @param int $payOrderId
     *
     * @return bool
     */
    public static function SetPayOrderToUnpaid(int $payOrderId): bool
    {
        $setRes = PayOrderModel::on()
                               ->where([
                                           'id'     => $payOrderId,
                                           'status' => SheetStatusEnum::Paying->value,
                                       ])
                               ->update(['status' => SheetStatusEnum::Unpaid->value]);

        return $setRes > 0;
    }

    /**
     * 格式化构建结算单列表
     *
     * @param array $data
     * @param array $publicParams
     * @param bool  $withDiscount
     * @param bool  $withThirdParty
     *
     * @return array
     */
    public static function FormatOrderListStructure(
        array $data, array $publicParams, bool $withDiscount = false, bool $withThirdParty = false
    ): array
    {
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));

        if (empty($data) || empty($hospitalId))
        {
            return [];
        }

        //获取关联数据ID
        $memberIds      = array_column($data, 'member_id');
        $createUserIds  = array_column($data, 'created_by');
        $cashierUserIds = array_column($data, 'cashier_by');
        $payOrderCodes  = array_column($data, 'pay_order_code');

        //批量获取关联数据
        $memberRes = [];
        $userRes   = [];
        if (!empty($memberIds))
        {
            $memberRes = MemberHelper::GetMemberOptionsByMemberIds($memberIds, true);
        }
        if (!empty($createUserIds) || !empty($cashierUserIds))
        {
            $userRes = HospitalUserHelper::GetUserOptionsByUserIds(array_merge($createUserIds,
                                                                               $cashierUserIds),
                                                                   $hospitalId,
                                                                   true);
        }

        //获取支付方式
        $thirdPartyOptions = PayModeThirdHelper::GetPayModeThirdOptions(true, null);
        $payModeOptions    = PayModeHelper::GetPayModeOptions(true, null);
        $payChannelOptions = PayChannelHelper::GetPayChannelOptions(true, null);

        //折扣记录
        $discountRecord = [];
        if ($withDiscount && !empty($payOrderCodes))
        {
            $discountRecord = PayOrderDiscountRecordHelper::GetDiscountRecordInfo($payOrderCodes, true);
        }

        //第三方支付记录
        $thirdPartyRecord = [];
        if ($withThirdParty && !empty($payOrderCodes))
        {
            $thirdPartyRecord = PayOrderThirdPartPayRecordHelper::GetThirdPartPayRecordInfo($payOrderCodes, true);
        }

        $result = [];
        foreach ($data as $value)
        {
            //获取会员信息
            $curMemberInfo = $memberRes[$value['member_id']] ?? null;

            // 创建人
            $curCreateUser = $userRes[$value['created_by']] ?? null;

            // 收银人
            $curCashierUser = $userRes[$value['cashier_by']] ?? null;

            // 第三方支付方式
            $thirdPartyPayMode = $thirdPartyOptions[$value['third_party_pay_mode']] ?? null;

            // 支付渠道
            $payChannel = $payChannelOptions[$value['pay_channel']] ?? null;

            // 支付方式
            $payMode = $payModeOptions[$value['pay_mode']] ?? null;

            // 折扣
            $discount = $discountRecord[$value['pay_order_code']] ?? null;

            // 第三方支付
            $thirdParty = $thirdPartyRecord[$value['pay_order_code']] ?? null;

            $checkPayRes = self::CheckOrderPay($value, $publicParams);
            $payAble     = $checkPayRes->isSuccess();

            $checkDeleteRes = self::CheckOrderDelete($value, $publicParams);
            $deleteAble     = $checkDeleteRes->isSuccess();

            $tmp = [
                'payOrderCode'         => $value['pay_order_code'],
                'memberInfo'           => $curMemberInfo,
                'salePrice'            => formatDisplayNumber($value['sale_price'], 2, false),
                'reducePrice'          => formatDisplayNumber($value['reduce_price'], 2, false),
                'dealPrice'            => formatDisplayNumber($value['deal_price'], 2, false),
                'couponPrice'          => formatDisplayNumber($value['coupon_price'], 2, false),
                'cashCouponPrice'      => formatDisplayNumber($value['cash_coupon_price'], 2, false),
                'discountPrice'        => formatDisplayNumber($value['discount_price'], 2, false),
                'thirdPartyPrice'      => formatDisplayNumber($value['third_party_price'], 2, false),
                'balancePrice'         => formatDisplayNumber($value['balance_price'], 2, false),
                'depositPrice'         => formatDisplayNumber($value['deposit_price'], 2, false),
                'balanceRechargePrice' => formatDisplayNumber($value['balance_recharge_price'], 2, false),
                'balanceGiftPrice'     => formatDisplayNumber($value['balance_gift_price'], 2, false),
                'payPrice'             => formatDisplayNumber($value['pay_price'], 2, false),
                'thirdPartyPayMode'    => $thirdPartyPayMode,
                'payChannel'           => $payChannel,
                'payMode'              => $payMode,
                'status'               => [
                    'id'   => $value['status'],
                    'name' => SheetStatusEnum::getDescription($value['status']),
                ],
                'createUser'           => $curCreateUser,
                'cashierUser'          => $curCashierUser,
                'orderTime'            => formatDisplayDateTime($value['order_time']),
                'createTime'           => formatDisplayDateTime($value['created_at']),
                'updateTime'           => formatDisplayDateTime($value['updated_at']),
                'payTime'              => formatDisplayDateTime($value['paid_at']),
                'payAble'              => $payAble,
                'deleteAble'           => $deleteAble,
                //金额扩展
                'couponPriceTotal'     => bcadd($value['coupon_price'], $value['cash_coupon_price'], 2),
                'reducePriceTotal'     => bcadd($value['reduce_price'], $value['discount_price'], 2),
                'payPriceTotal'        => numberAdd([
                                                        $value['third_party_price'],
                                                        $value['balance_price'],
                                                        $value['deposit_price'],
                                                        $value['pay_price']
                                                    ])
            ];

            if ($withDiscount)
            {
                $tmp['discount'] = $discount;
            }
            if ($withThirdParty)
            {
                $tmp['thirdParty'] = $thirdParty;
            }

            $result[] = $tmp;
        }

        return $result;
    }

    /**
     * 验证结算单是否可以去支付
     *
     * @param array|ArrayAccess $order
     * @param array             $publicParams
     *
     * @return LogicResult
     */
    public static function CheckOrderPay(array|ArrayAccess $order, array $publicParams): LogicResult
    {
        $userId = intval(Arr::get($publicParams, '_userId'));

        if (empty($order) || empty($publicParams) || empty($userId))
        {
            return self::Fail('验证结算单是否可发起支付，缺少必选参数', 400);
        }

        if (!SheetStatusEnum::PayAble($order['status']))
        {
            return self::Fail('结算单当前状态不允许发起支付', 601110);
        }

        //TODO:根据是否本人是否有收银权限来判断

        return self::Success();
    }

    /**
     * 验证结算单是否可以取消
     *
     * @param array|ArrayAccess $order
     * @param array             $publicParams
     *
     * @return LogicResult
     */
    public static function CheckOrderDelete(array|ArrayAccess $order, array $publicParams): LogicResult
    {
        $userId = intval(Arr::get($publicParams, '_userId'));
        if (empty($order) || empty($publicParams) || empty($userId))
        {
            return self::Fail('验证结算单是否可取消，缺少必选参数', 400);
        }

        if (!SheetStatusEnum::DeleteAble($order['status']))
        {
            return self::Fail('结算单当前状态不允许取消', 601111);
        }

        //TODO:根据是否本人是否有收银权限来判断

        return self::Success();
    }
}
