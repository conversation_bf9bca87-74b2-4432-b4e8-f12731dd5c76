<?php

namespace App\Support\PayOrder;

use App\Logics\Logic;
use App\Logics\LogicResult;
use App\Enums\PaySubOrderExtProcessStatusEnum;
use App\Enums\PaySubOrderTypeEnum;
use App\Enums\ItemTypeEnum;
use App\Models\PaySubOrderExtModel;

class PaySubOrderExtHelper extends Logic
{
    /**
     * 验证一个结算单下的附加信息是否正确
     *
     * 同一个结算单
     *
     * @param array    $paySubOrdersExtData
     * @param int|null $processStatus
     *
     * @return LogicResult
     */
    public static function CheckSubOrderExtValid(
        array $paySubOrdersExtData, ?int $processStatus = PaySubOrderExtProcessStatusEnum::Unprocessed->value
    ): LogicResult
    {
        $count            = count($paySubOrdersExtData);
        $payOrderCode     = '';
        $paySubOrderCodes = [];
        $type             = null;
        $sheetCodes       = [];
        $hospitalId       = null;

        //开始校验
        foreach ($paySubOrdersExtData as $value)
        {
            if (
                empty($value['pay_order_code']) ||
                empty($value['pay_sub_order_code']) ||
                empty($value['type']) ||
                empty($value['sheet_code']) ||
                empty($value['hospital_id']) ||
                empty($value['items']) ||
                !isset($value['process_status']) ||
                !isset($value['status'])
            )
            {
                return self::Fail('验证购买单附加信息，缺少必选参数', 400);
            }

            if (!is_array($value['items']))
            {
                return self::Fail('验证购买单附加信息，明细数据异常', 400);
            }

            if ($processStatus !== null && $value['process_status'] !== $processStatus)
            {
                return self::Fail('验证购买单附加信息，处理状态参数错误', 400);
            }

            if ($value['status'] != 1)
            {
                return self::Fail('验证购买单附加信息，状态参数错误', 400);
            }

            if (empty($payOrderCode))
            {
                $payOrderCode = $value['pay_order_code'];
            }
            else
            {
                if ($payOrderCode != $value['pay_order_code'])
                {
                    return self::Fail('验证购买单附加信息，结算单号不一致', 400);
                }
            }

            if (!in_array($value['pay_sub_order_code'], $paySubOrderCodes))
            {
                $paySubOrderCodes[] = $value['pay_sub_order_code'];
            }

            if ($type == null)
            {
                $type = $value['type'];
            }
            else
            {
                if ($type != $value['type'])
                {
                    return self::Fail('验证购买单附加信息，业务类型不一致', 400);
                }
            }

            if (!in_array($value['sheet_code'], $sheetCodes))
            {
                $sheetCodes[] = $value['sheet_code'];
            }

            if ($hospitalId == null)
            {
                $hospitalId = $value['hospital_id'];
            }
            else
            {
                if ($hospitalId != $value['hospital_id'])
                {
                    return self::Fail('验证购买单附加信息，医院ID不一致', 400);
                }
            }
        }

        if ($count != count($paySubOrderCodes))
        {
            return self::Fail('验证购买单附加信息，结算子单数量异常', 400);
        }

        if ($count != count($sheetCodes))
        {
            return self::Fail('验证购买单附加信息，购买单数量异常', 400);
        }

        return self::Success([
                                 'hospitalId'       => $hospitalId,
                                 'payOrderCode'     => $payOrderCode,
                                 'paySubOrderCodes' => $paySubOrderCodes,
                                 'sheetCodes'       => $sheetCodes,
                                 'type'             => $type,
                             ]);
    }

    /**
     * 判断子单下商品明细的类型
     *
     * 单品、组合、挂号、洗美
     * 暂不支持：服务套餐
     *
     * @param int   $subOrderType
     * @param array $item
     *
     * @return int|null
     */
    public static function DecideSubOrderExtItemType(int $subOrderType, array $item): ?int
    {

        if (isset($item['isSuit']) && $item['isSuit'] == 1)
        {
            return ItemTypeEnum::Spu->value;
        }
        elseif ($subOrderType == PaySubOrderTypeEnum::Registration->value)
        {
            return ItemTypeEnum::Registration->value;
        }
        elseif ($subOrderType == PaySubOrderTypeEnum::Beauty->value)
        {
            return ItemTypeEnum::Beauty->value;
        }
        elseif (in_array($subOrderType, [PaySubOrderTypeEnum::Recipe->value, PaySubOrderTypeEnum::Retail->value]))
        {
            return ItemTypeEnum::Sku->value;
        }

        return null;
    }

    /**
     * 判断子单下原始商品明细的类型
     *
     * 单品、组合、挂号、洗美
     * 暂不支持：服务套餐
     *
     * @param int   $subOrderType
     * @param array $originItem
     *
     * @return int|null
     */
    public static function DecideSubOrderExtOriginItemType(int $subOrderType, array $originItem): ?int
    {

        if (isset($originItem['is_suit']) && $originItem['is_suit'] == 1)
        {
            return ItemTypeEnum::Spu->value;
        }
        elseif ($subOrderType == PaySubOrderTypeEnum::Registration->value)
        {
            return ItemTypeEnum::Registration->value;
        }
        elseif ($subOrderType == PaySubOrderTypeEnum::Beauty->value)
        {
            return ItemTypeEnum::Beauty->value;
        }
        elseif (in_array($subOrderType, [PaySubOrderTypeEnum::Recipe->value, PaySubOrderTypeEnum::Retail->value]))
        {
            return ItemTypeEnum::Sku->value;
        }

        return null;
    }

    /**
     * 获取结算单下的附加信息
     *
     * @param string      $payOrderCode
     * @param string|null $paySubOrderCode
     * @param int|null    $type
     * @param string|null $sheetCode
     * @param int|null    $processStatus
     *
     * @return array
     */
    public static function GetSubOrderExtData(
        string $payOrderCode, ?string $paySubOrderCode = null, ?int $type = null, ?string $sheetCode = null,
        ?int   $processStatus = null
    ): array
    {
        $where = [
            'pay_order_code' => $payOrderCode,
            'status'         => 1,
        ];
        if (!empty($paySubOrderCode))
        {
            $where[] = ['pay_sub_order_code', '=', $paySubOrderCode];
        }
        if (!empty($type))
        {
            $where[] = ['type', '=', $type];
        }
        if (!empty($sheetCode))
        {
            $where[] = ['sheet_code', '=', $sheetCode];
        }
        if (!empty($processStatus))
        {
            $where[] = ['process_status', '=', $processStatus];
        }

        $paySubOrdersExtData = PaySubOrderExtModel::getData(
            where: $where
        );

        $paySubOrdersExtData = array_map(function ($value) {
            $value['items'] = json_decode($value['items'], true);

            return $value;
        }, $paySubOrdersExtData);

        return $paySubOrdersExtData;
    }
}
