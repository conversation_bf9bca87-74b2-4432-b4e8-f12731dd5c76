<?php

namespace App\Support\PayOrder;

use App\Logics\Logic;
use App\Enums\DiscountTypeEnum;
use App\Models\PayOrderDiscountRecordsModel;

class PayOrderDiscountRecordHelper extends Logic
{
    /**
     * 获取结算单折扣记录
     *
     * @param array $payOrderCodes
     * @param bool  $keyByCode
     *
     * @return array
     */
    public static function GetDiscountRecordInfo(array $payOrderCodes, bool $keyByCode = false): array
    {
        if (empty($payOrderCodes))
        {
            return [];
        }

        $discountRecord = PayOrderDiscountRecordsModel::getData(
            whereIn: ['pay_order_code' => $payOrderCodes],
        );

        $result = [];

        foreach ($discountRecord as $value)
        {
            $tmp = [
                'payOrderCode' => $value['pay_order_code'],
                'discount'     => formatDisplayNumber($value['discount'], 1, false),
                'price'        => formatDisplayNumber($value['discount_price'], 2, false),
                'type'         => [
                    'id'   => $value['discount_type'],
                    'name' => DiscountTypeEnum::getDescription($value['discount_type']),
                ],
                'reason'       => $value['discount_reason'],
            ];

            if ($keyByCode)
            {
                $result[$value['pay_order_code']] = $tmp;
            }
            else
            {
                $result[] = $tmp;
            }
        }

        return $result;
    }
}
