<?php

namespace App\Support\PayOrder;

use App\Logics\Logic;
use App\Models\PayOrderThirdPartyPayModel;

class PayOrderThirdPartPayRecordHelper extends Logic
{
    /**
     * 获取第三方支付记录
     *
     * @param array $payOrderCodes
     * @param bool  $keyByCode
     *
     * @return array
     */
    public static function GetThirdPartPayRecordInfo(array $payOrderCodes, bool $keyByCode = false): array
    {
        if (empty($payOrderCodes))
        {
            return [];
        }

        $thirdPartyRecord = PayOrderThirdPartyPayModel::getData(
            whereIn: ['pay_order_code' => $payOrderCodes],
        );

        $result = [];
        foreach ($thirdPartyRecord as $value)
        {
            $tmp = [
                'payOrderCode' => $value['pay_order_code'],
                'payPrice'     => formatDisplayNumber($value['pay_price'], 2, false),
                'remark'       => $value['remark'],
            ];

            if ($keyByCode)
            {
                $result[$value['pay_order_code']] = $tmp;
            }
            else
            {
                $result[] = $tmp;
            }
        }

        return $result;
    }
}
