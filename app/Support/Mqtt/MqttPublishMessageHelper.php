<?php

namespace App\Support\Mqtt;

use App\Logics\Logic;
use App\Logics\LogicResult;
use App\Services\Mqtt\MqttFacade as Mqtt;
use App\Enums\HospitalRoleEnum;
use App\Enums\MqttHisMessageTypeEnum;
use App\Models\UsersModel;

class MqttPublishMessageHelper extends Logic
{
    /**
     * 发布挂号新增的通知消息
     *
     * 选医生：发送到医生个人消息
     * 未选医生：发送到医生角色消息
     *
     * @param array $registration
     * @param array $publicParams
     *
     * @return LogicResult
     */
    public static function PublishRegistrationAddMessage(array $registration, array $publicParams): LogicResult
    {
        if (empty($registration) || empty($publicParams))
        {
            return self::Fail('发布挂号消息，缺少必选参数', 400);
        }

        $doctorId = $registration['doctor_id'];
        if ($doctorId > 0)
        {
            $user = UsersModel::getOne($doctorId);
            if (empty($user))
            {
                return self::Fail('发布挂号消息，挂号医生不存在', 400);
            }

            $topicName = MqttHisTopicHelper::GetHisUserTopicName($publicParams, $user->uid);
        }
        else
        {
            $topicName = MqttHisTopicHelper::GetHisRoleTopicName(HospitalRoleEnum::HOSPITAL_DOCTOR_ROLE_ID,
                                                                 $publicParams);
        }

        $res = Mqtt::publish($topicName,
                             MqttHisTopicHelper::BuildMessageStruct(MqttHisMessageTypeEnum::RegistrationAdd->value));

        if (!$res)
        {
            return self::Fail('发布挂号消息失败', 500);
        }

        return self::Success();
    }

    /**
     * 发布住院新增的通知消息
     *
     * 发送到医生角色消息
     *
     * @param array $inpatient
     * @param array $publicParams
     *
     * @return LogicResult
     */
    public static function PublishInpatientAddMessage(array $inpatient, array $publicParams): LogicResult
    {
        if (empty($inpatient) || empty($publicParams))
        {
            return self::Fail('发布住院消息，缺少必选参数', 400);
        }

        $topicName = MqttHisTopicHelper::GetHisRoleTopicName(HospitalRoleEnum::HOSPITAL_DOCTOR_ROLE_ID,
                                                             $publicParams);

        $res = Mqtt::publish($topicName,
                             MqttHisTopicHelper::BuildMessageStruct(MqttHisMessageTypeEnum::InpatientAdd->value));

        if (!$res)
        {
            return self::Fail('发布住院消息失败', 500);
        }

        return self::Success();
    }
}
