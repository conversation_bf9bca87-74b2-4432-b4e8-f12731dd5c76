<?php

namespace App\Support\Mqtt;

use App\Logics\Logic;
use Illuminate\Support\Arr;

class MqttHisTopicHelper extends Logic
{
    /**
     * 构建his消息结构
     *
     * @param string $messageType
     * @param mixed  $message
     *
     * @return string
     */
    public static function BuildMessageStruct(string $messageType, mixed $message = null): string
    {
        return json_encode([
                               'type' => $messageType,
                               'data' => $message,
                           ]);
    }

    /**
     * 获取his用户可订阅的主题
     *
     * @param array $publicParams
     *
     * @return array|string[]
     */
    public static function GetHisUserSubscribeTopics(array $publicParams): array
    {
        $userUid     = trim(Arr::get($publicParams, '_userUid'));
        $orgUid      = trim(Arr::get($publicParams, '_hospitalOrgUid'));
        $brandCode   = trim(Arr::get($publicParams, '_hospitalBrandCode'));
        $hospitalUid = trim(Arr::get($publicParams, '_hospitalUid'));
        if (empty($userUid) || empty($orgUid) || empty($brandCode) || empty($hospitalUid))
        {
            return [];
        }

        // 基础订阅
        $topics = [];

        // 系统升级
        $hisUpgradeTopicName = self::GetHisUpgradeTopicName();
        if (!empty($hisUpgradeTopicName))
        {
            $topics[] = $hisUpgradeTopicName;
        }

        // 个人消息
        $hisUserTopicName = self::GetHisUserTopicName($publicParams);
        if (!empty($hisUserTopicName))
        {
            $topics[] = $hisUserTopicName;
        }

        //TODO:增加分角色的订阅

        return $topics;
    }

    /**
     * 获取his系统升级主题
     *
     * @return string
     */
    public static function GetHisUpgradeTopicName(): string
    {
        return "his/system/upgrade";
    }

    /**
     * 获取his用户个人消息主题
     *
     * @param array  $publicParams
     * @param string $targetUserUid
     *
     * @return string|null
     */
    public static function GetHisUserTopicName(array $publicParams, string $targetUserUid = ''): ?string
    {
        $userUid     = $targetUserUid ?: trim(Arr::get($publicParams, '_userUid'));
        $orgUid      = trim(Arr::get($publicParams, '_hospitalOrgUid'));
        $brandCode   = trim(Arr::get($publicParams, '_hospitalBrandCode'));
        $hospitalUid = trim(Arr::get($publicParams, '_hospitalUid'));
        if (empty($userUid) || empty($orgUid) || empty($brandCode) || empty($hospitalUid))
        {
            return null;
        }

        return "his/system/common/org-{$orgUid}/brand-{$brandCode}/hospital-{$hospitalUid}/user-{$userUid}";
    }

    /**
     * 获取HIS角色消息主题
     *
     * @param string $roleName
     * @param array  $publicParams
     *
     * @return string|null
     */
    public static function GetHisRoleTopicName(string $roleName, array $publicParams): ?string
    {
        $orgUid      = trim(Arr::get($publicParams, '_hospitalOrgUid'));
        $brandCode   = trim(Arr::get($publicParams, '_hospitalBrandCode'));
        $hospitalUid = trim(Arr::get($publicParams, '_hospitalUid'));
        if (empty($roleName) || empty($orgUid) || empty($brandCode) || empty($hospitalUid))
        {
            return null;
        }

        return "his/system/common/org-{$orgUid}/brand-{$brandCode}/hospital-{$hospitalUid}/role-{$roleName}";
    }
}
