<?php

namespace App\Support\Mqtt;

use App\Logics\Logic;
use Illuminate\Http\JsonResponse;

class MqttHttpHelper extends Logic
{
    public static function MqttHttpResponse(int $status, bool $isSuperUser = false, int $statusCode = 200): JsonResponse
    {
        $code = match ($status)
        {
            0 => 'ignore',
            1 => 'allow',
            default => 'deny',
        };

        $result = [
            'result' => $code,
        ];

        if ($isSuperUser)
        {
            $result['is_superuser '] = true;
        }

        return response()
            ->json($result)
            ->setStatusCode($statusCode);
    }
}
