<?php

namespace App\Support\Member;

use App\Models\MemberModel;

class MemberHelper
{
    /**
     * 根据会员ID列表获取会员选项
     *
     * @param array $memberIds
     * @param bool  $keyByMemberId
     *
     * @return array
     */
    public static function GetMemberOptionsByMemberIds(array $memberIds, bool $keyByMemberId = false): array
    {
        $members = MemberModel::getManyByIds($memberIds)
                              ->keyBy('id');

        $result = [];
        foreach ($memberIds as $memberId)
        {
            $member = $members[$memberId] ?? null;
            if (!$member)
            {
                continue;
            }

            $tmp = [
                'uid'   => $member['uid'],
                'name'  => $member['name'],
                'phone' => secretCellphone($member['phone'])
            ];

            if ($keyByMemberId)
            {
                $result[$memberId] = $tmp;
            }
            else
            {
                $result[] = $tmp;
            }
        }

        return $result;
    }
}
