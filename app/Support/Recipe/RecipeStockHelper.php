<?php

namespace App\Support\Recipe;

use Illuminate\Support\Arr;
use App\Enums\ItemSaleTyeEnum;
use App\Enums\ItemUnitTypeEnum;
use App\Logics\Logic;
use App\Logics\LogicResult;
use App\Models\TestsConsumablesTemplatesItemModel;

/**
 * 处方库存辅助类（批量优化版本）
 * 处理处方相关的库存业务逻辑，支持精确数值计算
 */
class RecipeStockHelper extends Logic
{
    /**
     * 将扁平化的处方数据转换为库存验证格式
     *
     * @param array $recipeItemData 处方扁平化的商品数据
     * @param array $publicParams   公共参数
     *
     * @return LogicResult
     */
    public static function getRecipeItemRelationStockItem(array $recipeItemData, array $publicParams): LogicResult
    {
        if (empty($recipeItemData))
        {
            return self::Success();
        }

        // 验证公共参数
        $hospitalId      = intval(Arr::get($publicParams, '_hospitalId'));
        $hospitalBrandId = intval(Arr::get($publicParams, '_hospitalBrandId'));
        $hospitalOrgId   = intval(Arr::get($publicParams, '_hospitalOrgId'));
        if (empty($hospitalId) || empty($hospitalBrandId) || empty($hospitalOrgId))
        {
            return self::Fail('获取处方库存验证数据，缺少必选参数', 400);
        }

        // 所有商品库存所需库存数
        $allStockRequirements = [];

        // 化验项目开具次数
        $testItemsQuantity = [];

        foreach ($recipeItemData as $curRecipeItem)
        {
            $curItemId       = $curRecipeItem['item_id'];
            $curItemSaleType = $curRecipeItem['item_sale_type'];
            $curQuantity     = $curRecipeItem['quantity'];
            $curUnitType     = $curRecipeItem['unit_type'];
            if (empty($curItemId) || bccomp($curQuantity, 0, 2) <= 0)
            {
                continue;
            }

            // 商品
            if ($curItemSaleType == ItemSaleTyeEnum::FirstDrug->value)
            {
                if (!isset($allStockRequirements[$curItemId]))
                {
                    $allStockRequirements[$curItemId] = [
                        'pack_quantity' => 0,
                        'bulk_quantity' => 0,
                    ];
                }

                // 使用整装
                if ($curUnitType == ItemUnitTypeEnum::UNIT_TYPE_PACK->value)
                {
                    $allStockRequirements[$curItemId]['pack_quantity'] = numberAdd([
                                                                                       $allStockRequirements[$curItemId]['pack_quantity'],
                                                                                       $curQuantity
                                                                                   ]);
                }
                else
                {
                    $allStockRequirements[$curItemId]['bulk_quantity'] = numberAdd([
                                                                                       $allStockRequirements[$curItemId]['bulk_quantity'],
                                                                                       $curQuantity
                                                                                   ]);
                }
            }
            elseif ($curItemSaleType == ItemSaleTyeEnum::FirstTest->value)
            {
                if (!isset($testItemsQuantity[$curItemId]))
                {
                    $testItemsQuantity[$curItemId] = 0;
                }

                $testItemsQuantity[$curItemId] = numberAdd([$testItemsQuantity[$curItemId], $curQuantity]);
            }
        }

        // 获取化验项关联耗材所需要的库存
        if (!empty($testItemsQuantity))
        {
            $testItemIds       = array_keys($testItemsQuantity);
            $getConsumablesRes = TestsConsumablesTemplatesItemModel::getConsumablesByTestItemIds($testItemIds, $hospitalId, $hospitalBrandId, $hospitalOrgId);
            foreach ($getConsumablesRes as $curTestItemId => $curConsumableList)
            {
                foreach ($curConsumableList as $curConsumableItem)
                {
                    // 耗材的商品ID、使用单位
                    $consumableItemId   = $curConsumableItem['item_id'];
                    $consumableUnitType = $curConsumableItem['unit_type'];

                    // 耗材用量 = 单次用量 × 化验开具次数
                    $consumableUseQuantity = numberMul([$curConsumableItem['once_use'], $testItemsQuantity[$curTestItemId]]);

                    // 直接往总的库存需求中添加或累加
                    if (!isset($allStockRequirements[$consumableItemId]))
                    {
                        $allStockRequirements[$consumableItemId] = [
                            'pack_quantity' => 0,
                            'bulk_quantity' => 0,
                        ];
                    }

                    if ($consumableUnitType == ItemUnitTypeEnum::UNIT_TYPE_PACK->value)
                    {
                        $allStockRequirements[$consumableItemId]['pack_quantity'] = numberAdd([
                                                                                                  $allStockRequirements[$consumableItemId]['pack_quantity'],
                                                                                                  $consumableUseQuantity
                                                                                              ]);
                    }
                    else
                    {
                        $allStockRequirements[$consumableItemId]['bulk_quantity'] = numberAdd([
                                                                                                  $allStockRequirements[$consumableItemId]['bulk_quantity'],
                                                                                                  $consumableUseQuantity
                                                                                              ]);
                    }
                }
            }
        }

        // 返回所需的库存数量
        return self::Success($allStockRequirements);
    }
}
