<?php

namespace App\Support\PayMode;

use App\Logics\Logic;
use App\Logics\LogicResult;
use App\Models\HospitalPayChannelModel;

class HospitalPayModeHelper extends Logic
{
    /**
     * 获取医院支付渠道和支付方式
     *
     * 包含：
     * 1. 支付渠道
     * 2. 支付方式
     * 3. 支付渠道和支付方式的关联关系
     *
     * @param int $hospitalId
     *
     * @return LogicResult
     */
    public static function GetHospitalPayChannelAndModel(int $hospitalId): LogicResult
    {
        if (empty($hospitalId))
        {
            return self::Fail('获取医院可用的支付渠道和支付方式，缺少医院ID必选参数', 400);
        }

        $result                           = [
            'payChannels'     => [],
            'payModes'        => [],
            'payChannelsData' => [],
        ];
        $getHospitalPayChannelAndModelRes = HospitalPayChannelModel::GetHospitalPayChannelAndModel($hospitalId);
        if (empty($getHospitalPayChannelAndModelRes))
        {
            return self::Success($result);
        }

        $payChannels     = [];
        $payModels       = [];
        $payChannelsData = [];
        foreach ($getHospitalPayChannelAndModelRes as $value)
        {
            if (!isset($payChannels[$value['pay_channel_id']]))
            {
                $payChannels[$value['pay_channel_id']] = [
                    'id'   => $value['pay_channel_id'],
                    'name' => $value['pay_channel_alias_name'] ?? $value['pay_channel_name'],
                    'icon' => realpicturePath($value['pay_channel_icon']),
                ];
            }

            if (!isset($payModels[$value['id']]))
            {
                $payModels[$value['id']] = [
                    'id'   => $value['id'],
                    'name' => $value['alias_name'] ?? $value['name'],
                    'icon' => realPicturePath($value['icon']),
                ];
            }

            if (!isset($payChannelsData[$value['pay_channel_id']]))
            {
                $payChannelsData[$value['pay_channel_id']] = [
                    'id'       => $value['pay_channel_id'],
                    'name'     => $value['pay_channel_alias_name'] ?? $value['pay_channel_name'],
                    'icon'     => realpicturePath($value['pay_channel_icon']),
                    'children' => [],
                ];
            }

            $payChannelsData[$value['pay_channel_id']]['children'][] = [
                'id'   => $value['id'],
                'name' => $value['alias_name'] ?? $value['name'],
                'icon' => realPicturePath($value['icon']),
            ];
        }

        $result['payChannels']     = array_values($payChannels);
        $result['payModes']        = array_values($payModels);
        $result['payChannelsData'] = array_values($payChannelsData);

        return self::Success($result);
    }
}
