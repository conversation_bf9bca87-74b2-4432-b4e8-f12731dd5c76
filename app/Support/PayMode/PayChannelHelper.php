<?php

namespace App\Support\PayMode;

use App\Models\PayChannelModel;

class PayChannelHelper
{
    /**
     * 获取支付渠道
     *
     * @param bool     $keyById
     * @param int|null $status
     *
     * @return array
     */
    public static function GetPayChannelOptions(bool $keyById = false, ?int $status = 1): array
    {
        $where = [['status', '!=', null]];
        if ($status !== null)
        {
            $where = ['status' => $status];
        }

        $getPayChannelRes = PayChannelModel::getData(
            where:    $where,
            orderBys: ['order_by' => 'desc', 'id' => 'asc']
        );

        $payChannelInfo = [];
        foreach ($getPayChannelRes as $curInfo)
        {
            $tmp = [
                'id'   => $curInfo['id'],
                'name' => $curInfo['alias_name'] ?: $curInfo['name'],
                'icon' => realPicturePath($curInfo['icon']),
            ];

            if ($keyById)
            {
                $payChannelInfo[$curInfo['id']] = $tmp;
            }
            else
            {
                $payChannelInfo[] = $tmp;
            }
        }

        return $payChannelInfo;
    }
}
