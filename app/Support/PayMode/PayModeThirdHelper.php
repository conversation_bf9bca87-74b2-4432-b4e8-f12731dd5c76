<?php

namespace App\Support\PayMode;

use App\Logics\Logic;
use App\Models\PayModeThirdModel;

class PayModeThirdHelper extends Logic
{
    /**
     * 获取第三方支付方式
     *
     * @param bool     $keyById
     * @param int|null $status
     *
     * @return array
     */
    public static function GetPayModeThirdOptions(bool $keyById = false, ?int $status = 1): array
    {
        $where = [['status', '!=', null]];
        if ($status !== null)
        {
            $where = ['status' => $status];
        }

        $getPayModeThirdRes = PayModeThirdModel::getData(
            where:    $where,
            orderBys: ['order_by' => 'desc', 'id' => 'asc']
        );

        $payModeThirdInfo = [];
        foreach ($getPayModeThirdRes as $curInfo)
        {
            $tmp = [
                'id'   => $curInfo['id'],
                'name' => $curInfo['alias_name'] ?: $curInfo['name'],
                'icon' => realPicturePath($curInfo['icon']),
            ];

            if ($keyById)
            {
                $payModeThirdInfo[$curInfo['id']] = $tmp;
            }
            else
            {
                $payModeThirdInfo[] = $tmp;
            }
        }

        return $payModeThirdInfo;
    }
}
