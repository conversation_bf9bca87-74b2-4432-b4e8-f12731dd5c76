<?php

namespace App\Support\PayMode;

use Illuminate\Support\Facades\DB;
use App\Models\PayModeModel;

class PayModeHelper
{
    /**
     * 获取支付方式
     *
     * @param bool     $keyById
     * @param int|null $status
     *
     * @return array
     */
    public static function GetPayModeOptions(bool $keyById = false, ?int $status = 1): array
    {
        $where = [['status', '!=', null]];
        if ($status !== null)
        {
            $where = ['status' => $status];
        }

        $getPayModeRes = PayModeModel::getData(
            where:    $where,
            orderBys: ['order_by' => 'desc', 'id' => 'asc']
        );

        $payModeInfo = [];
        foreach ($getPayModeRes as $curInfo)
        {
            $tmp = [
                'id'   => $curInfo['id'],
                'name' => $curInfo['alias_name'] ?: $curInfo['name'],
                'icon' => realPicturePath($curInfo['icon']),
            ];

            if ($keyById)
            {
                $payModeInfo[$curInfo['id']] = $tmp;
            }
            else
            {
                $payModeInfo[] = $tmp;
            }
        }

        return $payModeInfo;
    }
}
