<?php

namespace App\Support\Recharge;

use ArrayAccess;
use Illuminate\Support\Arr;
use App\Logics\Logic;
use App\Logics\LogicResult;
use App\Enums\SheetStatusEnum;
use App\Models\MemberModel;
use App\Models\BalanceRechargeActivitiesModel;
use App\Support\User\HospitalUserHelper;

class RechargeSheetHelper extends Logic
{
    /**
     * 格式化构购买单主信息
     *
     * @param array       $sheets
     * @param array       $publicParams
     * @param bool        $withId
     * @param string|null $keyBy
     *
     * @return LogicResult
     */
    public static function FormatDetailStructure(
        array $sheets, array $publicParams, bool $withId = false, ?string $keyBy = null
    ): LogicResult
    {
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));

        if (empty($sheets))
        {
            return self::Fail('充值单信息错误', 400);
        }
        if (empty($publicParams) || empty($hospitalId))
        {
            return self::Fail('公共参数错误', 400);
        }


        //获取关联的数据
        $memberIds     = array_unique(array_column($sheets, 'member_id'));
        $createUserIds = array_unique(array_column($sheets, 'created_by'));

        //获取会员信息
        $getMemberRes = MemberModel::getManyByIds($memberIds)
                                   ->keyBy('id');

        //获取创建人信息
        $getCreateUserRes = HospitalUserHelper::GetUserOptionsByUserIds($createUserIds, $hospitalId, true);

        $result = [];
        foreach ($sheets as $sheet)
        {
            $curMemberInfo = !empty($getMemberRes[$sheet['member_id']]) ? [
                'uid'   => $getMemberRes[$sheet['member_id']]['uid'],
                'name'  => $getMemberRes[$sheet['member_id']]['name'],
                'phone' => secretCellphone($getMemberRes[$sheet['member_id']]['phone']),
            ] : null;
            $curCreateUser = $getCreateUserRes[$sheet['created_by']] ?? null;

            $tmp = [
                'sheetCode'  => $sheet['sheet_code'],
                'memberInfo' => $curMemberInfo,
                'price'      => formatDisplayNumber($sheet['price'], 2, false),
                'status'     => [
                    'id'   => $sheet['status'],
                    'name' => SheetStatusEnum::getDescription($sheet['status']),
                ],
                'createUser' => $curCreateUser,
                'orderTime'  => formatDisplayDateTime($sheet['order_time']),
                'createTime' => formatDisplayDateTime($sheet['created_at']),
                'updateTime' => formatDisplayDateTime($sheet['updated_at']),
            ];

            if ($withId)
            {
                $tmp['id'] = $sheet['id'];
            }

            if (!empty($keyBy) && isset($sheet[$keyBy]))
            {
                $result[$sheet[$keyBy]] = $tmp;
            }
            else
            {
                $result[] = $tmp;
            }
        }

        return self::Success($result);
    }

    /**
     * 格式化构购买单内商品明细
     *
     * @param array       $sheetItems
     * @param array       $publicParams
     * @param bool        $withItemPrice
     * @param string|null $keyBy
     *
     * @return LogicResult
     */
    public static function FormatSheetItemStructure(
        array $sheetItems, array $publicParams, bool $withItemPrice = false, ?string $keyBy = null
    ): LogicResult
    {
        if (empty($sheetItems))
        {
            return self::Fail('格式化充值单详情，明细不存在', 500071);
        }

        // 公共参数
        $orgId      = intval(Arr::get($publicParams, '_hospitalOrgId', 0));
        $brandId    = intval(Arr::get($publicParams, '_hospitalBrandId', 0));
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId', 0));
        if (empty($orgId) || empty($brandId) || empty($hospitalId))
        {
            return self::Fail('缺少医院必选参数', 400);
        }

        // 购买单内商品ID
        $itemIds = array_column($sheetItems, 'activity_id');

        // 获取购买单内商品
        $getItemInfoRes = [];
        if (!empty($itemIds))
        {
            $getItemInfoRes = BalanceRechargeActivitiesModel::getManyByIds($itemIds);
            $getItemInfoRes = $getItemInfoRes->isNotEmpty() ? $getItemInfoRes->keyBy('id')
                                                                             ->toArray() : [];
        }
        if (empty($getItemInfoRes))
        {
            return self::Fail('余额充值活动无效', 500010);
        }

        // 当前方法内定义递归构建逻辑
        $buildItem = function ($sheetItem) use (&$buildItem, $getItemInfoRes, $withItemPrice) {
            // 商品信息、价格
            $curItemInfo = $getItemInfoRes[$sheetItem['activity_id']] ?? [];

            $tmpItemData = [
                'uid'          => $sheetItem['uid'] ?? '',
                'itemUid'      => $curItemInfo['uid'] ?? '',
                'type'         => ['id' => 0, 'name' => '余额充值'],
                'name'         => $curItemInfo['title'] ?? '',
                'spec'         => '1次',
                'unit'         => '次',
                'quantity'     => formatDisplayNumber($sheetItem['quantity']),
                'price'        => formatDisplayNumber($sheetItem['balance_recharge']),
                'totalPrice'   => formatDisplayNumber($sheetItem['price']),
                'createTime'   => formatDisplayDateTime($sheetItem['created_at']),
                'itemInfo'     => [
                    'uid'   => $curItemInfo['uid'] ?? '',
                    'type'  => ['id' => 0, 'name' => '余额充值'],
                    'name'  => $curItemInfo['title'],
                    'price' => $withItemPrice ? formatDisplayNumber($sheetItem['balance_recharge']) : null,
                ],
                'isPrintItems' => null,
                'suitItems'    => [],
            ];

            return $tmpItemData;
        };

        // 执行主构建逻辑（排除掉子项）
        $returnItemData = [];
        foreach ($sheetItems as $item)
        {
            if (!empty($keyBy) && isset($item[$keyBy]))
            {
                $returnItemData[$item[$keyBy]] = $buildItem($item);
            }
            else
            {
                $returnItemData[] = $buildItem($item);
            }
        }

        return self::Success($returnItemData);
    }
}
