<?php

namespace App\Traits;

trait VersionTrait
{
    /**
     * 获取当前类命名空间最后一段（版本号）
     *
     * @return string|null 返回版本号V1、V2，如果无效返回 null
     */
    public static function getVersion(): ?string
    {
        // 获取当前类完整命名空间
        $fullNamespace = static::class; // e.g. App\Logics\V1\SomeClass

        // 提取命名空间部分（去掉类名）
        $namespaceParts = explode('\\', $fullNamespace);
        array_pop($namespaceParts); // 移除类名
        $lastPart = end($namespaceParts) ?: null; // 最后一段

        \Log::info("获取版本号:" . __FUNCTION__, ['$namespaceParts' => $namespaceParts, '$lastPart' => $lastPart]);
        if ($lastPart === null)
        {
            return null;
        }

        // 正则验证版本号格式，比如 V1、V2 …（V开头+数字）
        if (preg_match('/^V\d+$/i', $lastPart))
        {
            return $lastPart;
        }

        return null;
    }

    /**
     * 获取指定版本号下的逻辑类
     *
     * @param string      $logicClassName
     * @param string|null $version
     * @param string      $namespacePrefix
     *
     * @return string|null
     */
    public static function getLogicByVersion(string $logicClassName, ?string $version, string $namespacePrefix = 'App\\Logics'): ?string
    {
        if ($version === null)
        {
            return null;
        }

        // 拼接完整类名，要考虑version是空字符串的情况
        if (empty($version))
        {
            $logicClass = $namespacePrefix . '\\' . $logicClassName;
        }
        else
        {
            $logicClass = $namespacePrefix . '\\' . $version . '\\' . $logicClassName;
        }

        // 验证是否存在
        if (!class_exists($logicClass))
        {
            return null;
        }

        return $logicClass; // 返回完整类名（可用于 new $logicClass()）
    }
}
