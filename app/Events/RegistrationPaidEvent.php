<?php

namespace App\Events;

use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Contracts\Events\ShouldDispatchAfterCommit;
use Illuminate\Queue\SerializesModels;

/**
 * 挂号支付成功事件
 */
class RegistrationPaidEvent implements ShouldQueue, ShouldDispatchAfterCommit
{
    use Dispatchable, SerializesModels;

    public string $registrationCode;

    public string|null $version;

    public function __construct(string $registrationCode, ?string $version)
    {
        $this->registrationCode = $registrationCode;
        $this->version          = $version;
    }
}
