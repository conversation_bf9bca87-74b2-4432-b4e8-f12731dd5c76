<?php

namespace App\Events;

use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Contracts\Events\ShouldDispatchAfterCommit;
use Illuminate\Queue\SerializesModels;

/**
 * 余额充值支付成功事件
 */
class RechargePaidEvent implements ShouldQueue, ShouldDispatchAfterCommit
{
    use Dispatchable, SerializesModels;

    public string $sheetCode;

    public string|null $version;

    public function __construct(string $sheetCode, ?string $version)
    {
        $this->sheetCode = $sheetCode;
        $this->version   = $version;
    }
}
