<?php

namespace App\Events;

use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Contracts\Events\ShouldDispatchAfterCommit;
use Illuminate\Queue\SerializesModels;

/**
 * 结算单支付成功事件
 */
class PayOrderPaidEvent implements ShouldQueue, ShouldDispatchAfterCommit
{
    use Dispatchable, SerializesModels;

    public string $payOrderCode;
    public string $payPrice;

    public string|null $version;

    public function __construct(string $payOrderCode, string $payPrice, ?string $version)
    {
        $this->payOrderCode = $payOrderCode;
        $this->payPrice     = $payPrice;
        $this->version      = $version;
    }
}
