<?php

namespace App\Events;

use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Contracts\Events\ShouldDispatchAfterCommit;
use Illuminate\Queue\SerializesModels;

/**
 * 处方支付成功事件
 */
class RecipePaidEvent implements ShouldQueue, ShouldDispatchAfterCommit
{
    use Dispatchable, SerializesModels;

    public string $recipeCode;

    public string|null $version;

    public function __construct(string $recipeCode, ?string $version)
    {
        $this->recipeCode = $recipeCode;
        $this->version    = $version;
    }
}
