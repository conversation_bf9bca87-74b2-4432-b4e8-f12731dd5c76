<?php

namespace App\Infrastructure\Search;

use Exception;
use Meilisearch\Client;
use Meilisearch\Endpoints\Indexes;
use Meilisearch\Exceptions\ApiException;

/**
 * MeiLiSearch基础设施客户端
 * 负责与MeiLiSearch服务的底层交互
 */
class MeiLiSearchClient
{
    private static ?self $instance = null;
    private ?Client $client = null;

    /**
     * 私有构造函数，防止外部实例化
     */
    private function __construct()
    {
        $this->initializeClient();
    }

    /**
     * 防止克隆
     */
    private function __clone()
    {
    }

    /**
     * 防止反序列化
     * @throws Exception
     */
    public function __wakeup()
    {
        throw new Exception("Cannot unserialize singleton");
    }

    /**
     * 获取单例实例
     *
     * @return self
     */
    public static function getInstance(): self
    {
        if (self::$instance === null)
        {
            self::$instance = new self();
        }

        return self::$instance;
    }

    /**
     * 初始化MeiLiSearch客户端
     */
    private function initializeClient(): void
    {
        $host = config('search.engines.meilisearch.host', 'http://127.0.0.1:7700');
        $key  = config('search.engines.meilisearch.key', '');

        $this->client = new Client($host, $key);
    }

    /**
     * 获取MeiLiSearch原生客户端
     *
     * @return Client
     */
    public function getClient(): Client
    {
        if ($this->client === null)
        {
            $this->initializeClient();
        }

        return $this->client;
    }

    /**
     * 获取索引对象
     *
     * @param string $indexName 索引名称
     *
     * @return Indexes
     */
    public function getIndex(string $indexName): Indexes
    {
        return $this->getClient()
                    ->getIndex($indexName);
    }

    /**
     * 创建索引
     *
     * @param string $indexName 索引名称
     * @param array  $options   创建选项
     *
     * @return Indexes
     * @throws ApiException
     */
    public function createIndex(string $indexName, array $options = []): Indexes
    {
        $index = $this->getClient()
                      ->index($indexName);
        $index->create($indexName, $options);

        return $index;
    }

    /**
     * 获取索引信息
     *
     * @param string $indexName 索引名称
     *
     * @return array|null
     */
    public function getIndexInfo(string $indexName): ?array
    {
        try
        {
            $index = $this->getIndex($indexName);

            return [
                'exists' => true,
                'info'   => $index->fetchRawInfo(),
                'stats'  => $index->stats()
            ];
        } catch (Exception $e)
        {
            return [
                'exists' => false,
                'error'  => $e->getMessage()
            ];
        }
    }

    /**
     * 删除索引
     *
     * @param string $indexName 索引名称
     *
     * @return bool
     */
    public function deleteIndex(string $indexName): bool
    {
        try
        {
            $index = $this->getIndex($indexName);
            $index->delete();

            return true;
        } catch (Exception)
        {
            return false;
        }
    }

    /**
     * 更新索引设置
     *
     * @param string $indexName 索引名称
     * @param array  $settings  设置数组
     *
     * @return bool
     */
    public function updateIndexSettings(string $indexName, array $settings): bool
    {
        try
        {
            $index = $this->getIndex($indexName);
            $index->updateSettings($settings);

            return true;
        } catch (Exception)
        {
            return false;
        }
    }

    /**
     * 添加文档到索引
     *
     * @param string      $indexName  索引名称
     * @param array       $documents  文档数组
     * @param string|null $primaryKey 主键字段
     *
     * @return bool
     */
    public function addDocuments(string $indexName, array $documents, ?string $primaryKey = null): bool
    {
        try
        {
            $index = $this->getIndex($indexName);
            $index->addDocuments($documents, $primaryKey);

            return true;
        } catch (Exception)
        {
            return false;
        }
    }

    /**
     * 删除文档
     *
     * @param string $indexName   索引名称
     * @param array  $documentIds 文档ID数组
     *
     * @return bool
     */
    public function deleteDocuments(string $indexName, array $documentIds): bool
    {
        try
        {
            $index = $this->getIndex($indexName);
            $index->deleteDocuments($documentIds);

            return true;
        } catch (Exception)
        {
            return false;
        }
    }

    /**
     * 清空索引中的所有文档
     *
     * @param string $indexName 索引名称
     *
     * @return bool
     */
    public function deleteAllDocuments(string $indexName): bool
    {
        try
        {
            $index = $this->getIndex($indexName);
            $index->deleteAllDocuments();

            return true;
        } catch (Exception)
        {
            return false;
        }
    }

    /**
     * 搜索文档
     *
     * @param string $indexName 索引名称
     * @param string $query     搜索查询
     * @param array  $options   搜索选项
     *
     * @return array
     */
    public function search(string $indexName, string $query, array $options = []): array
    {
        try
        {
            $index = $this->getIndex($indexName);

            return $index->search($query, $options)
                         ->toArray();
        } catch (Exception $e)
        {
            return [
                'hits'  => [],
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * 生成索引名称
     *
     * @param string|int $orgId  机构ID
     * @param string     $prefix 前缀，默认为 'his_item'
     *
     * @return string
     */
    public static function generateIndexName(string|int $orgId, string $prefix = 'his_item'): string
    {
        return sprintf('%s_%s', $prefix, $orgId);
    }
}
