<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Response;
use App\Facades\TokenFacade;

/**
 * API请求登录状态验证中间件
 */
class LoginMiddleware
{
    public function handle(Request $request, Closure $next, string $withoutCheckHospital = '0'): JsonResponse|Response
    {
        $token = $request->bearerToken();
        if (empty($token))
        {
            return outputJsonError(1000, '', ['error' => 'Missing token']);
        }

        $sessionData = TokenFacade::all($token);
        if (empty($sessionData))
        {
            return outputJsonError(1000, '', ['error' => 'Session empty']);
        }

        if ($withoutCheckHospital == '0')
        {
            if (empty($sessionData['hospitalId']) || empty($sessionData['hospitalUid']) || empty($sessionData['hospitalUserId']) || empty($sessionData['hospitalUserUid']))
            {
                return outputJsonError(1001, '', ['error' => 'Session hospital or hospital_user empty']);
            }
        }

        // 系统保留参数
        addReservedParameter([
                                 'token'              => $token,
                                 '_hospitalUid'       => $sessionData['hospitalUid'] ?? '',
                                 '_hospitalId'        => intval($sessionData['hospitalId'] ?? 0),
                                 '_hospitalOrgId'     => intval($sessionData['hospitalOrgId'] ?? 0),
                                 '_hospitalOrgUid'    => $sessionData['hospitalOrgUid'] ?? '',
                                 '_hospitalBrandId'   => intval($sessionData['hospitalBrandId'] ?? 0),
                                 '_hospitalBrandCode' => $sessionData['hospitalBrandCode'] ?? '',
                                 '_userUid'           => $sessionData['userUid'] ?? '',
                                 '_userId'            => intval($sessionData['userId'] ?? 0),
                                 '_hospitalUserId'    => intval($sessionData['hospitalUserId'] ?? 0),
                                 '_hospitalUserUid'   => $sessionData['hospitalUserUid'] ?? '',
                                 '_ip'                => $request->ip(),
                             ]);

        return $next($request);
    }
}
