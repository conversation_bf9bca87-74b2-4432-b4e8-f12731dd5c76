<?php

namespace App\Http\Controllers\V1;

use Throwable;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;
use App\Http\Controllers\Controller;
use App\Enums\StockInventorySourceEnum;
use App\Logics\V1\StockInventoryLogic;
use App\Models\ItemModel;

class StockInventoryController extends Controller
{

    /**
     * 提交库存盘点
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws Throwable
     * @noinspection PhpUnused
     */
    public function SubmitInventory(Request $request): JsonResponse
    {
        // 基础参数验证
        $validator = Validator::make($request->all(), [
            'itemUid'       => ['required', 'string'],
            'itemBarcode'   => ['required', 'string'],
            'stock'         => ['required', 'array'],
            'inventoryType' => ['sometimes', 'nullable', Rule::in([StockInventorySourceEnum::TEMPORARY_FIXED->value])],
        ]);

        if ($validator->fails())
        {
            return outputJsonError(400,
                                   $validator->errors()
                                             ->first(),
                                   [
                                       'validatorError' => $validator->errors()
                                                                     ->all()
                                   ]);
        }

        // 获取商品信息
        $inventoryParams = $validator->validated();
        $itemUid         = $inventoryParams['itemUid'];
        $getItemRes      = ItemModel::getOneByUid($itemUid);
        if (empty($getItemRes))
        {
            return outputJsonError(33000, '商品不存在', ['error' => '商品不存在']);
        }

        $inventoryParams['itemId'] = $getItemRes['id'];
        $getInventoryRes           = StockInventoryLogic::submitStockInventory($inventoryParams, getRequestReservedParameters());
        if ($getInventoryRes->isFail())
        {
            return outputJsonError($getInventoryRes->getCode(), $getInventoryRes->getMessage(), ['error' => $getInventoryRes->getMessage()]);
        }

        return outputJsonResult();
    }
}
