<?php

namespace App\Http\Controllers\V1;

use Validator;
use Illuminate\Validation\Rules\Enum;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Response;
use Illuminate\Validation\ValidationException;
use Illuminate\Contracts\Container\BindingResolutionException;
use App\Http\Controllers\Controller;
use App\Enums\PrintReturnTypeEnum;
use App\Logics\PdfServiceLogic;
use App\Logics\V1\PrintLogic;
use App\Models\RecipeModel;
use App\Models\TestModel;
use App\Models\ImagesModel;
use App\Models\CaseSnapshotModel;
use App\Models\TestsResultsReportModel;
use App\Models\ImagesResultsReportModel;

/**
 * 打印相关
 * Class PrintController
 * @package      App\Http\Controllers\V1
 * @noinspection PhpUnused
 */
class PrintController extends Controller
{
    /**
     * 打印处方
     *
     * @param Request $request
     *
     * @return JsonResponse|Response
     * @throws ValidationException
     * @throws BindingResolutionException
     * @noinspection PhpUnused
     */
    public function PrintRecipe(Request $request): JsonResponse|Response
    {
        $validator = Validator::make($request->post(),
                                     [
                                         'recipeCode' => ['required', 'string'],
                                         'setting'    => ['sometimes', 'nullable', 'array'],
                                         'returnType' => [
                                             'sometimes',
                                             'nullable',
                                             'string',
                                             new Enum(PrintReturnTypeEnum::class)
                                         ],
                                     ],
                                     [],
                                     [
                                         'recipeCode' => '处方编码',
                                         'setting'    => '打印配置',
                                         'returnType' => '返回类型'
                                     ]);
        if ($validator->fails())
        {
            return outputJsonError(400,
                                   $validator->errors()
                                             ->first(),
                                   [
                                       'validatorError' => $validator->errors()
                                                                     ->all()
                                   ]);
        }

        // 获取打印参数和返回参数
        $setting    = $validator->validated()['setting'] ?? null;
        $returnType = $validator->validated()['returnType'] ?? 'json';

        // 获取处方信息
        $recipeCode   = $validator->validated()['recipeCode'];
        $getRecipeRes = RecipeModel::getData(['id'], ['recipe_code' => $recipeCode]);
        $getRecipeRes = $getRecipeRes ? current($getRecipeRes) : [];
        if (empty($getRecipeRes))
        {
            return outputJsonError(39000, '打印处方不存在', ['error' => '处方不存在']);
        }

        // 获取处方信息
        $getPrintRecipeRes = PrintLogic::GetPrintRecipe($getRecipeRes['id'],
                                                        getRequestReservedParameters());
        if ($getPrintRecipeRes->isFail())
        {
            return outputJsonError($getPrintRecipeRes->getCode(),
                                   $getPrintRecipeRes->getMessage(),
                                   ['error' => $getPrintRecipeRes->getMessage()]);
        }

        // 生成PDF
        if ($returnType == PrintReturnTypeEnum::Pdf->value)
        {
            $getPrintRecipeRes = PdfServiceLogic::GenerateRecipePdf($getPrintRecipeRes->getData(),
                                                                    $setting);
            if (empty($getPrintRecipeRes))
            {
                return outputJsonError(500, '生成PDF失败', ['error' => '生成PDF失败']);
            }

            return response()->make($getPrintRecipeRes, 200, [
                'Content-Type'        => 'application/pdf',
                'Content-Disposition' => 'inline; filename="recipe.pdf"'
            ]);
        }

        return outputJsonResult($getPrintRecipeRes->getData());
    }

    /**
     * 打印病历
     *
     * @param Request $request
     *
     * @return JsonResponse|Response
     * @throws BindingResolutionException
     * @throws ValidationException
     * @noinspection PhpUnused
     */
    public function PrintCase(Request $request): JsonResponse|Response
    {
        $validator = Validator::make($request->post(),
                                     [
                                         'caseCode'   => ['required', 'string'],
                                         'setting'    => ['sometimes', 'nullable', 'array'],
                                         'returnType' => [
                                             'sometimes',
                                             'nullable',
                                             'string',
                                             new Enum(PrintReturnTypeEnum::class)
                                         ],
                                     ],
                                     [],
                                     [
                                         'caseCode'   => '病历编码',
                                         'setting'    => '打印配置',
                                         'returnType' => '返回类型'
                                     ]);
        if ($validator->fails())
        {
            return outputJsonError(400,
                                   $validator->errors()
                                             ->first(),
                                   [
                                       'validatorError' => $validator->errors()
                                                                     ->all()
                                   ]);
        }

        // 获取打印参数和返回参数
        $setting    = $validator->validated()['setting'] ?? null;
        $returnType = $validator->validated()['returnType'] ?? 'json';

        // 获取病历信息
        $caseCode   = $validator->validated()['caseCode'];
        $getCaseRes = CaseSnapshotModel::getData(['case_id'], ['case_code' => $caseCode]);
        $getCaseRes = $getCaseRes ? current($getCaseRes) : [];
        if (empty($getCaseRes))
        {
            return outputJsonError(38000, '历史病历详情不存在', ['error' => '历史病历详情不存在']);
        }

        // 获取打印历史病历信息
        $getPrintCaseRes = PrintLogic::GetPrintCase($getCaseRes['case_id'],
                                                    getRequestReservedParameters());
        if ($getPrintCaseRes->isFail())
        {
            return outputJsonError($getPrintCaseRes->getCode(),
                                   $getPrintCaseRes->getMessage(),
                                   ['error' => $getPrintCaseRes->getMessage()]);
        }

        if ($returnType == PrintReturnTypeEnum::Pdf->value)
        {
            $getPrintCaseRes = PdfServiceLogic::GenerateCasePdf($getPrintCaseRes->getData(),
                                                                $setting);
            if (empty($getPrintCaseRes))
            {
                return outputJsonError(500, '生成PDF失败', ['error' => '生成PDF失败']);
            }

            return response()->make($getPrintCaseRes, 200, [
                'Content-Type'        => 'application/pdf',
                'Content-Disposition' => 'inline; filename="case.pdf"'
            ]);
        }

        return outputJsonResult($getPrintCaseRes->getData());
    }

    /**
     * 打印化验报告
     *
     * @param Request $request
     *
     * @return JsonResponse|Response
     * @throws BindingResolutionException
     * @throws ValidationException
     * @noinspection PhpUnused
     */
    public function PrintTestReport(Request $request): JsonResponse|Response
    {
        $validator = Validator::make($request->post(),
                                     [
                                         'testCode'   => ['required', 'string'],
                                         'resultUid'  => ['required', 'string'],
                                         'setting'    => ['sometimes', 'nullable', 'array'],
                                         'returnType' => [
                                             'sometimes',
                                             'nullable',
                                             'string',
                                             new Enum(PrintReturnTypeEnum::class)
                                         ],
                                     ],
                                     [],
                                     [
                                         'testCode'   => '化验编码',
                                         'resultUid'  => '化验结果UID',
                                         'setting'    => '打印配置',
                                         'returnType' => '返回类型'
                                     ]);
        if ($validator->fails())
        {
            return outputJsonError(400,
                                   $validator->errors()
                                             ->first(),
                                   [
                                       'validatorError' => $validator->errors()
                                                                     ->all()
                                   ]);
        }

        // 检测结果参数
        $validatedData = $validator->validated();
        $testCode      = $validatedData['testCode'];
        $resultUid     = $validatedData['resultUid'];

        // 获取打印参数和返回参数
        $setting    = $validatedData['setting'] ?? null;
        $returnType = $validatedData['returnType'] ?? 'json';

        // 获取化验信息
        $getTestRes = TestModel::getData(['id'], ['test_code' => $testCode]);
        $getTestRes = $getTestRes ? current($getTestRes) : [];
        if (empty($getTestRes))
        {
            return outputJsonError(40000, '化验不存在', ['error' => '化验不存在']);
        }

        // 获取化验报告单结果
        $getTesteResultRes = TestsResultsReportModel::getOneByUid($resultUid);
        if (empty($getTesteResultRes))
        {
            return outputJsonError(40006, '打印的化验结果不存在', ['error' => '打印的化验结果不存在']);
        }

        // 获取
        $getPrintTestReportRes = PrintLogic::GetPrintTestReportResult($getTestRes['id'],
                                                                      $getTesteResultRes['id'],
                                                                      getRequestReservedParameters());
        if ($getPrintTestReportRes->isFail())
        {
            return outputJsonError($getPrintTestReportRes->getCode(),
                                   $getPrintTestReportRes->getMessage(),
                                   ['error' => $getPrintTestReportRes->getMessage()]);
        }

        // 生成PDF
        if ($returnType == PrintReturnTypeEnum::Pdf->value)
        {
            $getGenerateTestReportPdfRes = PdfServiceLogic::GenerateTestReportPdf($getPrintTestReportRes->getData(),
                                                                                  $setting);
            if (empty($getGenerateTestReportPdfRes))
            {
                return outputJsonError(500, '生成PDF失败', ['error' => '生成PDF失败']);
            }

            return response()->make($getGenerateTestReportPdfRes, 200, [
                'Content-Type'        => 'application/pdf',
                'Content-Disposition' => 'inline; filename="testReport.pdf"'
            ]);
        }

        return outputJsonResult($getPrintTestReportRes->getData());
    }

    /**
     * 打印影像报告
     *
     * @param Request $request
     *
     * @return JsonResponse|Response
     * @throws BindingResolutionException
     * @throws ValidationException
     * @noinspection PhpUnused
     */
    public function PrintImageReport(Request $request): JsonResponse|Response
    {
        $validator = Validator::make($request->post(),
                                     [
                                         'imageCode'  => ['required', 'string'],
                                         'resultUid'  => ['required', 'string'],
                                         'setting'    => ['sometimes', 'nullable', 'array'],
                                         'returnType' => [
                                             'sometimes',
                                             'nullable',
                                             'string',
                                             new Enum(PrintReturnTypeEnum::class)
                                         ],
                                     ],
                                     [],
                                     [
                                         'imageCode'  => '影像编码',
                                         'resultUid'  => '影像结果UID',
                                         'setting'    => '打印配置',
                                         'returnType' => '返回类型'
                                     ]);
        if ($validator->fails())
        {
            return outputJsonError(400,
                                   $validator->errors()
                                             ->first(),
                                   [
                                       'validatorError' => $validator->errors()
                                                                     ->all()
                                   ]);
        }

        // 检测结果参数
        $validatedData = $validator->validated();
        $imageCode     = $validatedData['imageCode'];
        $resultUid     = $validatedData['resultUid'];

        // 获取打印参数和返回参数
        $setting    = $validatedData['setting'] ?? null;
        $returnType = $validatedData['returnType'] ?? 'json';

        // 获取影像信息
        $getImageRes = ImagesModel::getData(['id'], ['image_code' => $imageCode]);
        $getImageRes = $getImageRes ? current($getImageRes) : [];
        if (empty($getImageRes))
        {
            return outputJsonError(40101, '影像不存在', ['error' => '影像不存在']);
        }

        // 获取化验报告单结果
        $getImageResultRes = ImagesResultsReportModel::getOneByUid($resultUid);
        if (empty($getImageResultRes))
        {
            return outputJsonError(40107, '影像结果不存在', ['error' => '影像结果不存在']);
        }

        // 获取
        $getPrintImageReportRes = PrintLogic::GetPrintImageReportResult($getImageRes['id'],
                                                                        $getImageResultRes['id'],
                                                                        getRequestReservedParameters());
        if ($getPrintImageReportRes->isFail())
        {
            return outputJsonError($getPrintImageReportRes->getCode(),
                                   $getPrintImageReportRes->getMessage(),
                                   ['error' => $getPrintImageReportRes->getMessage()]);
        }

        // 生成PDF
        if ($returnType == PrintReturnTypeEnum::Pdf->value)
        {
            $getGenerateImageReportPdfRes = PdfServiceLogic::GenerateImageReportPdf($getPrintImageReportRes->getData(),
                                                                                    $setting);
            if (empty($getGenerateImageReportPdfRes))
            {
                return outputJsonError(500, '生成PDF失败', ['error' => '生成PDF失败']);
            }

            return response()->make($getGenerateImageReportPdfRes, 200, [
                'Content-Type'        => 'application/pdf',
                'Content-Disposition' => 'inline; filename="imageReport.pdf"'
            ]);
        }

        return outputJsonResult($getPrintImageReportRes->getData());
    }
}
