<?php

namespace App\Http\Controllers\V1;

use Throwable;
use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rules\Enum;
use App\Enums\SheetStatusEnum;
use App\Support\PayMode\HospitalPayModeHelper;
use App\Support\PayOrder\PayOrderHelper;
use App\Logics\V1\PayOrderLogic;
use App\Logics\V1\UserLogic;
use App\Logics\V1\PayLogic;

class OrderController extends Controller
{
    /**
     * 获取结算单列表筛选项
     *
     * @param Request $request
     *
     * @return JsonResponse
     */
    public function ListFilterOptions(Request $request): JsonResponse
    {
        $publicParams = getRequestReservedParameters();

        $statusOptions = SheetStatusEnum::caseToOptions();

        $createUsersRes = PayOrderLogic::GetCreateUsersOptions($publicParams);
        if ($createUsersRes->isFail())
        {
            return outputJsonError($createUsersRes->getCode(), '', ['error' => $createUsersRes->getMessage()]);
        }

        $cashierUsersRes = PayOrderLogic::GetCashierUsersOptions($publicParams);
        if ($cashierUsersRes->isFail())
        {
            return outputJsonError($cashierUsersRes->getCode(), '', ['error' => $cashierUsersRes->getMessage()]);
        }

        $hospitalPayModeRes = HospitalPayModeHelper::GetHospitalPayChannelAndModel(getPublicParamsHospitalId());
        if ($hospitalPayModeRes->isFail())
        {
            return outputJsonError($hospitalPayModeRes->getCode(), '', ['error' => $hospitalPayModeRes->getMessage()]);
        }

        return outputJsonResult([
                                    'statusOptions'       => $statusOptions,
                                    'createUsersOptions'  => $createUsersRes->getData(),
                                    'cashierUsersOptions' => $cashierUsersRes->getData(),
                                    'payModeOptions'      => $hospitalPayModeRes->getData('payModes', []),
                                ]);
    }

    /**
     * 获取结算单列表
     *
     * @param Request $request
     *
     * @return JsonResponse
     */
    public function List(Request $request): JsonResponse
    {
        //验证必传参数
        $validator = Validator::make(
            $request->post(),
            [
                'page'            => 'sometimes|nullable|integer',
                'count'           => 'sometimes|nullable|integer',
                'keywords'        => 'sometimes|nullable|string',
                'payOrderCode'    => 'sometimes|nullable|string',
                'status'          => ['sometimes', 'nullable', 'integer', new Enum(SheetStatusEnum::class)],
                'createUserUid'   => 'sometimes|nullable|string|between:8,64',
                'cashierUserUid ' => 'sometimes|nullable|string|between:8,64',
                'payMode'         => 'sometimes|nullable|integer',
                'startDate'       => 'sometimes|nullable|date',
                'endDate'         => 'sometimes|nullable|date',
            ],
            [],
            [
                'page'            => '页码',
                'count'           => '每页条数',
                'keywords'        => '关键词',
                'payOrderCode'    => '收款单号',
                'status'          => '收款状态',
                'createUserUid'   => '创建人',
                'cashierUserUid ' => '收银员',
                'payMode'         => '现付款支付方式',
                'startDate'       => '开始日期',
                'endDate'         => '结束日期',
            ]
        );

        if ($validator->fails())
        {
            return outputJsonError(400,
                                   $validator->errors()
                                             ->first(),
                                   [
                                       'validatorError' => $validator->errors()
                                                                     ->all()
                                   ]);
        }

        $params       = $validator->validated();
        $publicParams = getRequestReservedParameters();

        // 验证UID并转换ID
        $createUserUid  = trim($params['createUserUid'] ?? '');
        $cashierUserUid = trim($params['cashierUserUid'] ?? '');

        if (!empty($createUserUid))
        {
            $getUserRes = UserLogic::GetValidUserByIdOrUid(userUid: $createUserUid);
            if ($getUserRes->isFail())
            {
                return outputJsonError($getUserRes->getCode(), '', ['error' => $getUserRes->getMessage()]);
            }
            $params['createUserId'] = $getUserRes->getData('id');
        }

        if (!empty($cashierUserUid))
        {
            $getCashierUserRes = UserLogic::GetValidUserByIdOrUid(userUid: $cashierUserUid);
            if ($getCashierUserRes->isFail())
            {
                return outputJsonError($getCashierUserRes->getCode(),
                                       '',
                                       ['error' => $getCashierUserRes->getMessage()]);
            }
            $params['cashierUserId'] = $getCashierUserRes->getData('id');
        }

        $orderListResult = PayOrderLogic::SearchOrderList($params, $publicParams);
        if ($orderListResult->isFail())
        {
            return outputJsonError($orderListResult->getCode(),
                                   $orderListResult->getMessage(),
                                   ['error' => $orderListResult->getMessage()]);
        }

        return outputJsonResult($orderListResult->getData());
    }

    /**
     * 去支付
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws Throwable
     */
    public function GoPay(Request $request): JsonResponse
    {
        //验证必传参数
        $validator = Validator::make(
            $request->post(),
            [
                'payOrderCode' => 'sometimes|nullable|string|between:8,32',
                'payPrice'     => 'sometimes|nullable|numeric|min:0',
            ],
            [],
            [
                'payOrderCode' => '收款单号',
                'payPrice'     => '支付金额',
            ]
        );

        if ($validator->fails())
        {
            return outputJsonError(400,
                                   $validator->errors()
                                             ->first(),
                                   [
                                       'validatorError' => $validator->errors()
                                                                     ->all()
                                   ]);
        }

        $params       = $validator->validated();
        $publicParams = getRequestReservedParameters();

        $goPayResult = PayLogic::GoPay($params, $publicParams);
        if ($goPayResult->isFail())
        {
            return outputJsonError($goPayResult->getCode(),
                                   $goPayResult->getMessage(),
                                   ['error' => $goPayResult->getMessage()]);
        }

        return outputJsonResult($goPayResult->getData());
    }

    /**
     * 结算单详情
     *
     * @param Request $request
     *
     * @return JsonResponse
     */
    public function Detail(Request $request): JsonResponse
    {
        //验证必传参数
        $validator = Validator::make(
            $request->post(),
            [
                'payOrderCode' => 'sometimes|nullable|string|between:8,32',
            ],
            [],
            [
                'payOrderCode' => '收款单号',
            ]
        );

        if ($validator->fails())
        {
            return outputJsonError(400,
                                   $validator->errors()
                                             ->first(),
                                   [
                                       'validatorError' => $validator->errors()
                                                                     ->all()
                                   ]);
        }

        $params       = $validator->validated();
        $publicParams = getRequestReservedParameters();

        $detailResult = PayOrderLogic::GetDetail($params, $publicParams);
        if ($detailResult->isFail())
        {
            return outputJsonError($detailResult->getCode(),
                                   $detailResult->getMessage(),
                                   ['error' => $detailResult->getMessage()]);
        }

        return outputJsonResult($detailResult->getData());
    }

    /**
     * 取消结算单
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws Throwable
     */
    public function Cancel(Request $request): JsonResponse
    {
        //验证必传参数
        $validator = Validator::make(
            $request->post(),
            [
                'payOrderCode' => 'sometimes|nullable|string|between:8,32',
            ],
            [],
            [
                'payOrderCode' => '收款单号',
            ]
        );

        if ($validator->fails())
        {
            return outputJsonError(400,
                                   $validator->errors()
                                             ->first(),
                                   [
                                       'validatorError' => $validator->errors()
                                                                     ->all()
                                   ]);
        }

        $params       = $validator->validated();
        $publicParams = getRequestReservedParameters();
        $payOrderCode = trim($params['payOrderCode']);

        $payOrderRes = PayOrderHelper::GetValidPayOrder(
            payOrderCode: $payOrderCode,
            hospitalId:   getPublicParamsHospitalId(),
        );
        if ($payOrderRes->isFail())
        {
            return outputJsonError($payOrderRes->getCode(),
                                   $payOrderRes->getMessage(),
                                   ['error' => $payOrderRes->getMessage()]);
        }
        $payOrder             = $payOrderRes->getData();
        $params['payOrderId'] = $payOrder['id'];

        $cancelResult = PayOrderLogic::CancelOrder($params, $publicParams);
        if ($cancelResult->isFail())
        {
            return outputJsonError($cancelResult->getCode(),
                                   $cancelResult->getMessage(),
                                   ['error' => $cancelResult->getMessage()]);
        }

        return outputJsonResult();
    }
}
