<?php

namespace App\Http\Controllers\V1;

use Arr;
use Throwable;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use DateMalformedStringException;
use Illuminate\Support\Facades\Validator;
use App\Http\Controllers\Controller;
use App\Logics\V1\AppointmentLogic;
use App\Models\AppointmentModel;

/**
 * 预约相关
 * Class AppointmentController
 * @package App\Http\Controllers\V1
 */
class AppointmentController extends Controller
{
    /**
     * 获取预约基础数据
     *
     * @return JsonResponse
     */
    public function GetAddOptions(): JsonResponse
    {
        $getBasicDataRes = AppointmentLogic::GetAddAppointmentOptions(getRequestReservedParameters());
        if ($getBasicDataRes->isFail())
        {
            return outputJsonError($getBasicDataRes->getCode(), $getBasicDataRes->getMessage(), ['error' => $getBasicDataRes->getMessage()]);
        }

        return outputJsonResult($getBasicDataRes->getData());
    }

    /**
     * 获取预约列表筛选项
     *
     * @return JsonResponse
     */
    public function GetFilterOptions()
    {
        $getFilterOptionsRes = AppointmentLogic::GetFilterOptions(getRequestReservedParameters());
        if ($getFilterOptionsRes->isFail())
        {
            return outputJsonError($getFilterOptionsRes->getCode(), $getFilterOptionsRes->getMessage(), ['error' => $getFilterOptionsRes->getMessage()]);
        }

        return outputJsonResult($getFilterOptionsRes->getData());
    }

    /**
     * 获取预约列表
     *
     * @param Request $request
     *
     * @return JsonResponse
     */
    public function List(Request $request)
    {
        $validator = Validator::make($request->post(),
                                     [
                                         'keyword'              => ['sometimes', 'nullable', 'string'],
                                         'appointmentDate'      => ['sometimes', 'nullable', 'date'],
                                         'appointmentType'      => ['sometimes', 'nullable', 'integer'],
                                         'appointmentDoctorUid' => ['sometimes', 'nullable', 'string'],
                                         'page'                 => ['sometimes', 'nullable', 'integer'],
                                         'count'                => ['sometimes', 'nullable', 'integer'],
                                     ]);
        if ($validator->fails())
        {
            return outputJsonError(400,
                                   $validator->errors()
                                             ->first(),
                                   [
                                       'validatorError' => $validator->errors()
                                                                     ->all()
                                   ]);
        }

        $searchParams          = $validator->validated();
        $publicParams          = getRequestReservedParameters();
        $getAppointmentListRes = AppointmentLogic::GetAppointmentList($searchParams, $publicParams);
        if ($getAppointmentListRes->isFail())
        {
            return outputJsonError($getAppointmentListRes->getCode(), $getAppointmentListRes->getMessage(), ['error' => $getAppointmentListRes->getMessage()]);
        }

        return outputJsonResult($getAppointmentListRes->getData());
    }

    /**
     * 获取预约月统计数据
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws DateMalformedStringException
     */
    public function GetAppointmentMonthStatistics(Request $request)
    {
        $validator = Validator::make($request->post(),
                                     [
                                         'date' => ['required', 'date'],
                                     ],
                                     [],
                                     [
                                         'date' => '统计日期',
                                     ]);
        if ($validator->fails())
        {
            return outputJsonError(400,
                                   $validator->errors()
                                             ->first(),
                                   [
                                       'validatorError' => $validator->errors()
                                                                     ->all()
                                   ]);
        }

        $validatedData    = $validator->validated();
        $getStatisticsRes = AppointmentLogic::GetAppointmentStatistics($validatedData['date'], getRequestReservedParameters());
        if ($getStatisticsRes->isFail())
        {
            return outputJsonError($getStatisticsRes->getCode(), $getStatisticsRes->getMessage(), ['error' => $getStatisticsRes->getMessage()]);
        }

        return outputJsonResult($getStatisticsRes->getData());
    }

    /**
     * 添加预约
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws Throwable
     */
    public function Add(Request $request)
    {
        $validator = Validator::make($request->post(),
                                     [
                                         'type'       => 'required|integer|min:1',
                                         'category'   => 'required|integer|min:1',
                                         'date'       => 'required|date',
                                         'startTime'  => 'required|date_format:H:i',
                                         'endTime'    => 'required|date_format:H:i',
                                         'salutation' => 'required|string',
                                         'phone'      => 'required|size:11',
                                         'petUid'     => 'sometimes|nullable|array',
                                         'doctorUid'  => 'required|string',
                                         'remark'     => 'sometimes|nullable|string',
                                     ],
                                     [],
                                     [
                                         'type'       => '预约类型',
                                         'category'   => '预约分类',
                                         'date'       => '预约日期',
                                         'startTime'  => '开始时间',
                                         'endTime'    => '结束时间',
                                         'salutation' => '预约人称呼',
                                         'phone'      => '手机号码',
                                         'petUid'     => '宠物UID',
                                         'doctorUid'  => '医生UID',
                                         'remark'     => '备注',
                                     ]);

        if ($validator->fails())
        {
            return outputJsonError(400,
                                   $validator->errors()
                                             ->first(),
                                   [
                                       'validatorError' => $validator->errors()
                                                                     ->all()
                                   ]);
        }

        $validatedData     = $validator->validated();
        $addAppointmentRes = AppointmentLogic::AddAppointment($validatedData, getRequestReservedParameters());
        if ($addAppointmentRes->isFail())
        {
            return outputJsonError($addAppointmentRes->getCode(), $addAppointmentRes->getMessage(), ['error' => $addAppointmentRes->getMessage()]);
        }

        return outputJsonResult($addAppointmentRes->getData());
    }

    /**
     * 编辑预约
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws Throwable
     */
    public function Edit(Request $request): JsonResponse
    {
        $validator = Validator::make($request->post(),
                                     [
                                         'appointmentCode' => 'required|string',
                                         'type'            => 'required|integer|min:1',
                                         'category'        => 'required|integer|min:1',
                                         'date'            => 'required|date',
                                         'startTime'       => 'required|date_format:H:i',
                                         'endTime'         => 'required|date_format:H:i',
                                         'salutation'      => 'required|string',
                                         'phone'           => 'required|size:11',
                                         'petUid'          => 'sometimes|nullable|array',
                                         'doctorUid'       => 'required|string',
                                         'remark'          => 'sometimes|nullable|string',
                                     ],
                                     [],
                                     [
                                         'appointmentId' => '预约ID',
                                         'type'          => '预约类型',
                                         'category'      => '预约分类',
                                         'date'          => '预约日期',
                                         'startTime'     => '开始时间',
                                         'endTime'       => '结束时间',
                                         'salutation'    => '预约人称呼',
                                         'phone'         => '手机号码',
                                         'petUid'        => '宠物UID',
                                         'doctorUid'     => '医生UID',
                                         'remark'        => '备注',
                                     ]);

        if ($validator->fails())
        {
            return outputJsonError(400,
                                   $validator->errors()
                                             ->first(),
                                   [
                                       'validatorError' => $validator->errors()
                                                                     ->all()
                                   ]);
        }

        $validatedData   = $validator->validated();
        $appointmentCode = $validatedData['appointmentCode'];

        $getAppointmentRes = AppointmentModel::getData(where: ['appointment_code' => $appointmentCode]);
        $getAppointmentRes = Arr::first($getAppointmentRes);
        if (empty($getAppointmentRes))
        {
            return outputJsonError(34000, '预约不存在', ['error' => '预约不存在']);
        }

        $editAppointmentRes = AppointmentLogic::EditAppointment($getAppointmentRes['id'], $validatedData, getRequestReservedParameters());
        if ($editAppointmentRes->isFail())
        {
            return outputJsonError($editAppointmentRes->getCode(), $editAppointmentRes->getMessage(), ['error' => $editAppointmentRes->getMessage()]);
        }

        return outputJsonResult($editAppointmentRes->getData());
    }

    /**
     * 获取预约详情
     *
     * @param Request $request
     *
     * @return JsonResponse
     */
    public function Detail(Request $request): JsonResponse
    {
        $appointmentCode = $request->post('appointmentCode');
        if (empty($appointmentCode))
        {
            return outputJsonError(400, '预约单号不能为空', ['error' => '预约单号不能为空']);
        }

        // 获取预约信息
        $getAppointmentRes = AppointmentModel::getData(where: ['appointment_code' => $appointmentCode]);
        $getAppointmentRes = Arr::first($getAppointmentRes);
        if (empty($getAppointmentRes))
        {
            return outputJsonError(34000, '预约不存在', ['error' => '预约不存在']);
        }

        $getAppointmentDetailRes = AppointmentLogic::GetAppointmentDetail($getAppointmentRes['id'], getRequestReservedParameters());
        if ($getAppointmentDetailRes->isFail())
        {
            return outputJsonError($getAppointmentDetailRes->getCode(), $getAppointmentDetailRes->getMessage(), ['error' => $getAppointmentDetailRes->getMessage()]);
        }

        return outputJsonResult($getAppointmentDetailRes->getData());
    }

    /**
     * 取消预约
     *
     * @param Request $request
     *
     * @return JsonResponse
     */
    public function Cancel(Request $request): JsonResponse
    {
        $appointmentCode = $request->post('appointmentCode');
        $cancelReason    = $request->post('reason');
        if (empty($appointmentCode))
        {
            return outputJsonError(400, '预约单号不能为空', ['error' => '预约单号不能为空']);
        }
        if (empty($cancelReason))
        {
            return outputJsonError(400, '取消原因不能为空', ['error' => '取消原因不能为空']);
        }

        // 获取预约信息
        $getAppointmentRes = AppointmentModel::getData(where: ['appointment_code' => $appointmentCode]);
        $getAppointmentRes = Arr::first($getAppointmentRes);
        if (empty($getAppointmentRes))
        {
            return outputJsonError(34000, '预约不存在', ['error' => '预约不存在']);
        }

        $cancelAppointmentRes = AppointmentLogic::CancelAppointment($getAppointmentRes['id'], $cancelReason, getRequestReservedParameters());
        if ($cancelAppointmentRes->isFail())
        {
            return outputJsonError($cancelAppointmentRes->getCode(), $cancelAppointmentRes->getMessage(), ['error' => $cancelAppointmentRes->getMessage()]);
        }

        return outputJsonResult();
    }
}
