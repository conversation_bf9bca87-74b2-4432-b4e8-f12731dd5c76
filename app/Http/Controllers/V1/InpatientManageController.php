<?php

namespace App\Http\Controllers\V1;

use Validator;
use Throwable;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use App\Http\Controllers\Controller;
use App\Logics\V1\RoomLogic;
use App\Logics\V1\BedLogic;
use App\Models\RoomsModel;
use App\Models\RoomsBedsModel;

/**
 * 住院管理相关
 * Class InpatientManageController
 * @package      App\Http\Controllers\V1
 * @noinspection PhpUnused
 */
class InpatientManageController extends Controller
{
    /**
     * 住院住院部管理
     * @return JsonResponse
     */
    public function GetRoomList(): JsonResponse
    {
        $getRoomManageListRes = RoomLogic::GetRoomManageList(getRequestReservedParameters());
        if ($getRoomManageListRes->isFail())
        {
            return outputJsonError($getRoomManageListRes->getCode(),
                                   $getRoomManageListRes->getMessage(),
                                   ['error' => $getRoomManageListRes->getMessage()]);
        }

        return outputJsonResult($getRoomManageListRes->getData());
    }

    /**
     * 新增住院部
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws Throwable
     */
    public function AddRoom(Request $request): JsonResponse
    {
        $validator = Validator::make($request->post(),
                                     [
                                         'name' => ['required', 'string', 'max:50'],
                                     ],
                                     [],
                                     [
                                         'name' => '住院部名称',
                                     ]);
        if ($validator->fails())
        {
            return outputJsonError(400,
                                   $validator->errors()
                                             ->first(),
                                   [
                                       'validatorError' => $validator->errors()
                                                                     ->all()
                                   ]);
        }

        // 新增住院部
        $addRoomParams = $validator->validated();
        $getAddRoomRes = RoomLogic::AddRoom($addRoomParams, getRequestReservedParameters());
        if ($getAddRoomRes->isFail())
        {
            return outputJsonError($getAddRoomRes->getCode(),
                                   $getAddRoomRes->getMessage(),
                                   ['error' => $getAddRoomRes->getMessage()]);
        }

        return outputJsonResult($getAddRoomRes->getData());
    }

    /**
     * 编辑住院部
     *
     * @param Request $request
     *
     * @return JsonResponse
     */
    public function EditRoom(Request $request)
    {
        $validator = Validator::make($request->post(),
                                     [
                                         'roomUid' => ['required', 'string', 'between:8,64'],
                                         'name'    => ['required', 'string'],
                                     ],
                                     [],
                                     [
                                         'roomUid' => '住院部UID',
                                         'name'    => '住院部名称',
                                     ]);
        if ($validator->fails())
        {
            return outputJsonError(400,
                                   $validator->errors()
                                             ->first(),
                                   [
                                       'validatorError' => $validator->errors()
                                                                     ->all()
                                   ]);
        }

        // 获取住院部信息
        $roomUid    = $validator->validated()['roomUid'];
        $getRoomRes = RoomsModel::getOneByUid($roomUid);
        if (empty($getRoomRes))
        {
            return outputJsonError(37000, '住院部不存在', ['error' => '住院部不存在']);
        }

        // 编辑住院部
        $editRoomParams = $validator->validated();
        $getEditRoomRes = RoomLogic::EditRoom($getRoomRes['id'],
                                              $editRoomParams,
                                              getRequestReservedParameters());
        if ($getEditRoomRes->isFail())
        {
            return outputJsonError($getEditRoomRes->getCode(),
                                   $getEditRoomRes->getMessage(),
                                   ['error' => $getEditRoomRes->getMessage()]);
        }

        return outputJsonResult();
    }

    /**
     * 删除住院部
     *
     * @param Request $request
     *
     * @return JsonResponse
     */
    public function DeleteRoom(Request $request)
    {
        $validator = Validator::make($request->post(),
                                     [
                                         'roomUid' => ['required', 'string', 'between:8,64'],
                                     ],
                                     [],
                                     [
                                         'roomUid' => '住院部UID',
                                     ]);
        if ($validator->fails())
        {
            return outputJsonError(400,
                                   $validator->errors()
                                             ->first(),
                                   [
                                       'validatorError' => $validator->errors()
                                                                     ->all()
                                   ]);
        }

        // 获取住院部信息
        $roomUid    = $validator->validated()['roomUid'];
        $getRoomRes = RoomsModel::getOneByUid($roomUid);
        if (empty($getRoomRes))
        {
            return outputJsonError(37000, '住院部不存在', ['error' => '住院部不存在']);
        }

        // 删除住院部
        $getDeleteRes = RoomLogic::DeleteRoom($getRoomRes['id'], getRequestReservedParameters());
        if ($getDeleteRes->isFail())
        {
            return outputJsonError($getDeleteRes->getCode(), $getDeleteRes->getMessage(), ['error' => $getDeleteRes->getMessage()]);
        }

        return outputJsonResult();
    }

    /**
     * 新增床位
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws Throwable
     */
    public function AddBed(Request $request): JsonResponse
    {
        $validator = Validator::make($request->post(),
                                     [
                                         'roomUid' => ['required', 'string', 'between:8,64'],
                                     ],
                                     [],
                                     [
                                         'roomUid' => '住院部UID',
                                     ]);
        if ($validator->fails())
        {
            return outputJsonError(400,
                                   $validator->errors()
                                             ->first(),
                                   [
                                       'validatorError' => $validator->errors()
                                                                     ->all()
                                   ]);
        }

        // 获取住院部信息
        $roomUid    = $validator->validated()['roomUid'];
        $getRoomRes = RoomsModel::getOneByUid($roomUid);
        if (empty($getRoomRes))
        {
            return outputJsonError(37000, '住院部不存在', ['error' => '住院部不存在']);
        }

        // 新增床位
        $getAddBedRes = BedLogic::AddBed($getRoomRes['id'], getRequestReservedParameters());
        if ($getAddBedRes->isFail())
        {
            return outputJsonError($getAddBedRes->getCode(),
                                   $getAddBedRes->getMessage(),
                                   ['error' => $getAddBedRes->getMessage()]);
        }

        return outputJsonResult($getAddBedRes->getData());
    }

    /**
     * 禁用床位
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws Throwable
     */
    public function DisableBed(Request $request): JsonResponse
    {
        $validator = Validator::make($request->post(),
                                     [
                                         'bedUid' => ['required', 'string', 'between:8,64'],
                                     ],
                                     [],
                                     [
                                         'bedUid' => '床位UID',
                                     ]);
        if ($validator->fails())
        {
            return outputJsonError(400,
                                   $validator->errors()
                                             ->first(),
                                   [
                                       'validatorError' => $validator->errors()
                                                                     ->all()
                                   ]);
        }

        // 获取床位信息
        $bedUid    = $validator->validated()['bedUid'];
        $getBedRes = RoomsBedsModel::getOneByUid($bedUid);
        if (empty($getBedRes))
        {
            return outputJsonError(37001, '床位不存在', ['error' => '床位不存在']);
        }

        // 禁用床位
        $getDisableBedRes = BedLogic::DisableBed($getBedRes['id'], getRequestReservedParameters());
        if ($getDisableBedRes->isFail())
        {
            return outputJsonError($getDisableBedRes->getCode(),
                                   $getDisableBedRes->getMessage(),
                                   ['error' => $getDisableBedRes->getMessage()]);
        }

        return outputJsonResult();
    }

    /**
     * 启用床位
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws Throwable
     */
    public function EnableBed(Request $request): JsonResponse
    {
        $validator = Validator::make($request->post(),
                                     [
                                         'bedUid' => ['required', 'string', 'between:8,64'],
                                     ],
                                     [],
                                     [
                                         'bedUid' => '床位UID',
                                     ]);
        if ($validator->fails())
        {
            return outputJsonError(400,
                                   $validator->errors()
                                             ->first(),
                                   [
                                       'validatorError' => $validator->errors()
                                                                     ->all()
                                   ]);
        }

        // 获取床位信息
        $bedUid    = $validator->validated()['bedUid'];
        $getBedRes = RoomsBedsModel::getOneByUid($bedUid);
        if (empty($getBedRes))
        {
            return outputJsonError(37001, '床位不存在', ['error' => '床位不存在']);
        }

        // 启用床位
        $getEnableBedRes = BedLogic::EnableBed($getBedRes['id'], getRequestReservedParameters());
        if ($getEnableBedRes->isFail())
        {
            return outputJsonError($getEnableBedRes->getCode(),
                                   $getEnableBedRes->getMessage(),
                                   ['error' => $getEnableBedRes->getMessage()]);
        }

        return outputJsonResult();
    }
}
