<?php

namespace App\Http\Controllers\V1;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Validation\ValidationException;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rules\Enum;
use App\Enums\CloudPrinterLabelTypeEnum;
use App\Enums\CloudPrinterReceiptTypeEnum;
use App\Logics\V1\CloudPrinterLogic;

class SettingPrinterController extends Controller
{
    /**
     * 医院云打印机新增
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws ValidationException
     */
    public function Add(Request $request): JsonResponse
    {
        //验证必传参数
        $validator = Validator::make(
            $request->post(),
            [
                'deviceId'   => 'required|string|between:10,32',
                'deviceName' => 'required|string|between:2,20',
            ],
        );

        if ($validator->fails())
        {
            return outputJsonError(400,
                                   $validator->errors()
                                             ->first(),
                                   [
                                       'validatorError' => $validator->errors()
                                                                     ->all()
                                   ]);
        }

        $params       = $validator->validated();
        $publicParams = getRequestReservedParameters();
        $res          = CloudPrinterLogic::AddHospitalPrinter($params, $publicParams);
        if ($res->isFail())
        {
            return outputJsonError($res->getCode(), '', ['error' => $res->getMessage()]);
        }

        return outputJsonResult();
    }

    /**
     * 医院云打印机编辑
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws ValidationException
     */
    public function Edit(Request $request): JsonResponse
    {
        //验证必传参数
        $validator = Validator::make(
            $request->post(),
            [
                'deviceId'   => 'required|string|between:10,32',
                'deviceName' => 'required|string|between:2,20',
            ],
        );

        if ($validator->fails())
        {
            return outputJsonError(400,
                                   $validator->errors()
                                             ->first(),
                                   [
                                       'validatorError' => $validator->errors()
                                                                     ->all()
                                   ]);
        }

        $params       = $validator->validated();
        $publicParams = getRequestReservedParameters();
        $res          = CloudPrinterLogic::EditHospitalPrinter($params, $publicParams);
        if ($res->isFail())
        {
            return outputJsonError($res->getCode(), '', ['error' => $res->getMessage()]);
        }

        return outputJsonResult();
    }

    /**
     * 医院云打印机列表
     *
     * @return JsonResponse
     */
    public function List(): JsonResponse
    {
        $publicParams           = getRequestReservedParameters();
        $hospitalPrinterListRes = CloudPrinterLogic::HospitalPrinterList($publicParams);
        if ($hospitalPrinterListRes->isFail())
        {
            return outputJsonError($hospitalPrinterListRes->getCode(),
                                   '',
                                   ['error' => $hospitalPrinterListRes->getMessage()]);
        }

        return outputJsonResult($hospitalPrinterListRes->getData());
    }

    /**
     * 刷新打印机状态
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws ValidationException
     */
    public function Refresh(Request $request): JsonResponse
    {
        //验证必传参数
        $validator = Validator::make(
            $request->post(),
            [
                'deviceId' => 'required|string|between:10,32',
            ],
        );

        if ($validator->fails())
        {
            return outputJsonError(400,
                                   $validator->errors()
                                             ->first(),
                                   [
                                       'validatorError' => $validator->errors()
                                                                     ->all()
                                   ]);
        }

        $params       = $validator->validated();
        $publicParams = getRequestReservedParameters();
        $res          = CloudPrinterLogic::RefreshPrinterStatus($params, $publicParams);
        if ($res->isFail())
        {
            return outputJsonError($res->getCode(), '', ['error' => $res->getMessage()]);
        }

        return outputJsonResult();
    }

    /**
     * 打印标签
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws ValidationException
     */
    public function PrintLabel(Request $request): JsonResponse
    {
        //验证必传参数
        $validator = Validator::make(
            $request->post(),
            [
                'deviceId' => 'required|string|between:10,32',
                'copies'   => 'sometimes|nullable|integer|min:1|max:10',
                'type'     => ['required', 'string', new Enum(CloudPrinterLabelTypeEnum::class)],
                'content'  => 'required|array',
            ],
            [],
            [
                'deviceId' => '打印机设备ID',
                'copies'   => '打印份数',
                'type'     => '标签类型',
                'content'  => '打印内容',
            ]
        );

        if ($validator->fails())
        {
            return outputJsonError(400,
                                   $validator->errors()
                                             ->first(),
                                   [
                                       'validatorError' => $validator->errors()
                                                                     ->all()
                                   ]);
        }

        $params       = $validator->validated();
        $publicParams = getRequestReservedParameters();
        $res          = CloudPrinterLogic::PrintLabel($params, $publicParams);
        if ($res->isFail())
        {
            return outputJsonError($res->getCode(), '', ['error' => $res->getMessage()]);
        }

        return outputJsonResult();
    }

    /**
     * 打印小票
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws ValidationException|\Exception
     */
    public function PrintReceipt(Request $request): JsonResponse
    {
        //验证必传参数
        $validator = Validator::make(
            $request->post(),
            [
                'deviceId'  => 'required|string|between:10,32',
                'copies'    => 'sometimes|nullable|integer|min:1|max:2',
                'type'      => ['required', 'string', new Enum(CloudPrinterReceiptTypeEnum::class)],
                'content'   => 'required|array',
                'isReprint' => 'sometimes|nullable|boolean',
            ],
            [],
            [
                'deviceId'  => '打印机设备ID',
                'copies'    => '打印份数',
                'type'      => '小票类型',
                'content'   => '打印内容',
                'isReprint' => '是否重打',
            ]
        );

        if ($validator->fails())
        {
            return outputJsonError(400,
                                   $validator->errors()
                                             ->first(),
                                   [
                                       'validatorError' => $validator->errors()
                                                                     ->all()
                                   ]);
        }

        $params       = $validator->validated();
        $publicParams = getRequestReservedParameters();
        $res          = CloudPrinterLogic::PrintReceipt($params, $publicParams);
        if ($res->isFail())
        {
            return outputJsonError($res->getCode(), '', ['error' => $res->getMessage()]);
        }

        return outputJsonResult();
    }
}
