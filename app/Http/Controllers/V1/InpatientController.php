<?php

namespace App\Http\Controllers\V1;

use Throwable;
use Validator;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\ValidationException;
use App\Http\Controllers\Controller;
use App\Logics\V1\InpatientLogic;
use App\Models\InpatientModel;
use App\Models\OutpatientModel;

/**
 * 住院相关
 * Class InpatientController
 * @package      App\Http\Controllers\V1
 * @noinspection PhpUnused
 */
class InpatientController extends Controller
{

    /**
     * 住院-办理住院
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws ValidationException
     * @throws Throwable
     * @noinspection PhpUnused
     */
    public function Admit(Request $request): JsonResponse
    {
        // 验证参数有效性
        $validate = Validator::make($request->post(),
                                    [
                                        'outpatientCode' => ['required', 'string'],
                                        'gradeId'        => ['required', 'integer', 'min:1'],
                                        'inpatientDays'  => ['required', 'integer', 'min:1'],
                                        'roomId'         => ['required', 'integer', 'min:1'],
                                        'bedId'          => ['required', 'integer', 'min:1'],
                                    ],
                                    [],
                                    [
                                        'outpatientCode' => '住院编码',
                                        'gradeId'        => '住院等级',
                                        'inpatientDays'  => '住院天数',
                                        'roomId'         => '病房ID',
                                        'bedId'          => '床位ID',
                                    ]);

        if ($validate->fails())
        {
            return outputJsonError(400,
                                   $validate->errors()
                                            ->first(),
                                   [
                                       'validatorError' => $validate->errors()
                                                                    ->all()
                                   ]);
        }

        $publicParams   = getRequestReservedParameters();
        $outpatientCode = $validate->validated()['outpatientCode'];
        $gradeId        = $validate->validated()['gradeId'];
        $inpatientDays  = $validate->validated()['inpatientDays'];
        $roomId         = $validate->validated()['roomId'];
        $bedId          = $validate->validated()['bedId'];

        // 获取门诊数据
        $getOutpatientRes = OutpatientModel::getOutpatientByCode($outpatientCode);
        if (empty($getOutpatientRes))
        {
            return outputJsonError(36000, '门诊不存在', ['error' => '门诊不存在']);
        }

        $inpatientParams = [
            'gradeId' => $gradeId,
            'days'    => $inpatientDays,
            'roomId'  => $roomId,
            'bedId'   => $bedId
        ];
        $admitPatientRes = InpatientLogic::AdmitPatient($getOutpatientRes['id'], $inpatientParams, $publicParams);
        if ($admitPatientRes->isFail())
        {
            return outputJsonError($admitPatientRes->getCode(),
                                   $admitPatientRes->getMessage(),
                                   ['error' => $admitPatientRes->getMessage()]);
        }

        return outputJsonResult();
    }

    /**
     * 编辑住院
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws Throwable
     */
    public function Edit(Request $request)
    {
        $validate = Validator::make($request->post(),
                                    [
                                        'inpatientCode'         => ['required', 'string'],
                                        'gradeId'               => ['sometimes', 'nullable', 'int'],
                                        'expectedDischargeDate' => ['sometimes', 'nullable', 'date'],
                                        'roomId'                => ['sometimes', 'nullable', 'int'],
                                        'bedId'                 => ['sometimes', 'nullable', 'int'],
                                    ],
                                    [],
                                    [
                                        'inpatientCode'         => '住院编码',
                                        'gradeId'               => '护理级别',
                                        'expectedDischargeDate' => '预计出院日期',
                                        'roomId'                => '住院笼位',
                                        'bedId'                 => '住院笼位',
                                    ]);
        if ($validate->fails())
        {
            return outputJsonError(400,
                                   $validate->errors()
                                            ->first(),
                                   [
                                       'validatorError' => $validate->errors()
                                                                    ->all()
                                   ]);
        }

        $publicParams        = getRequestReservedParameters();
        $editInpatientParams = $validate->validated();
        $inpatientCode       = $editInpatientParams['inpatientCode'];

        // 获取住院数据
        $getInpatientRes = InpatientModel::getInpatientByCode($inpatientCode);
        if (empty($getInpatientRes))
        {
            return outputJsonError(36000, '门诊不存在', ['error' => '门诊不存在']);
        }

        $getEditInpatientRes = InpatientLogic::EditInpatient($getInpatientRes['id'], $editInpatientParams, $publicParams);
        if ($getEditInpatientRes->isFail())
        {
            return outputJsonError($getEditInpatientRes->getCode(), $getEditInpatientRes->getMessage(), ['error' => $getEditInpatientRes->getMessage()]);
        }

        return outputJsonResult();
    }

    /**
     * 获取住院列表
     *
     * @return JsonResponse
     * @noinspection PhpUnused
     */
    public function Lists(): JsonResponse
    {
        $getInpatientListRes = InpatientLogic::GetInpatientList(getPublicParamsHospitalId(),
                                                                getPublicParamsHospitalUserId());
        if ($getInpatientListRes->isFail())
        {
            return outputJsonError($getInpatientListRes->getCode(),
                                   $getInpatientListRes->getMessage(),
                                   ['error' => $getInpatientListRes->getMessage()]);
        }

        return outputJsonResult($getInpatientListRes->getData());
    }

    /**
     * 获取住院详情
     *
     * @param Request $request
     *
     * @return JsonResponse
     */
    public function Detail(Request $request): JsonResponse
    {
        $inpatientCode = $request->post('inpatientCode');
        if (empty($inpatientCode))
        {
            return outputJsonError(400, '住院编码不能为空', ['error' => '住院编码不能为空']);
        }

        // 获取住院数据
        $getInpatientRes = InpatientModel::getInpatientByCode($inpatientCode);
        if (empty($getInpatientRes))
        {
            return outputJsonError(37004, '住院不存在', ['error' => '住院不存在']);
        }

        $getInpatientDetailRes = InpatientLogic::GetInpatientDetail($getInpatientRes['id'], getRequestReservedParameters());
        if ($getInpatientDetailRes->isFail())
        {
            return outputJsonError($getInpatientDetailRes->getCode(), $getInpatientDetailRes->getMessage(), ['error' => $getInpatientDetailRes->getMessage()]);
        }

        return outputJsonResult($getInpatientDetailRes->getData());
    }

    /**
     * 住院状态变更
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws ValidationException
     * @throws Throwable
     * @noinspection PhpUnused
     */
    public function ChangeStatus(Request $request): JsonResponse
    {
        // 验证参数有效性
        $validate = Validator::make($request->post(),
                                    [
                                        'inpatientCode' => ['required', 'string'],
                                        'status'        => ['required', 'int', 'between:1,4'],
                                        'endInpatient'  => ['required_if:status,4', 'array'],
                                    ],
                                    [],
                                    [
                                        'inpatientCode' => '住院编码',
                                        'status'        => '门诊状态',
                                        'endInpatient'  => '出院结果'
                                    ]);

        if ($validate->fails())
        {
            return outputJsonError(400,
                                   $validate->errors()
                                            ->first(),
                                   [
                                       'validatorError' => $validate->errors()
                                                                    ->all()
                                   ]);
        }

        $publicParams       = getRequestReservedParameters();
        $inpatientCode      = $validate->validated()['inpatientCode'];
        $inpatientStatus    = (int) $validate->validated()['status'];
        $endInpatientParams = $request->has('endInpatient') ? $request->post('endInpatient') : [];

        // 获取住院数据
        $getInpatientRes = InpatientModel::getInpatientByCode($inpatientCode);
        if (empty($getInpatientRes))
        {
            return outputJsonError(37004, '住院不存在', ['error' => '住院不存在']);
        }

        match ($inpatientStatus)
        {
            // 开始诊断
            1 => $dealRes = InpatientLogic::StartInpatient($getInpatientRes['id'], $publicParams),

            // 暂停诊断
            2 => $dealRes = InpatientLogic::SuspendOutpatient($getInpatientRes['id'], $publicParams),

            // 结束诊断
            4 => $dealRes = InpatientLogic::EndInpatient($getInpatientRes['id'], $endInpatientParams, $publicParams),
        };

        if ($dealRes->isFail())
        {
            return outputJsonError($dealRes->getCode(),
                                   $dealRes->getMessage(),
                                   ['error' => $dealRes->getMessage()]);
        }

        return outputJsonResult();
    }

    /**
     * 获取医生正在诊断的住院
     *
     * @return JsonResponse
     *
     * @noinspection PhpUnused
     */
    public function GetDoctorInTreatmentInpatient(): JsonResponse
    {
        $hospitalId = getPublicParamsHospitalId();
        $doctorId   = getPublicParamsHospitalUserId();

        $getDoctorInTreatmentInpatientRes = InpatientLogic::GetDoctorInTreatmentInpatient($hospitalId, $doctorId);
        if ($getDoctorInTreatmentInpatientRes->isFail())
        {
            return outputJsonError($getDoctorInTreatmentInpatientRes->getCode(),
                                   $getDoctorInTreatmentInpatientRes->getMessage(),
                                   ['error' => $getDoctorInTreatmentInpatientRes->getMessage()]);
        }

        return outputJsonResult($getDoctorInTreatmentInpatientRes->getData());
    }

    /**
     * 检查是否可以结束诊断
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws ValidationException
     * @noinspection PhpUnused
     */
    public function CheckEndInpatient(Request $request): JsonResponse
    {
        $validate = Validator::make($request->post(),
                                    [
                                        'inpatientCode' => ['required', 'string'],
                                    ],
                                    [],
                                    [
                                        'inpatientCode' => '住院编码',
                                    ]);

        if ($validate->fails())
        {
            return outputJsonError(400,
                                   $validate->errors()
                                            ->first(),
                                   [
                                       'validatorError' => $validate->errors()
                                                                    ->all()
                                   ]);
        }

        $publicParams  = getRequestReservedParameters();
        $inpatientCode = $validate->validated()['inpatientCode'];

        // 获取住院数据
        $getInpatientRes = InpatientModel::getInpatientByCode($inpatientCode);
        if (empty($getInpatientRes))
        {
            return outputJsonError(37006, '住院不存在', ['error' => '住院不存在']);
        }

        $checkRes = InpatientLogic::CheckEditOrEndInpatient($getInpatientRes['id'], $publicParams);
        if ($checkRes->isFail())
        {
            return outputJsonError($checkRes->getCode(),
                                   $checkRes->getMessage(),
                                   ['error' => $checkRes->getMessage()]);
        }

        return outputJsonResult();
    }
}
