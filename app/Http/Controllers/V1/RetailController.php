<?php

namespace App\Http\Controllers\V1;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Validation\ValidationException;
use Illuminate\Support\Facades\Validator;
use App\Logics\V1\RetailLogic;
use App\Logics\V1\BuySheet\RetailSheetLogic;
use App\Logics\V1\MemberLogic;
use App\Logics\V1\UserLogic;
use App\Support\Retail\RetailSheetHelper;

class RetailController extends Controller
{
    /**
     * 搜索零售商品
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws ValidationException
     */
    public function SearchRetailItems(Request $request): JsonResponse
    {
        $validator = Validator::make($request->post(),
                                     [
                                         'keyword' => ['required', 'string'],
                                     ],
                                     [],
                                     [
                                         'keyword' => '搜索关键词',
                                     ]
        );

        if ($validator->fails())
        {
            return outputJsonError(400,
                                   $validator->errors()
                                             ->first(),
                                   [
                                       'validatorError' => $validator->errors()
                                                                     ->all()
                                   ]);
        }

        $searchParams = $validator->validated();
        $publicParams = getRequestReservedParameters();

        $getSearchItemsRes = RetailLogic::GetSearchItems($searchParams, $publicParams);
        if ($getSearchItemsRes->isFail())
        {
            return outputJsonError($getSearchItemsRes->getCode(),
                                   $getSearchItemsRes->getMessage(),
                                   ['error' => $getSearchItemsRes->getMessage()]);
        }

        return outputJsonResult($getSearchItemsRes->getData());
    }

    /**
     * 创建零售商品购买单
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws ValidationException
     */
    public function CreateSheet(Request $request): JsonResponse
    {
        //验证必传参数
        $validator = Validator::make(
            $request->post(),
            [
                'memberUid'  => 'sometimes|nullable|string|between:8,64',
                'sellerUid'  => 'sometimes|nullable|string|between:8,64',
                'items'      => 'required|array',
                'totalPrice' => 'required|numeric|min:0',
            ],
            [],
            [
                'memberUid'  => '会员UID',
                'sellerUid'  => '销售员UID',
                'items'      => '零售商品明细',
                'totalPrice' => '总金额',
            ]
        );

        if ($validator->fails())
        {
            return outputJsonError(400,
                                   $validator->errors()
                                             ->first(),
                                   [
                                       'validatorError' => $validator->errors()
                                                                     ->all()
                                   ]);
        }

        $params       = $validator->validated();
        $publicParams = getRequestReservedParameters();

        // 验证UID并转换ID
        $memberUid = $params['memberUid'];
        $sellerUid = $params['sellerUid'];
        // 兼容匿名购买
        if (!empty($memberUid))
        {
            $getMemberRes = MemberLogic::GetValidMemberByIdOrUid(memberUid: $memberUid);
            if ($getMemberRes->isFail())
            {
                return outputJsonError($getMemberRes->getCode(), '', ['error' => $getMemberRes->getMessage()]);
            }

            $params['memberId'] = $getMemberRes->getData('id');
        }

        $userRes = UserLogic::GetValidUserByIdOrUid(userUid: $sellerUid);
        if ($userRes->isFail())
        {
            return outputJsonError($userRes->getCode(), '', ['error' => $userRes->getMessage()]);
        }
        $params['sellerId'] = $userRes->getData('id');

        $createResult = RetailSheetLogic::CreateSheet($params, $publicParams);
        if ($createResult->isFail())
        {
            return outputJsonError($createResult->getCode(),
                                   $createResult->getMessage(),
                                   ['error' => $createResult->getMessage()]);
        }

        return outputJsonResult($createResult->getData());
    }

    /**
     * 编辑零售商品购买单
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws ValidationException
     */
    public function EditSheet(Request $request): JsonResponse
    {
        //验证必传参数
        $validator = Validator::make(
            $request->post(),
            [
                'sheetCode'  => 'required|string|between:8,32',
                'sellerUid'  => 'sometimes|nullable|string|between:8,64',
                'items'      => 'required|array',
                'totalPrice' => 'required|numeric|min:0',
            ],
            [],
            [
                'sheetCode'  => '零售商品购买单编码',
                'sellerUid'  => '销售员UID',
                'items'      => '零售商品明细',
                'totalPrice' => '总金额',
            ]
        );

        if ($validator->fails())
        {
            return outputJsonError(400,
                                   $validator->errors()
                                             ->first(),
                                   [
                                       'validatorError' => $validator->errors()
                                                                     ->all()
                                   ]);
        }

        $params       = $validator->validated();
        $publicParams = getRequestReservedParameters();
        $sheetCode    = trim($params['sheetCode']);
        $sellerUid    = $params['sellerUid'];

        $sheetRes = RetailSheetHelper::GetValidSheet($sheetCode);
        if ($sheetRes->isFail())
        {
            return outputJsonError($sheetRes->getCode(), '', ['error' => $sheetRes->getMessage()]);
        }
        $params['sheetId'] = $sheetRes->getData('id');

        $userRes = UserLogic::GetValidUserByIdOrUid(userUid: $sellerUid);
        if ($userRes->isFail())
        {
            return outputJsonError($userRes->getCode(), '', ['error' => $userRes->getMessage()]);
        }
        $params['sellerId'] = $userRes->getData('id');

        $editResult = RetailSheetLogic::EditSheet($params, $publicParams);
        if ($editResult->isFail())
        {
            return outputJsonError($editResult->getCode(),
                                   $editResult->getMessage(),
                                   ['error' => $editResult->getMessage()]);
        }

        return outputJsonResult($editResult->getData());
    }

    /**
     * 删除零售商品购买单
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws ValidationException
     */
    public function DeleteSheet(Request $request): JsonResponse
    {
        //验证必传参数
        $validator = Validator::make(
            $request->post(),
            [
                'sheetCode' => 'required|string|between:8,32',
            ],
            [],
            [
                'sheetCode' => '零售商品购买单编码',
            ]
        );

        if ($validator->fails())
        {
            return outputJsonError(400,
                                   $validator->errors()
                                             ->first(),
                                   [
                                       'validatorError' => $validator->errors()
                                                                     ->all()
                                   ]);
        }

        $params       = $validator->validated();
        $publicParams = getRequestReservedParameters();
        $sheetCode    = trim($params['sheetCode']);

        $sheetRes = RetailSheetHelper::GetValidSheet($sheetCode);
        if ($sheetRes->isFail())
        {
            return outputJsonError($sheetRes->getCode(), '', ['error' => $sheetRes->getMessage()]);
        }
        $params['sheetId'] = $sheetRes->getData('id');

        $deleteResult = RetailSheetLogic::DeleteSheet($params, $publicParams);
        if ($deleteResult->isFail())
        {
            return outputJsonError($deleteResult->getCode(),
                                   $deleteResult->getMessage(),
                                   ['error' => $deleteResult->getMessage()]);
        }

        return outputJsonResult($deleteResult->getData());
    }

    /**
     * 零售商品购买单详情
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws ValidationException
     */
    public function SheetDetail(Request $request): JsonResponse
    {
        //验证必传参数
        $validator = Validator::make(
            $request->post(),
            [
                'sheetCode' => 'required|string|between:8,32',
            ],
            [],
            [
                'sheetCode' => '零售商品购买单编码',
            ]
        );

        if ($validator->fails())
        {
            return outputJsonError(400,
                                   $validator->errors()
                                             ->first(),
                                   [
                                       'validatorError' => $validator->errors()
                                                                     ->all()
                                   ]);
        }

        $params       = $validator->validated();
        $publicParams = getRequestReservedParameters();
        $sheetCode    = trim($params['sheetCode']);

        $sheetRes = RetailSheetHelper::GetValidSheet($sheetCode);
        if ($sheetRes->isFail())
        {
            return outputJsonError($sheetRes->getCode(), '', ['error' => $sheetRes->getMessage()]);
        }
        $params['sheetId'] = $sheetRes->getData('id');

        $detailResult = RetailSheetLogic::GetSheetDetail($params, $publicParams);
        if ($detailResult->isFail())
        {
            return outputJsonError($detailResult->getCode(),
                                   $detailResult->getMessage(),
                                   ['error' => $detailResult->getMessage()]);
        }

        return outputJsonResult($detailResult->getData());
    }
}
