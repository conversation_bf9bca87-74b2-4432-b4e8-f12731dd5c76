<?php

namespace App\Http\Controllers\V1;

use Validator;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\ValidationException;
use App\Enums\StockPendingOrderStatusEnum;
use App\Http\Controllers\Controller;
use App\Logics\V1\StockPendingLogic;
use App\Models\StockPendingOrderModel;

/**
 * 仓储-待出库单
 * Class StockPendingController
 * @package      App\Http\Controllers\V1
 * @noinspection PhpUnused
 */
class StockPendingController extends Controller
{
    /**
     * 拣货出库单筛选项
     *
     * @return JsonResponse
     *
     * @noinspection PhpUnused
     */
    public function GetPendingOrderFilterOptions(): JsonResponse
    {
        $getPurchaseUserOptionsRes = StockPendingLogic::GetCreateStockPendingUserOptions(getRequestReservedParameters());
        $statusOptions             = StockPendingOrderStatusEnum::caseToOptions();

        return outputJsonResult([
                                    'userOptions'   => $getPurchaseUserOptionsRes->getData(),
                                    'statusOptions' => $statusOptions,
                                ]);
    }

    /**
     * 拣货出库单列表
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws ValidationException
     */
    public function Lists(Request $request): JsonResponse
    {
        $validate = Validator::make($request->post(),
                                    [
                                        'keywords'      => ['sometimes', 'nullable', 'string'],
                                        'orderCode'     => ['sometimes', 'nullable', 'string'],
                                        'startDate'     => ['sometimes', 'nullable', 'date_format:Y-m-d'],
                                        'endDate'       => ['sometimes', 'nullable', 'date_format:Y-m-d'],
                                        'createUserUid' => ['sometimes', 'nullable', 'string'],
                                        'status'        => ['sometimes', 'nullable', 'integer', Rule::in(StockPendingOrderStatusEnum::values())],
                                        'page'          => ['sometimes', 'nullable', 'integer'],
                                        'count'         => ['sometimes', 'nullable', 'integer'],
                                    ],
                                    [],
                                    [
                                        'keywords'      => '搜索关键词',
                                        'orderCode'     => '订单号',
                                        'startDate'     => '开始日期',
                                        'endDate'       => '结束日期',
                                        'createUserUid' => '订单创建人',
                                        'status'        => '出库状态',
                                        'page'          => '页码',
                                        'count'         => '每页条数',
                                    ]);
        if ($validate->fails())
        {
            return outputJsonError(400,
                                   $validate->errors()
                                            ->first(),
                                   [
                                       'validatorError' => $validate->errors()
                                                                    ->all()
                                   ]);
        }

        $searchParams           = $validate->validated();
        $getPendingOrderListRes = StockPendingLogic::GetPendingOrderLists($searchParams, getRequestReservedParameters());
        if ($getPendingOrderListRes->isFail())
        {
            return outputJsonError($getPendingOrderListRes->getCode(),
                                   $getPendingOrderListRes->getMessage(),
                                   ['error' => $getPendingOrderListRes->getMessage()]);
        }

        return outputJsonResult($getPendingOrderListRes->getData());
    }

    /**
     * 拣货出库单详情
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws ValidationException
     */
    public function Detail(Request $request): JsonResponse
    {
        $validate = Validator::make($request->post(),
                                    [
                                        'pendingOrderUid' => ['required', 'string'],
                                    ],
                                    [],
                                    [
                                        'pendingOrderUid' => '拣货出库单UID',
                                    ]);
        if ($validate->fails())
        {
            return outputJsonError(400,
                                   $validate->errors()
                                            ->first(),
                                   [
                                       'validatorError' => $validate->errors()
                                                                    ->all()
                                   ]);
        }

        $pendingOrderUid    = $validate->validated()['pendingOrderUid'];
        $getPendingOrderRes = StockPendingOrderModel::getOneByUid($pendingOrderUid);
        if (empty($getPendingOrderRes))
        {
            return outputJsonError(44000, '拣货出库单不存在', ['error' => '拣货出库单不存在']);
        }

        $getPendingOrderDetailRes = StockPendingLogic::GetPendingOrderDetail($getPendingOrderRes['id'], getRequestReservedParameters());
        if ($getPendingOrderDetailRes->isFail())
        {
            return outputJsonError($getPendingOrderDetailRes->getCode(),
                                   $getPendingOrderDetailRes->getMessage(),
                                   ['error' => $getPendingOrderDetailRes->getMessage()]);
        }

        return outputJsonResult($getPendingOrderDetailRes->getData());
    }
}
