<?php

namespace App\Http\Controllers\V1;

use Throwable;
use Validator;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\ValidationException;
use App\Http\Controllers\Controller;
use App\Enums\PageEnum;
use App\Logics\V1\TestLogic;
use App\Models\TestModel;
use App\Models\UsersModel;
use App\Models\TestsResultsReportModel;
use App\Models\TestsReportTemplatesModel;

/**
 * 化验检测相关
 * Class TestController
 * @package      App\Http\Controllers\V1
 * @noinspection PhpUnused
 */
class TestController extends Controller
{
    /**
     * 化验列表筛选项
     *
     * @return JsonResponse
     * @noinspection PhpUnused
     */
    public function GetTestFilterOptions(): JsonResponse
    {
        $getTestFilterOptionsRes = TestLogic::GetTestFilterOptions(getRequestReservedParameters());
        if ($getTestFilterOptionsRes->isFail())
        {
            return outputJsonError($getTestFilterOptionsRes->getCode(),
                                   $getTestFilterOptionsRes->getMessage(),
                                   ['error' => $getTestFilterOptionsRes->getMessage()]);
        }

        return outputJsonResult($getTestFilterOptionsRes->getData());
    }

    /**
     * 化验列表
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws ValidationException
     */
    public function Lists(Request $request): JsonResponse
    {
        $validator = Validator::make($request->post(),
                                     [
                                         'keywords'     => ['sometimes', 'nullable', 'string'],
                                         'caseCode'     => ['sometimes', 'nullable', 'string'],
                                         'testCode'     => ['sometimes', 'nullable', 'string'],
                                         'name'         => ['sometimes', 'nullable', 'string'],
                                         'startDate'    => ['sometimes', 'nullable', 'date'],
                                         'endDate'      => ['sometimes', 'nullable', 'date'],
                                         'doctorUid'    => ['sometimes', 'nullable', 'string'],
                                         'status'       => ['sometimes', 'nullable', 'integer'],
                                         'payStatus'    => ['sometimes', 'nullable', 'integer'],
                                         'testerStatus' => ['sometimes', 'nullable', 'integer'],
                                         'page'         => ['sometimes', 'nullable', 'integer'],
                                         'count'        => ['sometimes', 'nullable', 'integer'],
                                     ]);
        if ($validator->fails())
        {
            return outputJsonError(400,
                                   $validator->errors()
                                             ->first(),
                                   [
                                       'validatorError' => $validator->errors()
                                                                     ->all()
                                   ]);
        }

        $searchParams = $validator->validated();
        $iPage        = $validator->validated()['page'] ?? PageEnum::DefaultPageIndex->value;
        $iPageSize    = $validator->validated()['count'] ?? PageEnum::DefaultPageSize->value;

        $getTestListRes = TestLogic::GetTestList($searchParams, getRequestReservedParameters(), $iPage, $iPageSize);
        if ($getTestListRes->isFail())
        {
            return outputJsonError($getTestListRes->getCode(),
                                   $getTestListRes->getMessage(),
                                   ['error' => $getTestListRes->getMessage()]);
        }

        return outputJsonResult($getTestListRes->getData());
    }

    /**
     * 化验详情
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws ValidationException
     * @noinspection PhpUnused
     */
    public function Detail(Request $request): JsonResponse
    {
        $validator = Validator::make($request->post(),
                                     [
                                         'testCode' => ['required', 'string'],
                                     ],
                                     [],
                                     ['testCode' => '化验编码']);
        if ($validator->fails())
        {
            return outputJsonError(400,
                                   $validator->errors()
                                             ->first(),
                                   [
                                       'validatorError' => $validator->errors()
                                                                     ->all()
                                   ]);
        }

        // 获取化验信息
        $testCode   = $validator->validated()['testCode'];
        $getTestRes = TestModel::getData(['id'], ['test_code' => $testCode]);
        $getTestRes = $getTestRes ? current($getTestRes) : [];
        if (empty($getTestRes))
        {
            return outputJsonError(40000, '化验不存在', ['error' => '化验不存在']);
        }

        // 获取化验详情
        $getTestDetailRes = TestLogic::GetTestDetail($getTestRes['id'], getRequestReservedParameters());
        if ($getTestDetailRes->isFail())
        {
            return outputJsonError($getTestDetailRes->getCode(),
                                   $getTestDetailRes->getMessage(),
                                   ['error' => $getTestDetailRes->getMessage()]);
        }

        return outputJsonResult($getTestDetailRes->getData());
    }

    /**
     * 开始检测化验
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws ValidationException|Throwable
     * @noinspection PhpUnused
     */
    public function StartTest(Request $request): JsonResponse
    {
        $validator = Validator::make($request->post(),
                                     [
                                         'testCode' => ['required', 'string'],
                                         'tester1'  => ['sometimes', 'nullable', 'string'],
                                         'tester2'  => ['sometimes', 'nullable', 'string'],
                                         'tester3'  => ['sometimes', 'nullable', 'string'],
                                     ],
                                     [],
                                     [
                                         'testCode' => '化验编码',
                                         'tester1'  => '检测员1',
                                         'tester2'  => '检测员2',
                                         'tester3'  => '检测员3',
                                     ]);
        if ($validator->fails())
        {
            return outputJsonError(400,
                                   $validator->errors()
                                             ->first(),
                                   [
                                       'validatorError' => $validator->errors()
                                                                     ->all()
                                   ]);
        }

        // 开始检测参数
        $validatedData = $validator->validated();
        $testCode      = $validatedData['testCode'];
        $oneTesterId   = $validatedData['tester1'] ?? '';
        $twoTesterId   = $validatedData['tester2'] ?? '';
        $threeTesterId = $validatedData['tester3'] ?? '';

        // 检测员全部不存在
        if (empty($oneTesterId) && empty($twoTesterId) && empty($threeTesterId))
        {
            return outputJsonError(400, '化验检测员必选', ['error' => '化验检测员必选']);
        }

        // 检测员级别对应关系
        $testerUids = [
            1 => $oneTesterId,
            2 => $twoTesterId,
            3 => $threeTesterId,
        ];

        // 获取化验信息
        $getTestRes = TestModel::getData(['id'], ['test_code' => $testCode]);
        $getTestRes = $getTestRes ? current($getTestRes) : [];
        if (empty($getTestRes))
        {
            return outputJsonError(40000, '化验不存在', ['error' => '化验不存在']);
        }

        // 获取化验检测员基本信息
        $getTesterRes = UsersModel::getData(whereIn: ['uid' => array_filter($testerUids)]);
        if (empty($getTesterRes))
        {
            return outputJsonError(40002, '化验检测员不存在', ['error' => '化验检测员不存在']);
        }

        $testUids      = array_column($getTesterRes, 'uid');
        $diffTesterIds = array_diff(array_filter($testerUids), $testUids);
        if (!empty($diffTesterIds))
        {
            return outputJsonError(40002, '当前跟诊选择部分检测员信息不存在，请重新选择');
        }

        // 每级检测员对应检测员ID
        $getTesterRes = array_column($getTesterRes, null, 'uid');
        $testerIds    = [
            1 => $getTesterRes[$oneTesterId]['id'] ?? 0,
            2 => $getTesterRes[$twoTesterId]['id'] ?? 0,
            3 => $getTesterRes[$threeTesterId]['id'] ?? 0,
        ];

        // 开始检测
        $startTestRes = TestLogic::StartTest($getTestRes['id'], $testerIds, getRequestReservedParameters());
        if ($startTestRes->isFail())
        {
            return outputJsonError($startTestRes->getCode(),
                                   $startTestRes->getMessage(),
                                   ['error' => $startTestRes->getMessage()]);

        }

        return outputJsonResult();
    }

    /**
     * 化验检测员跟诊
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws ValidationException
     * @throws Throwable
     * @noinspection PhpUnused
     */
    public function FollowTester(Request $request): JsonResponse
    {
        $validator = Validator::make($request->post(),
                                     [
                                         'testCode' => ['required', 'string'],
                                         'tester1'  => ['sometimes', 'nullable', 'string'],
                                         'tester2'  => ['sometimes', 'nullable', 'string'],
                                         'tester3'  => ['sometimes', 'nullable', 'string'],
                                     ],
                                     [],
                                     [
                                         'testCode' => '化验编码',
                                         'tester1'  => '检测员1',
                                         'tester2'  => '检测员2',
                                         'tester3'  => '检测员3',
                                     ]);
        if ($validator->fails())
        {
            return outputJsonError(400,
                                   $validator->errors()
                                             ->first(),
                                   [
                                       'validatorError' => $validator->errors()
                                                                     ->all()
                                   ]);
        }

        // 开始检测参数
        $validatedData = $validator->validated();
        $testCode      = $validatedData['testCode'];
        $oneTesterId   = $validatedData['tester1'] ?? '';
        $twoTesterId   = $validatedData['tester2'] ?? '';
        $threeTesterId = $validatedData['tester3'] ?? '';

        // 检测员全部不存在
        if (empty($oneTesterId) && empty($twoTesterId) && empty($threeTesterId))
        {
            return outputJsonError(400, '化验检测员必选', ['error' => '化验检测员必选']);
        }

        // 检测员级别对应关系
        $testerUids = [
            1 => $oneTesterId,
            2 => $twoTesterId,
            3 => $threeTesterId,
        ];

        // 获取化验信息
        $getTestRes = TestModel::getData(['id'], ['test_code' => $testCode]);
        $getTestRes = $getTestRes ? current($getTestRes) : [];
        if (empty($getTestRes))
        {
            return outputJsonError(40000, '化验不存在', ['error' => '化验不存在']);
        }

        // 获取化验检测员基本信息
        $getTesterRes = UsersModel::getData(whereIn: ['uid' => array_filter($testerUids)]);
        if (empty($getTesterRes))
        {
            return outputJsonError(40002, '化验检测员不存在', ['error' => '化验检测员不存在']);
        }

        $testUids      = array_column($getTesterRes, 'uid');
        $diffTesterIds = array_diff(array_filter($testerUids), $testUids);
        if (!empty($diffTesterIds))
        {
            return outputJsonError(40002, '当前跟诊选择部分检测员信息不存在，请重新选择');
        }

        // 每级检测员对应检测员ID
        $getTesterRes = array_column($getTesterRes, null, 'uid');
        $testerIds    = [
            1 => $getTesterRes[$oneTesterId]['id'] ?? 0,
            2 => $getTesterRes[$twoTesterId]['id'] ?? 0,
            3 => $getTesterRes[$threeTesterId]['id'] ?? 0,
        ];

        // 录入检测员
        $startTestRes = TestLogic::TestFollowTester($getTestRes['id'], $testerIds, getRequestReservedParameters());
        if ($startTestRes->isFail())
        {
            return outputJsonError($startTestRes->getCode(),
                                   $startTestRes->getMessage(),
                                   ['error' => $startTestRes->getMessage()]);

        }

        return outputJsonResult();
    }

    /**
     * 保存化验结果
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws Throwable
     * @throws ValidationException
     * @noinspection PhpUnused
     */
    public function SaveResult(Request $request): JsonResponse
    {
        $validator = Validator::make($request->post(),
                                     [
                                         'testCode'     => ['required', 'string'],
                                         'resultType'   => ['required', 'string'],
                                         'textResult'   => ['sometimes', 'nullable', 'string'],
                                         'imageList'    => ['sometimes', 'nullable', 'array'],
                                         'fileList'     => ['sometimes', 'nullable', 'array'],
                                         'templateList' => ['sometimes', 'nullable', 'array'],
                                         'templateInfo' => ['sometimes', 'nullable', 'array'],
                                     ],
                                     [],
                                     [
                                         'testCode'     => '化验编码',
                                         'resultType'   => '化验结果类型',
                                         'textResult'   => '化验结果文本',
                                         'imageList'    => '化验结果图片列表',
                                         'fileList'     => '化验结果文件列表',
                                         'templateList' => '化验结果模版列表',
                                         'templateInfo' => '化验结果模版详情',
                                     ]);
        if ($validator->fails())
        {
            return outputJsonError(400,
                                   $validator->errors()
                                             ->first(),
                                   [
                                       'validatorError' => $validator->errors()
                                                                     ->all()
                                   ]);
        }

        // 检测结果参数
        $validatedData = $validator->validated();
        $testCode      = $validatedData['testCode'];

        // 获取化验信息
        $getTestRes = TestModel::getData(['id'], ['test_code' => $testCode]);
        $getTestRes = $getTestRes ? current($getTestRes) : [];
        if (empty($getTestRes))
        {
            return outputJsonError(40000, '化验不存在', ['error' => '化验不存在']);
        }

        $saveResultRes = TestLogic::SaveTestResult($getTestRes['id'], $validatedData, getRequestReservedParameters());
        if ($saveResultRes->isFail())
        {
            return outputJsonError($saveResultRes->getCode(),
                                   $saveResultRes->getMessage(),
                                   ['error' => $saveResultRes->getMessage()]);
        }

        return outputJsonResult();
    }

    /**
     * 获取化验项关联可用的模版列表
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws ValidationException
     * @noinspection PhpUnused
     */
    public function GetTestTemplateList(Request $request): JsonResponse
    {
        $validator = Validator::make($request->post(),
                                     ['testCode' => ['required', 'string']],
                                     [],
                                     ['testCode' => '化验编码']);
        if ($validator->fails())
        {
            return outputJsonError(400,
                                   $validator->errors()
                                             ->first(),
                                   [
                                       'validatorError' => $validator->errors()
                                                                     ->all()
                                   ]);
        }

        // 检测结果参数
        $validatedData = $validator->validated();
        $testCode      = $validatedData['testCode'];

        // 获取化验信息
        $getTestRes = TestModel::getData(['id'], ['test_code' => $testCode]);
        $getTestRes = $getTestRes ? current($getTestRes) : [];
        if (empty($getTestRes))
        {
            return outputJsonError(40000, '化验不存在', ['error' => '化验不存在']);
        }

        $getTestRelationTemplateListRes = TestLogic::GetTestRelationTemplateList($getTestRes['id'],
                                                                                 getRequestReservedParameters());
        if ($getTestRelationTemplateListRes->isFail())
        {
            return outputJsonError($getTestRelationTemplateListRes->getCode(),
                                   $getTestRelationTemplateListRes->getMessage(),
                                   ['error' => $getTestRelationTemplateListRes->getMessage()]);
        }

        return outputJsonResult($getTestRelationTemplateListRes->getData());
    }

    /**
     * 获取化验项关联模版报告单
     * 如果存在resultUid则通过报告单结果UID查看
     * 如果存在templateUid则通过模版UID查看报告单项目，可能不存在结果
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws ValidationException
     * @noinspection PhpUnused
     */
    public function GetTestTemplateReport(Request $request): JsonResponse
    {
        $validator = Validator::make($request->post(),
                                     [
                                         'testCode'    => ['required', 'string'],
                                         'resultUid'   => ['sometimes', 'nullable', 'string'],
                                         'templateUid' => ['sometimes', 'nullable', 'string'],
                                     ],
                                     [],
                                     [
                                         'testCode'    => '化验编码',
                                         'resultUid'   => '化验结果UID',
                                         'templateUid' => '化验模版UID',
                                     ]);
        if ($validator->fails())
        {
            return outputJsonError(400,
                                   $validator->errors()
                                             ->first(),
                                   [
                                       'validatorError' => $validator->errors()
                                                                     ->all()
                                   ]);
        }

        // 检测结果参数
        $validatedData = $validator->validated();
        $testCode      = $validatedData['testCode'];
        $resultUid     = $validatedData['resultUid'] ?? '';
        $templateUid   = $validatedData['templateUid'] ?? '';
        if (empty($resultUid) && empty($templateUid))
        {
            return outputJsonError(400,
                                   '化验结果UID或化验模版UID必选其一',
                                   ['error' => '化验结果UID或化验模版UID必选其一']);
        }

        // 获取化验信息
        $getTestRes = TestModel::getData(['id'], ['test_code' => $testCode]);
        $getTestRes = $getTestRes ? current($getTestRes) : [];
        if (empty($getTestRes))
        {
            return outputJsonError(40000, '化验不存在', ['error' => '化验不存在']);
        }

        // 获取化验报告单结果
        if (!empty($resultUid))
        {
            $getTesteResultRes = TestsResultsReportModel::getOneByUid($resultUid);
            if (empty($getTesteResultRes))
            {
                return outputJsonError(40006, '查看的化验结果不存在', ['error' => '查看的化验结果不存在']);
            }
        }

        // 获取化验项关联模版报告单
        if (!empty($templateUid))
        {
            $getTestTemplatesRes = TestsReportTemplatesModel::getOneByUid($templateUid);
            if (empty($getTestTemplatesRes))
            {
                return outputJsonError(40006, '查看的化验结果模版不存在', ['error' => '查看的化验结果模版不存在']);
            }
        }

        // 获取模版、模版结果（可选），信息
        $getTestTemplateReportRes = TestLogic::GetTestTemplateReport($getTestRes['id'],
                                                                     [
                                                                         'reportResultId' => $getTesteResultRes['id'] ?? 0,
                                                                         'templateId'     => $getTestTemplatesRes['id'] ?? 0,
                                                                     ],
                                                                     getRequestReservedParameters());
        if ($getTestTemplateReportRes->isFail())
        {
            return outputJsonError($getTestTemplateReportRes->getCode(),
                                   $getTestTemplateReportRes->getMessage(),
                                   ['error' => $getTestTemplateReportRes->getMessage()]);
        }

        return outputJsonResult($getTestTemplateReportRes->getData());
    }
}
