<?php

namespace App\Http\Controllers\V1;

use Throwable;
use Validator;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\ValidationException;
use App\Enums\StockOutboundStatusEnum;
use App\Http\Controllers\Controller;
use App\Logics\V1\StockTransferLogic;
use App\Models\PurchaseOrderModel;

/**
 * 仓储-调拨单
 * Class StockTransferController
 * @package App\Http\Controllers\V1
 */
class StockTransferController extends Controller
{
    /**
     * 调拨单列表筛选项
     *
     * @return JsonResponse
     */
    public function GetFilterOptions()
    {
        $stockOutboundStatusOptions = StockOutboundStatusEnum::caseToOptions();

        return outputJsonResult([
                                    'outboundStatusOptions' => $stockOutboundStatusOptions
                                ]);
    }

    /**
     * 获取调拨单列表
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws ValidationException
     */
    public function Lists(Request $request): JsonResponse
    {
        $validator = Validator::make($request->post(),
                                     [
                                         'keywords'       => ['sometimes', 'nullable', 'string'],
                                         'startDate'      => ['sometimes', 'nullable', 'date'],
                                         'endDate'        => ['sometimes', 'nullable', 'date'],
                                         'outboundStatus' => ['sometimes', 'nullable', 'integer', Rule::in(StockOutboundStatusEnum::values())],
                                         'page'           => ['sometimes', 'nullable', 'integer'],
                                         'count'          => ['sometimes', 'nullable', 'integer'],
                                     ],
                                     [],
                                     [
                                         'keywords'       => '搜索关键词',
                                         'startDate'      => '开始日期',
                                         'endDate'        => '结束日期',
                                         'outboundStatus' => '出库状态',
                                         'page'           => '页码',
                                         'count'          => '每页条数',
                                     ]);

        if ($validator->fails())
        {
            return outputJsonError(400,
                                   $validator->errors()
                                             ->first(),
                                   [
                                       'validatorError' => $validator->errors()
                                                                     ->all()
                                   ]);
        }

        $searchParams = $validator->validated();
        $publicParams = getRequestReservedParameters();

        $getPurchaseListRes = StockTransferLogic::GetStockTransferLists($searchParams, $publicParams);
        if ($getPurchaseListRes->isFail())
        {
            return outputJsonError($getPurchaseListRes->getCode(),
                                   $getPurchaseListRes->getMessage(),
                                   ['error' => $getPurchaseListRes->getMessage()]);
        }

        return outputJsonResult($getPurchaseListRes->getData());
    }

    /**
     * 获取调拨单详情
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @noinspection PhpUnused
     */
    public function Detail(Request $request): JsonResponse
    {
        $purchaseCode = $request->post('purchaseCode');
        if (empty($purchaseCode))
        {
            return outputJsonError(400, '调拨单编码不能为空', ['error' => '调拨单编码不能为空']);
        }

        // 获取调拨单信息
        $getPurchaseOrderRes = PurchaseOrderModel::getData(where: ['purchase_code' => $purchaseCode]);
        $getPurchaseOrderRes = $getPurchaseOrderRes ? current($getPurchaseOrderRes) : [];
        if (empty($getPurchaseOrderRes))
        {
            return outputJsonError(44000, '调拨单不存在', ['error' => '调拨单不存在']);
        }

        $getPurchaseDetailRes = StockTransferLogic::GetTransferOrderDetail($getPurchaseOrderRes['id'], getRequestReservedParameters());
        if ($getPurchaseDetailRes->isFail())
        {
            return outputJsonError($getPurchaseDetailRes->getCode(),
                                   $getPurchaseDetailRes->getMessage(),
                                   ['error' => $getPurchaseDetailRes->getMessage()]);
        }

        return outputJsonResult($getPurchaseDetailRes->getData());
    }

    /**
     * 调拨出库
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws Throwable
     */
    public function Outbound(Request $request)
    {
        $purchaseCode = $request->post('purchaseCode');
        if (empty($purchaseCode))
        {
            return outputJsonError(400, '调拨单编码不能为空', ['error' => '调拨单编码不能为空']);
        }

        // 获取调拨单信息
        $getPurchaseOrderRes = PurchaseOrderModel::getData(where: ['purchase_code' => $purchaseCode]);
        $getPurchaseOrderRes = $getPurchaseOrderRes ? current($getPurchaseOrderRes) : [];
        if (empty($getPurchaseOrderRes))
        {
            return outputJsonError(44000, '调拨单不存在', ['error' => '调拨单不存在']);
        }

        $getOutboundRes = StockTransferLogic::TransferOrderOutbound($getPurchaseOrderRes['id'], getRequestReservedParameters());
        if ($getOutboundRes->isFail())
        {
            return outputJsonError($getOutboundRes->getCode(), $getOutboundRes->getMessage(), ['error' => $getOutboundRes->getMessage()]);
        }

        return outputJsonResult($getOutboundRes->getData());
    }
}
