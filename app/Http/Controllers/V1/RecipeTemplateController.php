<?php

namespace App\Http\Controllers\V1;

use Throwable;
use Validator;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\ValidationException;
use App\Http\Controllers\Controller;
use App\Logics\V1\RecipeTemplateLogic;
use App\Models\RecipeModel;
use App\Models\RecipeTemplateModel;

/**
 * Class RecipeController
 * @package      App\Http\Controllers\V1
 * @noinspection PhpUnused
 */
class RecipeTemplateController extends Controller
{
    /**
     * 获取处方模版筛选项
     * @return JsonResponse
     * @noinspection PhpUnused
     */
    public function GetTemplateOptions(): JsonResponse
    {
        $getTemplateOptionsRes = RecipeTemplateLogic::GetTemplateOptions();
        if ($getTemplateOptionsRes->isFail())
        {
            return outputJsonError($getTemplateOptionsRes->getCode(),
                                   $getTemplateOptionsRes->getMessage(),
                                   ['error' => $getTemplateOptionsRes->getMessage()]);
        }

        return outputJsonResult($getTemplateOptionsRes->getData());
    }

    /**
     * 获取处方模版列表
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws ValidationException
     * @noinspection PhpUnused
     */
    public function GetRecipeTemplateList(Request $request): JsonResponse
    {
        $validator = Validator::make($request->post(),
                                     [
                                         'type'    => ['required', 'int', 'min:0'],
                                         'keyword' => ['sometimes', 'nullable', 'string'],
                                     ],
                                     [],
                                     [
                                         'type'    => '模版类型',
                                         'keyword' => '关键词',
                                     ]);
        if ($validator->fails())
        {
            return outputJsonError(400,
                                   $validator->errors()
                                             ->first(),
                                   [
                                       'validatorError' => $validator->errors()
                                                                     ->all()
                                   ]);
        }

        $typeId       = $validator->validated()['type'];
        $keyword      = $validator->validated()['keyword'] ?? '';
        $publicParams = getRequestReservedParameters();

        $getTemplateListRes = RecipeTemplateLogic::GetTemplateList($typeId, $publicParams, ['keyword' => $keyword]);
        if ($getTemplateListRes->isFail())
        {
            return outputJsonError($getTemplateListRes->getCode(),
                                   $getTemplateListRes->getMessage(),
                                   ['error' => $getTemplateListRes->getMessage()]);
        }

        return outputJsonResult($getTemplateListRes->getData());
    }

    /**
     * 获取处方模版详情
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws ValidationException
     * @noinspection PhpUnused
     */
    public function GetRecipeTemplateDetail(Request $request): JsonResponse
    {
        $validator = Validator::make($request->post(),
                                     [
                                         'templateUid' => ['required', 'string'],
                                     ],
                                     [],
                                     [
                                         'templateUid' => '模版UID',
                                     ]);
        if ($validator->fails())
        {
            return outputJsonError(400,
                                   $validator->errors()
                                             ->first(),
                                   [
                                       'validatorError' => $validator->errors()
                                                                     ->all()
                                   ]);
        }

        $templateUid  = $validator->validated()['templateUid'];
        $publicParams = getRequestReservedParameters();

        // 获取处方模版
        $getTemplateRes = RecipeTemplateModel::getOneByUid($templateUid);
        if (empty($getTemplateRes))
        {
            return outputJsonError(39100, '处方模版不存在', ['error' => '处方模版不存在']);
        }

        $getTemplateDetailRes = RecipeTemplateLogic::GetTemplateDetail($getTemplateRes['id'], $publicParams);
        if ($getTemplateDetailRes->isFail())
        {
            return outputJsonError($getTemplateDetailRes->getCode(),
                                   $getTemplateDetailRes->getMessage(),
                                   ['error' => $getTemplateDetailRes->getMessage()]);
        }

        return outputJsonResult($getTemplateDetailRes->getData());
    }

    /**
     * 添加处方模版
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws ValidationException
     * @throws Throwable
     * @noinspection PhpUnused
     */
    public function AddRecipeTemplate(Request $request): JsonResponse
    {
        $validator = Validator::make($request->post(),
                                     [
                                         'recipeCode' => ['required', 'string'],
                                         'name'       => ['required', 'string'],
                                         'isPrivate'  => ['sometimes', 'nullable', 'boolean']
                                     ],
                                     [],
                                     [
                                         'recipeCode' => '处方编码',
                                         'name'       => '模版名称',
                                     ]);
        if ($validator->fails())
        {
            return outputJsonError(400,
                                   $validator->errors()
                                             ->first(),
                                   [
                                       'validatorError' => $validator->errors()
                                                                     ->all()
                                   ]);
        }

        $recipeCode         = $validator->validated()['recipeCode'];
        $recipeTemplateName = $validator->validated()['name'];
        $isMyPrivate        = $validator->validated()['isPrivate'] ?? false;

        // 获取处方信息
        $getRecipeRes = RecipeModel::getData(['id'],
                                             [
                                                 'recipe_code' => $recipeCode,
                                                 'hospital_id' => getPublicParamsHospitalId()
                                             ]);
        $getRecipeRes = $getRecipeRes ? current($getRecipeRes) : [];
        if (empty($getRecipeRes))
        {
            return outputJsonError(39100, '处方不存在', ['error' => '处方不存在']);
        }

        // 添加处方模版
        $getAddRecipeTemplateRes = RecipeTemplateLogic::AddRecipeTemplate([
                                                                              'recipeId'  => $getRecipeRes['id'],
                                                                              'name'      => $recipeTemplateName,
                                                                              'isPrivate' => $isMyPrivate
                                                                          ],
                                                                          getRequestReservedParameters());
        if ($getAddRecipeTemplateRes->isFail())
        {
            return outputJsonError($getAddRecipeTemplateRes->getCode(),
                                   $getAddRecipeTemplateRes->getMessage(),
                                   ['error' => $getAddRecipeTemplateRes->getMessage()]);
        }

        return outputJsonResult();
    }

    /**
     * 删除处方模版
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws Throwable
     * @throws ValidationException
     * @noinspection PhpUnused
     */
    public function DeleteRecipeTemplate(Request $request): JsonResponse
    {
        $validator = Validator::make($request->post(),
                                     [
                                         'templateUid' => ['required', 'string'],
                                     ],
                                     [],
                                     [
                                         'templateUid' => '模版UID',
                                     ]);
        if ($validator->fails())
        {
            return outputJsonError(400,
                                   $validator->errors()
                                             ->first(),
                                   [
                                       'validatorError' => $validator->errors()
                                                                     ->all()
                                   ]);
        }

        $templateUid  = $validator->validated()['templateUid'];
        $publicParams = getRequestReservedParameters();

        // 获取处方模版
        $getTemplateRes = RecipeTemplateModel::getOneByUid($templateUid);
        if (empty($getTemplateRes))
        {
            return outputJsonError(39100, '处方模版不存在', ['error' => '处方模版不存在']);
        }

        $getDeleteRes = RecipeTemplateLogic::DeleteRecipeTemplate($getTemplateRes['id'], $publicParams);
        if ($getDeleteRes->isFail())
        {
            return outputJsonError($getDeleteRes->getCode(),
                                   $getDeleteRes->getMessage(),
                                   ['error' => $getDeleteRes->getMessage()]);
        }

        return outputJsonResult();
    }
}
