<?php

namespace App\Http\Controllers\V1;

use Validator;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\ValidationException;
use App\Http\Controllers\Controller;
use App\Enums\StockAddTypeEnum;
use App\Enums\StockReduceTypeEnum;
use App\Enums\StockItemExpireTypeEnum;
use App\Logics\V1\StockItemShelfLogic;
use App\Logics\V1\StockItemShelfAddLogic;
use App\Logics\V1\StockItemShelfReduceLogic;
use App\Models\ItemModel;
use App\Models\ItemBarcodeModel;

/**
 * 库存相关
 * Class StockController
 * @package      App\Http\Controllers\V1
 * @noinspection PhpUnused
 */
class StockItemShelfController extends Controller
{
    /**
     * 库存列表
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws ValidationException
     */
    public function Lists(Request $request): JsonResponse
    {
        $validate = Validator::make($request->post(),
                                    [
                                        'keywords' => ['sometimes', 'nullable', 'string'],
                                        'page'     => ['sometimes', 'nullable', 'integer'],
                                        'count'    => ['sometimes', 'nullable', 'integer'],
                                    ],
                                    [],
                                    [
                                        'keywords' => '搜索关键词',
                                        'page'     => '页码',
                                        'count'    => '每页条数',
                                    ]);
        if ($validate->fails())
        {
            return outputJsonError(400,
                                   $validate->errors()
                                            ->first(),
                                   [
                                       'validatorError' => $validate->errors()
                                                                    ->all()
                                   ]);
        }

        $searchParams         = $validate->validated();
        $getStockItemListsRes = StockItemShelfLogic::GetStockItemLists($searchParams, getRequestReservedParameters());
        if ($getStockItemListsRes->isFail())
        {
            return outputJsonError($getStockItemListsRes->getCode(),
                                   $getStockItemListsRes->getMessage(),
                                   ['error' => $getStockItemListsRes->getMessage()]);
        }

        return outputJsonResult($getStockItemListsRes->getData());
    }

    /**
     * 库存预警列表
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws ValidationException
     * @noinspection PhpUnused
     */
    public function WarningList(Request $request): JsonResponse
    {
        $validate = Validator::make($request->post(),
                                    [
                                        'keywords' => ['sometimes', 'nullable', 'string'],
                                        'page'     => ['sometimes', 'nullable', 'integer'],
                                        'count'    => ['sometimes', 'nullable', 'integer'],
                                    ],
                                    [],
                                    [
                                        'keywords' => '搜索关键词',
                                        'page'     => '页码',
                                        'count'    => '每页条数',
                                    ]);
        if ($validate->fails())
        {
            return outputJsonError(400,
                                   $validate->errors()
                                            ->first(),
                                   [
                                       'validatorError' => $validate->errors()
                                                                    ->all()
                                   ]);
        }

        $searchParams         = $validate->validated();
        $getStockItemListsRes = StockItemShelfLogic::GetWarningStockItemLists($searchParams, getRequestReservedParameters());
        if ($getStockItemListsRes->isFail())
        {
            return outputJsonError($getStockItemListsRes->getCode(),
                                   $getStockItemListsRes->getMessage(),
                                   ['error' => $getStockItemListsRes->getMessage()]);
        }

        return outputJsonResult($getStockItemListsRes->getData());
    }

    /**
     * 临/过期预警列表
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws ValidationException
     * @noinspection PhpUnused
     */
    public function ExpireList(Request $request): JsonResponse
    {
        $validate = Validator::make($request->post(),
                                    [
                                        'keywords'        => ['sometimes', 'nullable', 'string'],
                                        'expireType'      => ['sometimes', 'nullable', 'integer', Rule::in(StockItemExpireTypeEnum::values())], // 1:临期 2:过期
                                        'expireStartDate' => ['sometimes', 'nullable', 'date_format:Y-m-d'],
                                        'expireEndDate'   => ['sometimes', 'nullable', 'date_format:Y-m-d'],
                                        'page'            => ['sometimes', 'nullable', 'integer'],
                                        'count'           => ['sometimes', 'nullable', 'integer'],
                                    ],
                                    [],
                                    [
                                        'keywords'        => '搜索关键词',
                                        'expireType'      => '过期类型',
                                        'expireStartDate' => '过期开始日期',
                                        'expireEndDate'   => '过期结束日期',
                                        'page'            => '页码',
                                        'count'           => '每页条数',
                                    ]);
        if ($validate->fails())
        {
            return outputJsonError(400,
                                   $validate->errors()
                                            ->first(),
                                   [
                                       'validatorError' => $validate->errors()
                                                                    ->all()
                                   ]);
        }

        $searchParams         = $validate->validated();
        $getExpireItemListRes = StockItemShelfLogic::GetExpireStockItemLists($searchParams, getRequestReservedParameters());
        if ($getExpireItemListRes->isFail())
        {
            return outputJsonError($getExpireItemListRes->getCode(),
                                   $getExpireItemListRes->getMessage(),
                                   ['error' => $getExpireItemListRes->getMessage()]);
        }

        return outputJsonResult($getExpireItemListRes->getData());
    }

    /**
     * 获取商品库存详情
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @noinspection PhpUnused
     */
    public function Detail(Request $request): JsonResponse
    {
        $itemUid = $request->post('itemUid', '');
        if (empty($itemUid))
        {
            return outputJsonError(400, '商品UID，必选参数错误', ['error' => '商品UID，必选参数错误']);
        }

        // 获取商品信息
        $getItemRes = ItemModel::getOneByUid($itemUid);
        if (empty($getItemRes))
        {
            return outputJsonError(33000, '商品不存在', ['error' => '商品不存在']);
        }

        $getStockItemDetailRes = StockItemShelfLogic::GetStockItemDetail($getItemRes['id'], getRequestReservedParameters());
        if ($getStockItemDetailRes->isFail())
        {
            return outputJsonError($getStockItemDetailRes->getCode(),
                                   $getStockItemDetailRes->getMessage(),
                                   ['error' => $getStockItemDetailRes->getMessage()]);
        }

        return outputJsonResult($getStockItemDetailRes->getData());
    }

    /**
     * 获取商品库存详情-效期分组
     *
     * @param Request $request
     *
     * @return JsonResponse
     */
    public function ExpiryDetail(Request $request): JsonResponse
    {
        $orgId       = getPublicParamsHospitalOrgId();
        $itemBarcode = $request->post('itemBarcode', '');
        if (empty($itemBarcode))
        {
            return outputJsonError(400, '商品条码，必选参数错误', ['error' => '商品条码，必选参数错误']);
        }

        // 获取商品信息
        $getItemRes = ItemBarcodeModel::on()
                                      ->where([
                                                  'org_id'       => $orgId,
                                                  'item_barcode' => $itemBarcode,
                                                  'status'       => 1,
                                              ])
                                      ->first();
        if (empty($getItemRes))
        {
            return outputJsonError(33001, '商品条码不存在', ['error' => '商品条码不存在']);
        }

        $getStockItemExpiryDetailRes = StockItemShelfLogic::GetStockItemDetail($getItemRes['item_id'], getRequestReservedParameters(), true, true);
        if ($getStockItemExpiryDetailRes->isFail())
        {
            return outputJsonError($getStockItemExpiryDetailRes->getCode(),
                                   $getStockItemExpiryDetailRes->getMessage(),
                                   ['error' => $getStockItemExpiryDetailRes->getMessage()]);
        }

        return outputJsonResult($getStockItemExpiryDetailRes->getData());
    }

    /**
     * 获取出入库记录筛选项
     *
     * @return JsonResponse
     */
    public function GetOutInboundFilterOptions()
    {
        $getInboundFilterOptionsRes  = StockItemShelfAddLogic::GetListFilterOptions();
        $getOutboundFilterOptionsRes = StockItemShelfReduceLogic::GetListFilterOptions();

        return outputJsonResult([
                                    'inboundFilterOptions'  => $getInboundFilterOptionsRes->getData(),
                                    'outboundFilterOptions' => $getOutboundFilterOptionsRes->getData(),
                                ]);
    }

    /**
     * 库存入库记录
     *
     * @param Request $request
     *
     * @return JsonResponse
     */
    public function InboundRecord(Request $request)
    {
        $validate = Validator::make($request->post(),
                                    [
                                        'keywords'      => ['sometimes', 'nullable', 'string'],
                                        'type'          => ['sometimes', 'nullable', 'integer', Rule::in(StockAddTypeEnum::values())],
                                        'startDate'     => ['sometimes', 'nullable', 'date_format:Y-m-d'],
                                        'endDate'       => ['sometimes', 'nullable', 'date_format:Y-m-d'],
                                        'orderCode'     => ['sometimes', 'nullable', 'string'],
                                        'createUserUid' => ['sometimes', 'nullable', 'string'],
                                        'page'          => ['sometimes', 'nullable', 'integer'],
                                        'count'         => ['sometimes', 'nullable', 'integer'],
                                    ],
                                    [],
                                    [
                                        'keywords'      => '搜索关键词',
                                        'type'          => '入库类型',
                                        'startDate'     => '开始日期',
                                        'endDate'       => '结束日期',
                                        'orderCode'     => '订单号',
                                        'createUserUid' => '创建人',
                                        'page'          => '页码',
                                        'count'         => '每页条数',
                                    ]);
        if ($validate->fails())
        {
            return outputJsonError(400,
                                   $validate->errors()
                                            ->first(),
                                   [
                                       'validatorError' => $validate->errors()
                                                                    ->all()
                                   ]);
        }

        $getAddStockRecordRes = StockItemShelfAddLogic::GetAddStockRecord($validate->validated(), getRequestReservedParameters());
        if ($getAddStockRecordRes->isFail())
        {
            return outputJsonError($getAddStockRecordRes->getCode(), $getAddStockRecordRes->getMessage(), ['error' => $getAddStockRecordRes->getMessage()]);
        }

        return outputJsonResult($getAddStockRecordRes->getData());
    }

    /**
     * 库存出库记录
     *
     * @param Request $request
     *
     * @return JsonResponse
     */
    public function OutboundRecord(Request $request)
    {
        $validate = Validator::make($request->post(),
                                    [
                                        'keywords'      => ['sometimes', 'nullable', 'string'],
                                        'type'          => ['sometimes', 'nullable', 'integer', Rule::in(StockReduceTypeEnum::values())],
                                        'startDate'     => ['sometimes', 'nullable', 'date_format:Y-m-d'],
                                        'endDate'       => ['sometimes', 'nullable', 'date_format:Y-m-d'],
                                        'orderCode'     => ['sometimes', 'nullable', 'string'],
                                        'createUserUid' => ['sometimes', 'nullable', 'string'],
                                        'page'          => ['sometimes', 'nullable', 'integer'],
                                        'count'         => ['sometimes', 'nullable', 'integer'],
                                    ],
                                    [],
                                    [
                                        'keywords'      => '搜索关键词',
                                        'type'          => '入库类型',
                                        'startDate'     => '开始日期',
                                        'endDate'       => '结束日期',
                                        'orderCode'     => '订单号',
                                        'createUserUid' => '创建人',
                                        'page'          => '页码',
                                        'count'         => '每页条数',
                                    ]);
        if ($validate->fails())
        {
            return outputJsonError(400,
                                   $validate->errors()
                                            ->first(),
                                   [
                                       'validatorError' => $validate->errors()
                                                                    ->all()
                                   ]);
        }

        $getAddStockRecordRes = StockItemShelfReduceLogic::GetReduceStockRecord($validate->validated(), getRequestReservedParameters());
        if ($getAddStockRecordRes->isFail())
        {
            return outputJsonError($getAddStockRecordRes->getCode(), $getAddStockRecordRes->getMessage(), ['error' => $getAddStockRecordRes->getMessage()]);
        }

        return outputJsonResult($getAddStockRecordRes->getData());
    }
}
