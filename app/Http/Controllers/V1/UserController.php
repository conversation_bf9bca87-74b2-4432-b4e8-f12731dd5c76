<?php

namespace App\Http\Controllers\V1;

use Validator;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\ValidationException;
use App\Enums\HospitalUserByBusinessEnum;
use App\Http\Controllers\Controller;
use App\Logics\V1\HospitalUserLogic;

/**
 * 用户相关
 * Class UserController
 * @package      App\Http\Controllers\V1
 * @noinspection PhpUnused
 */
class UserController extends Controller
{
    /**
     * 获取当前用户可用的医院身份下的医院
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws ValidationException
     * @noinspection PhpUnused
     */
    public function GetUserByBusiness(Request $request): JsonResponse
    {
        $validate = Validator::make($request->post(),
                                    [
                                        'business' => [
                                            'required',
                                            'string',
                                            Rule::in(HospitalUserByBusinessEnum::values())
                                        ],
                                    ],
                                    [],
                                    [
                                        'business' => '业务类型',
                                    ]);
        if ($validate->fails())
        {
            return outputJsonError(400,
                                   $validate->errors()
                                            ->first(),
                                   [
                                       'validatorError' => $validate->errors()
                                                                    ->all()
                                   ]);
        }

        $businessType = $validate->validated()['business'];
        $getUsersRes  = HospitalUserLogic::GetHospitalUsersByBusiness($businessType, getRequestReservedParameters());

        return outputJsonResult($getUsersRes->getData());
    }
}
