<?php

namespace App\Http\Controllers\V1;

use Throwable;
use Validator;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\ValidationException;
use App\Http\Controllers\Controller;
use App\Enums\PageEnum;
use App\Logics\V1\ImageLogic;
use App\Models\UsersModel;
use App\Models\ImagesModel;
use App\Models\ImagesResultsReportModel;
use App\Models\ImagesReportTemplatesModel;

/**
 * 影像相关
 * Class ImageController
 * @package      App\Http\Controllers\V1
 * @noinspection PhpUnused
 */
class ImageController extends Controller
{
    /**
     * 影像列表筛选项
     *
     * @return JsonResponse
     * @noinspection PhpUnused
     */
    public function GetImageFilterOptions(): JsonResponse
    {
        $getImageFilterOptionsRes = ImageLogic::GetImageFilterOptions(getRequestReservedParameters());
        if ($getImageFilterOptionsRes->isFail())
        {
            return outputJsonError($getImageFilterOptionsRes->getCode(),
                                   $getImageFilterOptionsRes->getMessage(),
                                   ['error' => $getImageFilterOptionsRes->getMessage()]);
        }

        return outputJsonResult($getImageFilterOptionsRes->getData());
    }

    /**
     * 影像列表
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws ValidationException
     */
    public function Lists(Request $request): JsonResponse
    {
        $validator = Validator::make($request->post(),
                                     [
                                         'keywords'     => ['sometimes', 'nullable', 'string'],
                                         'caseCode'     => ['sometimes', 'nullable', 'string'],
                                         'imageCode'    => ['sometimes', 'nullable', 'string'],
                                         'name'         => ['sometimes', 'nullable', 'string'],
                                         'startDate'    => ['sometimes', 'nullable', 'date'],
                                         'endDate'      => ['sometimes', 'nullable', 'date'],
                                         'doctorUid'    => ['sometimes', 'nullable', 'string'],
                                         'status'       => ['sometimes', 'nullable', 'integer'],
                                         'payStatus'    => ['sometimes', 'nullable', 'integer'],
                                         'testerStatus' => ['sometimes', 'nullable', 'string'],
                                         'page'         => ['integer'],
                                         'count'        => ['integer'],
                                     ]);
        if ($validator->fails())
        {
            return outputJsonError(400,
                                   $validator->errors()
                                             ->first(),
                                   [
                                       'validatorError' => $validator->errors()
                                                                     ->all()
                                   ]);
        }

        $searchParams = $validator->validated();
        $iPage        = $validator->validated()['page'] ?? PageEnum::DefaultPageIndex->value;
        $iPageSize    = $validator->validated()['count'] ?? PageEnum::DefaultPageSize->value;

        $getImageListRes = ImageLogic::GetImageList($searchParams, getRequestReservedParameters(), $iPage, $iPageSize);
        if ($getImageListRes->isFail())
        {
            return outputJsonError($getImageListRes->getCode(),
                                   $getImageListRes->getMessage(),
                                   ['error' => $getImageListRes->getMessage()]);
        }

        return outputJsonResult($getImageListRes->getData());
    }

    /**
     * 影像详情
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws ValidationException
     * @noinspection PhpUnused
     */
    public function Detail(Request $request): JsonResponse
    {
        $validator = Validator::make($request->post(),
                                     [
                                         'imageCode' => ['required', 'string'],
                                     ]);
        if ($validator->fails())
        {
            return outputJsonError(400,
                                   $validator->errors()
                                             ->first(),
                                   [
                                       'validatorError' => $validator->errors()
                                                                     ->all()
                                   ]);
        }

        // 获取影像信息
        $imageCode   = $validator->validated()['imageCode'];
        $getImageRes = ImagesModel::getData(['id'], ['image_code' => $imageCode]);
        $getImageRes = $getImageRes ? current($getImageRes) : [];
        if (empty($getImageRes))
        {
            return outputJsonError(40101, '影像不存在', ['error' => '影像不存在']);
        }

        // 获取影像详情
        $getImageDetailRes = ImageLogic::GetImageDetail($getImageRes['id'], getRequestReservedParameters());
        if ($getImageDetailRes->isFail())
        {
            return outputJsonError($getImageDetailRes->getCode(),
                                   $getImageDetailRes->getMessage(),
                                   ['error' => $getImageDetailRes->getMessage()]);
        }

        return outputJsonResult($getImageDetailRes->getData());
    }

    /**
     * 开始检测影像
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws ValidationException|Throwable
     * @noinspection PhpUnused
     */
    public function StartTest(Request $request): JsonResponse
    {
        $validator = Validator::make($request->post(),
                                     [
                                         'imageCode' => ['required', 'string'],
                                         'tester1'   => ['sometimes', 'nullable', 'string'],
                                         'tester2'   => ['sometimes', 'nullable', 'string'],
                                         'tester3'   => ['sometimes', 'nullable', 'string'],
                                     ],
                                     [],
                                     [
                                         'imageCode' => '影像编码',
                                         'tester1'   => '检测员1',
                                         'tester2'   => '检测员2',
                                         'tester3'   => '检测员3',
                                     ]);
        if ($validator->fails())
        {
            return outputJsonError(400,
                                   $validator->errors()
                                             ->first(),
                                   [
                                       'validatorError' => $validator->errors()
                                                                     ->all()
                                   ]);
        }

        // 开始检测参数
        $validatedData = $validator->validated();
        $imageCode     = $validatedData['imageCode'];
        $oneTesterId   = $validatedData['tester1'] ?? '';
        $twoTesterId   = $validatedData['tester2'] ?? '';
        $threeTesterId = $validatedData['tester3'] ?? '';

        // 检测员全部不存在
        if (empty($oneTesterId) && empty($twoTesterId) && empty($threeTesterId))
        {
            return outputJsonError(400, '影像检测员必选', ['error' => '影像检测员必选']);
        }

        // 检测员级别对应关系
        $testerUids = [
            1 => $oneTesterId,
            2 => $twoTesterId,
            3 => $threeTesterId,
        ];

        // 获取影像信息
        $getImageRes = ImagesModel::getData(['id'], ['image_code' => $imageCode]);
        $getImageRes = $getImageRes ? current($getImageRes) : [];
        if (empty($getImageRes))
        {
            return outputJsonError(40101, '影像不存在', ['error' => '影像不存在']);
        }

        // 获取影像检测员基本信息
        $getTesterRes = UsersModel::getData(whereIn: ['uid' => array_filter($testerUids)]);
        if (empty($getTesterRes))
        {
            return outputJsonError(40102, '影像检测员不存在', ['error' => '影像检测员不存在']);
        }

        $testUids      = array_column($getTesterRes, 'uid');
        $diffTesterIds = array_diff(array_filter($testerUids), $testUids);
        if (!empty($diffTesterIds))
        {
            return outputJsonError(40102, '当前跟诊选择部分检测员信息不存在，请重新选择');
        }

        // 每级检测员对应检测员ID
        $getTesterRes = array_column($getTesterRes, null, 'uid');
        $testerIds    = [
            1 => $getTesterRes[$oneTesterId]['id'] ?? 0,
            2 => $getTesterRes[$twoTesterId]['id'] ?? 0,
            3 => $getTesterRes[$threeTesterId]['id'] ?? 0,
        ];

        // 开始检测
        $startTestRes = ImageLogic::StartTest($getImageRes['id'], $testerIds, getRequestReservedParameters());
        if ($startTestRes->isFail())
        {
            return outputJsonError($startTestRes->getCode(),
                                   $startTestRes->getMessage(),
                                   ['error' => $startTestRes->getMessage()]);

        }

        return outputJsonResult();
    }

    /**
     * 影像检测员跟诊
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws ValidationException
     * @throws Throwable
     * @noinspection PhpUnused
     */
    public function FollowTester(Request $request): JsonResponse
    {
        $validator = Validator::make($request->post(),
                                     [
                                         'imageCode' => ['required', 'string'],
                                         'tester1'   => ['sometimes', 'nullable', 'string'],
                                         'tester2'   => ['sometimes', 'nullable', 'string'],
                                         'tester3'   => ['sometimes', 'nullable', 'string'],
                                     ],
                                     [],
                                     [
                                         'imageCode' => '影像编码',
                                         'tester1'   => '检测员1',
                                         'tester2'   => '检测员2',
                                         'tester3'   => '检测员3',
                                     ]);
        if ($validator->fails())
        {
            return outputJsonError(400,
                                   $validator->errors()
                                             ->first(),
                                   [
                                       'validatorError' => $validator->errors()
                                                                     ->all()
                                   ]);
        }

        // 开始检测参数
        $validatedData = $validator->validated();
        $imageCode     = $validatedData['imageCode'];
        $oneTesterId   = $validatedData['tester1'] ?? '';
        $twoTesterId   = $validatedData['tester2'] ?? '';
        $threeTesterId = $validatedData['tester3'] ?? '';

        // 检测员全部不存在
        if (empty($oneTesterId) && empty($twoTesterId) && empty($threeTesterId))
        {
            return outputJsonError(400, '影像检测员必选', ['error' => '影像检测员必选']);
        }

        // 检测员级别对应关系
        $testerUids = [
            1 => $oneTesterId,
            2 => $twoTesterId,
            3 => $threeTesterId,
        ];

        // 获取影像信息
        $getImageRes = ImagesModel::getData(['id'], ['image_code' => $imageCode]);
        $getImageRes = $getImageRes ? current($getImageRes) : [];
        if (empty($getImageRes))
        {
            return outputJsonError(40101, '影像不存在', ['error' => '影像不存在']);
        }

        // 获取影像检测员基本信息
        $getTesterRes = UsersModel::getData(whereIn: ['uid' => array_filter($testerUids)]);
        if (empty($getTesterRes))
        {
            return outputJsonError(40102, '影像检测员不存在', ['error' => '影像检测员不存在']);
        }

        $testUids      = array_column($getTesterRes, 'uid');
        $diffTesterIds = array_diff(array_filter($testerUids), $testUids);
        if (!empty($diffTesterIds))
        {
            return outputJsonError(40102, '当前跟诊选择部分检测员信息不存在，请重新选择');
        }

        // 每级检测员对应检测员ID
        $getTesterRes = array_column($getTesterRes, null, 'uid');
        $testerIds    = [
            1 => $getTesterRes[$oneTesterId]['id'] ?? 0,
            2 => $getTesterRes[$twoTesterId]['id'] ?? 0,
            3 => $getTesterRes[$threeTesterId]['id'] ?? 0,
        ];

        // 录入检测员
        $startTestRes = ImageLogic::ImageFollowTester($getImageRes['id'], $testerIds, getRequestReservedParameters());
        if ($startTestRes->isFail())
        {
            return outputJsonError($startTestRes->getCode(),
                                   $startTestRes->getMessage(),
                                   ['error' => $startTestRes->getMessage()]);

        }

        return outputJsonResult();
    }

    /**
     * 保存影像结果
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws Throwable
     * @throws ValidationException
     * @noinspection PhpUnused
     */
    public function SaveResult(Request $request): JsonResponse
    {
        $validator = Validator::make($request->post(),
                                     [
                                         'imageCode'    => ['required', 'string'],
                                         'resultType'   => ['required', 'string'],
                                         'textResult'   => ['sometimes', 'nullable', 'string'],
                                         'imageList'    => ['sometimes', 'nullable', 'array'],
                                         'fileList'     => ['sometimes', 'nullable', 'array'],
                                         'templateList' => ['sometimes', 'nullable', 'array'],
                                         'templateInfo' => ['sometimes', 'nullable', 'array'],
                                     ],
                                     [],
                                     [
                                         'imageCode'    => '影像编码',
                                         'resultType'   => '影像结果类型',
                                         'textResult'   => '影像结果文本',
                                         'imageList'    => '影像结果图片列表',
                                         'fileList'     => '影像结果文件列表',
                                         'templateList' => '影像结果模版列表',
                                         'templateInfo' => '影像结果模版详情',
                                     ]);
        if ($validator->fails())
        {
            return outputJsonError(400,
                                   $validator->errors()
                                             ->first(),
                                   [
                                       'validatorError' => $validator->errors()
                                                                     ->all()
                                   ]);
        }

        // 检测结果参数
        $validatedData = $validator->validated();
        $imageCode     = $validatedData['imageCode'];

        // 获取影像信息
        $getImageRes = ImagesModel::getData(['id'], ['image_code' => $imageCode]);
        $getImageRes = $getImageRes ? current($getImageRes) : [];
        if (empty($getImageRes))
        {
            return outputJsonError(40101, '影像不存在', ['error' => '影像不存在']);
        }

        $saveResultRes = ImageLogic::SaveImageResult($getImageRes['id'],
                                                     $validatedData,
                                                     getRequestReservedParameters());
        if ($saveResultRes->isFail())
        {
            return outputJsonError($saveResultRes->getCode(),
                                   $saveResultRes->getMessage(),
                                   ['error' => $saveResultRes->getMessage()]);
        }

        return outputJsonResult();
    }

    /**
     * 获取化验项关联可用的模版列表
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws ValidationException
     * @noinspection PhpUnused
     */
    public function GetImageTemplateList(Request $request): JsonResponse
    {
        $validator = Validator::make($request->post(),
                                     ['imageCode' => ['required', 'string']],
                                     [],
                                     ['imageCode' => '影像编码']);
        if ($validator->fails())
        {
            return outputJsonError(400,
                                   $validator->errors()
                                             ->first(),
                                   [
                                       'validatorError' => $validator->errors()
                                                                     ->all()
                                   ]);
        }

        // 检测结果参数
        $validatedData = $validator->validated();
        $imageCode     = $validatedData['imageCode'];

        // 获取化验信息
        $getImageRes = ImagesModel::getData(['id'], ['image_code' => $imageCode]);
        $getImageRes = $getImageRes ? current($getImageRes) : [];
        if (empty($getImageRes))
        {
            return outputJsonError(40101, '影像不存在', ['error' => '影像不存在']);
        }

        $getTestRelationTemplateListRes = ImageLogic::GetImageRelationTemplateList($getImageRes['id'],
                                                                                   getRequestReservedParameters());
        if ($getTestRelationTemplateListRes->isFail())
        {
            return outputJsonError($getTestRelationTemplateListRes->getCode(),
                                   $getTestRelationTemplateListRes->getMessage(),
                                   ['error' => $getTestRelationTemplateListRes->getMessage()]);
        }

        return outputJsonResult($getTestRelationTemplateListRes->getData());
    }

    /**
     * 获取影像关联模版报告单结果
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws ValidationException
     * @noinspection PhpUnused
     */
    public function GetTestTemplateReport(Request $request): JsonResponse
    {
        $validator = Validator::make($request->post(),
                                     [
                                         'imageCode'   => ['required', 'string'],
                                         'resultUid'   => ['sometimes', 'nullable', 'string'],
                                         'templateUid' => ['sometimes', 'nullable', 'string'],
                                     ],
                                     [],
                                     [
                                         'imageCode'   => '影像编码',
                                         'resultUid'   => '影像结果UID',
                                         'templateUid' => '影像模版UID',
                                     ]);
        if ($validator->fails())
        {
            return outputJsonError(400,
                                   $validator->errors()
                                             ->first(),
                                   [
                                       'validatorError' => $validator->errors()
                                                                     ->all()
                                   ]);
        }

        // 检测结果参数
        $validatedData = $validator->validated();
        $imageCode     = $validatedData['imageCode'];
        $resultUid     = $validatedData['resultUid'] ?? '';
        $templateUid   = $validatedData['templateUid'] ?? '';

        // 获取影像信息
        $getImageRes = ImagesModel::getData(['id'], ['image_code' => $imageCode]);
        $getImageRes = $getImageRes ? current($getImageRes) : [];
        if (empty($getImageRes))
        {
            return outputJsonError(40101, '影像不存在', ['error' => '影像不存在']);
        }

        // 获取影像报告单结果
        if (!empty($resultUid))
        {
            $getImageResultRes = ImagesResultsReportModel::getOneByUid($resultUid);
            if (empty($getImageResultRes))
            {
                return outputJsonError(40107, '查看的影像结果不存在', ['error' => '查看的影像结果不存在']);
            }
        }

        // 获取模版信息
        if (!empty($templateUid))
        {
            $getImageTemplatesRes = ImagesReportTemplatesModel::getOneByUid($templateUid);
            if (empty($getImageTemplatesRes))
            {
                return outputJsonError(40107, '查看的影像结果模版不存在', ['error' => '查看的影像结果模版不存在']);
            }
        }

        $getImageTemplateReportRes = ImageLogic::GetImageTemplateReport($getImageRes['id'],
                                                                        [
                                                                            'reportResultId' => $getImageResultRes['id'] ?? 0,
                                                                            'templateId'     => $getImageTemplatesRes['id'] ?? 0,
                                                                        ],
                                                                        getRequestReservedParameters());
        if ($getImageTemplateReportRes->isFail())
        {
            return outputJsonError($getImageTemplateReportRes->getCode(),
                                   $getImageTemplateReportRes->getMessage(),
                                   ['error' => $getImageTemplateReportRes->getMessage()]);
        }

        return outputJsonResult($getImageTemplateReportRes->getData());
    }
}
