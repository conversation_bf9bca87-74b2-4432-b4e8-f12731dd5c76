<?php

namespace App\Http\Controllers\V1;

use Throwable;
use Validator;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\Rule;
use Illuminate\Validation\ValidationException;
use App\Http\Controllers\Controller;
use App\Enums\StockOutboundStatusEnum;
use App\Enums\StockRefundStatusEnum;
use App\Enums\StockRefundTypeEnum;
use App\Logics\V1\StockRefundLogic;
use App\Models\StockRefundModel;

class StockRefundController extends Controller
{
    /**
     * 退货单列表筛选项
     *
     * @return JsonResponse
     */
    public function GetFilterOptions()
    {
        $GetCreateUsersOptionsRes = StockRefundLogic::GetCreateUsersOptions(getRequestReservedParameters());
        if ($GetCreateUsersOptionsRes->isFail())
        {
            return outputJsonError($GetCreateUsersOptionsRes->getCode(),
                                   '',
                                   ['error' => $GetCreateUsersOptionsRes->getMessage()]);
        }

        $userOptions                = $GetCreateUsersOptionsRes->getData();
        $stockOutboundStatusOptions = StockOutboundStatusEnum::caseToOptions();
        $refundStatusOptions        = StockRefundStatusEnum::caseToOptions();
        $refundTypeOptions          = StockRefundTypeEnum::caseToOptions();

        return outputJsonResult([
                                    'userOptions'           => $userOptions,
                                    'outboundStatusOptions' => $stockOutboundStatusOptions,
                                    'refundStatusOptions'   => $refundStatusOptions,
                                    'refundTypeOptions'     => $refundTypeOptions,
                                ]);
    }

    /**
     * 获取退货单列表
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws ValidationException
     */
    public function Lists(Request $request): JsonResponse
    {
        $validator = Validator::make($request->post(),
                                     [
                                         'keywords'       => ['sometimes', 'nullable', 'string'],
                                         'startDate'      => ['sometimes', 'nullable', 'date'],
                                         'endDate'        => ['sometimes', 'nullable', 'date'],
                                         'refundStatus'   => ['sometimes', 'nullable', 'integer', Rule::in(StockRefundStatusEnum::values())],
                                         'outboundStatus' => ['sometimes', 'nullable', 'integer'],
                                         'createUserUid'  => ['sometimes', 'nullable', 'string'],
                                         'refundType'     => ['sometimes', 'nullable', 'integer', Rule::in(StockRefundTypeEnum::values())],
                                         'page'           => ['sometimes', 'nullable', 'integer'],
                                         'count'          => ['sometimes', 'nullable', 'integer'],
                                     ],
                                     [],
                                     [
                                         'keywords'       => '搜索关键词',
                                         'startDate'      => '开始日期',
                                         'endDate'        => '结束日期',
                                         'refundStatus'   => '退货单状态',
                                         'outboundStatus' => '出库状态',
                                         'createUserUid'  => '创建人',
                                         'refundType'     => '退货类型',
                                         'page'           => '页码',
                                         'count'          => '每页条数',
                                     ]);

        if ($validator->fails())
        {
            return outputJsonError(400,
                                   $validator->errors()
                                             ->first(),
                                   [
                                       'validatorError' => $validator->errors()
                                                                     ->all()
                                   ]);
        }

        $searchParams = $validator->validated();
        $publicParams = getRequestReservedParameters();

        $getPurchaseListRes = StockRefundLogic::GetRefundList($searchParams, $publicParams);
        if ($getPurchaseListRes->isFail())
        {
            return outputJsonError($getPurchaseListRes->getCode(),
                                   $getPurchaseListRes->getMessage(),
                                   ['error' => $getPurchaseListRes->getMessage()]);
        }

        return outputJsonResult($getPurchaseListRes->getData());
    }

    /**
     * 退货单详情
     *
     * @param Request $request
     *
     * @return JsonResponse
     */
    public function Detail(Request $request)
    {
        $refundCode = $request->post('refundCode');
        if (empty($refundCode))
        {
            return outputJsonError(400, '退货单编码不能为空', ['error' => '退货单编码不能为空']);
        }

        // 获取退货单信息
        $getRefundRes = StockRefundModel::getData(where: ['refund_code' => $refundCode]);
        $getRefundRes = $getRefundRes ? current($getRefundRes) : [];
        if (empty($getRefundRes))
        {
            return outputJsonError(44000, '退货单不存在', ['error' => '退货单不存在']);
        }

        $getRefundDetailRes = StockRefundLogic::GetRefundDetail($getRefundRes['id'], getRequestReservedParameters());
        if ($getRefundDetailRes->isFail())
        {
            return outputJsonError($getRefundDetailRes->getCode(), $getRefundDetailRes->getMessage(), ['error' => $getRefundDetailRes->getMessage()]);
        }

        return outputJsonResult($getRefundDetailRes->getData());
    }

    /**
     * 新增退货单
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws ValidationException
     * @throws Throwable
     */
    public function Add(Request $request): JsonResponse
    {
        $validator = Validator::make($request->post(),
                                     [

                                         'remark'     => ['sometimes', 'nullable', 'string'],
                                         'submitType' => ['required', 'integer', Rule::in([0, 1])],
                                         'items'      => ['required', 'array'],
                                     ],
                                     [],
                                     [
                                         'remark'     => '退货说明',
                                         'submitType' => '提交类型',
                                         'items'      => '退货商品',
                                     ]);
        if ($validator->fails())
        {
            return outputJsonError(400,
                                   $validator->errors()
                                             ->first(),
                                   [
                                       'validatorError' => $validator->errors()
                                                                     ->all()
                                   ]);
        }

        $addPurchaseParams = $validator->validated();
        $publicParams      = getRequestReservedParameters();

        $getAddPurchaseRes = StockRefundLogic::AddStockRefund($addPurchaseParams, $publicParams);
        if ($getAddPurchaseRes->isFail())
        {
            return outputJsonError($getAddPurchaseRes->getCode(), $getAddPurchaseRes->getMessage(), ['error' => $getAddPurchaseRes->getMessage()]);
        }

        return outputJsonResult($getAddPurchaseRes->getData());
    }

    /**
     * 编辑退货单
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws Throwable
     */
    public function Edit(Request $request): JsonResponse
    {
        $validator = Validator::make($request->post(),
                                     [
                                         'refundCode' => ['required', 'string'],
                                         'remark'     => ['sometimes', 'nullable', 'string'],
                                         'submitType' => ['required', 'integer', Rule::in([0, 1])],
                                         'items'      => ['required', 'array'],
                                     ],
                                     [],
                                     [
                                         'remark'     => '退货说明',
                                         'submitType' => '提交类型',
                                         'items'      => '退货商品',
                                     ]);
        if ($validator->fails())
        {
            return outputJsonError(400,
                                   $validator->errors()
                                             ->first(),
                                   [
                                       'validatorError' => $validator->errors()
                                                                     ->all()
                                   ]);
        }

        // 获取参数
        $refundCode = $validator->validated()['refundCode'];

        // 获取退货单信息
        $getRefundRes = StockRefundModel::getData(where: ['refund_code' => $refundCode]);
        $getRefundRes = $getRefundRes ? current($getRefundRes) : [];
        if (empty($getRefundRes))
        {
            return outputJsonError(44000, '退货单不存在', ['error' => '退货单不存在']);
        }

        $editRefundParams = $validator->validated();
        $publicParams     = getRequestReservedParameters();
        $getEditRefundRes = StockRefundLogic::EditStockRefund($getRefundRes['id'], $editRefundParams, $publicParams);
        if ($getEditRefundRes->isFail())
        {
            return outputJsonError($getEditRefundRes->getCode(), $getEditRefundRes->getMessage(), ['error' => $getEditRefundRes->getMessage()]);
        }

        return outputJsonResult($getEditRefundRes->getData());
    }

    /**
     * 退货出库
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws Throwable
     */
    public function Outbound(Request $request)
    {
        $refundCode = $request->post('refundCode');
        if (empty($refundCode))
        {
            return outputJsonError(400, '退货单编码不能为空', ['error' => '退货单编码不能为空']);
        }

        // 获取退货单信息
        $getRefundRes = StockRefundModel::getData(where: ['refund_code' => $refundCode]);
        $getRefundRes = $getRefundRes ? current($getRefundRes) : [];
        if (empty($getRefundRes))
        {
            return outputJsonError(44000, '退货单不存在', ['error' => '退货单不存在']);
        }

        $getOutboundRes = StockRefundLogic::StockRefundOutbound($getRefundRes['id'], getRequestReservedParameters());
        if ($getOutboundRes->isFail())
        {
            return outputJsonError($getOutboundRes->getCode(), $getOutboundRes->getMessage(), ['error' => $getOutboundRes->getMessage()]);
        }

        return outputJsonResult($getOutboundRes->getData());
    }

    /**
     * 删除退货单
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws Throwable
     */
    public function Delete(Request $request): JsonResponse
    {
        $refundCode = $request->post('refundCode');
        if (empty($refundCode))
        {
            return outputJsonError(400, '退货单编码不能为空', ['error' => '退货单编码不能为空']);
        }

        // 获取退货单信息
        $getRefundRes = StockRefundModel::getData(where: ['refund_code' => $refundCode]);
        $getRefundRes = $getRefundRes ? current($getRefundRes) : [];
        if (empty($getRefundRes))
        {
            return outputJsonError(44000, '退货单不存在', ['error' => '退货单不存在']);
        }

        $deleteRefundRes = StockRefundLogic::DeleteStockRefund($getRefundRes['id'], getRequestReservedParameters());
        if ($deleteRefundRes->isFail())
        {
            return outputJsonError($deleteRefundRes->getCode(), $deleteRefundRes->getMessage(), ['error' => $deleteRefundRes->getMessage()]);
        }

        return outputJsonResult();
    }
}
