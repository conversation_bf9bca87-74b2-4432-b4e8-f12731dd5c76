<?php

namespace App\Http\Controllers\V1;

use Throwable;
use Validator;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\ValidationException;
use App\Http\Controllers\Controller;
use App\Enums\PageEnum;
use App\Logics\V1\CaseLogic;
use App\Logics\V1\PetLogic;
use App\Models\UsersModel;
use App\Models\CasesModel;
use App\Models\CaseSnapshotModel;

/**
 * Class CaseController
 * @package      App\Http\Controllers\V1
 * @noinspection PhpUnused
 */
class CaseController extends Controller
{
    /**
     * @param Request $request
     *
     * @return JsonResponse
     * @throws ValidationException
     * @noinspection PhpUnused
     */
    public function Lists(Request $request): JsonResponse
    {
        // 验证参数有效性
        $validate = Validator::make($request->post(), [
            'page'      => ['sometimes', 'nullable', 'integer'],
            'count'     => ['sometimes', 'nullable', 'integer'],
            'keyword'   => ['sometimes', 'nullable', 'string'],
            'startDate' => ['sometimes', 'nullable', 'date'],
            'endDate'   => ['sometimes', 'nullable', 'date'],
            'doctorUid' => ['sometimes', 'nullable', 'string'],
            'rangeType' => ['required', 'integer', Rule::in(1, 2)],
        ]);

        if ($validate->fails())
        {
            return outputJsonError(400,
                                   $validate->errors()
                                            ->first(),
                                   [
                                       'validatorError' => $validate->errors()
                                                                    ->all()
                                   ]);
        }

        $searchParams = $validate->validated();
        if (!empty($searchParams['doctorUid']))
        {
            $getDoctorRes = UsersModel::getOneByUid($searchParams['doctorUid']);
            if (empty($getDoctorRes))
            {
                return outputJsonError(10100, '选择的病历主治医生不存在', ['error' => '选择的病历主治医生不存在']);
            }

            $searchParams['doctorId'] = $getDoctorRes['id'];
        }

        $publicParams     = getRequestReservedParameters();
        $getMemberListRes = CaseLogic::SearchCaseList($searchParams, $publicParams);
        if ($getMemberListRes->isFail())
        {
            return outputJsonError($getMemberListRes->getCode(),
                                   $getMemberListRes->getMessage(),
                                   ['error' => $getMemberListRes->getMessage()]);
        }

        return outputJsonResult($getMemberListRes->getData());
    }

    /**
     * 获取病历详情信息
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws ValidationException
     * @noinspection PhpUnused
     */
    public function GetCaseDetail(Request $request): JsonResponse
    {
        $validator = Validator::make($request->post(),
                                     [
                                         'caseCode' => ['required', 'string'],
                                     ],
                                     [],
                                     [
                                         'caseCode' => '病历编码',
                                     ]);

        if ($validator->fails())
        {
            return outputJsonError(400,
                                   $validator->errors()
                                             ->first(),
                                   [
                                       'validatorError' => $validator->errors()
                                                                     ->all()
                                   ]);
        }

        // 获取病历信息
        $caseCode   = $validator->validated()['caseCode'];
        $getCaseRes = CasesModel::getData(['id'],
                                          ['case_code' => $caseCode, 'hospital_id' => getPublicParamsHospitalId()]);
        $getCaseRes = $getCaseRes ? current($getCaseRes) : [];
        if (empty($getCaseRes))
        {
            return outputJsonError(38000, '查看的病历不存在', ['error' => '查看的病历不存在']);
        }

        $caseId       = $getCaseRes['id'];
        $publicParams = getRequestReservedParameters();

        // 获取病历诊断信息
        $getCaseDiagnoseRes = CaseLogic::GetCaseDetail($caseId, $publicParams);
        if ($getCaseDiagnoseRes->isFail())
        {
            return outputJsonError($getCaseDiagnoseRes->getCode(),
                                   $getCaseDiagnoseRes->getMessage(),
                                   ['error' => $getCaseDiagnoseRes->getMessage()]);
        }

        return outputJsonResult($getCaseDiagnoseRes->getData());
    }

    /**
     * 编辑病历宠物体征信息
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws ValidationException
     * @throws Throwable
     * @noinspection PhpUnused
     */
    public function EditPetVitalSign(Request $request): JsonResponse
    {
        // 验证参数有效性
        $validator = Validator::make($request->post(),
                                     [
                                         'caseCode'          => ['required', 'string'],
                                         'petWeight'         => ['sometimes', 'nullable', 'numeric'],
                                         'physical'          => ['sometimes', 'nullable', 'int'],
                                         'heartbeat'         => ['sometimes', 'nullable', 'int'],
                                         'respiration'       => ['sometimes', 'nullable', 'int'],
                                         'temperatureType'   => ['sometimes', 'nullable', 'int'],
                                         'temperature'       => ['sometimes', 'nullable', 'numeric'],
                                         'bloodPressureType' => ['sometimes', 'nullable', 'int'],
                                         'bloodPressure'     => ['sometimes', 'nullable', 'numeric'],
                                     ],
                                     [],
                                     [
                                         'caseCode'          => '病历编码',
                                         'petWeight'         => '宠物体重',
                                         'physical'          => '宠物体况',
                                         'heartbeat'         => '宠物心跳',
                                         'respiration'       => '宠物呼吸',
                                         'temperatureType'   => '体温测量部位',
                                         'temperature'       => '宠物体温',
                                         'bloodPressureType' => '血压测量部位',
                                         'bloodPressure'     => '宠物血压',
                                     ]);

        if ($validator->fails())
        {
            return outputJsonError(400,
                                   $validator->errors()
                                             ->first(),
                                   [
                                       'validatorError' => $validator->errors()
                                                                     ->all()
                                   ]);
        }

        $editParams   = $validator->validated();
        $publicParams = getRequestReservedParameters();

        // 获取病历信息
        $caseCode   = $editParams['caseCode'];
        $getCaseRes = CasesModel::getData(['id'],
                                          ['case_code' => $caseCode, 'hospital_id' => getPublicParamsHospitalId()]);
        $getCaseRes = $getCaseRes ? current($getCaseRes) : [];
        if (empty($getCaseRes))
        {
            return outputJsonError(38000, '病历不存在', ['error' => '病历不存在']);
        }

        $editVitalSignRes = CaseLogic::EditCasePetVitalSign($getCaseRes['id'], $editParams, $publicParams);
        if ($editVitalSignRes->isFail())
        {
            return outputJsonError($editVitalSignRes->getCode(),
                                   $editVitalSignRes->getMessage(),
                                   ['error' => $editVitalSignRes->getMessage()]);
        }

        return outputJsonResult();
    }

    /**
     * 编辑历史病历宠物体征信息
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws Throwable
     * @throws ValidationException
     * @noinspection PhpUnused
     */
    public function EditHistoryPetVitalSign(Request $request): JsonResponse
    {
        // 验证参数有效性
        $validator = Validator::make(data      : $request->post(),
                                     rules     : [
                                                     'caseCode'          => ['required', 'string'],
                                                     'petWeight'         => ['sometimes', 'nullable', 'numeric'],
                                                     'physical'          => ['sometimes', 'nullable', 'int'],
                                                     'heartbeat'         => ['sometimes', 'nullable', 'int'],
                                                     'respiration'       => ['sometimes', 'nullable', 'int'],
                                                     'temperatureType'   => ['sometimes', 'nullable', 'int'],
                                                     'temperature'       => ['sometimes', 'nullable', 'numeric'],
                                                     'bloodPressureType' => ['sometimes', 'nullable', 'int'],
                                                     'bloodPressure'     => ['sometimes', 'nullable', 'numeric'],
                                                 ],
                                     attributes: [
                                                     'caseCode'          => '病历编码',
                                                     'petWeight'         => '宠物体重',
                                                     'physical'          => '宠物体况',
                                                     'heartbeat'         => '宠物心跳',
                                                     'respiration'       => '宠物呼吸',
                                                     'temperatureType'   => '宠物体温，测量部位',
                                                     'temperature'       => '宠物体温',
                                                     'bloodPressureType' => '宠物血压，测量部位',
                                                     'bloodPressure'     => '宠物血压'
                                                 ]);

        if ($validator->fails())
        {
            return outputJsonError(400,
                                   $validator->errors()
                                             ->first(),
                                   [
                                       'validatorError' => $validator->errors()
                                                                     ->all()
                                   ]);
        }

        $editParams   = $validator->validated();
        $publicParams = getRequestReservedParameters();

        // 获取历史病历信息
        $caseCode   = $editParams['caseCode'];
        $getCaseRes = CaseSnapshotModel::getData(['case_id'],
                                                 [
                                                     'case_code' => $caseCode,
                                                     'status'    => 1
                                                 ]);
        $getCaseRes = $getCaseRes ? current($getCaseRes) : [];
        if (empty($getCaseRes))
        {
            return outputJsonError(38000, '历史病历不存在', ['error' => '历史病历不存在']);
        }

        $editHistoryVitalSignRes = CaseLogic::EditHistoryCasePetVitalSign($getCaseRes['case_id'],
                                                                          $editParams,
                                                                          $publicParams);
        if ($editHistoryVitalSignRes->isFail())
        {
            return outputJsonError($editHistoryVitalSignRes->getCode(),
                                   $editHistoryVitalSignRes->getMessage(),
                                   ['error' => $editHistoryVitalSignRes->getMessage()]);
        }

        return outputJsonResult();
    }

    /**
     * 编辑病历诊断信息
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws Throwable
     * @throws ValidationException
     * @noinspection PhpUnused
     */
    public function EditCaseDiagnose(Request $request): JsonResponse
    {
        $validator = Validator::make($request->post(),
                                     [
                                         'caseCode'            => ['required', 'string'],
                                         'pastIllness'         => ['sometimes', 'nullable', 'string'],
                                         'presentIllness'      => ['sometimes', 'nullable', 'string'],
                                         'chiefComplaint'      => ['sometimes', 'nullable', 'string'],
                                         'clinicalExamination' => ['sometimes', 'nullable', 'string'],
                                         'diseaseId'           => ['sometimes', 'nullable', 'numeric'],
                                         'secondDiseaseId'     => ['sometimes', 'nullable', 'numeric'],
                                         'otherDisease'        => ['sometimes', 'nullable', 'string'],
                                         'diagnose'            => ['sometimes', 'nullable', 'string'],
                                         'medicalOrders'       => ['sometimes', 'nullable', 'string']
                                     ],
                                     [],
                                     [
                                         'caseCode'            => '病历编码',
                                         'pastIllness'         => '既往病史',
                                         'presentIllness'      => '现在病史',
                                         'chiefComplaint'      => '主述',
                                         'clinicalExamination' => '临床检查',
                                         'diseaseId'           => '诊断疾病分类',
                                         'secondDiseaseId'     => '诊断第二疾病分类',
                                         'otherDisease'        => '诊断其他疾病',
                                         'diagnose'            => '医生诊断',
                                         'medicalOrders'       => '医嘱'
                                     ]);

        if ($validator->fails())
        {
            return outputJsonError(400,
                                   $validator->errors()
                                             ->first(),
                                   [
                                       'validatorError' => $validator->errors()
                                                                     ->all()
                                   ]);
        }

        $editParams   = $validator->validated();
        $publicParams = getRequestReservedParameters();

        // 获取病历信息
        $caseCode   = $editParams['caseCode'];
        $getCaseRes = CasesModel::getData(['id'],
                                          ['case_code' => $caseCode, 'hospital_id' => getPublicParamsHospitalId()]);
        $getCaseRes = $getCaseRes ? current($getCaseRes) : [];
        if (empty($getCaseRes))
        {
            return outputJsonError(38000, '病历不存在', ['error' => '病历不存在']);
        }

        $editDiagnoseRes = CaseLogic::EditCaseDiagnose($getCaseRes['id'], $editParams, $publicParams);
        if ($editDiagnoseRes->isFail())
        {
            return outputJsonError($editDiagnoseRes->getCode(),
                                   $editDiagnoseRes->getMessage(),
                                   ['error' => $editDiagnoseRes->getMessage()]);
        }

        return outputJsonResult();
    }

    /**
     * 编辑历史病历诊断信息
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws Throwable
     * @throws ValidationException
     * @noinspection PhpUnused
     */
    public function EditHistoryCaseDiagnose(Request $request): JsonResponse
    {
        $validator = Validator::make($request->post(),
                                     [
                                         'caseCode'            => ['required', 'string'],
                                         'pastIllness'         => ['sometimes', 'nullable', 'string'],
                                         'presentIllness'      => ['sometimes', 'nullable', 'string'],
                                         'chiefComplaint'      => ['sometimes', 'nullable', 'string'],
                                         'clinicalExamination' => ['sometimes', 'nullable', 'string'],
                                         'diseaseId'           => ['sometimes', 'nullable', 'numeric'],
                                         'secondDiseaseId'     => ['sometimes', 'nullable', 'numeric'],
                                         'otherDisease'        => ['sometimes', 'nullable', 'string'],
                                         'diagnose'            => ['sometimes', 'nullable', 'string'],
                                         'medicalOrders'       => ['sometimes', 'nullable', 'string']
                                     ],
                                     [],
                                     [
                                         'caseCode'            => '病历编码',
                                         'pastIllness'         => '既往病史',
                                         'presentIllness'      => '现在病史',
                                         'chiefComplaint'      => '主述',
                                         'clinicalExamination' => '临床检查',
                                         'diseaseId'           => '诊断疾病分类',
                                         'secondDiseaseId'     => '诊断第二疾病分类',
                                         'otherDisease'        => '诊断其他疾病',
                                         'diagnose'            => '医生诊断',
                                         'medicalOrders'       => '医嘱'
                                     ]);

        if ($validator->fails())
        {
            return outputJsonError(400,
                                   $validator->errors()
                                             ->first(),
                                   [
                                       'validatorError' => $validator->errors()
                                                                     ->all()
                                   ]);
        }

        $editParams   = $validator->validated();
        $publicParams = getRequestReservedParameters();

        // 获取病历信息
        $caseCode   = $editParams['caseCode'];
        $getCaseRes = CaseSnapshotModel::getData(['case_id'],
                                                 [
                                                     'case_code' => $caseCode,
                                                     'status'    => 1
                                                 ]);
        $getCaseRes = $getCaseRes ? current($getCaseRes) : [];
        if (empty($getCaseRes))
        {
            return outputJsonError(38000, '病历不存在', ['error' => '病历不存在']);
        }

        $editHistoryDiagnoseRes = CaseLogic::EditHistoryCaseDiagnose($getCaseRes['case_id'],
                                                                     $editParams,
                                                                     $publicParams);
        if ($editHistoryDiagnoseRes->isFail())
        {
            return outputJsonError($editHistoryDiagnoseRes->getCode(),
                                   $editHistoryDiagnoseRes->getMessage(),
                                   ['error' => $editHistoryDiagnoseRes->getMessage()]);
        }

        return outputJsonResult();
    }

    /**
     * 获取疾病分类
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws ValidationException
     * @noinspection PhpUnused
     */
    public function GetDiseaseCategory(Request $request): JsonResponse
    {
        $validator = Validator::make($request->post(),
                                     [
                                         'caseCode' => 'required|string',
                                     ],
                                     [],
                                     [
                                         'caseCode' => '病历编码',
                                     ]);

        if ($validator->fails())
        {
            return outputJsonError(400,
                                   $validator->errors()
                                             ->first(),
                                   [
                                       'validatorError' => $validator->errors()
                                                                     ->all()
                                   ]);
        }

        $caseCode     = $validator->validated()['caseCode'];
        $publicParams = getRequestReservedParameters();

        $getCaseRes = CasesModel::getData(['id'], ['case_code' => $caseCode]);
        $getCaseRes = $getCaseRes ? current($getCaseRes) : [];
        if (empty($getCaseRes))
        {
            return outputJsonError(38000, '病历不存在', ['error' => '病历不存在']);
        }

        $getDiseaseCategoryRes = CaseLogic::GetDiseaseCategory($getCaseRes['id'], $publicParams);
        if ($getDiseaseCategoryRes->isFail())
        {
            return outputJsonError($getDiseaseCategoryRes->getCode(),
                                   $getDiseaseCategoryRes->getMessage(),
                                   ['error' => $getDiseaseCategoryRes->getMessage()]);
        }

        return outputJsonResult($getDiseaseCategoryRes->getData());
    }

    /**
     * 获取诊断结果
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws ValidationException
     * @noinspection PhpUnused
     */
    public function GetTreatmentOutcome(Request $request): JsonResponse
    {
        $validator = Validator::make($request->post(),
                                     [
                                         'caseCode' => 'required|string',
                                     ],
                                     [],
                                     [
                                         'caseCode' => '病历编码',
                                     ]);

        if ($validator->fails())
        {
            return outputJsonError(400,
                                   $validator->errors()
                                             ->first(),
                                   [
                                       'validatorError' => $validator->errors()
                                                                     ->all()
                                   ]);
        }

        $caseCode     = $validator->validated()['caseCode'];
        $publicParams = getRequestReservedParameters();

        $getCaseRes = CasesModel::getData(['id'],
                                          ['case_code' => $caseCode, 'hospital_id' => getPublicParamsHospitalId()]);
        $getCaseRes = $getCaseRes ? current($getCaseRes) : [];
        if (empty($getCaseRes))
        {
            return outputJsonError(38000, '病历不存在', ['error' => '病历不存在']);
        }

        $getTreatmentOutcomeRes = CaseLogic::getEndCaseTreatmentOutcome($getCaseRes['id'], $publicParams);
        if ($getTreatmentOutcomeRes->isFail())
        {
            return outputJsonError($getTreatmentOutcomeRes->getCode(),
                                   $getTreatmentOutcomeRes->getMessage(),
                                   ['error' => $getTreatmentOutcomeRes->getMessage()]);
        }

        return outputJsonResult($getTreatmentOutcomeRes->getData());
    }

    /**
     * 获取宠物历史病历
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws ValidationException
     * @noinspection PhpUnused
     */
    public function GetHistoryCases(Request $request): JsonResponse
    {
        $validator = Validator::make($request->post(),
                                     [
                                         'petUid' => ['required', 'string'],
                                         'page'   => ['sometimes', 'nullable', 'required', 'integer'],
                                         'count'  => ['sometimes', 'nullable', 'required', 'integer'],
                                     ],
                                     [],
                                     [
                                         'petUid' => '宠物UID',
                                         'page'   => '页码',
                                         'count'  => '数量',
                                     ]);

        if ($validator->fails())
        {
            return outputJsonError(400,
                                   $validator->errors()
                                             ->first(),
                                   [
                                       'validatorError' => $validator->errors()
                                                                     ->all()
                                   ]);
        }

        $petUid    = $validator->validated()['petUid'];
        $iPage     = $validator->validated()['page'] ?? PageEnum::DefaultPageIndex->value;
        $iPageSize = $validator->validated()['count'] ?? PageEnum::DefaultPageSize->value;

        // 获取宠物信息
        $getPetRes = PetLogic::GetValidPetByIdOrUid(petUid: $petUid);
        if ($getPetRes->isFail())
        {
            return outputJsonError($getPetRes->getCode(),
                                   $getPetRes->getMessage(),
                                   ['error' => $getPetRes->getMessage()]);
        }

        $petId = $getPetRes->getData('id');
        if (empty($petId))
        {
            return outputJsonError(32000, '宠物不存在', ['error' => '宠物不存在']);
        }

        $getHistoryCasesRes = CaseLogic::GetHistoryCasesByPetId($petId,
                                                                getRequestReservedParameters(),
                                                                $iPage,
                                                                $iPageSize);
        if ($getHistoryCasesRes->isFail())
        {
            return outputJsonError($getHistoryCasesRes->getCode(),
                                   $getHistoryCasesRes->getMessage(),
                                   ['error' => $getHistoryCasesRes->getMessage()]);
        }

        return outputJsonResult($getHistoryCasesRes->getData());
    }

    /**
     * 获取历史病历详情
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws ValidationException
     * @noinspection PhpUnused
     */
    public function GetHistoryCaseDetail(Request $request): JsonResponse
    {
        $validator = Validator::make($request->post(),
                                     [
                                         'caseCode' => ['required', 'string'],
                                     ],
                                     [],
                                     [
                                         'caseCode' => '病历编码',
                                     ]);
        if ($validator->fails())
        {
            return outputJsonError(400,
                                   $validator->errors()
                                             ->first(),
                                   [
                                       'validatorError' => $validator->errors()
                                                                     ->all()
                                   ]);
        }

        // 获取病历信息
        $caseCode   = $validator->validated()['caseCode'];
        $getCaseRes = CaseSnapshotModel::getData(['case_id'], ['case_code' => $caseCode]);
        $getCaseRes = $getCaseRes ? current($getCaseRes) : [];
        if (empty($getCaseRes))
        {
            return outputJsonError(38000, '历史病历详情不存在', ['error' => '历史病历详情不存在']);
        }

        // 获取处方详情
        $getHistoryCaseDetailRes = CaseLogic::GetHistoryCaseDetail($getCaseRes['case_id'],
                                                                   getRequestReservedParameters());
        if ($getHistoryCaseDetailRes->isFail())
        {
            return outputJsonError($getHistoryCaseDetailRes->getCode(),
                                   $getHistoryCaseDetailRes->getMessage(),
                                   ['error' => $getHistoryCaseDetailRes->getMessage()]);
        }

        return outputJsonResult($getHistoryCaseDetailRes->getData());
    }

    /**
     * 获取病历汇总
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws ValidationException
     * @noinspection PhpUnused
     */
    public function GetCaseSummary(Request $request): JsonResponse
    {
        $validator = Validator::make($request->post(),
                                     [
                                         'caseCode' => ['required', 'string'],
                                     ],
                                     [],
                                     [
                                         'caseCode' => '病历编码',
                                     ]);

        if ($validator->fails())
        {
            return outputJsonError(400,
                                   $validator->errors()
                                             ->first(),
                                   [
                                       'validatorError' => $validator->errors()
                                                                     ->all()
                                   ]);
        }

        $caseCode   = $validator->validated()['caseCode'];
        $getCaseRes = CasesModel::getData(['id'],
                                          ['case_code' => $caseCode, 'hospital_id' => getPublicParamsHospitalId()]);
        $getCaseRes = $getCaseRes ? current($getCaseRes) : [];
        if (empty($getCaseRes))
        {
            return outputJsonError(38000, '病历不存在', ['error' => '病历不存在']);
        }

        $getCaseSummaryRes = CaseLogic::GetCaseSummary($getCaseRes['id'], getRequestReservedParameters());
        if ($getCaseSummaryRes->isFail())
        {
            return outputJsonError($getCaseSummaryRes->getCode(),
                                   $getCaseSummaryRes->getMessage(),
                                   ['error' => $getCaseSummaryRes->getMessage()]);
        }

        return outputJsonResult($getCaseSummaryRes->getData());
    }
}
