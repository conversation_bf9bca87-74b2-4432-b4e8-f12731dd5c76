<?php

namespace App\Http\Controllers\V1;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rules\Enum;
use Illuminate\Validation\ValidationException;
use App\Enums\SheetBusinessTypeEnum;
use App\Enums\DiscountTypeEnum;
use App\Logics\V1\MemberLogic;
use App\Logics\V1\UserLogic;
use App\Logics\V1\BuySheet\SheetUnionLogic;
use App\Logics\V1\CheckoutLogic;

class CheckoutController extends Controller
{
    /**
     * 待支付购买单筛选项
     *
     * @param Request $request
     *
     * @return JsonResponse
     */
    public function UnpaidFilterOptions(Request $request): JsonResponse
    {
        $publicParams = getRequestReservedParameters();

        $typeOptions = SheetBusinessTypeEnum::caseToOptions(key: 'uid');

        $membersRes = SheetUnionLogic::GetSheetMemberOptions($publicParams);
        if ($membersRes->isFail())
        {
            return outputJsonError($membersRes->getCode(), '', ['error' => $membersRes->getMessage()]);
        }

        $usersRes = SheetUnionLogic::GetCreateUsersOptions($publicParams);
        if ($usersRes->isFail())
        {
            return outputJsonError($usersRes->getCode(), '', ['error' => $usersRes->getMessage()]);
        }

        return outputJsonResult([
                                    'typeOptions'        => $typeOptions,
                                    'memberOptions'      => $membersRes->getData(),
                                    'createUsersOptions' => $usersRes->getData(),
                                ]);
    }

    /**
     * 待支付购买单列表
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws ValidationException
     */
    public function UnpaidList(Request $request): JsonResponse
    {
        //验证必传参数
        $validator = Validator::make(
            $request->post(),
            [
                'memberUid'     => 'sometimes|nullable|string|between:8,64',
                'createUserUid' => 'sometimes|nullable|string|between:8,64',
                'type'          => ['sometimes', 'nullable', 'string', new Enum(SheetBusinessTypeEnum::class)],
                'startDate'     => 'sometimes|nullable|date',
                'endDate'       => 'sometimes|nullable|date',
            ],
            [],
            [
                'memberUid'     => '会员UID',
                'createUserUid' => '开单人UID',
                'type'          => '业务类型',
                'startDate'     => '开单开始日期',
                'endDate'       => '开单结束日期',
            ]
        );

        if ($validator->fails())
        {
            return outputJsonError(400,
                                   $validator->errors()
                                             ->first(),
                                   [
                                       'validatorError' => $validator->errors()
                                                                     ->all()
                                   ]);
        }

        $params       = $validator->validated();
        $publicParams = getRequestReservedParameters();

        // 验证UID并转换ID
        $memberUid     = trim($params['memberUid'] ?? '');
        $createUserUid = trim($params['createUserUid'] ?? '');
        if (!empty($memberUid))
        {
            $getMemberRes = MemberLogic::GetValidMemberByIdOrUid(memberUid: $memberUid);
            if ($getMemberRes->isFail())
            {
                return outputJsonError($getMemberRes->getCode(), '', ['error' => $getMemberRes->getMessage()]);
            }
            $params['memberId'] = $getMemberRes->getData('id');
        }
        if (!empty($createUserUid))
        {
            $getUserRes = UserLogic::GetValidUserByIdOrUid(userUid: $createUserUid);
            if ($getUserRes->isFail())
            {
                return outputJsonError($getUserRes->getCode(), '', ['error' => $getUserRes->getMessage()]);
            }
            $params['createUserId'] = $getUserRes->getData('id');
        }

        $unpaidListResult = SheetUnionLogic::GetUnpaidSheetList($params, $publicParams);
        if ($unpaidListResult->isFail())
        {
            return outputJsonError($unpaidListResult->getCode(),
                                   $unpaidListResult->getMessage(),
                                   ['error' => $unpaidListResult->getMessage()]);
        }

        return outputJsonResult($unpaidListResult->getData());
    }

    /**
     * 结算准备
     *
     * 提交待结算的购买单，返回结算信息
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws ValidationException
     */
    public function Prepare(Request $request): JsonResponse
    {
        //验证必传参数
        $validator = Validator::make(
            $request->post(),
            [
                'sheets'     => 'required|array',
                'totalPrice' => 'required|numeric|min:0',
            ],
            [],
            [
                'sheets'     => '购买单列表',
                'totalPrice' => '总金额',
            ]
        );

        if ($validator->fails())
        {
            return outputJsonError(400,
                                   $validator->errors()
                                             ->first(),
                                   [
                                       'validatorError' => $validator->errors()
                                                                     ->all()
                                   ]);
        }

        $params       = $validator->validated();
        $publicParams = getRequestReservedParameters();

        $prepareResult = CheckoutLogic::CheckoutPrepare($params, $publicParams);
        if ($prepareResult->isFail())
        {
            return outputJsonError($prepareResult->getCode(),
                                   $prepareResult->getMessage(),
                                   ['error' => $prepareResult->getMessage()]);
        }

        return outputJsonResult($prepareResult->getData());
    }

    /**
     * 结算
     *
     * 结算生成支付单
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws ValidationException
     */
    public function Checkout(Request $request): JsonResponse
    {
        //验证必传参数
        $validator = Validator::make(
            $request->post(),
            [
                'sheets'             => 'required|array',
                'totalPrice'         => 'required|numeric|min:0',
                'activityPrice'      => 'required|numeric|min:0',
                'couponPrice'        => 'required|numeric|min:0',
                'discountPrice'      => 'required|numeric|min:0',
                'thirdPartyPayPrice' => 'required|numeric|min:0',
                'balancePayPrice'    => 'required|numeric|min:0',
                'cashPayPrice'       => 'required|numeric|min:0',
                //折扣
                'discount'           => 'sometimes|nullable|array',
                'discount.type'      => ['required_with:discount', 'integer', new Enum(DiscountTypeEnum::class)],
                'discount.rate'      => 'required_with:discount|numeric|min:0|max:9.9',
                'discount.price'     => 'required_with:discount|numeric|min:0.01',
                'discount.reason'    => 'required_with:discount|string|between:2,100',
                //第三方支付
                'thirdParty'         => 'sometimes|nullable|array',
                'thirdParty.type'    => 'required_with:thirdParty|integer|min:1',
                'thirdParty.price'   => 'required_with:thirdParty|numeric|min:0.01',
                'thirdParty.remark'  => 'required_with:thirdParty|string|between:2,64',
                //余额
                'balance'            => 'sometimes|nullable|array',
                'balance.use'        => 'required_with:balance|boolean',
                'balance.price'      => 'required_with:balance|numeric|min:0.01',
                //现金
                'cash'               => 'sometimes|nullable|array',
                'cash.channel'       => 'required_with:cash|integer|min:1',
                'cash.mode'          => 'required_with:cash|integer|min:1',
                'cash.price'         => 'required_with:cash|numeric|min:0.01',
            ],
            [],
            [
                'sheets'             => '购买单列表',
                'totalPrice'         => '总金额',
                'activityPrice'      => '活动优惠金额',
                'couponPrice'        => '代金券/现金券优惠金额',
                'discountPrice'      => '折扣优惠金额',
                'thirdPartyPayPrice' => '第三方支付金额',
                'balancePayPrice'    => '余额支付金额',
                'cashPayPrice'       => '现付款支付金额',
                //折扣
                'discount'           => '折扣信息',
                'discount.type'      => '折扣类型',
                'discount.rate'      => '折扣率',
                'discount.price'     => '折扣金额',
                'discount.reason'    => '折扣原因',
                //第三方支付
                'thirdParty'         => '第三方支付信息',
                'thirdParty.type'    => '第三方支付类型',
                'thirdParty.price'   => '第三方支付金额',
                'thirdParty.remark'  => '第三方支付备注',
                //余额
                'balance'            => '余额信息',
                'balance.use'        => '是否使用余额',
                'balance.price'      => '余额支付金额',
                //现付款
                'cash'               => '现付款信息',
                'cash.channel'       => '现付款支付渠道',
                'cash.mode'          => '现付款支付方式',
                'cash.price'         => '现付款支付金额',
            ]
        );

        if ($validator->fails())
        {
            return outputJsonError(400,
                                   $validator->errors()
                                             ->first(),
                                   [
                                       'validatorError' => $validator->errors()
                                                                     ->all()
                                   ]);
        }

        $params       = $validator->validated();
        $publicParams = getRequestReservedParameters();

        $doResult = CheckoutLogic::DoCheckout($params, $publicParams);
        if ($doResult->isFail())
        {
            return outputJsonError($doResult->getCode(),
                                   $doResult->getMessage(),
                                   ['error' => $doResult->getMessage()]);
        }

        return outputJsonResult($doResult->getData());
    }
}
