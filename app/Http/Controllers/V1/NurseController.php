<?php

namespace App\Http\Controllers\V1;

use Throwable;
use Validator;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\ValidationException;
use App\Http\Controllers\Controller;
use App\Enums\PageEnum;
use App\Enums\NurseExecutionStatusEnum;
use App\Models\UsersModel;
use App\Logics\V1\NurseLogic;
use App\Models\NurseModel;

/**
 * 处置相关
 * Class ImageController
 * @package      App\Http\Controllers\V1
 * @noinspection PhpUnused
 */
class NurseController extends Controller
{
    /**
     * 处置列表筛选项
     *
     * @return JsonResponse
     * @noinspection PhpUnused
     */
    public function GetNurseFilterOptions(): JsonResponse
    {
        $getNurseFilterOptionsRes = NurseLogic::GetNurseFilterOptions(getRequestReservedParameters());
        if ($getNurseFilterOptionsRes->isFail())
        {
            return outputJsonError($getNurseFilterOptionsRes->getCode(),
                                   $getNurseFilterOptionsRes->getMessage(),
                                   ['error' => $getNurseFilterOptionsRes->getMessage()]);
        }

        return outputJsonResult($getNurseFilterOptionsRes->getData());
    }

    /**
     * 处置列表
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws ValidationException
     */
    public function Lists(Request $request): JsonResponse
    {
        $validator = Validator::make($request->post(),
                                     [
                                         'status'    => ['required', Rule::in(NurseExecutionStatusEnum::values())], // 0:待执行 1:已执行
                                         'keywords'  => ['sometimes', 'nullable', 'string'],
                                         'caseCode'  => ['sometimes', 'nullable', 'string'],
                                         'nurseCode' => ['sometimes', 'nullable', 'string'],
                                         'name'      => ['sometimes', 'nullable', 'string'],
                                         'startDate' => ['sometimes', 'nullable', 'date'],
                                         'endDate'   => ['sometimes', 'nullable', 'date'],
                                         'doctorUid' => ['sometimes', 'nullable', 'string'],
                                         'page'      => ['integer'],
                                         'count'     => ['integer'],
                                     ]);
        if ($validator->fails())
        {
            return outputJsonError(400,
                                   $validator->errors()
                                             ->first(),
                                   [
                                       'validatorError' => $validator->errors()
                                                                     ->all()
                                   ]);
        }

        $searchParams = $validator->validated();
        $iPage        = $searchParams['page'] ?? PageEnum::DefaultPageIndex->value;
        $iPageSize    = $searchParams['count'] ?? PageEnum::DefaultPageSize->value;

        $getNurseListRes = NurseLogic::GetNurseList($searchParams, getRequestReservedParameters(), $iPage, $iPageSize);
        if ($getNurseListRes->isFail())
        {
            return outputJsonError($getNurseListRes->getCode(),
                                   $getNurseListRes->getMessage(),
                                   ['error' => $getNurseListRes->getMessage()]);
        }

        return outputJsonResult($getNurseListRes->getData());
    }

    /**
     * 开始检测处置
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws ValidationException|Throwable
     * @noinspection PhpUnused
     */
    public function StartExecution(Request $request): JsonResponse
    {
        $validator = Validator::make($request->post(),
                                     [
                                         'nurseCode' => ['required', 'string'],
                                         'executor1' => ['sometimes', 'nullable', 'string'],
                                         'executor2' => ['sometimes', 'nullable', 'string'],
                                         'executor3' => ['sometimes', 'nullable', 'string'],
                                     ],
                                     [
                                         'nurseCode.required' => '处置编码，必选参数错误',
                                         'nurseCode.string'   => '处置编码，参数类型错误',
                                     ]);
        if ($validator->fails())
        {
            return outputJsonError(400,
                                   $validator->errors()
                                             ->first(),
                                   [
                                       'validatorError' => $validator->errors()
                                                                     ->all()
                                   ]);
        }

        // 开始检测参数
        $validatedData   = $validator->validated();
        $nurseCode       = $validatedData['nurseCode'];
        $oneExecutorId   = $validatedData['executor1'] ?? '';
        $twoExecutorId   = $validatedData['executor2'] ?? '';
        $threeExecutorId = $validatedData['executor3'] ?? '';

        // 执行人全部不存在
        if (empty($oneExecutorId) && empty($twoExecutorId) && empty($threeExecutorId))
        {
            return outputJsonError(40202, '处置执行人必选', ['error' => '处置执行人必选']);
        }

        // 执行人级别对应关系
        $executorUids = [
            1 => $oneExecutorId,
            2 => $twoExecutorId,
            3 => $threeExecutorId,
        ];

        // 获取处置信息
        $getNurseRes = NurseModel::getData(['id'], ['nurse_code' => $nurseCode]);
        $getNurseRes = $getNurseRes ? current($getNurseRes) : [];
        if (empty($getNurseRes))
        {
            return outputJsonError(40201, '处置不存在', ['error' => '处置不存在']);
        }

        // 获取处置执行人基本信息
        $getExecutorRes = UsersModel::getData(whereIn: ['uid' => array_filter($executorUids)]);
        if (empty($getExecutorRes))
        {
            return outputJsonError(40202, '处置执行人不存在', ['error' => '处置执行人不存在']);
        }

        $uids            = array_column($getExecutorRes, 'uid');
        $diffExecutorIds = array_diff(array_filter($executorUids), $uids);
        if (!empty($diffExecutorIds))
        {
            return outputJsonError(40202, '当前选择部分执行人信息不存在，请重新选择');
        }

        // 每级执行人对应执行人ID
        $getExecutorRes = array_column($getExecutorRes, null, 'uid');
        $executorIds    = [
            1 => $getExecutorRes[$oneExecutorId]['id'] ?? 0,
            2 => $getExecutorRes[$twoExecutorId]['id'] ?? 0,
            3 => $getExecutorRes[$threeExecutorId]['id'] ?? 0,
        ];

        // 开始执行
        $getStartExecutionRes = NurseLogic::StartExecution($getNurseRes['id'], $executorIds, getRequestReservedParameters());
        if ($getStartExecutionRes->isFail())
        {
            return outputJsonError($getStartExecutionRes->getCode(),
                                   $getStartExecutionRes->getMessage(),
                                   ['error' => $getStartExecutionRes->getMessage()]);

        }

        return outputJsonResult();
    }

    /**
     * 处置执行人跟诊
     *
     * @param Request $request
     *
     * @return JsonResponse
     * @throws ValidationException
     * @throws Throwable
     * @noinspection PhpUnused
     */
    public function Executor(Request $request): JsonResponse
    {
        $validator = Validator::make($request->post(),
                                     [
                                         'nurseCode' => ['required', 'string'],
                                         'executor1' => ['sometimes', 'nullable', 'string'],
                                         'executor2' => ['sometimes', 'nullable', 'string'],
                                         'executor3' => ['sometimes', 'nullable', 'string'],
                                     ],
                                     [
                                         'nurseCode.required' => '处置编码，必选参数错误',
                                         'nurseCode.string'   => '处置编码，参数类型错误',
                                     ]);
        if ($validator->fails())
        {
            return outputJsonError(400,
                                   $validator->errors()
                                             ->first(),
                                   [
                                       'validatorError' => $validator->errors()
                                                                     ->all()
                                   ]);
        }

        // 跟诊执行参数
        $validatedData   = $validator->validated();
        $nurseCode       = $validatedData['nurseCode'];
        $oneExecutorId   = $validatedData['executor1'] ?? '';
        $twoExecutorId   = $validatedData['executor2'] ?? '';
        $threeExecutorId = $validatedData['executor3'] ?? '';

        // 执行人全部不存在
        if (empty($oneExecutorId) && empty($twoExecutorId) && empty($threeExecutorId))
        {
            return outputJsonError(40202, '处置执行人必选', ['error' => '处置执行人必选']);
        }

        // 执行人级别对应关系
        $executorUids = [
            1 => $oneExecutorId,
            2 => $twoExecutorId,
            3 => $threeExecutorId,
        ];

        // 获取处置信息
        $getNurseRes = NurseModel::getData(['id'], ['nurse_code' => $nurseCode]);
        $getNurseRes = $getNurseRes ? current($getNurseRes) : [];
        if (empty($getNurseRes))
        {
            return outputJsonError(40201, '处置不存在', ['error' => '处置不存在']);
        }

        // 获取处置执行人基本信息
        $getExecutorRes = UsersModel::getData(whereIn: ['uid' => array_filter($executorUids)]);
        if (empty($getExecutorRes))
        {
            return outputJsonError(40202, '处置执行人不存在', ['error' => '处置执行人不存在']);
        }

        $uids            = array_column($getExecutorRes, 'uid');
        $diffExecutorIds = array_diff(array_filter($executorUids), $uids);
        if (!empty($diffExecutorIds))
        {
            return outputJsonError(40202, '当前跟诊选择部分执行人信息不存在，请重新选择');
        }

        // 每级执行人对应执行人ID
        $getExecutorRes = array_column($getExecutorRes, null, 'uid');
        $executorIds    = [
            1 => $getExecutorRes[$oneExecutorId]['id'] ?? 0,
            2 => $getExecutorRes[$twoExecutorId]['id'] ?? 0,
            3 => $getExecutorRes[$threeExecutorId]['id'] ?? 0,
        ];

        // 录入执行人
        $followExecutorRes = NurseLogic::NurseFollowExecutor($getNurseRes['id'], $executorIds, getRequestReservedParameters());
        if ($followExecutorRes->isFail())
        {
            return outputJsonError($followExecutorRes->getCode(),
                                   $followExecutorRes->getMessage(),
                                   ['error' => $followExecutorRes->getMessage()]);

        }

        return outputJsonResult();
    }
}
