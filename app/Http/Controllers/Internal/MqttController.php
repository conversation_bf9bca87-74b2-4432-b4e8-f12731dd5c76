<?php

namespace App\Http\Controllers\Internal;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use App\Http\Controllers\Controller;
use App\Logics\Internal\MqttAuthLogic;
use App\Support\Mqtt\MqttHttpHelper;

class MqttController extends Controller
{
    public function Login(Request $request): JsonResponse
    {
        // 获取原始请求体字符串
        $jsonString = $request->getContent();

        if (!json_validate($jsonString))
        {
            return MqttHttpHelper::MqttHttpResponse(status: 0);
        }

        $json = json_decode($jsonString, true);

        if (!isset($json['username']) || !isset($json['password']))
        {
            return MqttHttpHelper::MqttHttpResponse(status: 0);
        }

        $checkRes = MqttAuthLogic::CheckLogin($json['username'], $json['password']);
        if ($checkRes->isFail())
        {
            //真正的失败，返回拒绝
            if ($checkRes->getCode() == 500)
            {
                return MqttHttpHelper::MqttHttpResponse(status: 2);
            }

            return MqttHttpHelper::MqttHttpResponse(status: 0);
        }

        return MqttHttpHelper::MqttHttpResponse(status: 1);
    }

    public function Auth(Request $request): JsonResponse
    {
        // 获取原始请求体字符串
        $jsonString = $request->getContent();

        if (!json_validate($jsonString))
        {
            return MqttHttpHelper::MqttHttpResponse(status: 0);
        }

        $json = json_decode($jsonString, true);
        if (!isset($json['username']) || !isset($json['action']) || !isset($json['topic']))
        {
            return MqttHttpHelper::MqttHttpResponse(status: 0);
        }

        $checkRes = MqttAuthLogic::CheckAuth($json['username'], $json['action'], $json['topic']);
        if ($checkRes->isFail())
        {
            //真正的失败，返回拒绝
            if ($checkRes->getCode() == 500)
            {
                return MqttHttpHelper::MqttHttpResponse(status: 2);
            }

            return MqttHttpHelper::MqttHttpResponse(status: 0);
        }

        return MqttHttpHelper::MqttHttpResponse(status: 1);
    }
}
