<?php

namespace App\Enums;

enum MemberBalanceOperateResourceTypeEnum: string
{
    use EnumToolTrait;

    case Recharge = 'recharge';
    case Order = 'order';
    case Change = 'change';
    case TransferIn = 'transfer_in';
    case RechargeImport = 'recharge_import';
    case RechargeRefund = 'recharge_refund';
    case Extract = 'extract';
    case Cancel = 'cancel';
    case Refund = 'refund';
    case ChangeInit = 'change_init';
    case TransferOut = 'transfer_out';

    public const array DESCRIPTIONS = [
        self::Recharge->value       => '余额充值',
        self::Order->value          => '订单使用',
        self::Change->value         => '余额修正',
        self::TransferIn->value     => '帐户转入',
        self::RechargeImport->value => '余额充值',
        self::RechargeRefund->value => '充值退款',
        self::Extract->value        => '余额提现',
        self::Cancel->value         => '取消订单',
        self::Refund->value         => '订单退款',
        self::ChangeInit->value     => '期初余额',
        self::TransferOut->value    => '帐户转出',
    ];
}
