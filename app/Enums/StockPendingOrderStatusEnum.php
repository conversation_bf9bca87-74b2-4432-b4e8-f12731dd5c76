<?php

namespace App\Enums;

/**
 * 仓储-待出库单-出库状态
 */
enum StockPendingOrderStatusEnum: int
{
    use EnumToolTrait;

    // 自动出库中（待出库）
    case PendingDispatch = 0;

    case  CompletedDispatch = 1;

    case Refunded = 2;

    /**
     * 枚举值对应的描述
     *
     * @noinspection PhpUnused
     */
    public const array DESCRIPTIONS = [
        self::PendingDispatch->value   => '自动出库中',
        self::CompletedDispatch->value => '已自动出库',
        self::Refunded->value          => '已退款',
    ];
}
