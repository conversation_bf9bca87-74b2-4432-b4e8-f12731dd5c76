<?php

namespace App\Enums;

/**
 * 采购类型
 */
enum PurchaseTypeEnum: int
{
    use EnumToolTrait;

    case SelfPurchase = 1;

    case GroupPurchase = 2;

    case Transfer = 10;

    public const array DESCRIPTIONS = [
        self::SelfPurchase->value  => '自采',
        self::GroupPurchase->value => '集采',
        self::Transfer->value      => '调拨',
    ];

    public static function options(): array
    {
        return array_map(fn($item) => [
            'id'   => $item->value,
            'name' => $item->getDescription($item->value),
        ], self::cases());
    }

    public static function getPurchaseIsSelfPurchase(int $purchaseType): bool
    {
        return self::SelfPurchase->value == $purchaseType;
    }

    public static function getPurchaseIsGroupPurchase(int $purchaseType): bool
    {
        return self::GroupPurchase->value == $purchaseType;
    }

    public static function getPurchaseIsTransfer(int $purchaseType): bool
    {
        return self::Transfer->value == $purchaseType;
    }
}
