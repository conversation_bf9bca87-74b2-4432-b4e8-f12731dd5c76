<?php

namespace App\Enums;

/**
 * 库存出库状态（出库单中如果需要出库状态，最好值一致，可以共用）
 * 以下使用的业务表：
 * 1、stock_consumables_receive_order.outbound_status
 * 2、stock_refund.outbound_status
 */
enum StockOutboundStatusEnum: int
{
    use EnumToolTrait;

    case NotOutbound = 0;
    case PartiallyOutbound = 1;
    case FullyOutbound = 2;

    public const array DESCRIPTIONS = [
        self::NotOutbound->value       => '未出库',
        self::PartiallyOutbound->value => '部分出库',
        self::FullyOutbound->value     => '已全部出库',
    ];
}
