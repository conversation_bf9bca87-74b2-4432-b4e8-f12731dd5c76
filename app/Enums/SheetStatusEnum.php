<?php

namespace App\Enums;

/**
 * 购买单状态
 */
enum SheetStatusEnum: int
{
    use EnumToolTrait;

    case Cancelled = 0;
    case Unpaid = 1;
    case Paying = 2;
    case Paid = 3;

    public const array DESCRIPTIONS = [
        0 => '已取消',
        1 => '待结账',
        2 => '结账中',
        3 => '已结账',
    ];

    public static function EditAble(int $status): bool
    {
        return self::Unpaid->value == $status;
    }

    public static function DeleteAble(int $status): bool
    {
        return self::Unpaid->value == $status;
    }

    public static function PayAble(int $status): bool
    {
        return self::Unpaid->value == $status;
    }
}
