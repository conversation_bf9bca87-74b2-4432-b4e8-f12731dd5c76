<?php

namespace App\Enums;

/**
 * 挂号医生选择模式
 * 关联表：his_registrations.doctor_selected_type
 */
enum RegistrationDoctorSelectedTypeEnum: int
{
    use EnumToolTrait;

    case FrontDesk = 1;

    case DoctorSelected = 2;

    case TransferWithDoctor = 3;

    public const array DESCRIPTIONS = [
        self::FrontDesk->value          => '前台指定',
        self::DoctorSelected->value     => '医生主动接诊',
        self::TransferWithDoctor->value => '院内转诊指定',
    ];
}
