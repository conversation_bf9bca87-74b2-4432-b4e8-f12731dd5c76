<?php

namespace App\Enums;

/**
 * 宠物免疫状态
 */
enum PetPreventStatusEnum: int
{
    use EnumToolTrait;

    case Unknown = 0;

    case NotPrevent = 1;

    case Prevent = 2;

    /**
     * 枚举值对应的描述
     *
     * @noinspection PhpUnused
     */
    public const array DESCRIPTIONS = [
        self::Unknown->value    => '未知',
        self::NotPrevent->value => '未免疫',
        self::Prevent->value    => '已免疫',
    ];
}
