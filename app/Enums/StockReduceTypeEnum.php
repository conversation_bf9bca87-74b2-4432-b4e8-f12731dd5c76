<?php

namespace App\Enums;

/**
 * 仓储-库存动态-增加类型
 * 对应stock_reduce_type表
 */
enum StockReduceTypeEnum: int
{
    use EnumToolTrait;

    case OutpatientUse = 1;

    case Retail = 2;

    case ConsumablesUse = 3;

    case Transfer = 4;

    case Return = 5;

    case Inventory = 6;

    public const array DESCRIPTIONS = [
        self::OutpatientUse->value  => '门诊/住院使用出库',
        self::Retail->value         => '零售出库',
        self::ConsumablesUse->value => '耗材领用出库',
        self::Transfer->value       => '调拨出库',
        self::Return->value         => '退货出库',
        self::Inventory->value      => '盘点出库',
    ];

    public static function typeIsMustSubType(int $type): bool
    {
        return false;
    }

    /**
     * 出库类型是否必须填写备注
     *
     * @param int $type
     *
     * @return bool
     */
    public static function typeIsMustRemark(int $type): bool
    {
        return in_array($type, self::values());
    }

    /**
     * 可缺货出库类型（有多少出多少）
     * @return array
     */
    public static function getLooseStockTypes(): array
    {
        return [self::OutpatientUse->value, self::Retail->value, self::Return->value];
    }

    /**
     * 所有可选项（用于前端筛选）
     *
     * @return array
     */
    public static function options(): array
    {
        return array_map(fn($item) => [
            'id'   => $item->value,
            'name' => $item->getDescription($item->value),
        ], self::cases());
    }
}
