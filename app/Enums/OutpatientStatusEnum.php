<?php

namespace App\Enums;

/**
 * 门诊状态
 */
enum OutpatientStatusEnum: int
{
    use EnumToolTrait;

    case Waiting = 0;

    case InTreatment = 1;

    case Stop = 2;

    case Completed = 3;

    /**
     * 枚举值对应的描述
     *
     * @noinspection PhpUnused
     */
    public const array DESCRIPTIONS = [
        self::Waiting->value     => '候诊中',
        self::InTreatment->value => '就诊中',
        self::Stop->value        => '暂停中',
        self::Completed->value   => '结束',
    ];

    /**
     * 有效门诊状态
     */
    public const array VALID_STATUS = [
        self::Waiting->value,
        self::InTreatment->value,
        self::Stop->value
    ];
}
