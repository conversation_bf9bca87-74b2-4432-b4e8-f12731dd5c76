<?php

namespace App\Enums;

/**
 * 住院就诊状态
 */
enum InpatientTreatmentStatusEnum: int
{
    use EnumToolTrait;

    case Waiting = 0;

    case InTreatment = 1;

    case Stop = 2;

    case Completed = 3;

    /**
     * 枚举值对应的描述
     *
     * @noinspection PhpUnused
     */
    public const array DESCRIPTIONS = [
        self::Waiting->value     => '候诊中',
        self::InTreatment->value => '就诊中',
        self::Stop->value        => '已停诊',
        self::Completed->value   => '已结束',
    ];
}
