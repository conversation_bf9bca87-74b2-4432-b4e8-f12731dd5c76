<?php

namespace App\Enums;

/**
 * 采购单状态
 */
enum PurchaseOrderStatusEnum: int
{
    use EnumToolTrait;

    case Draft = 0;
    case Pending = 1;
    case Approved = 2;
    case Rejected = 3;
    case Cancelled = - 1;

    public const array DESCRIPTIONS = [
        self::Draft->value     => '草稿',
        self::Pending->value   => '待审核',
        self::Approved->value  => '审核通过',
        self::Rejected->value  => '驳回',
        self::Cancelled->value => '已作废',
    ];
}
