<?php

namespace App\Enums;

enum PayStatusEnum: int
{
    use EnumToolTrait;

    case Unpaid = 0;
    case Paid = 1;
    case Paying = 2;

    public const array DESCRIPTIONS = [
        0 => '未支付',
        1 => '已支付',
        2 => '支付中',
    ];

    /**
     * 验证是否可删除
     *
     * @param int $status
     *
     * @return bool
     */
    public static function DeleteAble(int $status): bool
    {
        return self::Unpaid->value == $status;
    }
}
