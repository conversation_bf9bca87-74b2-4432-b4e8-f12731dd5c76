<?php

namespace App\Enums;

/**
 * 宠物体重变动来源
 */
enum PetWeightSourceTypeEnum: int
{
    use EnumToolTrait;

    case Add = 1;

    case Edit = 2;

    case Registration = 3;

    case OutpatientCase = 4;

    case InpatientCase = 5;

    /**
     * 枚举值对应的描述
     * @noinspection PhpUnused
     */
    public const array DESCRIPTIONS = [
        self::Add->value            => '新增宠物',
        self::Edit->value           => '修改宠物',
        self::Registration->value   => '新增挂号',
        self::OutpatientCase->value => '门诊体况',
        self::InpatientCase->value  => '住院体况',
    ];
}
