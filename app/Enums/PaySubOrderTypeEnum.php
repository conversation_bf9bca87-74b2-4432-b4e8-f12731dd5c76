<?php

namespace App\Enums;

enum PaySubOrderTypeEnum: int
{
    use EnumToolTrait;


    case Registration = 1;
    case Recipe = 2;
    case Retail = 3;
    case Beauty = 4;
    case Recharge = 5;

    public const array DESCRIPTIONS = [
        self::Registration->value => '挂号',
        self::Recipe->value       => '处方',
        self::Retail->value       => '零售',
        self::Beauty->value       => '洗美',
        self::Recharge->value     => '充值',
    ];

    public static function IsMedicalOrder(int $type): bool
    {
        return in_array($type, [
            self::Registration->value,
            self::Recipe->value,
        ]);
    }
}
