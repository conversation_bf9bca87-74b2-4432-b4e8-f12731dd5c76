<?php

namespace App\Enums;

/**
 * 处置执行状态
 * Class NurseExecutionStatusEnum
 * @package App\Enums
 */
enum NurseExecutionStatusEnum: int
{

    use EnumToolTrait;

    case NotExecuted = 0;

    case Executed = 1;

    case Invalid = 2;

    public const array DESCRIPTIONS = [
        self::NotExecuted->value => '未执行',
        self::Executed->value    => '已执行',
        self::Invalid->value     => '已作废',
    ];

    /**
     * 根据状态获取对应的查询条件
     *
     * @param int    $status
     * @param string $tableName
     *
     * @return array|array[]
     */
    public static function getConditions(int $status, string $tableName = ''): array
    {
        $tablePrefix = '';
        if (!empty($tableName))
        {
            $tablePrefix = $tableName . '.';
        }

        return match (self::tryFrom($status))
        {
            self::NotExecuted => [
                [$tablePrefix . 'status', '=', 1],
                [$tablePrefix . 'execution_status', '=', 0],
            ],
            self::Executed => [
                [$tablePrefix . 'status', '=', 1],
                [$tablePrefix . 'execution_status', '=', 1],
            ],
            default => [],
        };
    }

    /**
     * 根据记录信息返回当前统一状态（前端识别用）
     */
    public static function FormatNurseExecutionStatus(array $taskInfo): array
    {
        if (empty($taskInfo))
        {
            return [];
        }

        // 已作废
        if (empty($taskInfo['status']))
        {
            return [
                'id'   => self::Invalid->value,
                'name' => self::DESCRIPTIONS[self::Invalid->value],
            ];
        }

        // 待执行
        if ($taskInfo['execution_status'] == self::NotExecuted->value)
        {
            return [
                'id'   => self::NotExecuted->value,
                'name' => self::DESCRIPTIONS[self::NotExecuted->value],
            ];
        }

        // 已执行
        if ($taskInfo['execution_status'] == self::Executed->value)
        {
            return [
                'id'   => self::Executed->value,
                'name' => self::DESCRIPTIONS[self::Executed->value],
            ];
        }

        return [
            'id'   => '',
            'name' => '',
        ];
    }
}
