<?php

namespace App\Enums;

/**
 * 床位记录说明
 * Class BedEnum
 * @package App\Enums
 */
enum BedRecordEnum: int
{
    use EnumToolTrait;

    /**
     * 住院、出院
     */
    case InpatientStart = 1;    // 门诊转住院>>入笼（默认住院操作）

    case InpatientEnd = 2;      // 住院结束>>释放笼位

    /**
     * 更换
     */
    case BedChangeOut = 3;      // 更换床位>>释放笼位

    case BedChangeIn = 4;       // 更换床位>>入笼

    /**
     * 枚举值对应的描述
     *
     * @noinspection PhpUnused
     */
    public const array DESCRIPTIONS = [
        self::InpatientStart->value => '门诊转住院>>入笼',
        self::InpatientEnd->value   => '住院结束>>释放笼位',
        self::BedChangeOut->value   => '更换床位>>释放笼位',
        self::BedChangeIn->value    => '更换床位>>入笼',
    ];

    /**
     * 释放床位的操作类型
     */
    public const array RELEASE_BED_OPERATIONS = [
        self::InpatientEnd->value => '住院结束>>释放笼位',
        self::BedChangeOut->value => '更换床位>>释放笼位',
    ];

    /**
     * 占用床位的操作类型
     */
    public const array OCCUPY_BED_OPERATIONS = [
        self::InpatientStart->value => '门诊转住院>>入笼',
        self::BedChangeIn->value    => '更换床位>>入笼',
    ];

    /**
     * 获取释放床位操作的描述
     *
     * @param int|null $operationType 操作类型，为null时返回默认操作
     *
     * @return string
     */
    public static function getReleaseBedDescription(?int $operationType = null): string
    {
        // 如果没有指定操作类型，使用默认的住院结束操作
        $operationType = $operationType ?? self::InpatientEnd->value;

        return self::RELEASE_BED_OPERATIONS[$operationType] ?? '住院结束>>释放笼位';
    }

    /**
     * 获取占用床位操作的描述
     *
     * @param int|null $operationType 操作类型，为null时返回默认操作
     *
     * @return string
     */
    public static function getOccupyBedDescription(?int $operationType = null): string
    {
        // 如果没有指定操作类型，使用默认的门诊转住院操作
        $operationType = $operationType ?? self::InpatientStart->value;

        return self::OCCUPY_BED_OPERATIONS[$operationType] ?? '门诊转住院>>入笼';
    }
}
