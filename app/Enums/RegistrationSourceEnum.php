<?php

namespace App\Enums;

/**
 * 挂号来源记录
 * 关联表：his_registration_source，此处定义为了方便程序中特殊使用
 */
enum RegistrationSourceEnum: int
{
    use EnumToolTrait;

    case FrontDesk = 1;

    case InHospitalTransfer = 2;

    case TransferHospital = 3;

    public const array DESCRIPTIONS = [
        self::FrontDesk->value          => '前台挂号',
        self::InHospitalTransfer->value => '院内转诊',
        self::TransferHospital->value   => '转院挂号',
    ];
}
