<?php

namespace App\Enums;

/**
 * 仓储-库存动态-增加类型
 * 对应stock_add_type表
 */
enum StockAddTypeEnum: int
{
    use EnumToolTrait;

    // 采购入库
    case Purchase = 1;

    // 调拨入库
    case Transfer = 2;

    // 门诊退货入库
    case OutpatientReturn = 3;

    // 零售退货入库
    case RetailReturn = 4;

    // 盘点入库
    case Inventory = 5;

    /**
     * 枚举值对应的描述
     *
     * @noinspection PhpUnused
     */
    public const array DESCRIPTIONS = [
        self::Purchase->value         => '采购入库',
        self::Transfer->value         => '调拨入库',
        self::OutpatientReturn->value => '门诊退货入库',
        self::RetailReturn->value     => '零售退货入库',
        self::Inventory->value        => '盘点入库',
    ];

    /**
     * 入库类型是否必须填写子类型
     *
     * @param int $type
     *
     * @return bool
     */
    public static function typeIsMustSubType(int $type): bool
    {
        return false;
    }

    /**
     * 入库类型是否必须填写备注
     *
     * @param int $type
     *
     * @return bool
     */
    public static function typeIsMustRemark(int $type): bool
    {
        return in_array($type, self::values());
    }

    /**
     * 入库类型需要计算加权成本价
     *
     * @return array
     */
    public static function getNeedWeightedPrice(): array
    {
        return [self::Purchase->value, self::Transfer->value];
    }

    /**
     * 所有可选项（用于前端筛选）
     *
     * @return array
     */
    public static function options(): array
    {
        return array_map(fn($item) => [
            'id'   => $item->value,
            'name' => $item->getDescription($item->value),
        ], self::cases());
    }
}
