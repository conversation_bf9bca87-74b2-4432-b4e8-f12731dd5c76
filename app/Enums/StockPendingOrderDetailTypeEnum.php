<?php

namespace App\Enums;

/**
 * 仓储-待出库单详情-出库源类型
 * Class StockPendingOrderDetailTypeEnum
 * @package App\Enums
 */
enum StockPendingOrderDetailTypeEnum: int
{
    use EnumToolTrait;

    case Drug = 1;
    case SuitDrug = 2;
    case Consumables = 3;

    public const array DESCRIPTIONS = [
        self::Drug->value        => '药品商品',
        self::SuitDrug->value    => '组合项目药品商品子项目',
        self::Consumables->value => '化验耗材',
    ];
}
