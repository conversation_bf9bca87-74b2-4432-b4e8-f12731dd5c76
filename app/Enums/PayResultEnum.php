<?php

namespace App\Enums;

enum PayResultEnum: int
{
    use EnumToolTrait;

    case Unknown = 0;
    case Success = 1;
    case Fail = 2;
    case Pending = 3;

    public const array DESCRIPTIONS = [
        self::Unknown->value => '未知',
        self::Success->value => '成功',
        self::Fail->value    => '失败',
        self::Pending->value => '处理中',
    ];

    public static function ToCallbackResult(int $payResult): int
    {
        return match ($payResult)
        {
            self::Success->value => 1,
            self::Fail->value => 2,
            default => 0,
        };
    }
}
