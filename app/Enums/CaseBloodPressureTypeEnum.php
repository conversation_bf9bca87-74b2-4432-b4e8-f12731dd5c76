<?php

namespace App\Enums;

/**
 * 病历血压测量部位
 */
enum CaseBloodPressureTypeEnum: int
{
    use EnumToolTrait;

    case LeftFront = 1;
    case RightFront = 2;
    case LeftBack = 3;
    case RightBack = 4;

    public const array DESCRIPTIONS = [
        self::LeftFront->value  => '左前肢',
        self::RightFront->value => '右前肢',
        self::LeftBack->value   => '左后肢',
        self::RightBack->value  => '右后肢',
    ];
}
