<?php

namespace App\Enums;

/**
 * 挂号状态
 */
enum RegistrationStatusEnum: int
{
    use EnumToolTrait;

    // 已作废
    case Invalid = 0;

    // 已使用
    case Used = 1;

    // 未使用
    case Unused = 2;

    const array DESCRIPTIONS = [
        self::Invalid->value => '已作废',
        self::Used->value    => '已使用',
        self::Unused->value  => '未使用',
    ];

    /**
     * 验证是否可删除
     *
     * @param int $status
     *
     * @return bool
     */
    public static function DeleteAble(int $status): bool
    {
        return self::Unused->value == $status;
    }
}
