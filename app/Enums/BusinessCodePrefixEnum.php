<?php

namespace App\Enums;

/**
 * 业务编码前缀
 */
enum BusinessCodePrefixEnum: string
{
    // 宠物病历号前缀
    case PET_NUMBER = 'PN';

    // 门诊号挂号前缀
    case MZGH = '10';

    // 门诊前缀
    case MZJL = '11';

    // 门诊病历
    case MZBL = '12';

    // 门诊处方
    case MZCF = '13';

    // 门诊化验
    case MZHY = '14';

    // 门诊影像
    case MZYX = '15';

    // 门诊处置
    case MZCZ = '16';

    // 院内转诊
    case YNZZ = '17';

    // 住院手续
    case ZYSX = '18';

    // 住院病历
    case ZYBL = '19';

    // 内部转院
    case NBZY = '20';

    // 预约
    case YYDH = '21';

    // 采购单号
    case CGDH = 'CG';

    // 到货单号
    case DHDH = 'DH';

    // 耗材领用单
    case HCLY = 'HC';

    // 调拨出库
    case DBCK = 'DB';

    // 退货出库
    case THCK = 'TH';

    // 盘点单号
    case PDDH = 'PD';

    // 充值购买单
    case CZGMD = 'CZ';

    // 洗美购买单
    case XMGMD = 'XM';

    // 零售购买单
    case LSGMD = 'LS';

    // 医疗订单
    case YLDD = '70';

    // 零售订单
    case LSDD = '71';

    // 洗美订单
    case XMDD = '72';

    // 余额充值订单
    case YECZDD = '73';

    // 结算单主订单号
    case JSDDM = '80';

    // 结算单子订单号
    case JSDDS = '81';

}

