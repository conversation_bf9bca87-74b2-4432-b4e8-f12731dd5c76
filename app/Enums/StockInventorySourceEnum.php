<?php

namespace App\Enums;

/**
 * 仓储-库存盘点单-盘点来源
 * stock_inventory_record.source
 */
enum StockInventorySourceEnum: int
{
    use EnumToolTrait;

    case DYNAMIC = 1;

    case CENTRAL = 2;

    case TEMPORARY = 3;

    case TEMPORARY_FIXED = 4;

    public const array DESCRIPTIONS = [
        self::DYNAMIC->value         => '动态盘点单',
        self::CENTRAL->value         => '集中盘点单',
        self::TEMPORARY->value       => '临时盘点单',
        self::TEMPORARY_FIXED->value => '临时单品盘点单',
    ];

    /**
     * 是否临时单品盘点单
     *
     * @param int $value
     *
     * @return bool
     */
    public static function getIsSingleItemTemporary(int $value): bool
    {
        return $value === self::TEMPORARY_FIXED->value;
    }
}
