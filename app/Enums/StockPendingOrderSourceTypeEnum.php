<?php

namespace App\Enums;

/**
 * 仓储-待出库单-来源类型
 * Class StockPendingOrderDetailTypeEnum
 * @package App\Enums
 */
enum StockPendingOrderSourceTypeEnum: int
{
    use EnumToolTrait;

    case Outpatient = 1;

    case Inpatient = 2;

    case Beauty = 3;

    public const array DESCRIPTIONS = [
        self::Outpatient->value => '门诊',
        self::Inpatient->value  => '住院',
        self::Beauty->value     => '美容洗澡',
    ];
}
