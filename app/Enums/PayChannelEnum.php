<?php

namespace App\Enums;

enum PayChannelEnum: int
{
    use EnumToolTrait;

    case Zero = 0;
    case Manual = 1;

    public const array DESCRIPTIONS = [
        self::Zero->value   => '零元支付',
        self::Manual->value => '手工收款',
    ];

    /**
     * 检查某个值是否存在于枚举中
     *
     * @param mixed $value
     *
     * @return bool
     */
    public static function exists(mixed $value): bool
    {
        // 排除 Zero
        return in_array($value, array_column(array_filter(self::cases(), fn($case) => $case !== self::Zero), 'value'));
    }
}
