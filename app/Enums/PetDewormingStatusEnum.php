<?php

namespace App\Enums;

/**
 * 宠物驱虫状态
 */
enum PetDewormingStatusEnum: int
{
    use EnumToolTrait;

    case Unknown = 0;
    case NotDeworming = 1;
    case Deworming = 2;

    /**
     * 枚举值对应的描述
     *
     * @noinspection PhpUnused
     */
    public const array DESCRIPTIONS = [
        self::Unknown->value      => '未知',
        self::NotDeworming->value => '未驱虫',
        self::Deworming->value    => '已驱虫',
    ];
}
