<?php

namespace App\Enums;

enum RabbitMqExchangeAndQueueEnum: string
{
    /*
     * ================================================================================
     *  队列
     * ================================================================================
     */
    case PaidRegistrationOrderQueue = 'paid.registration.queue';//创建结算成功的挂号订单队列
    case PaidRecipeOrderQueue = 'paid.recipe.queue';//创建结算成功的处方订单队列
    case PaidRetailOrderQueue = 'paid.retail.queue';//创建结算成功的零售订单队列
    case PaidBeautyOrderQueue = 'paid.beauty.queue';//创建结算成功的洗美订单队列
    case PaidRechargeOrderQueue = 'paid.recharge.queue';//创建结算成功的充值订单队列
    case StockPendingOrderQueue = 'stock.pending.queue';// 处方单支付后创建待出库单并出库
    case RetailOrderOutboundQueue = 'stock.retail.queue'; // 零售单支付后出库
}
