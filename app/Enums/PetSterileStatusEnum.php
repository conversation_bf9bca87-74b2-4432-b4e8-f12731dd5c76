<?php

namespace App\Enums;

/**
 * 宠物绝育状态
 */
enum PetSterileStatusEnum: int
{
    use EnumToolTrait;

    case Unknown = 0;
    case NotSterile = 1;
    case Sterile = 2;

    /**
     * 枚举值对应的描述
     *
     * @noinspection PhpUnused
     */
    public const array DESCRIPTIONS = [
        self::Unknown->value    => '未知',
        self::NotSterile->value => '未绝育',
        self::Sterile->value    => '已绝育',
    ];
}
