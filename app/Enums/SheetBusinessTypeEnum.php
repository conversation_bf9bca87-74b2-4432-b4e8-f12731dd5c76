<?php

namespace App\Enums;

/**
 * 支付单总的业务类型
 */
enum SheetBusinessTypeEnum: string
{
    use EnumToolTrait;

    case Recharge = 'recharge';
    case Registration = 'registration';
    case Recipe = 'recipe';
    case Retail = 'retail';
    case Beauty = 'beauty';

    public const array DESCRIPTIONS = [
        self::Recharge->value     => '充值',
        self::Registration->value => '挂号',
        self::Recipe->value       => '处方',
        self::Retail->value       => '零售',
        self::Beauty->value       => '洗美',
    ];

    public const array BUSINESS_PAY_SUB_ORDER_TYPE = [
        self::Recharge->value     => PaySubOrderTypeEnum::Recharge->value,
        self::Registration->value => PaySubOrderTypeEnum::Registration->value,
        self::Recipe->value       => PaySubOrderTypeEnum::Recipe->value,
        self::Retail->value       => PaySubOrderTypeEnum::Retail->value,
        self::Beauty->value       => PaySubOrderTypeEnum::Beauty->value,
    ];

    public const array SUB_ORDER_TYPE_BUSINESS_TYPE = [
        PaySubOrderTypeEnum::Recharge->value     => self::Recharge->value,
        PaySubOrderTypeEnum::Registration->value => self::Registration->value,
        PaySubOrderTypeEnum::Recipe->value       => self::Recipe->value,
        PaySubOrderTypeEnum::Retail->value       => self::Retail->value,
        PaySubOrderTypeEnum::Beauty->value       => self::Beauty->value,
    ];

    /**
     * 是否必须为单独结算/支付的业务
     *
     * @param string $businessType
     *
     * @return bool
     */
    public static function IsSinglePay(string $businessType): bool
    {
        return in_array($businessType, [
            self::Recharge->value,
        ]);
    }

    /**
     * 是否允许非业务单发起者的编辑
     *
     * @param string $businessType
     *
     * @return bool
     */
    public static function EditAble(string $businessType): bool
    {
        return in_array($businessType, [
            self::Retail->value,
            self::Beauty->value,
        ]);
    }

    /**
     * 是否允许非业务单发起者的删除
     *
     * @param string $businessType
     *
     * @return bool
     */
    public static function DeleteAble(string $businessType): bool
    {
        return in_array($businessType, [
            self::Registration->value,
            self::Retail->value,
            self::Beauty->value,
        ]);
    }

}
