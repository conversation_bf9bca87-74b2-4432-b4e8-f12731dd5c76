<?php

namespace App\Enums;

enum StockGroupTypeEnum: string
{
    case None = 'none';                     // 不分组
    case ByExpire = 'expire';               // 按效期分组
    case ByShelfAndExpire = 'shelf_expire'; // 按货位+效期分组

    /**
     * 获取分组字段
     */
    public function getGroupByFields(): array
    {
        return match ($this)
        {
            self::None => [],
            self::ByExpire => ['expired_date'],
            self::ByShelfAndExpire => ['shelf_code', 'expired_date'],
        };
    }

    /**
     * 获取查询字段
     */
    public function getSelectFields(): array
    {
        $baseFields = [
            'item_id',
            'DB::raw("sum(effective_pack_quantity) as pack_quantity")',
            'DB::raw("sum(effective_bulk_quantity) as bulk_quantity")',
            'expired_date'
        ];

        return match ($this)
        {
            self::None => ['*'], // 不分组时查询所有字段
            self::ByExpire => $baseFields,
            self::ByShelfAndExpire => array_merge($baseFields, ['shelf_code', 'item_barcode']),
        };
    }
}
