<?php

namespace App\Enums;

/**
 * 结算类型
 */
enum CheckoutTypeEnum: string
{
    use EnumToolTrait;

    case Activity = 'activity';
    case Coupon = 'coupon';
    case Discount = 'discount';
    case ThirdParty = 'third_party';
    case Balance = 'balance';
    case Cash = 'cash';

    public const array DESCRIPTIONS = [
        self::Activity->value   => '活动',
        self::Coupon->value     => '优惠券',
        self::Discount->value   => '折扣',
        self::ThirdParty->value => '第三方支付',
        self::Balance->value    => '余额',
        self::Cash->value       => '现金',
    ];

    /**
     * 各购买单类型支持的结算类型
     */
    public const array SHEET_BUSINESS_TYPE_SUPPORT = [
        SheetBusinessTypeEnum::Recharge->value     => [
            self::Cash->value,
        ],
        SheetBusinessTypeEnum::Registration->value => [
            self::Discount->value,
            self::Balance->value,
            self::Cash->value,
        ],

        SheetBusinessTypeEnum::Recipe->value => [
            self::Discount->value,
            self::ThirdParty->value,
            self::Balance->value,
            self::Cash->value,
        ],

        SheetBusinessTypeEnum::Retail->value => [
            self::Discount->value,
            self::ThirdParty->value,
            self::Balance->value,
            self::Cash->value,
        ],

        SheetBusinessTypeEnum::Beauty->value => [
            self::Discount->value,
            self::ThirdParty->value,
            self::Balance->value,
            self::Cash->value,
        ],
    ];

    public static function GetSheetTypeSupportType(string $businessType): array
    {
        return self::SHEET_BUSINESS_TYPE_SUPPORT[$businessType] ?? [];
    }

    public static function GetSheetTypesSupportType(array $businessTypes): array
    {
        $types = [];
        foreach ($businessTypes as $businessType)
        {
            $types = array_merge($types, self::GetSheetTypeSupportType($businessType));
        }

        return array_unique($types);
    }
}
