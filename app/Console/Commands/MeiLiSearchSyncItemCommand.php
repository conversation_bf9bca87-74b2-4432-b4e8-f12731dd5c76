<?php

namespace App\Console\Commands;

use Exception;
use Illuminate\Console\Command;
use App\Facades\SearchFacade;
use App\Enums\ItemTypeEnum;
use App\Logics\V1\MeiLiSearchSyncLogic;

class MeiLiSearchSyncItemCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:meiLiSearch
                        {operation : The operation type:info|test|init|sync|flush}
                        {keywords? : Test search keywords}
                        {category? : Test search pet category id}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'MeiLiSearch 商品索引管理命令';

    /**
     * 机构ID
     *
     * @var int
     */
    private int $orgId = 1;

    /**
     * Execute the console command.
     */
    public function handle(): bool
    {
        $operation = $this->argument('operation');

        switch ($operation)
        {
            case 'info':
                return $this->aboutInfo();
            case 'test':
                return $this->test();
            case 'init':
                return $this->init();
            case 'sync':
                return $this->sync();
            case 'flush':
                return $this->flush();
            default:
                $this->error('Unknown operation: ' . $operation);

                return false;
        }
    }

    private function aboutInfo(): bool
    {
        $this->line('获取索引信息...');

        // 构建公共参数
        $publicParams = [
            '_hospitalOrgId' => $this->orgId
        ];

        // 调用Logic检查索引状态
        $result = MeiLiSearchSyncLogic::GetIndexStatus($publicParams);

        if ($result->isFail())
        {
            $this->error('获取索引信息失败: ' . $result->getMessage());

            return false;
        }

        $data = $result->getData();
        $this->line('索引名称: ' . $data['index_name']);

        if ($data['exists'])
        {
            $this->line('索引状态: 存在');
            if (isset($data['stats']))
            {
                $stats = $data['stats'];
                $this->line('文档主键：' . ($data['info']['primaryKey'] ?? 'N/A'), '');
                $this->line('文档数量: ' . ($stats['numberOfDocuments'] ?? 'N/A'));
                $this->line('索引大小: ' . ($stats['rawDocumentDbSize'] ?? 'N/A') . ' bytes');
            }
        }
        else
        {
            $this->line('索引状态: 不存在');
        }

        return true;
    }

    private function test(): bool
    {
        $keywords = $this->argument('keywords') ?? '';
        $category = $this->argument('category') ?? '';

        $this->line("测试搜索 - 关键词: '$keywords', 分类: '$category'");

        try
        {
            $result = SearchFacade::searchRecipeItems(keywords: $keywords, orgId: $this->orgId);

            $this->line('搜索结果数量: ' . count($result));

            if (!empty($result))
            {
                $this->line('前5个结果:');
                foreach (array_slice($result, 0, 5) as $index => $item)
                {
                    $this->line(sprintf('%d. ID: %s, 名称: %s, 类型: %s',
                                        $index + 1,
                                        $item['id'] ?? 'N/A',
                                        $item['name'] ?? 'N/A',
                                        $item['item_type'] ?? 'N/A'));
                }
            }

            return true;
        } catch (Exception $e)
        {
            $this->error('搜索测试失败: ' . $e->getMessage());

            return false;
        }
    }

    private function init(): bool
    {
        $this->line('开始初始化索引...');

        // 构建公共参数
        $publicParams = [
            '_hospitalOrgId' => $this->orgId
        ];

        // 调用Logic初始化索引
        $result = MeiLiSearchSyncLogic::InitializeIndex($publicParams);

        if ($result->isFail())
        {
            $this->error('初始化索引失败: ' . $result->getMessage());

            return false;
        }

        $data = $result->getData();
        if ($data['existed'])
        {
            $this->line('索引已存在，已更新设置');
        }
        else
        {
            $this->line('索引创建成功');
        }

        $this->line('索引名称: ' . $data['index_name']);
        $this->line('初始化完成');

        return true;
    }

    private function sync(): bool
    {
        $this->line("开始同步商品数据...\n");

        // 构建公共参数
        $publicParams = [
            '_hospitalOrgId' => $this->orgId
        ];

        // 同步各种类型的商品
        $itemTypes = [
            ItemTypeEnum::Sku->value    => '普通商品',
            ItemTypeEnum::Beauty->value => '洗美商品',
            ItemTypeEnum::Spu->value    => '组合商品'
        ];

        $totalSynced = 0;
        foreach ($itemTypes as $itemType => $typeName)
        {
            $this->line("正在同步$typeName...");

            $result = MeiLiSearchSyncLogic::SyncItems($itemType, $publicParams);

            if ($result->isFail())
            {
                $this->error("同步{$typeName}失败: " . $result->getMessage());

                return false;
            }

            $data        = $result->getData();
            $syncedCount = $data['synced_count'] ?? 0;
            $totalSynced += $syncedCount;

            $this->line("同步{$typeName}完成: $syncedCount 条记录\n");
        }

        $this->line("全部同步完成，共同步 $totalSynced 条记录");

        return true;
    }

    private function flush(): bool
    {
        $this->line('开始清空索引...');

        // 构建公共参数
        $publicParams = [
            '_hospitalOrgId' => $this->orgId
        ];

        // 调用Logic清空索引
        $result = MeiLiSearchSyncLogic::FlushIndex($publicParams);

        if ($result->isFail())
        {
            $this->error('清空索引失败: ' . $result->getMessage());

            return false;
        }

        $data = $result->getData();
        $this->line('索引清空成功: ' . $data['index_name']);
        $this->line('清空完成');

        return true;
    }
}
