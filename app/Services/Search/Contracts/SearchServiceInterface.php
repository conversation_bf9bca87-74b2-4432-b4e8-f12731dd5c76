<?php

namespace App\Services\Search\Contracts;
interface SearchServiceInterface
{
    /**
     * 处方药品搜索
     *
     * @param string   $keywords              搜索关键词
     * @param int      $purchaseHospitalId    采购医院的ID
     * @param array    $supportPetCategoryIds 适用的宠物类型ID数组
     * @param array    $firstSaleTypeId       一级商品类型ID数组
     * @param int      $limit                 返回数量
     * @param int|null $orgId                 机构ID(用于构建索引名称，实现多租户)
     *
     * @return array 符合结构：[['id' => 1, 'name' => '药品名', ...], ...]
     */
    public function searchRecipeItems(string $keywords, int $purchaseHospitalId = 0, array $supportPetCategoryIds = [], array $firstSaleTypeId = [], int $limit = 200, int|null $orgId = null): array;

    /**
     * 洗美服务搜索
     *
     * @param string   $keywords              搜索关键词
     * @param array    $supportPetCategoryIds 适用的宠物类型ID数组
     * @param array    $firstSaleTypeId       一级商品类型ID数组
     * @param int      $limit                 返回数量
     * @param int|null $orgId                 机构ID(用于构建索引名称，实现多租户)
     *
     * @return array 符合结构：[['id' => 1, 'name' => '药品名', ...], ...]
     */
    public function searchBeautyItems(string $keywords, array $supportPetCategoryIds = [], array $firstSaleTypeId = [], int $limit = 200, int|null $orgId = null): array;

    /**
     * 零售商品搜索
     *
     * @param string   $keywords        搜索关键词
     * @param array    $firstSaleTypeId 一级商品类型ID数组
     * @param int      $limit           返回数量
     * @param int|null $orgId           机构ID(用于构建索引名称，实现多租户)
     *
     * @return array 符合结构：[['id' => 1, 'name' => '药品名', ...], ...]
     */
    public function searchRetailItems(string $keywords, int $purchaseHospitalId = 0, array $firstSaleTypeId = [], int $limit = 200, int|null $orgId = null): array;

    /**
     * 采购商品搜索
     *
     * @param string   $keywords
     * @param int      $purchaseHospitalId
     * @param array    $itemIds
     * @param int      $brandId
     * @param int      $limit
     * @param int|null $orgId
     *
     * @return array
     */
    public function searchPurchaseItems(string $keywords, int $purchaseHospitalId = 0, array $itemIds = [], int $brandId = 0, int $limit = 200, int|null $orgId = null): array;

    public function searchConsumablesItems(string $keywords, int $hospitalId, array $firstSaleTypeIds = [], int $limit = 200, int|null $orgId = null): array;

    /**
     * 设置搜索的索引名称
     */
    public function setIndexName(string $indexName): self;

    /**
     * 获取搜索的索引名称
     */
    public function getIndexName(): string;
}
