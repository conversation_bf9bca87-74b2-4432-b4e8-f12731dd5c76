<?php

namespace App\Services\Search;

use App\Services\Search\Contracts\SearchServiceInterface;
use Illuminate\Support\ServiceProvider;
use Meilisearch\Client as MeilisearchClient;
use Elastic\Elasticsearch\Client as ElasticsearchClient;
use Elastic\Elasticsearch\ClientBuilder as ElasticsearchClientBuilder;

class SearchServiceProvider extends ServiceProvider
{
    public function register(): void
    {
        // 绑定Meilisearch客户端
        $this->app->singleton('meilisearch.client', function () {
            return new MeilisearchClient(
                config('search.engines.meilisearch.host'),
                config('search.engines.meilisearch.key')
            );
        });

        // 绑定Elasticsearch客户端
        $this->app->singleton('elasticsearch.client', function () {
            return ElasticsearchClientBuilder::create()
                                             ->setHosts([config('search.engines.elasticsearch.hosts')])
                                             ->build();
        });

        // 根据配置绑定搜索服务
        $this->app->singleton(SearchServiceInterface::class, function ($app) {
            // 根据配置动态切换引擎
            $engine = config('search.default');

            return match ($engine)
            {
                'elasticsearch' => new ElasticsearchService(
                    $app->make('elasticsearch.client')
                ),
                default         => new MeiLiSearchService(
                    $app->make('meilisearch.client')
                ),
            };
        });
    }
}
