<?php

namespace App\Services\Search;

use Meilisearch\Client;
use App\Services\Search\Contracts\SearchServiceInterface;
use App\Services\Search\Traits\IndexNameTrait;
use App\Infrastructure\Search\MeiLiSearchClient;
use App\Enums\ItemSaleTyeEnum;

class MeiLiSearchService implements SearchServiceInterface
{
    use IndexNameTrait;

    private MeiLiSearchClient $client;
    private string $indexName = '';

    public function __construct(?Client $legacyClient = null)
    {
        // 使用基础设施层的客户端
        $this->client = MeiLiSearchClient::getInstance();
    }

    public function setIndexName(string $indexName): self
    {
        $this->indexName = $indexName;

        return $this;
    }

    public function getIndexName(): string
    {
        return $this->indexName;
    }

    public function searchRecipeItems(string $keywords, int $purchaseHospitalId = 0, array $supportPetCategoryIds = [], array $firstSaleTypeId = ItemSaleTyeEnum::RECIPE_FIRST_SALE_TYPE, int $limit = 200, int|null $orgId = null): array
    {
        if (!empty($orgId))
        {
            $indexName = $this->buildRecipeItemsIndexName($orgId);
        }
        else
        {
            $indexName = $this->getIndexName();
        }

        // 构建宠物类型筛选条件
        if (empty($supportPetCategoryIds))
        {
            $supportPetCategoryFilter = '';
        }
        else
        {
            $supportPetCategoryFilter = sprintf('(item_support_pet_category IS EMPTY OR item_support_pet_category IN [%s])',
                                                implode(',', $supportPetCategoryIds));
        }

        // 构建符合的一级项目类型和医院采购记录筛选条件
        $firstSaleTypeAndHospitalFilter = '';
        if (!empty($firstSaleTypeId))
        {
            if ($purchaseHospitalId > 0)
            {
                // 需要采购记录的类型（1: 药品）
                $needPurchaseTypes = [ItemSaleTyeEnum::FirstDrug->value];

                // 不需要采购记录的类型（2：化验 3: 影像检查, 4: 医疗处置）
                $noNeedPurchaseTypes = [ItemSaleTyeEnum::FirstTest->value, ItemSaleTyeEnum::FirstImage->value, ItemSaleTyeEnum::FirstNurses->value];

                $needPurchaseTypesInQuery   = array_intersect($firstSaleTypeId, $needPurchaseTypes);
                $noNeedPurchaseTypesInQuery = array_intersect($firstSaleTypeId, $noNeedPurchaseTypes);

                // 构建条件
                $conditions = [];
                if (!empty($needPurchaseTypesInQuery))
                {
                    $conditions[] = sprintf('(first_sale_type.id IN [%s] AND purchase_hospital_ids IN [%d])',
                                            implode(',', $needPurchaseTypesInQuery),
                                            $purchaseHospitalId);
                }

                // 对于不需要采购记录的类型：first_sale_type.id in (3,4)
                if (!empty($noNeedPurchaseTypesInQuery))
                {
                    $conditions[] = sprintf('first_sale_type.id IN [%s]', implode(',', $noNeedPurchaseTypesInQuery));
                }

                if (!empty($conditions))
                {
                    $firstSaleTypeAndHospitalFilter = '(' . implode(' OR ', $conditions) . ')';
                }
            }
            else
            {
                // 没有指定医院ID时，直接按项目类型筛选
                $firstSaleTypeAndHospitalFilter = sprintf('first_sale_type.id IN [%s]', implode(',', $firstSaleTypeId));
            }
        }

        // 构建最终的筛选条件数组，过滤掉空字符串
        $filters = array_filter([
                                    $firstSaleTypeAndHospitalFilter,
                                    $supportPetCategoryFilter,
                                    'is_recipe_allow = true',
                                    'status = 1',
                                ]);

        $searchOptions = [
            'filter'                => $filters,
            'limit'                 => $limit,
            'attributesToRetrieve'  => ['*'],
            'attributesToHighlight' => ['name', 'alias_name', 'basis_name'],
            'showRankingScore'      => true,
        ];

        $results = $this->client->search($indexName, $keywords, $searchOptions);

        //var_dump($results);

        return $this->formatResults($results['hits'] ?? []);
    }

    public function searchBeautyItems(string $keywords, array $supportPetCategoryIds = [], array $firstSaleTypeId = [5], int $limit = 200, int|null $orgId = null): array
    {
        if (!empty($orgId))
        {
            $indexName = $this->buildBeautyItemsIndexName($orgId);
        }
        else
        {
            $indexName = $this->getIndexName();
        }

        // 构建宠物类型筛选条件
        $supportPetCategoryFilter = empty($supportPetCategoryIds) ? '' : sprintf('item_support_pet_category IS EMPTY OR item_support_pet_category IN [%s]',
                                                                                 implode(',', $supportPetCategoryIds));

        // 构建一级项目类型筛选条件
        $firstSaleTypeFilter = empty($firstSaleTypeId) ? '' : sprintf('first_sale_type.id IN [%s]',
                                                                      implode(',', $firstSaleTypeId));

        $searchOptions = [
            'filter'                => [
                $firstSaleTypeFilter,
                $supportPetCategoryFilter,
                'status = 1',
            ],
            'limit'                 => $limit,
            'attributesToRetrieve'  => ['*'],
            'attributesToHighlight' => ['name', 'alias_name', 'basis_name'],
            'showRankingScore'      => true,
        ];

        $results = $this->client->search($indexName, $keywords, $searchOptions);

        //var_dump($results);

        return $this->formatResults($results['hits'] ?? []);
    }

    public function searchRetailItems(string $keywords, int $purchaseHospitalId = 0, array $firstSaleTypeId = [1], int $limit = 200, int|null $orgId = null): array
    {
        if (!empty($orgId))
        {
            $indexName = $this->buildRetailItemsIndexName($orgId);
        }
        else
        {
            $indexName = $this->getIndexName();
        }

        // TODO 指定医院有采购记录
        $hospitalPurchaseFilter = '';
        if ($purchaseHospitalId > 0)
        {
            $hospitalPurchaseFilter = sprintf('purchase_hospital_ids IN [%d]', $purchaseHospitalId);
        }

        // 构建一级项目类型筛选条件
        $firstSaleTypeFilter = empty($firstSaleTypeId) ? '' : sprintf('first_sale_type.id IN [%s]',
                                                                      implode(',', $firstSaleTypeId));

        if (isLikelyBarcode($keywords))
        {
            $barcodeFilter  = "barcode = '$keywords'";
            $barcodeOptions = [
                'filter'                => [
                    $firstSaleTypeFilter,
                    $barcodeFilter,
                    $hospitalPurchaseFilter,
                    'is_retail_allow = true',
                    'status = 1',
                ],
                'limit'                 => $limit,
                'attributesToRetrieve'  => ['*'],
                'attributesToHighlight' => ['name', 'alias_name', 'basis_name'],
                'showRankingScore'      => true,
            ];

            $barcodeResult = $this->client->search($indexName, '', $barcodeOptions);
            if (!empty($barcodeResult['hits']))
            {
                return $this->formatResults($barcodeResult['hits']);
            }
        }

        $fullTextOptions = [
            'filter'                => [
                $firstSaleTypeFilter,
                $hospitalPurchaseFilter,
                'is_retail_allow = true',
                'status = 1',
            ],
            'limit'                 => $limit,
            'attributesToRetrieve'  => ['*'],
            'attributesToHighlight' => ['name', 'alias_name', 'basis_name'],
            'showRankingScore'      => true,
        ];

        $fullTextResult = $this->client->search($indexName, $keywords, $fullTextOptions);

        //var_dump($results);

        return $this->formatResults($fullTextResult['hits'] ?? []);
    }

    /**
     * 采购商品搜索
     *
     * @param string   $keywords           商品名称关键字
     * @param int      $purchaseHospitalId 指定医院ID存在采购记录（到货签收后认为存在采购记录）
     * @param array    $itemIds            指定商品ID
     * @param int      $brandId            指定商品品牌ID
     * @param int      $limit              指定获取条目
     * @param int|null $orgId              指定机构ID
     *
     * @return array
     */
    public function searchPurchaseItems(string $keywords, int $purchaseHospitalId = 0, array $itemIds = [], int $brandId = 0, int $limit = 200, int|null $orgId = null): array
    {
        if (!empty($orgId))
        {
            $indexName = $this->buildPurchaseItemsIndexName($orgId);
        }
        else
        {
            $indexName = $this->getIndexName();
        }

        // 商品采购状态有效
        $isPurchasableFilter = 'is_purchasable = 1';

        // 采购单商品只可以增加一级分类：药品，
        $firstSaleTypeFilter = 'first_sale_type.id IN [1]';

        // 排除组合
        $isSuitFilter = 'is_suit = 0';

        // 指定医院有采购记录
        $hospitalPurchaseFilter = '';
        if ($purchaseHospitalId > 0)
        {
            $hospitalPurchaseFilter = sprintf('purchase_hospital_ids IN [%d]', $purchaseHospitalId);
        }

        // 指定商品ID
        $itemIdsFilter = '';
        if (!empty($itemIds))
        {
            $itemIdsFilter = sprintf('id IN [%s]', implode(',', $itemIds));
        }

        // 构建品牌筛选条件
        $brandFilter = '';
        if ($brandId > 0)
        {
            $brandFilter = sprintf('brand.id = %d', $brandId);
        }

        // 执行搜索
        $searchOptions = [
            'filter'                => [
                $isPurchasableFilter,
                $itemIdsFilter,
                $firstSaleTypeFilter,
                $hospitalPurchaseFilter,
                $brandFilter,
                $isSuitFilter,
                'status = 1',
            ],
            'limit'                 => $limit,
            'attributesToRetrieve'  => ['*'],
            'attributesToHighlight' => ['name', 'alias_name', 'basis_name'],
            'showRankingScore'      => true,
        ];

        $results = $this->client->search($indexName, $keywords, $searchOptions);

        return $this->formatResults($results['hits'] ?? []);
    }

    public function searchConsumablesItems(string $keywords, int $hospitalId, array $firstSaleTypeIds = [1], int $limit = 200, int|null $orgId = null): array
    {
        if (!empty($orgId))
        {
            $indexName = $this->buildConsumablesItemsIndexName($orgId);
        }
        else
        {
            $indexName = $this->getIndexName();
        }

        // 一级项目类型：默认药品
        $firstSaleTypeFilter = '';
        if (!empty($firstSaleTypeIds))
        {
            $firstSaleTypeFilter = sprintf('first_sale_type.id IN [%s]', implode(',', $firstSaleTypeIds));
        }

        // 是耗材领用标记：默认是
        $isReceiveAllowFilter = 'is_receive_allow = true';

        // 指定医院有采购记录
        $hospitalPurchaseFilter = sprintf('purchase_hospital_ids IN [%d]', $hospitalId);

        // 排除组合
        $isSuitFilter = 'is_suit = 0';

        $searchOptions = [
            'filter'                => [
                $firstSaleTypeFilter,
                $isReceiveAllowFilter,
                //$hospitalPurchaseFilter, // TODO 指定医院有采购记录
                $isSuitFilter,
                'status = 1',
            ],
            'limit'                 => $limit,
            'attributesToRetrieve'  => ['*'],
            'attributesToHighlight' => ['name', 'alias_name', 'basis_name'],
            'showRankingScore'      => true,
        ];

        $results = $this->client->search($indexName, $keywords, $searchOptions);

        return $this->formatResults($results['hits'] ?? []);
    }

    private function formatResults(array $hits): array
    {
        return array_map(function ($item) {
            return [
                ...$item,
                'highlight_name'       => $item['_formatted']['name'] ?? null,
                'highlight_alias_name' => $item['_formatted']['alias_name'] ?? null,
                'highlight_basis_name' => $item['_formatted']['basis_name'] ?? null,
                'ranking_score'        => $item['_rankingScore'] ?? null,
            ];
        }, $hits);
    }
}
