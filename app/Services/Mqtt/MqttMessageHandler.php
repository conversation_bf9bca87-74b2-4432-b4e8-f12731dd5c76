<?php

namespace App\Services\Mqtt;

use Illuminate\Support\Facades\Log;
use Exception;

class MqttMessageHandler
{
    protected array $handlers = [];

    /**
     * 注册消息处理器
     */
    public function register(string $topic, callable $handler): void
    {
        $this->handlers[$topic] = $handler;
    }

    /**
     * 处理接收到的消息
     */
    public function handle(string $topic, string $message, array $properties = []): void
    {
        try {
            // 查找精确匹配的处理器
            if (isset($this->handlers[$topic])) {
                call_user_func($this->handlers[$topic], $topic, $message, $properties);
                return;
            }

            // 查找通配符匹配的处理器
            foreach ($this->handlers as $pattern => $handler) {
                if ($this->matchTopic($pattern, $topic)) {
                    call_user_func($handler, $topic, $message, $properties);
                    return;
                }
            }

            // 如果没有找到处理器，记录日志
            Log::warning('未找到MQTT消息处理器', [
                'topic' => $topic,
                'message' => $message
            ]);

        } catch (Exception $e) {
            Log::error('MQTT消息处理失败', [
                'topic' => $topic,
                'message' => $message,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 匹配主题模式
     */
    protected function matchTopic(string $pattern, string $topic): bool
    {
        // 将MQTT通配符转换为正则表达式
        $regex = str_replace(
            ['/', '+', '#'],
            ['\/', '[^\/]+', '.*'],
            $pattern
        );
        
        return preg_match('/^' . $regex . '$/', $topic) === 1;
    }

    /**
     * 获取所有注册的处理器
     */
    public function getHandlers(): array
    {
        return $this->handlers;
    }

    /**
     * 移除处理器
     */
    public function remove(string $topic): void
    {
        unset($this->handlers[$topic]);
    }

    /**
     * 清空所有处理器
     */
    public function clear(): void
    {
        $this->handlers = [];
    }
}
