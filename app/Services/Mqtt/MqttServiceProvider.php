<?php

namespace App\Services\Mqtt;

use Illuminate\Support\ServiceProvider;
use Illuminate\Contracts\Support\DeferrableProvider;

class MqttServiceProvider extends ServiceProvider implements DeferrableProvider
{
    /**
     * 注册服务
     */
    public function register(): void
    {
        // 合并配置文件
        $this->mergeConfigFrom(
            __DIR__ . '/config/mqtt.php',
            'mqtt'
        );

        // 注册MQTT服务为单例
        $this->app->singleton('mqtt', function ($app) {
            return new MqttService();
        });

        // 注册MQTT管理器
        $this->app->singleton('mqtt.manager', function ($app) {
            return new MqttManager($app);
        });

        // 注册MQTT消息处理器
        $this->app->singleton('mqtt.handler', function ($app) {
            return new MqttMessageHandler();
        });

        // 注册别名
        $this->app->alias('mqtt', MqttService::class);
        $this->app->alias('mqtt.manager', MqttManager::class);
        $this->app->alias('mqtt.handler', MqttMessageHandler::class);
    }

    /**
     * 启动服务
     */
    public function boot(): void
    {
        // 发布配置文件
        $this->publishes([
            __DIR__ . '/config/mqtt.php' => config_path('mqtt.php'),
        ], 'mqtt-config');

        // 发布示例文件
        $this->publishes([
            __DIR__ . '/Examples' => app_path('Services/Mqtt/Examples'),
        ], 'mqtt-examples');

        // 注册Artisan命令
        if ($this->app->runningInConsole()) {
            $this->commands([
                Commands\MqttTestCommand::class,
            ]);
        }
    }

    /**
     * 获取服务提供者提供的服务
     */
    public function provides(): array
    {
        return [
            'mqtt',
            'mqtt.manager',
            'mqtt.handler',
            MqttService::class,
            MqttManager::class,
            MqttMessageHandler::class
        ];
    }
}
