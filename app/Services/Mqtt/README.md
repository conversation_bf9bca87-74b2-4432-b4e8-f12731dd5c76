# MQTT服务提供者

这是一个基于 `php-mqtt/client` 的Laravel MQTT服务提供者，提供了完整的MQTT功能，包括发布、订阅、消息处理等。

## 功能特性

- 被动连接（按需连接）
- 支持QoS 0, 1, 2
- 支持SSL/TLS连接
- 消息处理器模式
- 批量操作支持
- 自动重连机制
- 完整的日志记录
- 门面模式支持

## 安装配置

### 1. 注册服务提供者

在 `config/app.php` 中添加服务提供者：

```php
'providers' => [
    // ...
    App\Services\Mqtt\MqttServiceProvider::class,
],
```

### 2. 注册门面

在 `config/app.php` 中添加门面别名：

```php
'aliases' => [
    // ...
    'Mqtt' => App\Services\Mqtt\MqttFacade::class,
],
```

### 3. 发布配置文件

```bash
php artisan vendor:publish --tag=mqtt-config
```

### 4. 环境变量配置

在 `.env` 文件中配置MQTT服务器信息：

```env
MQTT_HOST=*************
MQTT_PORT=1883
MQTT_USERNAME=admin
MQTT_PASSWORD=public
MQTT_CLIENT_ID=laravel_app
```

## 基本使用

### 使用门面

```php
use App\Services\Mqtt\MqttFacade as Mqtt;

// 发布消息
Mqtt::publish('test/topic', 'Hello MQTT!');

// 订阅消息
Mqtt::subscribe('test/topic', function ($topic, $message) {
    echo "收到消息: {$message} 来自主题: {$topic}";
});

// 检查连接状态
if (Mqtt::isConnected()) {
    echo "MQTT已连接";
}
```

### 使用依赖注入

```php
use App\Services\Mqtt\MqttService;

class MyController extends Controller
{
    public function __construct(private MqttService $mqtt)
    {
    }

    public function publishMessage()
    {
        $this->mqtt->publish('device/status', json_encode([
            'device_id' => 'device001',
            'status' => 'online',
            'timestamp' => time()
        ]), 1);
    }
}
```

### 使用管理器

```php
use App\Services\Mqtt\MqttManager;

class MqttController extends Controller
{
    public function __construct(private MqttManager $mqttManager)
    {
    }

    public function batchPublish()
    {
        $messages = [
            [
                'topic' => 'device/001/status',
                'message' => json_encode(['status' => 'online']),
                'qos' => 1
            ],
            [
                'topic' => 'device/002/status', 
                'message' => json_encode(['status' => 'offline']),
                'qos' => 1
            ]
        ];

        $results = $this->mqttManager->publishBatch($messages);
        return response()->json($results);
    }
}
```

## 高级用法

### 消息处理器

```php
use App\Services\Mqtt\MqttMessageHandler;
use App\Services\Mqtt\MqttService;

$handler = new MqttMessageHandler();
$mqtt = new MqttService();

// 注册处理器
$handler->register('device/+/status', function ($topic, $message) {
    // 处理设备状态消息
    $deviceId = explode('/', $topic)[1];
    $status = json_decode($message, true);
    
    // 保存到数据库或其他处理
    Device::where('device_id', $deviceId)->update(['status' => $status]);
});

// 订阅并使用处理器
$mqtt->subscribe('device/+/status', function ($topic, $message) use ($handler) {
    $handler->handle($topic, $message);
});
```

### 自定义连接配置

```php
$customConfig = [
    'host' => '*************',
    'port' => 8883,
    'username' => 'custom_user',
    'password' => 'custom_pass',
    'client_id' => 'custom_client',
    'ca_file' => '/path/to/ca.crt',
    'verify_peer' => true
];

$mqtt = new MqttService($customConfig);
```

## API参考

### MqttService 主要方法

- `connect()`: 连接到MQTT服务器
- `disconnect()`: 断开连接
- `publish($topic, $message, $qos = 0, $retain = false)`: 发布消息
- `subscribe($topic, $callback, $qos = 0)`: 订阅主题
- `unsubscribe($topic)`: 取消订阅
- `loop($allowSleep = true, $exitWhenEmpty = false, $timeout = 1)`: 消息循环
- `isConnected()`: 检查连接状态
- `getClientId()`: 获取客户端ID

### QoS级别说明

- **QoS 0**: 最多一次传递，不保证消息到达
- **QoS 1**: 至少一次传递，保证消息到达但可能重复
- **QoS 2**: 恰好一次传递，保证消息到达且不重复

## 主题通配符

- `+`: 单级通配符，匹配一个层级
- `#`: 多级通配符，匹配多个层级

示例：
- `device/+/status` 匹配 `device/001/status`, `device/002/status`
- `sensor/#` 匹配 `sensor/temp`, `sensor/temp/room1`, `sensor/humidity/room2`

## 错误处理

所有MQTT操作都有完整的错误处理和日志记录。检查Laravel日志文件获取详细的错误信息。

## 示例代码

查看 `app/Services/Mqtt/Examples/MqttExample.php` 获取完整的使用示例。

## 注意事项

1. 使用被动连接模式，只有在需要时才会建立连接
2. 长时间运行的订阅应该在队列或后台进程中执行
3. 生产环境建议启用SSL/TLS连接
4. 合理设置keep_alive和timeout参数
5. 注意MQTT服务器的连接数限制
