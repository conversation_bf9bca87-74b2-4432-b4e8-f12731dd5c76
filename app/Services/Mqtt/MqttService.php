<?php

namespace App\Services\Mqtt;

use PhpMqtt\Client\MqttClient;
use PhpMqtt\Client\ConnectionSettings;
use PhpMqtt\Client\Exceptions\MqttClientException;
use Illuminate\Support\Facades\Log;
use Exception;

class MqttService
{
    protected ?MqttClient $client = null;
    protected array $config;
    protected bool $connected = false;

    public function __construct(array $config = [])
    {
        $this->config = array_merge([
            'host' => config('mqtt.host', env('MQTT_HOST', 'localhost')),
            'port' => config('mqtt.port', env('MQTT_PORT', 1883)),
            'username' => config('mqtt.username', env('MQTT_USERNAME')),
            'password' => config('mqtt.password', env('MQTT_PASSWORD')),
            'client_id' => config('mqtt.client_id', 'laravel_' . uniqid()),
            'clean_session' => config('mqtt.clean_session', true),
            'keep_alive' => config('mqtt.keep_alive', 60),
            'timeout' => config('mqtt.timeout', 5),
            'ca_file' => config('mqtt.ca_file'),
            'cert_file' => config('mqtt.cert_file'),
            'key_file' => config('mqtt.key_file'),
            'verify_peer' => config('mqtt.verify_peer', false),
            'verify_peer_name' => config('mqtt.verify_peer_name', false),
        ], $config);
    }

    /**
     * 获取或创建MQTT客户端连接（被动连接）
     */
    protected function getClient(): MqttClient
    {
        if ($this->client === null) {
            $this->client = new MqttClient(
                $this->config['host'],
                $this->config['port'],
                $this->config['client_id']
            );
        }

        return $this->client;
    }

    /**
     * 连接到MQTT服务器
     */
    public function connect(): bool
    {
        try {
            if ($this->connected) {
                return true;
            }

            $client = $this->getClient();
            $connectionSettings = new ConnectionSettings();

            // 设置连接参数
            if (!empty($this->config['username'])) {
                $connectionSettings = $connectionSettings->setUsername($this->config['username']);
            }
            if (!empty($this->config['password'])) {
                $connectionSettings = $connectionSettings->setPassword($this->config['password']);
            }

            $connectionSettings = $connectionSettings
                ->setKeepAliveInterval($this->config['keep_alive'])
                ->setConnectTimeout($this->config['timeout'])
                ->setUseTls(false)
                ->setTlsSelfSignedAllowed(!$this->config['verify_peer'])
                ->setTlsVerifyPeer($this->config['verify_peer'])
                ->setTlsVerifyPeerName($this->config['verify_peer_name']);

            // 设置SSL证书
            if (!empty($this->config['ca_file'])) {
                $connectionSettings = $connectionSettings->setTlsCertificateAuthorityFile($this->config['ca_file']);
            }
            if (!empty($this->config['cert_file'])) {
                $connectionSettings = $connectionSettings->setTlsClientCertificateFile($this->config['cert_file']);
            }
            if (!empty($this->config['key_file'])) {
                $connectionSettings = $connectionSettings->setTlsClientCertificateKeyFile($this->config['key_file']);
            }

            $client->connect($connectionSettings, $this->config['clean_session']);
            $this->connected = true;

            Log::info('MQTT连接成功', [
                'host' => $this->config['host'],
                'port' => $this->config['port'],
                'client_id' => $this->config['client_id']
            ]);

            return true;
        } catch (MqttClientException $e) {
            Log::error('MQTT连接失败', [
                'error' => $e->getMessage(),
                'host' => $this->config['host'],
                'port' => $this->config['port']
            ]);
            return false;
        }
    }

    /**
     * 断开连接
     */
    public function disconnect(): bool
    {
        try {
            if ($this->client && $this->connected) {
                $this->client->disconnect();
                $this->connected = false;
                Log::info('MQTT连接已断开');
            }
            return true;
        } catch (Exception $e) {
            Log::error('MQTT断开连接失败', ['error' => $e->getMessage()]);
            return false;
        }
    }

    /**
     * 发布消息
     */
    public function publish(string $topic, string $message, int $qos = 0, bool $retain = false): bool
    {
        try {
            if (!$this->connect()) {
                return false;
            }

            $this->getClient()->publish($topic, $message, $qos, $retain);

            Log::debug('MQTT消息发布成功', [
                'topic' => $topic,
                'message' => $message,
                'qos' => $qos,
                'retain' => $retain
            ]);

            return true;
        } catch (Exception $e) {
            Log::error('MQTT消息发布失败', [
                'topic' => $topic,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * 订阅主题
     */
    public function subscribe(string $topic, callable $callback, int $qos = 0): bool
    {
        try {
            if (!$this->connect()) {
                return false;
            }

            $this->getClient()->subscribe($topic, $callback, $qos);

            Log::info('MQTT主题订阅成功', [
                'topic' => $topic,
                'qos' => $qos
            ]);

            return true;
        } catch (Exception $e) {
            Log::error('MQTT主题订阅失败', [
                'topic' => $topic,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * 取消订阅
     */
    public function unsubscribe(string $topic): bool
    {
        try {
            if (!$this->connected) {
                return true;
            }

            $this->getClient()->unsubscribe($topic);

            Log::info('MQTT取消订阅成功', ['topic' => $topic]);

            return true;
        } catch (Exception $e) {
            Log::error('MQTT取消订阅失败', [
                'topic' => $topic,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * 循环监听消息
     */
    public function loop(bool $allowSleep = true, bool $exitWhenEmpty = false, int $timeout = 1): void
    {
        try {
            if (!$this->connect()) {
                return;
            }

            $this->getClient()->loop($allowSleep, $exitWhenEmpty, $timeout);
        } catch (Exception $e) {
            Log::error('MQTT循环监听失败', ['error' => $e->getMessage()]);
        }
    }

    /**
     * 中断循环
     */
    public function interrupt(): void
    {
        try {
            if ($this->client) {
                $this->client->interrupt();
            }
        } catch (Exception $e) {
            Log::error('MQTT中断循环失败', ['error' => $e->getMessage()]);
        }
    }

    /**
     * 检查连接状态
     */
    public function isConnected(): bool
    {
        return $this->connected && $this->client !== null;
    }

    /**
     * 获取客户端ID
     */
    public function getClientId(): string
    {
        return $this->config['client_id'];
    }

    /**
     * 获取配置信息
     */
    public function getConfig(): array
    {
        return $this->config;
    }

    /**
     * 析构函数，自动断开连接
     */
    public function __destruct()
    {
        $this->disconnect();
    }
}
