<?php

namespace App\Services\Mqtt\Examples;

use App\Services\Mqtt\MqttService;
use App\Services\Mqtt\MqttMessageHandler;
use Illuminate\Support\Facades\Log;

/**
 * MQTT使用示例类
 */
class MqttExample
{
    protected MqttService $mqtt;
    protected MqttMessageHandler $messageHandler;

    public function __construct(MqttService $mqtt)
    {
        $this->mqtt = $mqtt;
        $this->messageHandler = new MqttMessageHandler();
        $this->setupMessageHandlers();
    }

    /**
     * 设置消息处理器
     */
    protected function setupMessageHandlers(): void
    {
        // 处理设备状态消息
        $this->messageHandler->register('device/+/status', function ($topic, $message, $properties) {
            $deviceId = $this->extractDeviceId($topic);
            Log::info("设备状态更新", [
                'device_id' => $deviceId,
                'status' => $message,
                'topic' => $topic
            ]);
        });

        // 处理传感器数据
        $this->messageHandler->register('sensor/+/data', function ($topic, $message, $properties) {
            $sensorId = $this->extractSensorId($topic);
            $data = json_decode($message, true);
            
            Log::info("传感器数据", [
                'sensor_id' => $sensorId,
                'data' => $data,
                'topic' => $topic
            ]);
        });

        // 处理系统通知
        $this->messageHandler->register('system/notification/#', function ($topic, $message, $properties) {
            Log::info("系统通知", [
                'topic' => $topic,
                'message' => $message
            ]);
        });
    }

    /**
     * 发布设备控制命令
     */
    public function publishDeviceCommand(string $deviceId, string $command, array $params = []): bool
    {
        $topic = "device/{$deviceId}/command";
        $message = json_encode([
            'command' => $command,
            'params' => $params,
            'timestamp' => time()
        ]);

        return $this->mqtt->publish($topic, $message, 1);
    }

    /**
     * 发布传感器配置
     */
    public function publishSensorConfig(string $sensorId, array $config): bool
    {
        $topic = "sensor/{$sensorId}/config";
        $message = json_encode($config);

        return $this->mqtt->publish($topic, $message, 1, true); // 保留消息
    }

    /**
     * 订阅设备主题
     */
    public function subscribeToDevice(string $deviceId): bool
    {
        $topics = [
            "device/{$deviceId}/status",
            "device/{$deviceId}/data",
            "device/{$deviceId}/error"
        ];

        foreach ($topics as $topic) {
            $success = $this->mqtt->subscribe($topic, function ($topic, $message, $properties) {
                $this->messageHandler->handle($topic, $message, $properties);
            }, 1);

            if (!$success) {
                return false;
            }
        }

        return true;
    }

    /**
     * 批量发布系统状态
     */
    public function publishSystemStatus(array $status): bool
    {
        $messages = [];
        
        foreach ($status as $key => $value) {
            $messages[] = [
                'topic' => "system/status/{$key}",
                'message' => json_encode($value),
                'qos' => 0,
                'retain' => true
            ];
        }

        // 批量发布
        foreach ($messages as $msg) {
            $success = $this->mqtt->publish(
                $msg['topic'], 
                $msg['message'], 
                $msg['qos'], 
                $msg['retain']
            );
            
            if (!$success) {
                return false;
            }
        }

        return true;
    }

    /**
     * 启动消息监听
     */
    public function startListening(int $timeout = 60): void
    {
        Log::info("开始MQTT消息监听", ['timeout' => $timeout]);
        
        // 订阅所有需要的主题
        $this->mqtt->subscribe('device/+/status', function ($topic, $message, $properties) {
            $this->messageHandler->handle($topic, $message, $properties);
        });

        $this->mqtt->subscribe('sensor/+/data', function ($topic, $message, $properties) {
            $this->messageHandler->handle($topic, $message, $properties);
        });

        $this->mqtt->subscribe('system/notification/#', function ($topic, $message, $properties) {
            $this->messageHandler->handle($topic, $message, $properties);
        });

        // 开始循环监听
        $startTime = time();
        while (time() - $startTime < $timeout) {
            $this->mqtt->loop(true, false, 1);
        }

        Log::info("MQTT消息监听结束");
    }

    /**
     * 从主题中提取设备ID
     */
    protected function extractDeviceId(string $topic): ?string
    {
        if (preg_match('/device\/([^\/]+)\//', $topic, $matches)) {
            return $matches[1];
        }
        return null;
    }

    /**
     * 从主题中提取传感器ID
     */
    protected function extractSensorId(string $topic): ?string
    {
        if (preg_match('/sensor\/([^\/]+)\//', $topic, $matches)) {
            return $matches[1];
        }
        return null;
    }

    /**
     * 发送心跳消息
     */
    public function sendHeartbeat(): bool
    {
        $message = json_encode([
            'client_id' => $this->mqtt->getClientId(),
            'timestamp' => time(),
            'status' => 'online'
        ]);

        return $this->mqtt->publish('system/heartbeat', $message, 0, true);
    }

    /**
     * 发送离线消息
     */
    public function sendOfflineMessage(): bool
    {
        $message = json_encode([
            'client_id' => $this->mqtt->getClientId(),
            'timestamp' => time(),
            'status' => 'offline'
        ]);

        return $this->mqtt->publish('system/heartbeat', $message, 0, true);
    }
}
