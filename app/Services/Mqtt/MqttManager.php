<?php

namespace App\Services\Mqtt;

use Illuminate\Support\Manager;

class MqttManager extends Manager
{
    /**
     * 获取默认驱动名称
     */
    public function getDefaultDriver(): string
    {
        return 'default';
    }

    /**
     * 创建默认MQTT连接
     */
    public function createDefaultDriver(): MqttService
    {
        return new MqttService();
    }

    /**
     * 创建自定义MQTT连接
     */
    public function createCustomDriver(array $config): MqttService
    {
        return new MqttService($config);
    }

    /**
     * 快速发布消息
     */
    public function publish(string $topic, string $message, int $qos = 0, bool $retain = false): bool
    {
        return $this->driver()->publish($topic, $message, $qos, $retain);
    }

    /**
     * 快速订阅主题
     */
    public function subscribe(string $topic, callable $callback, int $qos = 0): bool
    {
        return $this->driver()->subscribe($topic, $callback, $qos);
    }

    /**
     * 批量发布消息
     */
    public function publishBatch(array $messages): array
    {
        $results = [];
        $service = $this->driver();
        
        foreach ($messages as $message) {
            $topic = $message['topic'] ?? '';
            $payload = $message['message'] ?? '';
            $qos = $message['qos'] ?? 0;
            $retain = $message['retain'] ?? false;
            
            $results[] = [
                'topic' => $topic,
                'success' => $service->publish($topic, $payload, $qos, $retain)
            ];
        }
        
        return $results;
    }

    /**
     * 批量订阅主题
     */
    public function subscribeBatch(array $subscriptions): array
    {
        $results = [];
        $service = $this->driver();
        
        foreach ($subscriptions as $subscription) {
            $topic = $subscription['topic'] ?? '';
            $callback = $subscription['callback'] ?? null;
            $qos = $subscription['qos'] ?? 0;
            
            if ($callback && is_callable($callback)) {
                $results[] = [
                    'topic' => $topic,
                    'success' => $service->subscribe($topic, $callback, $qos)
                ];
            }
        }
        
        return $results;
    }
}
