<?php

namespace App\Services\Mqtt\Commands;

use Illuminate\Console\Command;
use App\Services\Mqtt\MqttService;
use App\Services\Mqtt\MqttMessageHandler;

class MqttTestCommand extends Command
{
    /**
     * 命令签名
     */
    protected $signature = 'mqtt:test 
                            {action : 操作类型 (publish|subscribe|listen)}
                            {--topic=test/topic : MQTT主题}
                            {--message=Hello MQTT! : 发布的消息内容}
                            {--qos=0 : QoS级别 (0,1,2)}
                            {--retain : 是否保留消息}
                            {--timeout=30 : 监听超时时间(秒)}';

    /**
     * 命令描述
     */
    protected $description = 'MQTT功能测试命令';

    protected MqttService $mqtt;
    protected MqttMessageHandler $handler;

    public function __construct(MqttService $mqtt)
    {
        parent::__construct();
        $this->mqtt = $mqtt;
        $this->handler = new MqttMessageHandler();
    }

    /**
     * 执行命令
     */
    public function handle(): int
    {
        $action = $this->argument('action');

        switch ($action) {
            case 'publish':
                return $this->handlePublish();
            case 'subscribe':
                return $this->handleSubscribe();
            case 'listen':
                return $this->handleListen();
            default:
                $this->error("未知操作: {$action}");
                $this->info("可用操作: publish, subscribe, listen");
                return 1;
        }
    }

    /**
     * 处理发布消息
     */
    protected function handlePublish(): int
    {
        $topic = $this->option('topic');
        $message = $this->option('message');
        $qos = (int) $this->option('qos');
        $retain = $this->option('retain');

        $this->info("正在发布消息到MQTT服务器...");
        $this->info("主题: {$topic}");
        $this->info("消息: {$message}");
        $this->info("QoS: {$qos}");
        $this->info("保留: " . ($retain ? '是' : '否'));

        if ($this->mqtt->publish($topic, $message, $qos, $retain)) {
            $this->info("✅ 消息发布成功!");
            return 0;
        } else {
            $this->error("❌ 消息发布失败!");
            return 1;
        }
    }

    /**
     * 处理订阅消息
     */
    protected function handleSubscribe(): int
    {
        $topic = $this->option('topic');
        $qos = (int) $this->option('qos');

        $this->info("正在订阅MQTT主题...");
        $this->info("主题: {$topic}");
        $this->info("QoS: {$qos}");

        $success = $this->mqtt->subscribe($topic, function ($receivedTopic, $message, $properties = []) {
            $this->info("📨 收到消息:");
            $this->info("  主题: {$receivedTopic}");
            $this->info("  消息: {$message}");
            $this->info("  时间: " . date('Y-m-d H:i:s'));
            
            if (!empty($properties)) {
                $this->info("  属性: " . json_encode($properties));
            }
        }, $qos);

        if ($success) {
            $this->info("✅ 订阅成功!");
            $this->info("按 Ctrl+C 停止监听");
            
            // 开始监听消息
            try {
                while (true) {
                    $this->mqtt->loop(true, false, 1);
                }
            } catch (\Exception $e) {
                $this->info("监听已停止");
            }
            
            return 0;
        } else {
            $this->error("❌ 订阅失败!");
            return 1;
        }
    }

    /**
     * 处理监听多个主题
     */
    protected function handleListen(): int
    {
        $timeout = (int) $this->option('timeout');
        
        $this->info("正在启动MQTT消息监听器...");
        $this->info("超时时间: {$timeout}秒");
        
        // 设置消息处理器
        $this->setupMessageHandlers();
        
        // 订阅多个测试主题
        $topics = [
            'test/+',
            'device/+/status',
            'sensor/+/data',
            'system/#'
        ];

        foreach ($topics as $topic) {
            $success = $this->mqtt->subscribe($topic, function ($receivedTopic, $message, $properties = []) {
                $this->handler->handle($receivedTopic, $message, $properties);
            }, 1);

            if ($success) {
                $this->info("✅ 已订阅: {$topic}");
            } else {
                $this->error("❌ 订阅失败: {$topic}");
            }
        }

        $this->info("🎧 开始监听消息 (按 Ctrl+C 停止)...");
        
        $startTime = time();
        try {
            while (time() - $startTime < $timeout) {
                $this->mqtt->loop(true, false, 1);
                
                // 每10秒显示一次状态
                if ((time() - $startTime) % 10 === 0) {
                    $remaining = $timeout - (time() - $startTime);
                    $this->info("⏰ 剩余监听时间: {$remaining}秒");
                }
            }
        } catch (\Exception $e) {
            $this->info("监听已停止: " . $e->getMessage());
        }

        $this->info("🛑 监听结束");
        return 0;
    }

    /**
     * 设置消息处理器
     */
    protected function setupMessageHandlers(): void
    {
        // 处理测试消息
        $this->handler->register('test/+', function ($topic, $message, $properties) {
            $this->info("🧪 [测试消息] {$topic}: {$message}");
        });

        // 处理设备状态
        $this->handler->register('device/+/status', function ($topic, $message, $properties) {
            $deviceId = explode('/', $topic)[1] ?? 'unknown';
            $this->info("📱 [设备状态] 设备{$deviceId}: {$message}");
        });

        // 处理传感器数据
        $this->handler->register('sensor/+/data', function ($topic, $message, $properties) {
            $sensorId = explode('/', $topic)[1] ?? 'unknown';
            $this->info("🌡️  [传感器数据] 传感器{$sensorId}: {$message}");
        });

        // 处理系统消息
        $this->handler->register('system/#', function ($topic, $message, $properties) {
            $this->info("⚙️  [系统消息] {$topic}: {$message}");
        });

        // 默认处理器
        $this->handler->register('#', function ($topic, $message, $properties) {
            $this->info("📬 [其他消息] {$topic}: {$message}");
        });
    }
}
