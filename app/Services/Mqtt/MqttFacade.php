<?php

namespace App\Services\Mqtt;

use Illuminate\Support\Facades\Facade;

/**
 * MQTT门面类
 * 
 * @method static bool connect()
 * @method static bool disconnect()
 * @method static bool publish(string $topic, string $message, int $qos = 0, bool $retain = false)
 * @method static bool subscribe(string $topic, callable $callback, int $qos = 0)
 * @method static bool unsubscribe(string $topic)
 * @method static void loop(bool $allowSleep = true, bool $exitWhenEmpty = false, int $timeout = 1)
 * @method static void interrupt()
 * @method static bool isConnected()
 * @method static string getClientId()
 * @method static array getConfig()
 * 
 * @see \App\Services\Mqtt\MqttService
 */
class MqttFacade extends Facade
{
    /**
     * 获取门面对应的服务容器绑定名称
     */
    protected static function getFacadeAccessor(): string
    {
        return 'mqtt';
    }
}
