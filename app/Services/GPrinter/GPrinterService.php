<?php

namespace App\Services\GPrinter;

use App\Services\GPrinter\Exceptions\GPrinterException;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class GPrinterService
{
    protected string $apiBaseUrl;
    protected string $memberCode;
    protected string $apiKey;

    public function __construct()
    {
        $this->apiBaseUrl = config('gprinter.api_base_url');
        $this->memberCode = config('gprinter.member_code');
        $this->apiKey     = config('gprinter.api_key');
    }

    /**
     * 01. 发送打印任务
     *
     * @param string $deviceId  设备ID
     * @param string $msgDetail 打印内容
     * @param array  $options   其他选项
     *
     * @return array
     * @throws GPrinterException
     */
    public function print(string $deviceId, string $msgDetail, array $options = []): array
    {
        $reqTime = $this->getReqTime();

        // 默认参数
        $params = [
            'reqTime'    => $reqTime,
            'memberCode' => $this->memberCode,
            'deviceID'   => $deviceId,
            'mode'       => $options['mode'] ?? '2',
            'msgDetail'  => $msgDetail,
            'charset'    => '1',
        ];

        // 添加可选参数
        if (isset($options['charset']))
        {
            $params['charset'] = $options['charset'];
        }

        if (isset($options['cmdType']))
        {
            $params['cmdType'] = $options['cmdType'];
        }

        if (isset($options['msgNo']))
        {
            $params['msgNo'] = $options['msgNo'];
        }

        if (isset($options['reprint']))
        {
            $params['reprint'] = $options['reprint'];
        }

        if (isset($options['multi']))
        {
            $params['multi'] = $options['multi'];
        }

        if (isset($options['times']))
        {
            $params['times'] = $options['times'];
        }

        if (isset($options['voice']))
        {
            $params['voice'] = $options['voice'];
        }

        // 生成签名
        $params['securityCode'] = $this->generateSecurityCode(
            $this->memberCode . $deviceId . ($params['msgNo'] ?? '') . $reqTime . $this->apiKey
        );

        Log::debug('GPrinter API request sendMsg params', $params);

        $response = $this->sendRequest('apisc/sendMsg', $params);

        Log::debug('GPrinter API sendMsg response', [$response]);

        return $response;
    }

    /**
     * 02. 发送语音播报
     *
     * @param string $deviceId 设备ID
     * @param string $voice    语音内容
     * @param array  $options  其他选项
     *
     * @return array
     * @throws GPrinterException
     */
    public function voice(string $deviceId, string $voice, array $options = []): array
    {
        $reqTime = $this->getReqTime();

        $params = [
            'reqTime'    => $reqTime,
            'memberCode' => $this->memberCode,
            'deviceID'   => $deviceId,
            'voice'      => $voice,
        ];

        // 生成签名
        $params['securityCode'] = $this->generateSecurityCode(
            $this->memberCode . $deviceId . $reqTime . $this->apiKey
        );

        return $this->sendRequest('apisc/sendVoice', $params);
    }

    /**
     * 03. 查询打印设备列表
     *
     * @param string|null $token 授权token
     *
     * @return array
     * @throws GPrinterException
     */
    public function getDeviceList(?string $token = null): array
    {
        $reqTime = $this->getReqTime();

        $params = [
            'reqTime'    => $reqTime,
            'memberCode' => $this->memberCode,
        ];

        if ($token)
        {
            $params['token'] = $token;
        }

        // 生成签名
        $params['securityCode'] = $this->generateSecurityCode(
            $this->memberCode . $reqTime . $this->apiKey
        );

        return $this->sendRequest('apisc/listDevice', $params);
    }

    /**
     * 04. 分页查询设备列表
     *
     * @param int         $pageNum  页码，默认1
     * @param int         $pageSize 每页条数，默认10，支持10,20,50
     * @param string|null $token    授权token
     *
     * @return array
     * @throws GPrinterException
     */
    public function pageDevices(int $pageNum = 1, int $pageSize = 10, ?string $token = null): array
    {
        $reqTime = $this->getReqTime();

        $params = [
            'reqTime'    => $reqTime,
            'memberCode' => $this->memberCode,
            'pageNum'    => $pageNum,
            'pageSize'   => $pageSize,
        ];

        if ($token)
        {
            $params['token'] = $token;
        }

        // 生成签名
        $params['securityCode'] = $this->generateSecurityCode(
            $this->memberCode . $reqTime . $this->apiKey
        );

        return $this->sendRequest('apisc/pageDevices', $params);
    }

    /**
     * 05. 添加打印机
     *
     * @param string   $deviceId 设备ID
     * @param string   $devName  设备名称
     * @param int|null $grpID    分组ID
     *
     * @return array
     * @throws GPrinterException
     */
    public function addDevice(string $deviceId, string $devName, ?int $grpID = null): array
    {
        $reqTime = $this->getReqTime();

        $params = [
            'reqTime'    => $reqTime,
            'memberCode' => $this->memberCode,
            'deviceID'   => $deviceId,
            'devName'    => $devName,
        ];

        if ($grpID !== null)
        {
            $params['grpID'] = $grpID;
        }

        // 生成签名
        $params['securityCode'] = $this->generateSecurityCode(
            $this->memberCode . $reqTime . $this->apiKey . $deviceId
        );

        return $this->sendRequest('apisc/adddev', $params);
    }

    /**
     * 06. 查询打印机信息
     *
     * @param string      $deviceId 设备ID
     * @param string|null $token    授权token
     *
     * @return array
     * @throws GPrinterException
     */
    public function getDevice(string $deviceId, ?string $token = null): array
    {
        $reqTime = $this->getReqTime();

        $params = [
            'reqTime'    => $reqTime,
            'memberCode' => $this->memberCode,
            'deviceID'   => $deviceId,
        ];

        if ($token)
        {
            $params['token'] = $token;
        }

        // 生成签名
        $params['securityCode'] = $this->generateSecurityCode(
            $this->memberCode . $reqTime . $this->apiKey . $deviceId
        );

        return $this->sendRequest('apisc/device', $params, 'GET');
    }

    /**
     * 07. 查询设备详情
     *
     * @param string      $deviceId 设备ID
     * @param string|null $token    授权token
     *
     * @return array
     * @throws GPrinterException
     */
    public function getDeviceInfo(string $deviceId, ?string $token = null): array
    {
        $reqTime = $this->getReqTime();

        $params = [
            'reqTime'    => $reqTime,
            'memberCode' => $this->memberCode,
            'deviceID'   => $deviceId,
        ];

        if ($token)
        {
            $params['token'] = $token;
        }

        // 生成签名
        $params['securityCode'] = $this->generateSecurityCode(
            $this->memberCode . $reqTime . $deviceId . $this->apiKey
        );

        return $this->sendRequest('apisc/deviceInfo', $params);
    }

    /**
     * 08. 修改打印机信息
     *
     * @param string   $deviceId 设备ID
     * @param string   $devName  设备名称
     * @param int|null $grpID    分组ID
     *
     * @return array
     * @throws GPrinterException
     */
    public function editDevice(string $deviceId, string $devName, ?int $grpID = null): array
    {
        $reqTime = $this->getReqTime();

        $params = [
            'reqTime'    => $reqTime,
            'memberCode' => $this->memberCode,
            'deviceID'   => $deviceId,
            'devName'    => $devName,
        ];

        if ($grpID !== null)
        {
            $params['grpID'] = $grpID;
        }

        // 生成签名
        $params['securityCode'] = $this->generateSecurityCode(
            $this->memberCode . $reqTime . $this->apiKey . $deviceId
        );

        return $this->sendRequest('apisc/editdev', $params);
    }

    /**
     * 09. 删除打印机
     *
     * @param string $deviceId 设备ID
     *
     * @return array
     * @throws GPrinterException
     */
    public function deleteDevice(string $deviceId): array
    {
        $reqTime = $this->getReqTime();

        $params = [
            'reqTime'    => $reqTime,
            'memberCode' => $this->memberCode,
            'deviceID'   => $deviceId,
        ];

        // 生成签名
        $params['securityCode'] = $this->generateSecurityCode(
            $this->memberCode . $reqTime . $this->apiKey . $deviceId
        );

        return $this->sendRequest('apisc/deldev', $params);
    }

    /**
     * 10. 取消打印任务
     *
     * @param string $deviceId 设备ID
     * @param int    $all      是否全部取消，1为全部取消
     *
     * @return array
     * @throws GPrinterException
     */
    public function cancelPrint(string $deviceId, int $all = 0): array
    {
        $reqTime = $this->getReqTime();

        $params = [
            'reqTime'    => $reqTime,
            'memberCode' => $this->memberCode,
            'deviceID'   => $deviceId,
            'all'        => $all,
        ];

        // 生成签名
        $params['securityCode'] = $this->generateSecurityCode(
            $this->memberCode . $reqTime . $this->apiKey . $deviceId
        );

        return $this->sendRequest('apisc/cancelPrint', $params);
    }

    /**
     * 11. 获取打印机状态
     *
     * @param string|null $deviceId    设备ID
     * @param string|null $productCode 产品代码
     * @param string|null $token       授权token
     *
     * @return array
     * @throws GPrinterException
     */
    public function getStatus(?string $deviceId = null, ?string $productCode = null, ?string $token = null): array
    {
        $reqTime = $this->getReqTime();

        $params = [
            'reqTime'    => $reqTime,
            'memberCode' => $this->memberCode,
        ];

        if ($deviceId)
        {
            $params['deviceID'] = $deviceId;
        }

        if ($productCode)
        {
            $params['productCode'] = $productCode;
        }

        if ($token)
        {
            $params['token'] = $token;
        }

        // 生成签名
        $params['securityCode'] = $this->generateSecurityCode(
            $this->memberCode . $reqTime . $this->apiKey
        );

        return $this->sendRequest('apisc/getStatus', $params);
    }

    /**
     * 12. 设置Logo
     *
     * @param string $deviceId 设备ID
     * @param string $imgUrl   图片地址
     *
     * @return array
     * @throws GPrinterException
     */
    public function setLogo(string $deviceId, string $imgUrl): array
    {
        $reqTime = $this->getReqTime();

        $params = [
            'reqTime'    => $reqTime,
            'memberCode' => $this->memberCode,
            'deviceID'   => $deviceId,
            'imgUrl'     => $imgUrl,
        ];

        // 生成签名
        $params['securityCode'] = $this->generateSecurityCode(
            $this->memberCode . $reqTime . $this->apiKey . $deviceId
        );

        return $this->sendRequest('apisc/setLogo', $params);
    }

    /**
     * 13. 删除Logo
     *
     * @param string $deviceId 设备ID
     *
     * @return array
     * @throws GPrinterException
     */
    public function deleteLogo(string $deviceId): array
    {
        $reqTime = $this->getReqTime();

        $params = [
            'reqTime'    => $reqTime,
            'memberCode' => $this->memberCode,
            'deviceID'   => $deviceId,
        ];

        // 生成签名
        $params['securityCode'] = $this->generateSecurityCode(
            $this->memberCode . $reqTime . $this->apiKey . $deviceId
        );

        return $this->sendRequest('apisc/deleteLogo', $params);
    }

    /**
     * 14. 设置打印音量
     *
     * @param string $deviceId 设备ID
     * @param int    $volume   音量大小，取值范围 0-100
     *
     * @return array
     * @throws GPrinterException
     */
    public function setVolume(string $deviceId, int $volume): array
    {
        $reqTime = $this->getReqTime();

        $params = [
            'reqTime'    => $reqTime,
            'memberCode' => $this->memberCode,
            'deviceID'   => $deviceId,
            'volume'     => $volume,
        ];

        // 生成签名
        $params['securityCode'] = $this->generateSecurityCode(
            $this->memberCode . $reqTime . $this->apiKey . $deviceId
        );

        return $this->sendRequest('apisc/sendVolume', $params);
    }

    /**
     * 15. 设置语音播报类型
     *
     * @param string $deviceId  设备ID
     * @param int    $voiceType 语音类型
     *
     * @return array
     * @throws GPrinterException
     */
    public function setVoiceType(string $deviceId, int $voiceType): array
    {
        $reqTime = $this->getReqTime();

        $params = [
            'reqTime'    => $reqTime,
            'memberCode' => $this->memberCode,
            'deviceID'   => $deviceId,
            'voiceType'  => $voiceType,
        ];

        // 生成签名
        $params['securityCode'] = $this->generateSecurityCode(
            $this->memberCode . $reqTime . $this->apiKey . $deviceId
        );

        return $this->sendRequest('apisc/setVoiceType', $params);
    }

    /**
     * 16. 查询模板列表
     *
     * @return array
     * @throws GPrinterException
     */
    public function listTemplate(): array
    {
        $reqTime = $this->getReqTime();

        $params = [
            'reqTime'    => $reqTime,
            'memberCode' => $this->memberCode,
        ];

        // 生成签名
        $params['securityCode'] = $this->generateSecurityCode(
            $this->memberCode . $reqTime . $this->apiKey
        );

        return $this->sendRequest('apisc/listTemplate', $params);
    }

    /**
     * 17. 指定模板打印
     *
     * @param string $deviceId   设备ID
     * @param string $templateID 模板ID
     * @param string $tData      模板数据，JSON格式字符串
     * @param array  $options    其他选项
     *
     * @return array
     * @throws GPrinterException
     */
    public function templatePrint(string $deviceId, string $templateID, string $tData, array $options = []): array
    {
        $reqTime = $this->getReqTime();

        $params = [
            'reqTime'    => $reqTime,
            'memberCode' => $this->memberCode,
            'deviceID'   => $deviceId,
            'templetID'  => $templateID,
            'tData'      => $tData,
        ];

        // 添加可选参数
        if (isset($options['charset']))
        {
            $params['charset'] = $options['charset'];
        }

        if (isset($options['msgNo']))
        {
            $params['msgNo'] = $options['msgNo'];
        }

        if (isset($options['reprint']))
        {
            $params['reprint'] = $options['reprint'];
        }

        if (isset($options['multi']))
        {
            $params['multi'] = $options['multi'];
        }

        if (isset($options['times']))
        {
            $params['times'] = $options['times'];
        }

        if (isset($options['productCode']))
        {
            $params['productCode'] = $options['productCode'];
        }

        if (isset($options['token']))
        {
            $params['token'] = $options['token'];
        }

        // 生成签名
        $params['securityCode'] = $this->generateSecurityCode(
            $this->memberCode . $deviceId . ($params['msgNo'] ?? '') . $reqTime . $this->apiKey
        );

        return $this->sendRequest('apisc/templetPrint', $params);
    }

    /**
     * 生成安全签名
     *
     * @param string $data 签名数据
     *
     * @return string
     */
    protected function generateSecurityCode(string $data): string
    {
        return md5($data);
    }

    /**
     * 获取当前时间戳(13位)
     *
     * @return string
     */
    protected function getReqTime(): string
    {
        return strval(round(microtime(true) * 1000));
    }

    /**
     * 发送HTTP请求
     *
     * @param string $endpoint API端点
     * @param array  $data     请求数据
     * @param string $method   HTTP方法，默认POST
     *
     * @return array
     * @throws GPrinterException
     */
    protected function sendRequest(string $endpoint, array $data, string $method = 'POST'): array
    {
        try
        {
            $url = rtrim($this->apiBaseUrl, '/') . '/' . ltrim($endpoint, '/');

            if ($method === 'GET')
            {
                $response = Http::asForm()
                                ->get($url, $data);
            }
            else
            {
                $response = Http::asForm()
                                ->post($url, $data);
            }

            if ($response->failed())
            {
                Log::error('GPrinter API request failed', [
                    'url'      => $url,
                    'data'     => $data,
                    'method'   => $method,
                    'status'   => $response->status(),
                    'response' => $response->body(),
                ]);

                throw new GPrinterException('API request failed with status: ' . $response->status());
            }

            $result = $response->json();

            // 检查API返回码
            if (!isset($result['code']) || $result['code'] < 0)
            {
                $message = $result['msg'] ?? 'Unknown error';
                Log::error('GPrinter API returned error', [
                    'code'    => $result['code'] ?? null,
                    'message' => $message,
                    'data'    => $data,
                ]);

                throw new GPrinterException('API error: ' . $message, $result['code'] ?? - 1);
            }

            return $result;
        } catch (\Exception $e)
        {
            Log::error('GPrinter API request exception', [
                'message' => $e->getMessage(),
                'trace'   => $e->getTraceAsString(),
            ]);

            throw new GPrinterException('Failed to send request: ' . $e->getMessage(), $e->getCode(), $e);
        }
    }
}
