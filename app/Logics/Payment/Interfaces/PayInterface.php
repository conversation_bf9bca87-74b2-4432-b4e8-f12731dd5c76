<?php

namespace App\Logics\Payment\Interfaces;

use App\Logics\LogicResult;

interface PayInterface
{
    /**
     * 构建支付请求的数据
     *
     * @param array $params
     *
     * @return LogicResult
     */
    public static function BuildPayRequestData(array $params): LogicResult;

    /**
     * 请求支付
     *
     * @param array $requestData
     *
     * @return LogicResult
     */
    public static function RequestPay(array $requestData): LogicResult;

    /**
     * 支付结果确认
     *
     * @param array $params
     *
     * @return LogicResult
     */
    public static function ResponsePay(array $params): LogicResult;
}
