<?php

namespace App\Logics\Payment\ZeroPay;

use Illuminate\Support\Arr;
use App\Logics\Logic;
use App\Logics\LogicResult;
use App\Logics\Payment\Interfaces\PayInterface;
use App\Enums\PayResultEnum;

class ZeroPayLogic extends Logic implements PayInterface
{
    public static function BuildPayRequestData(array $params): LogicResult
    {
        $payOrderCode = trim(Arr::get($params, 'payOrderCode', ''));
        $payPrice     = trim(Arr::get($params, 'payPrice', ''));
        $payChannelId = intval(Arr::get($params, 'payChannelId', 0));
        $payModeId    = intval(Arr::get($params, 'payModeId', 0));
        $cashierId    = intval(Arr::get($params, 'cashierId', 0));

        if (empty($payOrderCode))
        {
            return self::Fail('结算单号参数必选', 400);
        }
        if (!is_numeric($payPrice) || bccomp($payPrice, '0', 2) != 0)
        {
            return self::Fail('支付金额参数不适用于0元支付', 400);
        }
        if ($payChannelId != 0)
        {
            return self::Fail('支付渠道参数不适用于0元支付', 400);
        }
        if ($payModeId != 0)
        {
            return self::Fail('支付方式参数不适用于0元支付', 400);
        }
        if (empty($cashierId))
        {
            return self::Fail('收银员参数必选', 400);
        }

        $requestData = [
            'payOrderCode' => $payOrderCode,
            'payPrice'     => $payPrice,
            'payChannelId' => $payChannelId,
            'payModeId'    => $payModeId,
            'cashierId'    => $cashierId,
        ];

        return self::Success($requestData);
    }

    public static function RequestPay(array $requestData): LogicResult
    {
        $payOrderCode = trim(Arr::get($requestData, 'payOrderCode', ''));
        $payPrice     = trim(Arr::get($requestData, 'payPrice', ''));
        $payChannelId = intval(Arr::get($requestData, 'payChannelId', 0));
        $payModeId    = intval(Arr::get($requestData, 'payModeId', 0));
        $cashierId    = intval(Arr::get($requestData, 'cashierId', 0));

        if (empty($payOrderCode))
        {
            return self::Fail('结算单号参数必选', 400);
        }
        if (!is_numeric($payPrice) || bccomp($payPrice, '0', 2) != 0)
        {
            return self::Fail('支付金额参数不适用于0元支付', 400);
        }
        if ($payChannelId != 0)
        {
            return self::Fail('支付渠道参数不适用于0元支付', 400);
        }
        if ($payModeId != 0)
        {
            return self::Fail('支付方式参数不适用于0元支付', 400);
        }
        if (empty($cashierId))
        {
            return self::Fail('收银员参数必选', 400);
        }

        $result = [
            'payResult'     => PayResultEnum::Success->value,
            'payOrderCode'  => $payOrderCode,
            'payPrice'      => $payPrice,
            'payChannelId'  => $payChannelId,
            'payModeId'     => $payModeId,
            'cashierId'     => $cashierId,
            'referenceNo'   => $payOrderCode,//0元使用结算单号作为参考号
            'transactionId' => $payOrderCode,//0元使用结算单号作为交易流水号
            'payTime'       => getCurrentTimeWithMilliseconds(),
        ];

        return self::Success($result);
    }

    public static function ResponsePay(array $params): LogicResult
    {
        $payResult     = Arr::get($params, 'payResult');
        $payOrderCode  = trim(Arr::get($params, 'payOrderCode', ''));
        $payPrice      = trim(Arr::get($params, 'payPrice', ''));
        $payChannelId  = intval(Arr::get($params, 'payChannelId', 0));
        $payModeId     = intval(Arr::get($params, 'payModeId', 0));
        $referenceNo   = trim(Arr::get($params, 'referenceNo', ''));
        $transactionId = trim(Arr::get($params, 'transactionId', ''));
        $payTime       = trim(Arr::get($params, 'payTime', ''));

        if (!PayResultEnum::exists($payResult))
        {
            return self::Fail('无效的支付回调信息，支付结果参数错误', 602900);
        }
        if (empty($payOrderCode))
        {
            return self::Fail('无效的支付回调信息，结算单号参数错误', 602900);
        }
        if (!is_numeric($payPrice) || bccomp($payPrice, '0', 2) != 0)
        {
            return self::Fail('无效的支付回调信息，支付金额参数不适用于0元支付', 602900);
        }
        if ($payChannelId != 0)
        {
            return self::Fail('无效的支付回调信息，支付渠道参数不适用于0元支付', 602900);
        }
        if ($payModeId != 0)
        {
            return self::Fail('无效的支付回调信息，支付方式参数不适用于0元支付', 602900);
        }
        if (empty($referenceNo))
        {
            return self::Fail('无效的支付回调信息，支付参考号参数错误', 602900);
        }
        if (empty($transactionId))
        {
            return self::Fail('无效的支付回调信息，支付交易号参数错误', 602900);
        }
        if (empty($payTime))
        {
            return self::Fail('无效的支付回调信息，支付时间参数错误', 602900);
        }

        return self::Success($params);
    }
}
