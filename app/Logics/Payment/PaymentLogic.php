<?php

namespace App\Logics\Payment;

use Throwable;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Arr;
use App\Logics\Logic;
use App\Logics\LogicResult;
use App\Logics\Payment\Interfaces\PayInterface;
use App\Enums\PayChannelEnum;
use App\Enums\PayResultEnum;
use App\Models\PayRequestRecordsModel;
use App\Models\PayResponseRecordsModel;
use App\Logics\Payment\ManualPay\ManualPayLogic;
use App\Logics\Payment\ZeroPay\ZeroPayLogic;

class PaymentLogic extends Logic
{
    const int PAY_DIRECTION_PAY = 1;//支付
    const int PAY_DIRECTION_REFUND = 2;//退款

    /**
     * 支付渠道与支付逻辑类映射
     *
     * @var array
     */
    const array PAY_CHANNEL_LOGIC_MAP = [
        PayChannelEnum::Zero->value   => ZeroPayLogic::class,
        PayChannelEnum::Manual->value => ManualPayLogic::class,
    ];

    /**
     * 支付渠道是否直接支付
     *
     * true:直接支付（本地调用） false:非直接支付（第三方接口）
     *
     * @var array
     */
    const array PAY_CHANNEL_IS_DIRECT = [
        PayChannelEnum::Zero->value   => true,//0元支付
        PayChannelEnum::Manual->value => true,//手工收款
    ];

    /**
     * 请求支付
     *
     * @param array $params
     * @param array $publicParams
     *
     * @return LogicResult
     */
    public static function RequestPay(array $params, array $publicParams): LogicResult
    {
        $payOrderCode = trim(Arr::get($params, 'payOrderCode', ''));
        $payPrice     = trim(Arr::get($params, 'payPrice', ''));
        $payChannelId = intval(Arr::get($params, 'payChannelId', 0));
        $payModeId    = intval(Arr::get($params, 'payModeId', 0));
        //公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        $orgId      = intval(Arr::get($publicParams, '_hospitalOrgId'));
        $brandId    = intval(Arr::get($publicParams, '_hospitalBrandId'));
        $userId     = intval(Arr::get($publicParams, '_userId'));
        if (empty($payOrderCode))
        {
            return self::Fail('结算单号参数必选', 400);
        }
        if (!is_numeric($payPrice) || bccomp($payPrice, '0', 2) < 0)
        {
            return self::Fail('支付金额参数错误', 400);
        }
        if (bccomp($payPrice, '0', 2) > 0)
        {
            if (empty($payChannelId))
            {
                return self::Fail('支付渠道参数必选', 400);
            }
            if (empty($payModeId))
            {
                return self::Fail('支付方式参数必选', 400);
            }
        }
        if (empty($hospitalId) || empty($orgId) || empty($brandId))
        {
            return self::Fail('公共参数必选', 400);
        }
        if (empty($userId))
        {
            return self::Fail('用户ID参数必选', 400);
        }

        $requestRecordData = [
            'org_id'         => $orgId,
            'brand_id'       => $brandId,
            'hospital_id'    => $hospitalId,
            'pay_order_code' => $payOrderCode,
            'pay_price'      => $payPrice,
            'pay_channel'    => $payChannelId,
            'pay_mode'       => $payModeId,
            'request_data'   => '',
            'type'           => self::PAY_DIRECTION_PAY,
            'status'         => 0,
            'cashier_by'     => $userId,
        ];

        $requestPayParams = [
            ...$params,
            'hospitalId' => $hospitalId,
            'orgId'      => $orgId,
            'brandId'    => $brandId,
            'cashierId'  => $userId,
        ];

        try
        {
            $payLogic = self::GetPayLogicClass($payChannelId);
            if (!self::CheckPayLogicIsSupportPay($payLogic))
            {
                return self::Fail('支付处理逻辑类注册异常', 600100);
            }

            //预构造支付请求参数
            $buildRequestDataRes = $payLogic::BuildPayRequestData($requestPayParams);
            if ($buildRequestDataRes->isFail())
            {
                return $buildRequestDataRes;
            }
            $requestData = $buildRequestDataRes->getData();

            $requestRecordData['request_data'] = json_encode($requestData,
                                                             JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);

            //记录支付请求
            $requestRecordId = PayRequestRecordsModel::insertOne($requestRecordData);
            if (empty($requestRecordId))
            {
                return self::Fail('记录支付请求失败', 602651);
            }

            //发起请求
            $payRes = $payLogic::RequestPay($requestData);
            if ($payRes->isFail())
            {
                return $payRes;
            }
            $payResData = $payRes->getData();

            $result = [
                'payResult'     => $payResData['payResult'],
                'payOrderCode'  => $payResData['payOrderCode'],
                'payPrice'      => $payResData['payPrice'],
                'payChannelId'  => $payChannelId,
                'payModeId'     => $payModeId,
                'referenceNo'   => $payResData['referenceNo'] ?? '',
                'transactionId' => $payResData['transactionId'] ?? '',
                'payTime'       => $payResData['payTime'] ?? '',
                'isDirect'      => self::CheckPayChannelIsDirect($payChannelId),
            ];

            return self::Success($result);
        } catch (Throwable $throwable)
        {
            Log::error(__CLASS__ . '::' . __METHOD__ . ' 支付处理逻辑[支付请求]异常', [
                'code'    => $throwable->getCode(),
                'message' => $throwable->getMessage(),
                'file'    => $throwable->getFile(),
                'line'    => $throwable->getLine(),
                'trace'   => $throwable->getTraceAsString(),
            ]);

            return self::Fail('支付请求异常', 602650);
        }
    }

    /**
     * 解析支付结果
     *
     * @param array $params
     *
     * @return LogicResult
     */
    public static function ResponsePay(array $params): LogicResult
    {
        //获取一下参数
        $payOrderCode = trim(Arr::get($params, 'payOrderCode', ''));
        $payChannelId = intval(Arr::get($params, 'payChannelId', 0));

        //必选参数验证
        if (empty($payOrderCode))
        {
            return self::Fail('结算单号参数必选', 400);
        }
        //兼容0元支付逻辑
        if ($payChannelId < 0)
        {
            return self::Fail('支付渠道参数无效', 400);
        }

        try
        {
            $payLogic = self::GetPayLogicClass($payChannelId);
            if (!self::CheckPayLogicIsSupportPay($payLogic))
            {
                return self::Fail('支付处理逻辑类注册异常', 600100);
            }

            //解析支付结果回调到标准结果
            $parseResponseRes = $payLogic::ResponsePay($params);
            if ($parseResponseRes->isFail())
            {
                return $parseResponseRes;
            }
            $responseData  = $parseResponseRes->getData();
            $payResult     = $responseData['payResult'];
            $payPrice      = $responseData['payPrice'];
            $payModeId     = $responseData['payModeId'];
            $referenceNo   = $responseData['referenceNo'];
            $transactionId = $responseData['transactionId'];
            $payTime       = $responseData['payTime'];
            $result        = $responseData['result'] ?? PayResultEnum::getDescription($payResult);
            $remark        = $responseData['remark'] ?? '';

            $responseRecordData = [
                'pay_order_code' => $payOrderCode,
                'pay_price'      => $payPrice,
                'pay_channel'    => $payChannelId,
                'pay_mode'       => $payModeId,
                'type'           => self::PAY_DIRECTION_PAY,
                'reference_no'   => $referenceNo,
                'transaction_id' => $transactionId,
                'result'         => $result,
                'remark'         => $remark,
                'status'         => PayResultEnum::ToCallbackResult($payResult),
                'paid_at'        => $payTime,
                'response_data'  => json_encode($params, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES),
            ];

            //记录支付响应
            $responseRecordId = PayResponseRecordsModel::insertOne($responseRecordData);
            if (empty($responseRecordId))
            {
                return self::Fail('记录支付响应失败', 602653);
            }

            $responseData['responseRecordId'] = $responseRecordId;

            return self::Success($responseData);
        } catch (Throwable $throwable)
        {
            Log::error(__CLASS__ . '::' . __METHOD__ . ' 支付处理逻辑[支付回调]异常', [
                'code'    => $throwable->getCode(),
                'message' => $throwable->getMessage(),
                'file'    => $throwable->getFile(),
                'line'    => $throwable->getLine(),
                'trace'   => $throwable->getTraceAsString(),
            ]);

            return self::Fail('支付回调异常', 602652);
        }
    }

    private static function GetPayLogicClass(int $payChannelId): ?string
    {
        return self::PAY_CHANNEL_LOGIC_MAP[$payChannelId] ?? null;
    }

    private static function CheckPayChannelIsDirect(int $payChannelId): bool
    {
        return self::PAY_CHANNEL_IS_DIRECT[$payChannelId] ?? false;
    }

    private static function CheckPayLogicIsSupportPay(?string $payLogic): bool
    {
        if (!$payLogic)
        {
            return false;
        }

        return is_subclass_of($payLogic, PayInterface::class);
    }
}
