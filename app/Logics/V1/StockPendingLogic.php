<?php

namespace App\Logics\V1;

use DB;
use Arr;
use Log;
use Throwable;
use App\Enums\PageEnum;
use App\Enums\SheetStatusEnum;
use App\Enums\PaySubOrderTypeEnum;
use App\Enums\StockPendingOrderStatusEnum;
use App\Support\Item\ItemHelper;
use App\Support\Stock\StockPendingHelper;
use App\Support\Stock\StockQuantityConversionHelper;
use App\Logics\Logic;
use App\Logics\LogicResult;
use App\Models\UsersModel;
use App\Models\StockPendingOrderModel;
use App\Models\StockPendingOrderDetailModel;
use App\Models\MedicalOrderModel;
use App\Models\MedicalOrderItemModel;
use App\Models\MedicalOrderItemDetailModel;
use App\Enums\StockPendingOrderSourceTypeEnum;
use App\Enums\StockReduceTypeEnum;
use App\Models\StockPendingOrderAutoOutboundRecordModel;
use App\Models\StockItemShelfReduceModel;

class StockPendingLogic extends Logic
{
    /**
     * 获取创建拣货出库单用户
     *
     * @param array $publicParams
     *
     * @return LogicResult
     */
    public static function GetCreateStockPendingUserOptions(array $publicParams): LogicResult
    {
        if (empty($publicParams))
        {
            return self::Fail('获取创建拣货出库单用户，缺少必选参数', 400);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        if (empty($hospitalId))
        {
            return self::Fail('获取创建拣货出库单用户，缺少医院ID必选参数', 400);
        }

        $getStockPendingUserRes = StockPendingOrderModel::getData(fields: ['created_by'], where: ['hospital_id' => $hospitalId], group: 'created_by');
        if (empty($getStockPendingUserRes))
        {
            return self::Success();
        }

        $userIds    = array_unique(array_column($getStockPendingUserRes, 'created_by'));
        $getUserRes = UsersModel::getUserByIds($userIds);

        $returnUserOptions = [];
        foreach ($getUserRes as $curUser)
        {
            $returnUserOptions[] = [
                'uid'  => $curUser['uid'],
                'name' => $curUser['name'],
            ];
        }

        return self::Success($returnUserOptions);
    }

    /**
     * 获取有效出库单
     *
     * @param int   $pendingOrderId
     * @param array $publicParams
     *
     * @return LogicResult
     */
    public static function GetValidStockPending(int $pendingOrderId, array $publicParams): LogicResult
    {
        if (empty($pendingOrderId))
        {
            return self::Fail('获取有效出库单，缺少出库单ID', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('获取有效出库单，缺少公共必选参数', 400);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        if (empty($hospitalId))
        {
            return self::Fail('获取有效出库单，缺少医院ID必选参数', 400);
        }

        // 获取出库单信息
        $getPendingRes = StockPendingOrderModel::getData(where: ['id' => $pendingOrderId, 'hospital_id' => $hospitalId]);
        $getPendingRes = $getPendingRes ? current($getPendingRes) : [];
        if (empty($getPendingRes))
        {
            return self::Fail('出库单不存在或无效', 44000);
        }

        return self::Success($getPendingRes);
    }

    /**
     * 根据处方ID创建待出库单
     *
     * @param int   $recipeId
     * @param array $publicParams
     *
     * @return LogicResult
     * @throws Throwable
     * @noinspection PhpUnused
     */
    public static function AddStockPendingFromRecipeId(int $recipeId, array $publicParams): LogicResult
    {
        // 参数清理和验证
        if (empty($recipeId))
        {
            return self::Fail('创建处方待出库记录，缺少处方ID', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('创建处方待出库记录，缺少公共参数', 400);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        $userId     = intval(Arr::get($publicParams, '_userId'));
        if (empty($hospitalId))
        {
            return self::Fail('创建处方待出库记录，缺少医院ID', 400);
        }

        // 获取处方
        $getRecipeRes = RecipeLogic::GetValidRecipeById($recipeId, $publicParams);
        if ($getRecipeRes->isFail())
        {
            return $getRecipeRes;
        }

        // 处方信息
        $getRecipeRes = $getRecipeRes->getData();
        $caseId       = $getRecipeRes['case_id'];

        // 获取病历信息
        $getCaseRes = CaseLogic::GetValidCaseById($caseId, $publicParams);
        if ($getCaseRes->isFail())
        {
            return $getCaseRes;
        }

        $getCaseRes           = $getCaseRes->getData();
        $caseSourceType       = $getCaseRes['source_type'];
        $caseSourceRelationId = $getCaseRes['source_relation_id'];

        // 获取医疗订单
        $getOrderRes = MedicalOrderModel::getData(where: [
                                                             'type'        => PaySubOrderTypeEnum::Recipe->value,
                                                             'relation_id' => $recipeId,
                                                             'hospital_id' => $hospitalId,
                                                             'status'      => SheetStatusEnum::Paid->value
                                                         ]);
        $getOrderRes = !empty($getOrderRes) ? current($getOrderRes) : [];
        if (empty($getOrderRes))
        {
            return self::Fail('未找到有效的医疗订单', 501001);
        }

        // 订单编号
        $orderId   = $getOrderRes['id'];
        $orderCode = $getOrderRes['order_code'];

        // 获取订单明细
        $getOrderItemsRes = MedicalOrderItemModel::getData(where: ['order_id' => $orderId, 'hospital_id' => $hospitalId, 'status' => 1]);
        if (empty($getOrderItemsRes))
        {
            return self::Fail('订单中未找到商品信息', 501001);
        }

        // 获取组合明细，可能为空
        $getOrderItemDetailsRes = MedicalOrderItemDetailModel::getData(where: ['order_id' => $orderId, 'hospital_id' => $hospitalId, 'status' => 1]);

        // 组装待出库详情数据
        $getBuildPendingDetailsRes = StockPendingHelper::BuildPendingDetailsData($getOrderItemsRes, $getOrderItemDetailsRes, $publicParams);
        if ($getBuildPendingDetailsRes->isFail())
        {
            return $getBuildPendingDetailsRes;
        }

        // 如果为空，则说明无需出库
        $getBuildPendingDetailsRes = $getBuildPendingDetailsRes->getData();
        if (empty($getBuildPendingDetailsRes))
        {
            return self::Success();
        }

        // 是否存在
        $checkPendingOrderRes = StockPendingOrderModel::getTotalNumber(where: [
                                                                                  'source_type'   => StockPendingOrderSourceTypeEnum::Outpatient->value,
                                                                                  'relation_id'   => $orderId,
                                                                                  'relation_code' => $orderCode,
                                                                              ]);
        if ($checkPendingOrderRes > 0)
        {
            return self::Success();
        }

        try
        {
            DB::beginTransaction();
            $insertStockPendingOrderData = [
                'uid'           => generateUUID(),
                'org_id'        => $getOrderRes['org_id'],
                'brand_id'      => $getOrderRes['brand_id'],
                'hospital_id'   => $getOrderRes['hospital_id'],
                'source_type'   => $caseSourceType,
                'source_id'     => $caseSourceRelationId,
                'case_id'       => $caseId,
                'recipe_id'     => $recipeId,
                'relation_id'   => $orderId,
                'relation_code' => $orderCode,
                'member_id'     => $getOrderRes['member_id'],
                'pet_id'        => $getOrderRes['pet_id'],
                'status'        => StockPendingOrderStatusEnum::PendingDispatch->value,
                'created_by'    => $userId,
            ];
            $stockPendingId              = StockPendingOrderModel::insertOne($insertStockPendingOrderData);

            foreach ($getBuildPendingDetailsRes as &$curPendingDetail)
            {
                $curPendingDetail['pending_order_id'] = $stockPendingId;
                $curPendingDetail['recipe_id']        = $getOrderRes['relation_id'];
            }
            StockPendingOrderDetailModel::insert($getBuildPendingDetailsRes);

            DB::commit();

            // 调用执行出库
            DB::beginTransaction();
            $getPendingOutboundRes = self::StockPendingOutbound($stockPendingId, $publicParams);
            if ($getPendingOutboundRes->isFail())
            {
                return $getPendingOutboundRes;
            }

            // 记录执行日志
            StockPendingOrderAutoOutboundRecordModel::insert([
                                                                 'pending_order_id' => $stockPendingId,
                                                                 'status'           => $getPendingOutboundRes->isSuccess() ? 1 : 2,
                                                                 'response'         => json_encode($getPendingOutboundRes->toArray(), JSON_UNESCAPED_UNICODE),
                                                             ]);
            DB::commit();

            return self::Success();
        } catch (Throwable $throwable)
        {
            DB::rollBack();

            Log::error(__CLASS__ . '::' . __METHOD__ . ' 创建待出库单异常', [
                'code'    => $throwable->getCode(),
                'message' => $throwable->getMessage(),
                'file'    => $throwable->getFile(),
                'line'    => $throwable->getLine(),
                'trace'   => $throwable->getTraceAsString(),
            ]);

            return self::Fail('创建待出库单异常', 44003);
        }
    }

    /**
     * 获取拣货出库单列表
     *
     * @param array $searchParams 搜索参数
     * @param array $publicParams 公共参数
     *
     * @return LogicResult
     */
    public static function GetPendingOrderLists(array $searchParams, array $publicParams): LogicResult
    {
        if (empty($publicParams))
        {
            return self::Fail('获取拣货出库单列表，缺少公共必选参数', 400);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        if (empty($hospitalId))
        {
            return self::Fail('获取拣货出库单列表，缺少医院ID必选参数', 400);
        }

        // 搜索参数处理
        $keywords      = trimWhitespace(Arr::get($searchParams, 'keywords', ''));
        $orderCode     = trimWhitespace(Arr::get($searchParams, 'orderCode', ''));
        $startDate     = trimWhitespace(Arr::get($searchParams, 'startDate', ''));
        $endDate       = trimWhitespace(Arr::get($searchParams, 'endDate', ''));
        $createUserUid = trimWhitespace(Arr::get($searchParams, 'createUserUid', ''));
        $status        = Arr::get($searchParams, 'status');
        $page          = intval(Arr::get($searchParams, 'page', PageEnum::DefaultPageIndex->value)) ?: PageEnum::DefaultPageIndex->value;
        $count         = intval(Arr::get($searchParams, 'count', PageEnum::DefaultPageSize->value)) ?: PageEnum::DefaultPageSize->value;
        if (!empty($startDate) && !checkDateIsValid($startDate))
        {
            return self::Fail('获取拣货出库单列表，开始日期格式错误', 400);
        }
        if (!empty($endDate) && !checkDateIsValid($endDate))
        {
            return self::Fail('获取拣货出库单列表，结束日期格式错误', 400);
        }
        if (is_numeric($status) && StockPendingOrderStatusEnum::notExists($status))
        {
            return self::Fail('获取拣货出库单列表，出库状态错误', 400);
        }

        // 构建查询参数
        $queryParams = [
            'hospitalId'    => $hospitalId,
            'keywords'      => $keywords,
            'relationCode'  => $orderCode,
            'startDate'     => $startDate,
            'endDate'       => $endDate,
            'createUserUid' => $createUserUid,
            'status'        => $status,
        ];

        // 获取拣货出库单列表数据
        $pendingOrderListRes = StockPendingOrderModel::getPendingOrderListData($queryParams, $page, $count);
        $totalCount          = $pendingOrderListRes['total'] ?? 0;
        $pendingOrderList    = $pendingOrderListRes['data'] ?? [];
        if ($totalCount <= 0 || empty($pendingOrderList))
        {
            return self::Success(['total' => 0, 'data' => []]);
        }

        $getFormatPendingOrderRes = self::FormatPendingOrder($pendingOrderList);
        if ($getFormatPendingOrderRes->isFail())
        {
            return $getFormatPendingOrderRes;
        }

        return self::Success(['total' => $totalCount, 'data' => $getFormatPendingOrderRes->getData()]);
    }

    /**
     * 获取拣货出库单详情
     *
     * @param int   $pendingOrderId
     * @param array $publicParams
     *
     * @return LogicResult
     */
    public static function GetPendingOrderDetail(int $pendingOrderId, array $publicParams): LogicResult
    {
        if (empty($pendingOrderId))
        {
            return self::Fail('获取拣货出库单详情，缺少出库单ID必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('获取拣货出库单详情，缺少公共必选参数', 400);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        if (empty($hospitalId))
        {
            return self::Fail('获取拣货出库单详情，缺少医院ID必选参数', 400);
        }

        // 获取拣货出库单信息
        $getPendingOrderRes = StockPendingOrderModel::getPendingOrderListData(['pendingOrderId' => $pendingOrderId, 'hospitalId' => $hospitalId]);
        $getPendingOrderRes = !empty($getPendingOrderRes['data']) ? current($getPendingOrderRes['data']) : [];
        if (empty($getPendingOrderRes))
        {
            return self::Fail('拣货出库单不存在', 44000);
        }

        // 格式化拣货出库单信息
        $getFormatPendingOrderRes = self::FormatPendingOrder([$getPendingOrderRes]);
        if ($getFormatPendingOrderRes->isFail())
        {
            return $getFormatPendingOrderRes;
        }

        $getFormatPendingOrderRes = $getFormatPendingOrderRes->getData(0);
        if (empty($getFormatPendingOrderRes))
        {
            return self::Fail('拣货出库单不存在', 44000);
        }

        // 获取拣货出库单商品明细
        $getPendingOrderDetailRes = StockPendingOrderDetailModel::getData(where: ['pending_order_id' => $pendingOrderId]);
        if (empty($getPendingOrderDetailRes))
        {
            return self::Fail('拣货出库单商品明细不存在', 44001);
        }

        // 获取商品基础信息
        $itemIds        = array_unique(array_column($getPendingOrderDetailRes, 'item_id'));
        $getItemInfoRes = ItemLogic::GetItemFullInfo(itemIds: $itemIds, publicParams: $publicParams);
        if ($getItemInfoRes->isFail())
        {
            return $getItemInfoRes;
        }

        $getItemInfoRes = $getItemInfoRes->getData();
        $getItemInfoRes = array_column($getItemInfoRes, null, 'id');

        $returnPendingOrderDetail = [];
        foreach ($getPendingOrderDetailRes as $curPendingOrderDetail)
        {
            $curItemInfo = $getItemInfoRes[$curPendingOrderDetail['item_id']] ?? [];
            if (empty($curItemInfo))
            {
                continue;
            }

            $returnPendingOrderDetail[] = [
                'uid'                  => $curPendingOrderDetail['uid'],
                'itemUid'              => $curItemInfo['uid'],
                'itemBarcode'          => $curItemInfo['item_barcode_info']['item_barcode'] ?? '',
                'itemName'             => ItemHelper::ItemDisplayName($curItemInfo),
                'packQuantity'         => $curPendingOrderDetail['pack_quantity'],
                'bulkQuantity'         => $curPendingOrderDetail['bulk_quantity'],
                'outboundPackQuantity' => $curPendingOrderDetail['outbound_pack_quantity'],
                'outboundBulkQuantity' => $curPendingOrderDetail['outbound_bulk_quantity'],
                'itemInfo'             => ItemHelper::FormatItemInfoStructure($curItemInfo),
            ];
        }

        $getFormatPendingOrderRes['items'] = $returnPendingOrderDetail;

        return self::Success($getFormatPendingOrderRes);
    }

    /**
     * 出库单出库
     *
     * @param int $pendingOrderId
     * @param     $publicParams
     *
     * @return LogicResult
     * @throws Throwable
     */
    public static function StockPendingOutbound(int $pendingOrderId, $publicParams): LogicResult
    {
        if (empty($pendingOrderId))
        {
            return self::Fail('执行待出库单出库，缺少待出库单ID', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('执行待出库单出库，缺少公共参数', 400);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        $userId     = intval(Arr::get($publicParams, '_userId'));
        if (empty($hospitalId) || empty($userId))
        {
            return self::Fail('执行待出库单出库，缺少必选参数', 400);
        }

        // 获取待出库单
        $getPendingRes = self::GetValidStockPending($pendingOrderId, $publicParams);
        if ($getPendingRes->isFail())
        {
            return $getPendingRes;
        }

        $getPendingRes = $getPendingRes->getData();
        if ($getPendingRes['status'] != 0)
        {
            return self::Fail('执行待出库单出库，出库单已出库，请勿重复出库', 44002);
        }

        // 获取待出库详情
        $getPendingDetailsRes = StockPendingOrderDetailModel::getData(where: [
                                                                                 'pending_order_id' => $pendingOrderId,
                                                                                 'hospital_id'      => $hospitalId,
                                                                                 'status'           => 1
                                                                             ]);
        if (empty($getPendingDetailsRes))
        {
            return self::Fail('出库单明细不存在', 44001);
        }

        // 获取商品信息
        $itemIds        = array_unique(array_column($getPendingDetailsRes, 'item_id'));
        $getItemInfoRes = ItemLogic::GetItemFullInfo(itemIds: $itemIds, publicParams: $publicParams);
        if ($getItemInfoRes->isFail())
        {
            return $getItemInfoRes;
        }

        $getItemInfoRes = array_column($getItemInfoRes->getData(), null, 'id');
        if (count($itemIds) != count($getItemInfoRes))
        {
            return self::Fail('部分商品信息不存在', 33000);
        }

        // 是否存在相关业务数据出库记录
        $getReduceTotalRes = StockItemShelfReduceModel::getTotalNumber(where: [
                                                                                  'type'        => StockReduceTypeEnum::OutpatientUse->value,
                                                                                  'relation_id' => $getPendingRes['id'],
                                                                                  'status'      => 1
                                                                              ]);
        if ($getReduceTotalRes > 0)
        {
            return self::Fail('执行待出库单出库，请勿重复出库', 44002);
        }

        // 组合商品出库信息
        $outboundItemData = [];
        foreach ($getPendingDetailsRes as $curPendingItem)
        {
            $curItemId      = $curPendingItem['item_id'];
            $curItemInfo    = $getItemInfoRes[$curItemId] ?? [];
            $curItemBarcode = $curItemInfo['item_barcode_info']['item_barcode'] ?? '';
            if (empty($curItemInfo) || empty($curItemBarcode))
            {
                continue;
            }

            if ($curPendingItem['is_outbound_complete'] == 1)
            {
                continue;
            }

            $outboundItemData[] = [
                'itemId'           => $curItemInfo['id'],
                'itemBarcode'      => $curItemBarcode,
                'reduceType'       => StockReduceTypeEnum::OutpatientUse->value,
                'reduceSubType'    => 0,
                'relationCode'     => $getPendingRes['relation_code'],
                'relationId'       => $getPendingRes['relation_id'],
                'relationDetailId' => $curPendingItem['order_item_id'],
                'packQuantity'     => $curPendingItem['pack_quantity'],
                'bulkQuantity'     => $curPendingItem['bulk_quantity'],
                'remark'           => StockReduceTypeEnum::getDescription(StockReduceTypeEnum::OutpatientUse->value),
            ];
        }

        if (empty($outboundItemData))
        {
            return self::Fail('无有效出库明细记录', 44001);
        }

        try
        {
            DB::beginTransaction();

            // 执行出库操作
            $getOutboundRes = StockItemShelfReduceLogic::ReduceStockShelfQuantity($outboundItemData, $publicParams);
            if ($getOutboundRes->isFail())
            {
                DB::rollBack();

                return $getOutboundRes;
            }

            // 汇总商品出库数量，统一转化成散装数量
            $outboundItemQuantity = [];
            $getOutboundRes       = $getOutboundRes->getData();
            foreach ($getOutboundRes as $curOutboundItemId => $curOutboundInfo)
            {
                $curItemInfo                              = $getItemInfoRes[$curOutboundItemId] ?? [];
                $curItemRatio                             = $curItemInfo['bulk_ratio'];
                $outboundItemQuantity[$curOutboundItemId] = StockQuantityConversionHelper::convertToTotalBulkQuantity($curOutboundInfo['reducePackQuantity'],
                                                                                                                      $curOutboundInfo['reduceBulkQuantity'],
                                                                                                                      $curItemRatio);
            }

            // 更新待出库单明细出库信息
            foreach ($getPendingDetailsRes as $curPendingDetail)
            {
                $curItemId    = $curPendingDetail['item_id'];
                $curItemInfo  = $getItemInfoRes[$curItemId] ?? [];
                $curItemRatio = $curItemInfo['bulk_ratio'];
                if (empty($curItemInfo))
                {
                    continue;
                }

                // 实际出库的总散装数量（当前商品剩余可分配数量）
                $actualTotalBulk = $outboundItemQuantity[$curItemId] ?? 0;
                if (bccomp($actualTotalBulk, 0, 2) <= 0)
                {
                    $curUpdateData = ['lack_reason' => 1];
                }
                else
                {
                    // 当前出库记录需要出库的总散装数量
                    $needOutPackQuantity      = $curPendingDetail['pack_quantity'];
                    $needOutBulkQuantity      = $curPendingDetail['bulk_quantity'];
                    $needOutTotalBulkQuantity = StockQuantityConversionHelper::convertToTotalBulkQuantity($needOutPackQuantity, $needOutBulkQuantity, $curItemRatio);

                    // 分配给当前记录的散装数量
                    $allocatedBulk = bccomp($actualTotalBulk, $needOutTotalBulkQuantity, 2) >= 0 ? $needOutTotalBulkQuantity : $actualTotalBulk;

                    // 从总出库数量中扣减已分配的数量
                    $outboundItemQuantity[$curItemId] = numberSub([$actualTotalBulk, $allocatedBulk]);

                    // 转换为整散格式
                    $allocatedResult       = StockQuantityConversionHelper::convertToPackAndBulkQuantity($allocatedBulk, $curItemRatio);
                    $allocatedPack         = $allocatedResult['packQuantity'];
                    $allocatedBulkQuantity = $allocatedResult['bulkQuantity'];

                    // 是否全部出库完成（比较总散装数量）
                    $isOutboundComplete = bccomp($allocatedBulk, $needOutTotalBulkQuantity, 2) >= 0 ? 1 : 0;

                    $curUpdateData = [
                        'outbound_pack_quantity' => $allocatedPack,
                        'outbound_bulk_quantity' => $allocatedBulkQuantity,
                        'is_outbound_complete'   => $isOutboundComplete,
                    ];
                }

                // 更新出库记录
                StockPendingOrderDetailModel::updateOne($curPendingDetail['id'], $curUpdateData);
            }

            // 更新出库单状态
            StockPendingOrderModel::updateOne($pendingOrderId, ['status' => 1, 'outbound_at' => getCurrentTimeWithMilliseconds()]);

            // 删除处方库存占用
            $getDeleteOccupyRes = RecipeStockOccupyLogic::DeleteRecipeStockOccupy($getPendingRes['recipe_id'], $publicParams);
            if ($getDeleteOccupyRes->isFail())
            {
                DB::rollBack();

                return self::Fail('执行待出库单出库，删除处方库存占用失败', 39011);
            }

            DB::commit();

            return self::Success();

        } catch (Throwable $throwable)
        {
            DB::rollBack();

            Log::error(__CLASS__ . '::' . __METHOD__ . ' 执行待出库单出库异常', [
                'code'    => $throwable->getCode(),
                'message' => $throwable->getMessage(),
                'file'    => $throwable->getFile(),
                'line'    => $throwable->getLine(),
                'trace'   => $throwable->getTraceAsString(),
            ]);

            return self::Fail('执行待出库单出库异常', 45004);
        }
    }

    /**
     * 格式化拣货单
     *
     * @param array $arrStockPendingOrder
     *
     * @return LogicResult
     */
    private static function FormatPendingOrder(array $arrStockPendingOrder): LogicResult
    {
        if (empty($arrStockPendingOrder))
        {
            return self::Success();
        }

        $returnPendingOrderList = [];
        foreach ($arrStockPendingOrder as $curPendingOrderInfo)
        {
            $returnPendingOrderList[] = [
                'uid'          => $curPendingOrderInfo['uid'],
                'relationCode' => $curPendingOrderInfo['relation_code'],
                'petInfo'      => [
                    'uid'  => $curPendingOrderInfo['pet_uid'],
                    'name' => $curPendingOrderInfo['pet_name'],
                ],
                'memberInfo'   => [
                    'uid'   => $curPendingOrderInfo['member_uid'],
                    'name'  => $curPendingOrderInfo['member_name'],
                    'phone' => secretCellphone($curPendingOrderInfo['member_phone']),
                ],
                'createUser'   => [
                    'uid'  => $curPendingOrderInfo['user_uid'],
                    'name' => $curPendingOrderInfo['user_name'],
                ],
                'createTime'   => $curPendingOrderInfo['created_at'],
                'status'       => [
                    'id'   => $curPendingOrderInfo['status'],
                    'name' => StockPendingOrderStatusEnum::getDescription($curPendingOrderInfo['status']),
                ]
            ];
        }

        return self::Success($returnPendingOrderList);
    }
}
