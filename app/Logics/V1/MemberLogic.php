<?php

namespace App\Logics\V1;

use Log;
use Exception;
use Throwable;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use App\Logics\Logic;
use App\Logics\LogicResult;
use App\Enums\PageEnum;
use App\Enums\MemberGenderEnum;
use App\Enums\MemberProfileTypeEnum;
use App\Models\MemberModel;
use App\Models\MemberFromModel;
use App\Models\MemberUniqueModel;
use App\Models\MemberRemarkModel;
use App\Models\MemberPetsModel;
use App\Models\HisBrandModel;

class MemberLogic extends Logic
{
    /**
     * 获取有效会员信息
     *
     * @param int    $memberId
     * @param string $memberUid
     *
     * @return LogicResult
     */
    public static function GetValidMemberByIdOrUid(int $memberId = 0, string $memberUid = ''): LogicResult
    {
        if (empty($memberId) && empty($memberUid))
        {
            return self::Fail('查找有效会员，缺少必选参数', 400);
        }

        $where = ['delete_status' => 0, 'status' => 1];
        if (!empty($memberId))
        {
            $where['id'] = $memberId;
        }
        if (!empty($memberUid))
        {
            $where['uid'] = $memberUid;
        }

        $getMemberRes = MemberModel::getData(where: $where);
        if (empty($getMemberRes))
        {
            return self::Fail('会员无效、不存在', 30001);
        }

        return self::Success(current($getMemberRes));
    }

    /**
     * 查找会员列表
     *
     * @param array $searchParams
     * @param array $publicParams
     *
     * @return LogicResult
     */
    public static function SearchMemberList(array $searchParams, array $publicParams): LogicResult
    {
        if (empty($searchParams) || empty($publicParams))
        {
            return self::Fail('查找会员列表，缺少必选参数', 400);
        }

        // 公共参数
        $hospitalId      = $publicParams['_hospitalId'];
        $hospitalBrandId = $publicParams['_hospitalBrandId'];
        $hospitalOrgId   = $publicParams['_hospitalOrgId'];
        if (empty($hospitalId) || empty($hospitalBrandId) || empty($hospitalOrgId))
        {
            return self::Fail('查找会员列表，缺少医院必选参数', 400);
        }

        // 获取用户基本条件，如果医院品牌互通，那么获取整个组织下所有互通的品牌下医院的数据。否则不互通只获取当前品牌下所有医院。默认品牌下医院必须为互通
        $brandIsChain = HisBrandModel::getBrandIsShare($hospitalBrandId);

        $hospitalBrandNotWhere = [];
        if (!empty($brandIsChain))
        {
            $hospitalBrandWhere = [['org_id', '=', $hospitalOrgId]];

            // 获取当前组织下不互通的品牌
            $getNotShareBrandRes = HisBrandModel::getShareBrandByOrgId($hospitalOrgId, [$hospitalBrandId], 0);
            if ($getNotShareBrandRes->isNotEmpty())
            {
                $hospitalBrandNotWhere['brand_id'] = $getNotShareBrandRes->pluck('id')
                                                                         ->toArray();
            }
        }
        else
        {
            $hospitalBrandWhere = [['brand_id', '=', $hospitalBrandId]];
        }

        // 基础条件
        $memberBaseWhere = array_merge($hospitalBrandWhere, [['status', '=', 1], ['delete_status', '=', 0]]);

        // 存在单条件多维度查询条件
        $memberIds = [];
        $keyword   = trim(Arr::get($searchParams, 'keyword', ''));
        if (!empty($keyword))
        {
            // 1. 优先验证是否手机号查询
            if (checkValidCellphone($keyword))
            {
                $phoneWhere     = array_merge($memberBaseWhere, [['phone', '=', $keyword]]);
                $membersByPhone = MemberModel::getData(fields    : ['id'],
                                                       where     : $phoneWhere,
                                                       whereNotIn: $hospitalBrandNotWhere);
                if (!empty($membersByPhone))
                {
                    $memberIds = array_merge($memberIds, array_column($membersByPhone, 'id'));
                }
            }
            else
            {
                // 2. 先匹配用户名称
                $nameLikeWhere = array_merge($memberBaseWhere, [['name', 'like', '%' . $keyword . '%']]);
                $membersByName = MemberModel::getData(fields    : ['id'],
                                                      where     : $nameLikeWhere,
                                                      whereNotIn: $hospitalBrandNotWhere,
                                                      pageSize  : 50);
                if (!empty($membersByName))
                {
                    $memberIds = array_merge($memberIds, array_column($membersByName, 'id'));
                }

                // 3. 匹配宠物名称、病历号
                $petStatusWhere = array_merge($hospitalBrandWhere, [['status', '=', 1]]);

                // 使用OR条件组合宠物名称和病历号查询
                $petLikeWhereOr = [
                    ['name', 'like', '%' . $keyword . '%'],
                    ['record_number', 'like', '%' . $keyword . '%']
                ];

                $membersByPet = MemberPetsModel::getData(fields    : ['member_id'],
                                                         where     : $petStatusWhere,
                                                         orWhere   : $petLikeWhereOr,
                                                         whereNotIn: $hospitalBrandNotWhere,
                                                         pageSize  : 50);
                if (!empty($membersByPet))
                {
                    $memberIds = array_merge($memberIds, array_column($membersByPet, 'member_id'));
                }

                // 去重
                $memberIds = array_unique($memberIds);

                // 限制最多50个结果
                $memberIds = array_slice($memberIds, 0, 100);
            }

            // 如果没有找到任何匹配的会员
            if (empty($memberIds))
            {
                return self::Success(['total' => 0, 'data' => []]);
            }
        }

        // 单个条件查询
        $withWhere = [];
        if (!empty($searchParams['phone']))
        {
            $withWhere[] = ['phone', '=', $searchParams['phone']];
        }
        if (!empty($searchParams['name']))
        {
            $withWhere[] = ['name', 'like', '%' . $searchParams['name'] . '%'];
        }
        if (!empty($searchParams['startDate']) && strtotime($searchParams['startDate']) !== false)
        {
            $withWhere[] = ['created_at', '>=', $searchParams['startDate'] . ' 00:00:00'];
        }
        if (!empty($searchParams['endDate']) && strtotime($searchParams['endDate']) !== false)
        {
            $withWhere[] = ['created_at', '<=', $searchParams['endDate'] . ' 23:59:59'];
        }

        // 组合条件
        $getWhere = array_merge($memberBaseWhere, $withWhere);

        // in条件
        $getWhereIn = [];
        if (!empty($memberIds))
        {
            $getWhereIn['id'] = $memberIds;
        }

        // 获取条目总数
        $getMemberTotalRes = MemberModel::getTotalNumber($getWhere, $getWhereIn, $hospitalBrandNotWhere);
        if (empty($getMemberTotalRes) || $getMemberTotalRes <= 0)
        {
            return self::Success(['total' => 0, 'data' => []]);
        }

        // 获取条目数据
        $getMemberListRes = MemberModel::getData(where     : $getWhere,
                                                 whereIn   : $getWhereIn,
                                                 whereNotIn: $hospitalBrandNotWhere,
                                                 pageIndex : $searchParams['page'] ?? PageEnum::DefaultPageIndex->value,
                                                 pageSize  : $searchParams['count'] ?? PageEnum::DefaultPageSize->value);
        if (empty($getMemberListRes))
        {
            return self::Success(['total' => 0, 'data' => []]);
        }

        // 获取会员医院备注信息
        $getMemberRemarkRes = MemberRemarkModel::getMemberRemark($hospitalId, $memberIds);

        $returnMemberList = [];
        foreach ($getMemberListRes as $curMemberInfo)
        {
            $tmpMemberInfo = [
                'uid'       => $curMemberInfo['uid'],
                'name'      => $curMemberInfo['name'],
                'phone'     => secretCellphone($curMemberInfo['phone']),
                'birthday'  => $curMemberInfo['birthday'],
                'remark'    => $getMemberRemarkRes[$curMemberInfo['id']]['remark'] ?? '',
                'createdAt' => $curMemberInfo['created_at'],
            ];

            $returnMemberList[] = $tmpMemberInfo;
        }

        return self::Success(['total' => $getMemberTotalRes, 'data' => $returnMemberList]);
    }

    /**
     * 新增医院会员
     *
     * @param array $memberParams
     * @param array $publicParams
     *
     * @return LogicResult
     * @throws Throwable
     */
    public static function AddMember(array $memberParams, array $publicParams): LogicResult
    {
        if (empty($memberParams))
        {
            return self::Fail('新增会员，缺少必选参数', 400);
        }

        // 公共参数
        $hospitalOrgId   = intval(Arr::get($publicParams, '_hospitalOrgId'));
        $hospitalBrandId = intval(Arr::get($publicParams, '_hospitalBrandId'));
        $hospitalId      = intval(Arr::get($publicParams, '_hospitalId'));

        // 接受统一验证后的参数
        $getValidateRes = self::_validateAddMemberParams($memberParams);
        if ($getValidateRes->isFail())
        {
            return $getValidateRes;
        }

        // 获取数据
        $getAddMemberParams = $getValidateRes->getData();
        $phone              = $getAddMemberParams['phone'];
        $memberRemark       = $getAddMemberParams['remark'];
        unset($getAddMemberParams['remark']);

        // 获取当前医院是否存在此手机号
        $getHospitalMemberRes = MemberModel::getData(where: [
                                                                'phone'       => $phone,
                                                                'hospital_id' => $hospitalId,
                                                            ]);
        $getHospitalMemberRes = !empty($getHospitalMemberRes) ? current($getHospitalMemberRes) : [];
        if (!empty($getHospitalMemberRes) && $getHospitalMemberRes['delete_status'] == 0)
        {
            return self::Fail('当前医院已存在此手机号', 30000);
        }

        // 当前品牌是否互通，如果互通的话则获取所有互通的品牌，反之则只有当前品牌
        $brandIsChain = HisBrandModel::getBrandIsShare($hospitalBrandId);
        if (!empty($brandIsChain))
        {
            // 获取可查看当前医院数据，互通的所有品牌
            $getAllowedBrandIdsRes = HospitalLogic::GetAllowedBrandIdsByHospitalId($hospitalId);
            if ($getAllowedBrandIdsRes->isFail())
            {
                return $getAllowedBrandIdsRes;
            }

            $allowedBrandIds = $getAllowedBrandIdsRes->getData('allowedBrandIds', []);
        }
        else
        {
            $allowedBrandIds = [$hospitalBrandId];
        }

        // 获取当前品牌体系下是否存在此手机号
        $getBrandMemberRes = MemberModel::getData(where  : ['phone' => $phone, 'is_share' => $brandIsChain],
                                                  whereIn: ['brand_id' => $allowedBrandIds]);

        $getBrandMemberRes = !empty($getBrandMemberRes) ? current($getBrandMemberRes) : [];
        if (!empty($getBrandMemberRes) && $getBrandMemberRes['delete_status'] == 0)
        {
            return self::Fail('当前医院品牌体系下已存在此手机号', 30000);
        }

        /**
         * 会员手机号不存在
         * 1、未注册过手机号
         * 2、已注册过，但是已经注销。那么重新生成
         */

        // 获取用户平台唯一用户信息
        $getMemberUnionRes = MemberUniqueModel::getOneByPhone($phone, true);

        // 开始生成会员记录
        DB::beginTransaction();

        // 如果用户平台唯一用户信息存在，则使用已存在的。不存在则生成
        if (!empty($getMemberUnionRes))
        {
            $memberUnionId = $getMemberUnionRes['id'];
        }
        else
        {
            $insertMemberUnionData = [
                'phone'       => $phone,
                'hospital_id' => $hospitalId,
            ];
            $memberUnionId         = MemberUniqueModel::insertOne($insertMemberUnionData);
        }

        $memberUid = generateUUID();
        // 组合基础数据
        $baseMemberInfo = [
            'uid'         => $memberUid,
            'unique_id'   => $memberUnionId,
            'hospital_id' => $hospitalId,
            'brand_id'    => $hospitalBrandId,
            'is_share'    => $brandIsChain,
            'org_id'      => $hospitalOrgId,
        ];

        $insertMemberData = array_merge($baseMemberInfo, $getAddMemberParams);
        $memberLastId     = MemberModel::insertOne($insertMemberData);

        // 如果备注不为空，则添加备注
        if (!empty($memberRemark))
        {
            $insertMemberRemarkData = [
                'org_id'      => $hospitalOrgId,
                'brand_id'    => $hospitalBrandId,
                'hospital_id' => $hospitalId,
                'unique_id'   => $memberUnionId,
                'member_id'   => $memberLastId,
                'remark'      => $memberRemark,
            ];
            MemberRemarkModel::insertOne($insertMemberRemarkData);
        }

        DB::commit();

        // 创建新会员成功
        return self::Success(['memberUid' => $memberUid]);
    }

    /**
     * 编辑会员
     *
     * @param int   $memberId
     * @param array $editMemberParams
     * @param array $publicParams
     *
     * @return LogicResult
     * @throws Throwable
     */
    public static function EditMember(int $memberId, array $editMemberParams, array $publicParams): LogicResult
    {
        if (empty($memberId))
        {
            return self::Fail('memberId，缺少必选参数', 400);
        }

        if (empty($editMemberParams) || empty($publicParams))
        {
            return self::Fail('memberParams，缺少必选参数', 400);
        }

        // 公共参数
        $hospitalOrgId   = intval(Arr::get($publicParams, '_hospitalOrgId'));
        $hospitalBrandId = intval(Arr::get($publicParams, '_hospitalBrandId'));
        $hospitalId      = intval(Arr::get($publicParams, '_hospitalId'));

        // 获取会员信息
        $getMemberInfoRes = self::GetValidMemberByIdOrUid($memberId);
        if ($getMemberInfoRes->isFail())
        {
            return $getMemberInfoRes;
        }

        $getMemberInfoRes = $getMemberInfoRes->getData();

        // 接受统一验证后的参数
        $getValidateRes = self::_validateAddMemberParams($editMemberParams);
        if ($getValidateRes->isFail())
        {
            return $getValidateRes;
        }

        // 不可修改手机号
        if (isset($editMemberParams['phone']) && $editMemberParams['phone'] != $getMemberInfoRes['phone'])
        {
            return self::Fail('手机号不可修改', 30003);
        }

        // 转化后的参数对应POST过来修改的参数
        $editMemberParamsRelationMap = [
            'name'             => 'name',
            'gender'           => 'gender',
            'birthday'         => 'birthday',
            'from_id'          => 'fromId',
            'avatar'           => 'avatar',
            'wechat_number'    => 'wechatNumber',
            'email'            => 'email',
            'other_mobile_one' => 'otherMobile1',
            'other_mobile_two' => 'otherMobile2',
            'telephone'        => 'telephone',
            'province_id'      => 'provinceId',
            'city_id'          => 'cityId',
            'area_id'          => 'areaId',
            'street_id'        => 'streetId',
            'address'          => 'address',
        ];

        // 比较新旧数据，只更新变化的字段
        $updateData = [];
        foreach ($getValidateRes->getData() as $fieldKey => $newValue)
        {
            $tmpEditFieldName = $editMemberParamsRelationMap[$fieldKey] ?? '';
            if (empty($tmpEditFieldName))
            {
                continue;
            }

            // 如果传递的参数中不存在这项默认不修改，过滤
            if (!isset($editMemberParams[$tmpEditFieldName]))
            {
                continue;
            }

            // 验证之后的新值与旧值不一致
            if ($newValue != $getMemberInfoRes[$fieldKey])
            {
                $updateData[$fieldKey] = $newValue;
            }
        }

        // 特殊字段，备注修改。单独验证
        $insertMemberRemarkData = [];
        $updateRemarkData       = [];
        $getOldRemarkRes        = MemberRemarkModel::GetMemberRemark($hospitalId, [$memberId]);
        $getOldRemarkRes        = $getOldRemarkRes[$memberId] ?? [];
        if (empty($getOldRemarkRes))
        {
            if (!empty($editMemberParams['remark']))
            {
                $insertMemberRemarkData = [
                    'org_id'      => $hospitalOrgId,
                    'brand_id'    => $hospitalBrandId,
                    'hospital_id' => $hospitalId,
                    'unique_id'   => $getMemberInfoRes['unique_id'],
                    'member_id'   => $memberId,
                    'remark'      => $editMemberParams['remark'],
                ];
            }
        }
        else
        {
            if ($getOldRemarkRes['remark'] != $editMemberParams['remark'])
            {
                $updateRemarkData['remark'] = $editMemberParams['remark'];
            }
        }

        // 如果没有需要更新的数据，直接返回成功
        if (empty($updateData) && empty($insertMemberRemarkData) && empty($updateRemarkData))
        {
            return self::Success();
        }

        try
        {
            DB::beginTransaction();

            // 更新会员信息
            if (!empty($updateData))
            {
                MemberModel::updateOne($memberId, $updateData);
            }

            // 新增会员备注
            if (!empty($insertMemberRemarkData))
            {
                MemberRemarkModel::insertOne($insertMemberRemarkData);
            }

            if (!empty($getOldRemarkRes) && !empty($updateRemarkData))
            {
                MemberRemarkModel::updateOne($getOldRemarkRes['id'], $updateRemarkData);
            }

            DB::commit();

            return self::Success();

        } catch (Throwable $throwable)
        {
            DB::rollBack();

            Log::error(__CLASS__ . '::' . __METHOD__ . ' 更新会员信息失败', [
                'code'    => $throwable->getCode(),
                'message' => $throwable->getMessage(),
                'file'    => $throwable->getFile(),
                'line'    => $throwable->getLine(),
                'trace'   => $throwable->getTraceAsString(),
            ]);
        }

        return self::Fail();
    }

    /**
     * 获取用户详情信息
     *
     * @param int $memberId
     * @param int $hospitalId
     *
     * @return LogicResult
     * @throws Exception
     */
    public static function GetMemberDetailFullInfo(int $memberId = 0, int $hospitalId = 0): LogicResult
    {
        if (empty($memberId))
        {
            self::Fail('缺少必选参数，memberId', 400);
        }

        // 获取有效会员信息
        $getMemberBaseInfoRes = self::GetValidMemberByIdOrUid($memberId);
        if ($getMemberBaseInfoRes->isFail())
        {
            return $getMemberBaseInfoRes;
        }

        $getMemberBaseInfoRes = $getMemberBaseInfoRes->getData();

        // 会员地址信息
        if ($getMemberBaseInfoRes['province_id'] || $getMemberBaseInfoRes['city_id'] || $getMemberBaseInfoRes['area_id'] || $getMemberBaseInfoRes['street_id'])
        {
            $getMemberAddressRes = AreaLogic::GetProvinceCityAreaStreetById($getMemberBaseInfoRes['province_id'],
                                                                            $getMemberBaseInfoRes['city_id'],
                                                                            $getMemberBaseInfoRes['area_id'],
                                                                            $getMemberBaseInfoRes['street_id']);
        }

        // 会员来源
        $getMemberFromRes = MemberFromModel::getOne($getMemberBaseInfoRes['from_id']);

        // 获取会员在当前医院附属信息
        $hospitalRemark = '';
        if (!empty($hospitalId))
        {
            // 获取在当前医院的备注
            $getMemberHospitalRemarkRes = MemberRemarkModel::getData(where: [
                                                                                'member_id'   => $memberId,
                                                                                'hospital_id' => $hospitalId,
                                                                                'status'      => 1
                                                                            ]);
            $hospitalRemark             = !empty($getMemberHospitalRemarkRes) ? current($getMemberHospitalRemarkRes)['remark'] : '';
        }

        // 拼装返回用户信息
        $returnMemberInfo = [
            'uid'          => $getMemberBaseInfoRes['uid'],
            'phone'        => secretCellphone($getMemberBaseInfoRes['phone']),
            'name'         => $getMemberBaseInfoRes['name'],
            'gender'       => [
                'id'   => $getMemberBaseInfoRes['gender'],
                'name' => MemberGenderEnum::getDescription($getMemberBaseInfoRes['gender']),
            ],
            'birthday'     => $getMemberBaseInfoRes['birthday'],
            'age'          => calculatePersonAge($getMemberBaseInfoRes['birthday']),
            'avatar'       => $getMemberBaseInfoRes['avatar'],
            'wechatNumber' => $getMemberBaseInfoRes['wechat_number'],
            'otherMobile1' => $getMemberBaseInfoRes['other_mobile_one'],
            'otherMobile2' => $getMemberBaseInfoRes['other_mobile_two'],
            'telephone'    => $getMemberBaseInfoRes['telephone'],
            'email'        => $getMemberBaseInfoRes['email'],
            'remark'       => $hospitalRemark,
        ];

        // 用户地址信息
        $returnMemberAddressInfo = [];
        if (!empty($getMemberAddressRes) && $getMemberAddressRes instanceof LogicResult && $getMemberAddressRes->isSuccess())
        {
            $returnMemberAddressInfo = array_merge($getMemberAddressRes->getData(), [
                'addressDetail' => $getMemberBaseInfoRes['address'],
            ]);
        }

        // 用户来源信息
        $returnMemberFromInfo = [];
        if (!empty($getMemberFromRes))
        {
            $returnMemberFromInfo = [
                'id'   => $getMemberFromRes['id'],
                'name' => $getMemberFromRes['name'],
            ];
        }

        return self::Success([
                                 'memberInfo'  => $returnMemberInfo,
                                 'addressInfo' => $returnMemberAddressInfo,
                                 'fromInfo'    => $returnMemberFromInfo,
                             ]);
    }

    /**
     * 编辑用户微画像
     *
     * @param int   $memberId
     * @param array $editProfileParams
     *
     * @return LogicResult
     */
    public static function EditMicroProfile(int $memberId, array $editProfileParams): LogicResult
    {
        if (empty($memberId))
        {
            return self::Fail('memberId，缺少必选参数', 400);
        }
        if (empty($editProfileParams))
        {
            return self::Fail('editProfileParams，缺少必选参数', 400);
        }

        $profileType      = trim(Arr::get($editProfileParams, 'type', ''));
        $profileTypeValue = intval(Arr::get($editProfileParams, 'levelNumber', 0));
        if (empty($profileType) || !in_array($profileType, MemberProfileTypeEnum::names()))
        {
            return self::Fail('editProfileParams.type，参数值错误', 400);
        }
        if ($profileTypeValue < 0)
        {
            return self::Fail('editProfileParams.levelNumber，参数值错误', 400);
        }

        // 获取有效会员信息
        $getMemberInfoRes = self::GetValidMemberByIdOrUid($memberId);
        if ($getMemberInfoRes->isFail())
        {
            return $getMemberInfoRes;
        }

        // 获取更新的画像字段名
        $upFieldName = MemberProfileTypeEnum::getCaseNameRelationFieldName($profileType);
        if (empty($upFieldName))
        {
            return self::Fail('画像类型参数错误', 400);
        }

        // 更新字段
        $updateData   = [
            $upFieldName => $profileTypeValue,
        ];
        $updateResult = MemberModel::updateOne($memberId, $updateData);
        if (!$updateResult)
        {
            return self::Fail('会员画像更新失败', 30005);
        }

        return self::Success([
                                 $profileType => $profileTypeValue
                             ]);
    }

    /**
     * 新增、编辑会员获取统一参数
     *
     * @param array $memberParams
     *
     * @return LogicResult
     */
    private static function _validateAddMemberParams(array $memberParams): LogicResult
    {
        $phone        = Arr::get($memberParams, 'phone', '');
        $name         = trim(Arr::get($memberParams, 'name', ''));
        $gender       = Arr::get($memberParams, 'gender', 0);
        $birthday     = Arr::get($memberParams, 'birthday', '');
        $fromId       = Arr::get($memberParams, 'fromId', 0);
        $avatar       = Arr::get($memberParams, 'avatar', '');
        $wechatNumber = Arr::get($memberParams, 'wechatNumber', '');
        $email        = Arr::get($memberParams, 'email', '');
        $otherMobile1 = Arr::get($memberParams, 'otherMobile1', '');
        $otherMobile2 = Arr::get($memberParams, 'otherMobile2', '');
        $telephone    = Arr::get($memberParams, 'telephone', '');
        $provinceId   = Arr::get($memberParams, 'provinceId', 0);
        $cityId       = Arr::get($memberParams, 'cityId', 0);
        $areaId       = Arr::get($memberParams, 'areaId', 0);
        $streetId     = Arr::get($memberParams, 'streetId', 0);
        $address      = trim(Arr::get($memberParams, 'address', ''));
        $remark       = trim(Arr::get($memberParams, 'remark', ''));

        if (empty($phone))
        {
            return self::Fail('会员，缺少必选参数:phone', 400);
        }
        if (!checkValidCellphone($phone))
        {
            return self::Fail('会员手机号格式错误', 400);
        }
        if (empty($name))
        {
            return self::Fail('会员，缺少必选参数:name', 400);
        }
        if (!is_numeric($gender) || empty(MemberGenderEnum::exists($gender)))
        {
            return self::Fail('会员，性别参数错误', 400);
        }
        if (empty($birthday) || strtotime($birthday) === false)
        {
            return self::Fail('会员，生日参数错误', 400);
        }
        if (empty($fromId) || empty(MemberFromModel::getOne($fromId)))
        {
            return self::Fail('会员，渠道来源参数错误', 400);
        }

        // 验证地址选择是否正确
        if ($provinceId > 0 || $cityId > 0 || $areaId > 0 || $streetId > 0)
        {
            $getProvinceRes = AreaLogic::GetProvinceCityAreaStreetById($provinceId, $cityId, $areaId, $streetId);
            if ($getProvinceRes->isFail())
            {
                return $getProvinceRes;
            }

            // 容错处理，如果获取到完整地址，则使用获取到的
            $provinceId = $getProvinceRes->getData('province.id', 0);
            $cityId     = $getProvinceRes->getData('city.id', 0);
            $areaId     = $getProvinceRes->getData('area.id', 0);
            $streetId   = $getProvinceRes->getData('street.id', 0);
        }

        return self::Success([
                                 'phone'            => $phone,
                                 'name'             => $name,
                                 'gender'           => $gender,
                                 'birthday'         => $birthday,
                                 'from_id'          => $fromId,
                                 'avatar'           => $avatar,
                                 'wechat_number'    => $wechatNumber,
                                 'email'            => $email,
                                 'other_mobile_one' => $otherMobile1,
                                 'other_mobile_two' => $otherMobile2,
                                 'telephone'        => $telephone,
                                 'province_id'      => $provinceId,
                                 'city_id'          => $cityId,
                                 'area_id'          => $areaId,
                                 'street_id'        => $streetId,
                                 'address'          => $address,
                                 'remark'           => $remark,
                             ]);
    }
}
