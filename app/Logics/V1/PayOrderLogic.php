<?php

namespace App\Logics\V1;

use Throwable;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Logics\Logic;
use App\Logics\LogicResult;
use App\Logics\V1\Order\Interfaces\OrderItemInterface;
use App\Enums\BusinessCodePrefixEnum;
use App\Enums\SheetStatusEnum;
use App\Enums\SheetBusinessTypeEnum;
use App\Enums\PaySubOrderTypeEnum;
use App\Enums\MemberBalanceOperateResourceTypeEnum;
use App\Enums\PaySubOrderExtProcessStatusEnum;
use App\Models\UsersModel;
use App\Models\PayOrderModel;
use App\Models\PaySubOrderModel;
use App\Models\PaySubOrderExtModel;
use App\Models\PayOrderDiscountRecordsModel;
use App\Models\PayOrderThirdPartyPayModel;
use App\Support\Common\PriceSplitHelper;
use App\Support\PayOrder\PayOrderHelper;
use App\Support\User\HospitalUserHelper;
use App\Support\Balance\MemberBalanceHelper;
use App\Support\Hospital\HospitalHelper;
use App\Support\PayOrder\PaySubOrderExtHelper;
use App\Support\Concurrent\ConcurrentTask;
use App\Logics\V1\BuySheet\SheetUnionLogic;
use App\Logics\V1\Order\BeautyOrderLogic;
use App\Logics\V1\Order\MedicalOrderLogic;
use App\Logics\V1\Order\RechargeOrderLogic;
use App\Logics\V1\Order\RetailOrderLogic;

/**
 * 结算单逻辑
 */
class PayOrderLogic extends Logic
{

    /**
     * 购买单业务类型与逻辑类映射
     *
     * @var array
     */
    const array ORDER_LOGIC_MAP = [
        PaySubOrderTypeEnum::Recharge->value     => RechargeOrderLogic::class,
        PaySubOrderTypeEnum::Registration->value => MedicalOrderLogic::class,
        PaySubOrderTypeEnum::Recipe->value       => MedicalOrderLogic::class,
        PaySubOrderTypeEnum::Retail->value       => RetailOrderLogic::class,
        PaySubOrderTypeEnum::Beauty->value       => BeautyOrderLogic::class,
    ];

    /**
     * 获取结算单创建人选项
     *
     * @param array $publicParams
     *
     * @return LogicResult
     */
    public static function GetCreateUsersOptions(array $publicParams): LogicResult
    {
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        if (empty($hospitalId))
        {
            return self::Fail('获取结算单创建人选项，缺少医院ID必选参数', 400);
        }

        $userIds = PayOrderModel::GetOrderCreateUsersOptions($hospitalId);
        if (empty($userIds))
        {
            return self::Success([]);
        }

        return self::Success(HospitalUserHelper::GetUserOptionsByUserIds($userIds, $hospitalId));
    }

    /**
     * 获取结算单收银人选项
     *
     * @param array $publicParams
     *
     * @return LogicResult
     */
    public static function GetCashierUsersOptions(array $publicParams): LogicResult
    {
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        if (empty($hospitalId))
        {
            return self::Fail('获取结算单收银人选项，缺少医院ID必选参数', 400);
        }

        $userIds = PayOrderModel::GetOrderCashierUsersOptions($hospitalId);
        if (empty($userIds))
        {
            return self::Success([]);
        }

        return self::Success(HospitalUserHelper::GetUserOptionsByUserIds($userIds, $hospitalId));
    }

    /**
     * 获取结算单列表
     *
     * @param array $params
     * @param array $publicParams
     *
     * @return LogicResult
     */
    public static function SearchOrderList(array $params, array $publicParams): LogicResult
    {
        //业务参数
        $page           = intval(Arr::get($params, 'page', 1));
        $count          = intval(Arr::get($params, 'count', 10));
        $keywords       = trim(Arr::get($params, 'keywords', ''));
        $payOrderCode   = trim(Arr::get($params, 'payOrderCode', ''));
        $status         = Arr::get($params, 'status');
        $createUserUid  = trim(Arr::get($params, 'createUserUid', ''));
        $createUserId   = intval(Arr::get($params, 'createUserId', 0));
        $cashierUserUid = trim(Arr::get($params, 'cashierUserUid', ''));
        $cashierUserId  = intval(Arr::get($params, 'cashierUserId', 0));
        $payMode        = Arr::get($params, 'payMode');
        $startDate      = trim(Arr::get($params, 'startDate', ''));
        $endDate        = trim(Arr::get($params, 'endDate', ''));
        //公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        if (empty($hospitalId))
        {
            return self::Fail('获取结算单列表，缺少医院ID必选参数', 400);
        }

        if (!empty($createUserUid) || !empty($createUserId))
        {
            $createUser = UsersModel::getOneByIdOrUid(id: $createUserId, uid: $createUserUid);
            if (empty($createUser))
            {
                return self::Fail('创建人不存在', 10100);
            }
            $createUserId = $createUser->id;
        }
        if (!empty($cashierUserUid) || !empty($cashierUserId))
        {
            $cashierUser = UsersModel::getOneByIdOrUid(id: $cashierUserId, uid: $cashierUserUid);
            if (empty($cashierUser))
            {
                return self::Fail('收银员不存在', 10100);
            }
            $cashierUserId = $cashierUser->id;
        }

        //构建查询条件
        $where = [
            'o.hospital_id' => $hospitalId,
        ];

        if ($payOrderCode != '')
        {
            $where[] = ['o.pay_order_code', '=', $payOrderCode];
        }
        if (SheetStatusEnum::exists($status))
        {
            $where[] = ['o.status', '=', $status];
        }
        if ($createUserId > 0)
        {
            $where[] = ['o.created_by', '=', $createUserId];
        }
        if ($cashierUserId > 0)
        {
            $where[] = ['o.cashier_by', '=', $cashierUserId];
        }
        if (is_integer($payMode))
        {
            $where[] = ['o.pay_mode', '=', $payMode];
        }
        if (!empty($startDate))
        {
            $where[] = ['o.created_at', '>=', $startDate . ' 00:00:00'];
        }
        if (!empty($endDate))
        {
            $where[] = ['o.created_at', '<=', $endDate . ' 23:59:59'];
        }

        $searchResult = PayOrderModel::SearchOrder(
            keywords: $keywords,
            where:    $where,
            orderBy:  ['o.id' => 'desc'],
            page:     $page,
            count:    $count
        );

        $total = $searchResult->total();
        $data  = $searchResult->items();

        if (empty($data))
        {
            return self::Success(['total' => $total, 'data' => []]);
        }


        return self::Success([
                                 'total' => $total,
                                 'data'  => PayOrderHelper::FormatOrderListStructure($data, $publicParams)
                             ]);
    }

    /**
     * 创建结算单
     *
     * @param array $params
     * @param array $publicParams
     *
     * @return LogicResult
     * @throws Throwable
     */
    public static function CreatePayOrder(array $params, array $publicParams): LogicResult
    {
        //业务参数，已在CheckoutLogic的CheckoutPrepare、Checking中验证
        $memberInfo       = Arr::get($params, 'memberInfo');
        $prepareData      = Arr::get($params, 'prepareData', []);
        $checkoutTypeData = Arr::get($params, 'checkoutTypeData', []);
        $priceData        = Arr::get($params, 'priceData', []);
        //公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        $orgId      = intval(Arr::get($publicParams, '_hospitalOrgId'));
        $brandId    = intval(Arr::get($publicParams, '_hospitalBrandId'));
        $userId     = intval(Arr::get($publicParams, '_userId'));

        if (empty($prepareData) || empty($checkoutTypeData) || empty($priceData))
        {
            return self::Fail('生成结算订单，缺少必选参数', 400);
        }
        if (empty($hospitalId) || empty($orgId) || empty($brandId))
        {
            return self::Fail('生成结算订单，缺少组织/品牌/医院ID必选参数', 400);
        }
        if (empty($userId))
        {
            return self::Fail('生成结算订单，缺少当前登录用户ID必选参数', 400);
        }

        //会员信息
        $memberId = $memberInfo['id'] ?? 0;

        //价格信息
        $totalPrice         = $priceData['totalPrice'];
        $activityPrice      = $priceData['activityPrice'];
        $couponPrice        = $priceData['couponPrice'];
        $discountPrice      = $priceData['discountPrice'];
        $thirdPartyPayPrice = $priceData['thirdPartyPayPrice'];
        $balancePayPrice    = $priceData['balancePayPrice'];
        $cashPayPrice       = $priceData['cashPayPrice'];

        //结算方式信息
        $discount   = $checkoutTypeData['discount'] ?? null;
        $thirdParty = $checkoutTypeData['thirdParty'] ?? null;
        $balance    = $checkoutTypeData['balance'] ?? null;
        $cash       = $checkoutTypeData['cash'] ?? null;

        //构造支付单数据
        $payOrderCode = generateBusinessCodeNumber(BusinessCodePrefixEnum::JSDDM);
        $payOrderData = [
            'pay_order_code'         => $payOrderCode,
            'org_id'                 => $orgId,
            'brand_id'               => $brandId,
            'hospital_id'            => $hospitalId,
            'member_id'              => $memberId,
            'sale_price'             => $totalPrice,
            'reduce_price'           => $activityPrice,
            'deal_price'             => bcsub($totalPrice, $activityPrice, 2),
            'coupon_price'           => $couponPrice,
            'cash_coupon_price'      => '0.00',
            'discount_price'         => $discountPrice,
            'third_party_price'      => $thirdPartyPayPrice,
            'balance_price'          => $balancePayPrice,
            'deposit_price'          => '0.00',
            'balance_recharge_price' => '0.00',//下单时后置更新
            'balance_gift_price'     => '0.00',//下单时后置更新
            'pay_price'              => $cashPayPrice,
            'third_party_pay_mode'   => $thirdParty['type'] ?? 0,
            'pay_channel'            => $cash['channel'] ?? 0,
            'pay_mode'               => $cash['mode'] ?? 0,
            'status'                 => SheetStatusEnum::Unpaid->value,
            'created_by'             => $userId,
            'cashier_by'             => 0,//TODO:支付成功时写
            'order_time'             => getCurrentTimeWithMilliseconds(),
        ];
        //构造支付子订单详情，按照购买单的维度进行拆单
        $paySubOrderData   = [];
        $paySubOrderExt    = [];
        $sheetsBusinessMap = [];//锁定购买单数据
        foreach ($prepareData['data'] ?: [] as $businessData)
        {
            $businessType = $businessData['type']['uid'];
            $subOrderType = SheetBusinessTypeEnum::BUSINESS_PAY_SUB_ORDER_TYPE[$businessType] ?? 0;
            if (empty($subOrderType) || !isset($businessData['data']))
            {
                return self::Fail('生成结算订单，业务购买单数据异常', 601000);
            }

            if (!isset($sheetsBusinessMap[$businessType]))
            {
                $sheetsBusinessMap[$businessType] = [
                    'codes' => [],
                ];
            }

            foreach ($businessData['data'] as $sheet)
            {
                $price      = $sheet['price'] ?? null;
                $sheetCode  = $sheet['sheetCode'] ?? null;
                $sheetItems = $sheet['items'] ?? [];
                if (empty($price))
                {
                    return self::Fail('生成结算订单，业务购买单总价数据异常', 601000);
                }
                if (empty($sheetCode))
                {
                    return self::Fail('生成结算订单，业务购买单编码数据异常', 601000);
                }
                if (empty($sheetItems))
                {
                    return self::Fail('生成结算订单，业务购买单明细数据异常', 601000);
                }

                $sheetsBusinessMap[$businessType]['codes'][] = $sheetCode;

                $paySubOrderCode   = generateBusinessCodeNumber(BusinessCodePrefixEnum::JSDDS);
                $paySubOrderData[] = [
                    'pay_order_id'           => 0,
                    'pay_order_code'         => $payOrderCode,
                    'pay_sub_order_code'     => $paySubOrderCode,
                    'type'                   => $subOrderType,
                    'org_id'                 => $orgId,
                    'brand_id'               => $brandId,
                    'hospital_id'            => $hospitalId,
                    'member_id'              => $memberId,
                    'sale_price'             => $price,
                    'reduce_price'           => '0.00',//TODO:有商品活动时，需要拆分活动优惠
                    'deal_price'             => $price,
                    'coupon_price'           => '0.00',//TODO:有代金券时，需要拆分代金券抵扣
                    'cash_coupon_price'      => '0.00',
                    'discount_price'         => '0.00',//后置均摊金额
                    'third_party_price'      => '0.00',//后置均摊金额
                    'balance_price'          => '0.00',//下单时后置均摊金额
                    'deposit_price'          => '0.00',//后置均摊金额
                    'balance_recharge_price' => '0.00',//下单时后置均摊金额
                    'balance_gift_price'     => '0.00',//下单时后置均摊金额
                    'pay_price'              => $price,//下单时后置修改
                    'third_party_pay_mode'   => $thirdParty['type'] ?? 0,
                    'pay_channel'            => $cash['channel'] ?? 0,
                    'pay_mode'               => $cash['mode'] ?? 0,
                    'status'                 => SheetStatusEnum::Unpaid->value,
                    'created_by'             => $userId,
                    'cashier_by'             => 0,//TODO:支付成功时写
                    'order_time'             => getCurrentTimeWithMilliseconds(),
                ];

                $paySubOrderExt[] = [
                    'pay_order_code'     => $payOrderCode,
                    'pay_sub_order_code' => $paySubOrderCode,
                    'type'               => $subOrderType,
                    'sheet_code'         => $sheetCode,
                    'hospital_id'        => $hospitalId,
                    'items'              => json_encode($sheetItems),
                    'price'              => $price,
                ];
            }
        }

        if (bccomp($discountPrice, 0, 2) != 0)
        {
            $splitRes = PriceSplitHelper::CommonSplitPrice($paySubOrderData, 'discount_price', $discountPrice);
            if ($splitRes->isFail())
            {
                return $splitRes;
            }

            $paySubOrderData = $splitRes->getData();
        }
        if (bccomp($thirdPartyPayPrice, 0, 2) != 0)
        {
            $splitRes = PriceSplitHelper::CommonSplitPrice($paySubOrderData, 'third_party_price', $thirdPartyPayPrice);
            if ($splitRes->isFail())
            {
                return $splitRes;
            }

            $paySubOrderData = $splitRes->getData();
        }
        //余额均摊再下边的事务中，在获取余额使用批次后，再均摊

        if (empty($payOrderData) || empty($paySubOrderData) || empty($paySubOrderExt))
        {
            return self::Fail('生成结算订单，构造结算单数据异常', 601001);
        }

        //折扣记录
        $discountRecord = [];
        if ($discountPrice > 0 && !empty($discount) && $priceData['maxDiscountPrice'])
        {
            $maxDiscountPrice = $priceData['maxDiscountPrice'];
            $discountRecord   = [
                'pay_order_code'  => $payOrderCode,
                'org_id'          => $orgId,
                'brand_id'        => $brandId,
                'hospital_id'     => $hospitalId,
                'member_id'       => $memberId,
                'pay_price'       => $maxDiscountPrice,
                'discount'        => $discount['rate'],
                'discount_price'  => $discountPrice,
                'discount_type'   => $discount['type'],
                'discount_reason' => $discount['reason'],
                'created_by'      => $userId,
            ];
        }

        //第三方支付记录
        $thirdPartyRecord = [];
        if ($thirdPartyPayPrice > 0 && !empty($thirdParty))
        {
            $thirdPartyRecord = [
                'pay_order_code' => $payOrderCode,
                'org_id'         => $orgId,
                'brand_id'       => $brandId,
                'hospital_id'    => $hospitalId,
                'member_id'      => $memberId,
                'pay_price'      => $thirdPartyPayPrice,
                'pay_mode'       => $thirdParty['type'],
                'remark'         => $thirdParty['remark'],
                'created_by'     => $userId,
            ];
        }

        $usedData = [];
        if ($balancePayPrice > 0 && !empty($balance) && $memberId > 0)
        {
            $usedData['balance'] = [
                'memberId' => $memberId,
                'price'    => $balancePayPrice,
            ];
        }
        /*
         * TODO:押金
         *
        if ($depositPayPrice > 0 && $memberId > 0)
        {
            $usedData['deposit'] = [
                'memberId' => $memberId,
                'price'    => $depositPayPrice,
            ];
        }
        */

        $recordData = [
            'discount'   => $discountRecord,
            'thirdParty' => $thirdPartyRecord,
        ];

        $createRes = self::DoCreatePayOrder(
            $payOrderData,
            $paySubOrderData,
            $paySubOrderExt,
            $usedData,
            $recordData,
            $sheetsBusinessMap,
            $publicParams
        );

        if ($createRes->isFail())
        {
            return $createRes;
        }

        $result = [
            'payOrderCode' => $payOrderCode,
            'payPrice'     => $cashPayPrice,
        ];

        return self::Success($result);
    }

    /**
     * 创建结算单及附加信息
     *
     * 事务
     *
     * @param array $payOrderData
     * @param array $paySubOrderData
     * @param array $paySubOrderExtData
     * @param array $usedData
     * @param array $recordData
     * @param array $lockSheets
     * @param array $publicParams
     *
     * @return LogicResult
     * @throws Throwable
     */
    private static function DoCreatePayOrder(
        array $payOrderData,
        array $paySubOrderData,
        array $paySubOrderExtData,
        array $usedData = [],
        array $recordData = [],
        array $lockSheets = [],
        array $publicParams = []
    ): LogicResult
    {
        if (empty($payOrderData) || empty($paySubOrderData) || empty($paySubOrderExtData))
        {
            return self::Fail('生成结算订单，结算单数据异常', 601001);
        }

        if (!empty($usedData) && empty($publicParams))
        {
            return self::Fail('生成结算订单，结算单数据缺失公共参数', 601001);
        }

        try
        {
            DB::beginTransaction();

            $orderId = PayOrderModel::insertOne($payOrderData);
            if (empty($orderId))
            {
                DB::rollBack();

                return self::Fail('结算单创建失败，主订单写入失败', 601091);
            }

            //如果存在使用余额，这里需要从批次扣减余额并获取最终储值、赠送各使用的金额
            $useBalancePrice  = $usedData['balance']['price'] ?? 0;
            $useBalanceDetail = null;
            if (isset($usedData['balance']))
            {
                $memberId        = $usedData['balance']['memberId'];
                $useBalancePrice = $usedData['balance']['price'];
                $orderCode       = $payOrderData['pay_order_code'];

                $useRes = MemberBalanceLogic::UseMemberBalance($memberId,
                                                               $useBalancePrice,
                                                               MemberBalanceOperateResourceTypeEnum::Order->value,
                                                               $orderId,
                                                               $orderCode,
                                                               $publicParams);
                if ($useRes->isFail())
                {
                    DB::rollBack();

                    return $useRes;
                }

                $useBalanceDetail = $useRes->getData();
            }


            //如果使用了余额，并且获取到使用详情
            if (!empty($useBalanceDetail))
            {
                $useBalanceRecharge = $useBalanceDetail['useBalanceRecharge'];
                $useBalanceGift     = $useBalanceDetail['useBalanceGift'];

                //更新主订单信息
                $updateOrderBalanceResult = PayOrderModel::updateOne($orderId, [
                    'balance_recharge_price' => $useBalanceRecharge,
                    'balance_gift_price'     => $useBalanceGift,
                ]);
                if (empty($updateOrderBalanceResult))
                {
                    DB::rollBack();

                    return self::Fail('结算单创建失败，主订单更新余额使用失败', 601091);
                }

                //均摊余额金额到子订单
                /**
                 * 注意：必须先均摊不改变pay_price的两个余额，否则pay_price被余额抵扣完会发生除以零的错误
                 */
                $splitRes = PriceSplitHelper::CommonSplitPrice($paySubOrderData,
                                                               'balance_recharge_price',
                                                               $useBalanceRecharge);
                if ($splitRes->isFail())
                {
                    DB::rollBack();

                    return $splitRes;
                }
                $paySubOrderData = $splitRes->getData();

                $splitRes = PriceSplitHelper::CommonSplitPrice($paySubOrderData, 'balance_price', $useBalancePrice);
                if ($splitRes->isFail())
                {
                    DB::rollBack();

                    return $splitRes;
                }
                $paySubOrderData = $splitRes->getData();
            }

            foreach ($paySubOrderData as $key => $itemInfo)
            {
                $paySubOrderData[$key]['pay_order_id'] = $orderId;
            }

            //插入前验证金额是否平衡
            if (!PayOrderHelper::VerifyPayOrderPrice($paySubOrderData))
            {
                DB::rollBack();

                return self::Fail('结算单数据异常', 601002);
            };

            $insertSubRes = PaySubOrderModel::insert($paySubOrderData);
            if (empty($insertSubRes))
            {
                DB::rollBack();

                return self::Fail('结算单创建失败，子订单写入失败', 601091);
            }

            $insertExtRes = PaySubOrderExtModel::insert($paySubOrderExtData);
            if (empty($insertExtRes))
            {
                DB::rollBack();

                return self::Fail('结算单创建失败，子订单扩展信息写入失败', 601091);
            }

            //写入附加记录
            if (!empty($recordData['discount']))
            {
                PayOrderDiscountRecordsModel::insertOne($recordData['discount']);
            }
            if (!empty($recordData['thirdParty']))
            {
                PayOrderThirdPartyPayModel::insertOne($recordData['thirdParty']);
            }

            //如果需要锁定则调用Union锁定
            if (!empty($lockSheets))
            {
                $lockRes = SheetUnionLogic::DoLockSheet($lockSheets, $publicParams);
                if ($lockRes->isFail())
                {
                    DB::rollBack();

                    return $lockRes;
                }
            }

            DB::commit();

            return self::Success([
                                     'orderId' => $orderId
                                 ]);

        } catch (Throwable $throwable)
        {
            DB::rollBack();

            Log::error(__CLASS__ . '::' . __METHOD__ . ' 创建结算单异常', [
                'code'    => $throwable->getCode(),
                'message' => $throwable->getMessage(),
                'file'    => $throwable->getFile(),
                'line'    => $throwable->getLine(),
                'trace'   => $throwable->getTraceAsString(),
            ]);

            return self::Fail('结算单创建失败，写入异常', 601091);
        }
    }

    public static function GetDetail(array $params, array $publicParams): LogicResult
    {
        $payOrderCode = trim(Arr::get($params, 'payOrderCode', ''));
        $payOrderId   = intval(Arr::get($params, 'payOrderId', 0));
        //公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));

        if (empty($payOrderCode) && empty($payOrderId))
        {
            return self::Fail('获取结算单详情，缺少结算单号或ID必选参数', 400);
        }
        if (empty($hospitalId))
        {
            return self::Fail('获取结算单详情，缺少医院ID必选参数', 400);
        }

        $payOrder = PayOrderModel::GetPayOrderByCodeOrId($payOrderCode, $payOrderId, $hospitalId);
        if (empty($payOrder))
        {
            return self::Fail('结算单不存在', 601100);
        }
        $payOrderCode = $payOrder['pay_order_code'];
        $payOrderId   = $payOrder['id'];

        $subOrder = PaySubOrderModel::on()
                                    ->where('pay_order_code', $payOrderCode)
                                    ->get();
        if ($subOrder->isEmpty())
        {
            return self::Fail('结算单子单不存在', 601003);
        }

        $subOrderExt = PaySubOrderExtModel::on()
                                          ->where('pay_order_code', $payOrderCode)
                                          ->get();
        if ($subOrderExt->isEmpty())
        {
            return self::Fail('结算单子单扩展信息不存在', 601004);
        }

        //获取订单结构化数据
        $orderInfos = PayOrderHelper::FormatOrderListStructure([$payOrder], $publicParams, true, true);
        $orderInfo  = !empty($orderInfos) ? current($orderInfos) : [];
        if (empty($orderInfo))
        {
            return self::Fail('结算单不存在', 601100);
        }


        //按照业务进行分组
        $sheetsBusinessMap = [];
        foreach ($subOrderExt as $value)
        {
            $businessType = SheetBusinessTypeEnum::SUB_ORDER_TYPE_BUSINESS_TYPE[$value['type']] ?? null;
            if (empty($businessType))
            {
                continue;
            }

            if (!isset($sheetsBusinessMap[$businessType]))
            {
                $sheetsBusinessMap[$businessType] = [
                    'codes' => [],
                ];
            }

            $sheetsBusinessMap[$businessType]['codes'][] = $value['sheet_code'];
        }

        //获取购买单摘要数据
        $sheetSummaryRes = SheetUnionLogic::GetSheetSummary($sheetsBusinessMap, $publicParams);
        if ($sheetSummaryRes->isFail())
        {
            return $sheetSummaryRes;
        }
        $sheetSummary = $sheetSummaryRes->getData();

        //构建详情，未支付前使用ext构建详情
        foreach ($sheetSummary as $key => $value)
        {
            foreach ($value['data'] ?? [] as $index => $sheet)
            {
                $curSubOrderExt = $subOrderExt->firstWhere('sheet_code', $sheet['sheetCode']);
                if (empty($curSubOrderExt))
                {
                    continue;
                }
                $sheetSummary[$key]['data'][$index]['items'] = json_decode($curSubOrderExt['items'], true);
            }
        }

        $result = [
            ...$orderInfo,
            'data' => $sheetSummary,

        ];

        return self::Success($result);
    }


    /**
     * 取消结算单
     *
     * @param array $params
     * @param array $publicParams
     *
     * @return LogicResult
     * @throws Throwable
     */
    public static function CancelOrder(array $params, array $publicParams): LogicResult
    {
        $payOrderCode = trim(Arr::get($params, 'payOrderCode', ''));
        $payOrderId   = intval(Arr::get($params, 'payOrderId', 0));
        //公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        $userId     = intval(Arr::get($publicParams, '_userId'));

        if (empty($payOrderCode) && empty($payOrderId))
        {
            return self::Fail('取消结算单，缺少结算单号或ID必选参数', 400);
        }
        if (empty($hospitalId))
        {
            return self::Fail('取消结算单，缺少医院ID必选参数', 400);
        }
        if (empty($userId))
        {
            return self::Fail('取消结算单，缺少登录用户ID必选参数', 400);
        }

        $orderRes = PayOrderHelper::GetValidPayOrder(
            payOrderCode: $payOrderCode,
            payOrderId:   $payOrderId,
            hospitalId:   $hospitalId,
        );
        if ($orderRes->isFail())
        {
            return $orderRes;
        }
        $order = $orderRes->getData();

        //判断是否可以取消
        $checkDeleteRes = PayOrderHelper::CheckOrderDelete($order, $publicParams);
        if ($checkDeleteRes->isFail())
        {
            return $checkDeleteRes;
        }

        $paySubOrdersExtData = PaySubOrderExtHelper::GetSubOrderExtData(payOrderCode: $payOrderCode);
        if (empty($paySubOrdersExtData))
        {
            return self::Fail('取消结算单，获取结算子单扩展数据失败', 601004);
        }

        $sheetsBusinessMap = [];//解锁购买单数据
        foreach ($paySubOrdersExtData as $value)
        {
            $businessType = SheetBusinessTypeEnum::SUB_ORDER_TYPE_BUSINESS_TYPE[$value['type']] ?? null;
            if (empty($businessType))
            {
                continue;
            }

            if (!isset($sheetsBusinessMap[$businessType]))
            {
                $sheetsBusinessMap[$businessType] = [
                    'codes' => [],
                ];
            }

            $sheetsBusinessMap[$businessType]['codes'][] = $value['sheet_code'];
        }

        try
        {
            DB::beginTransaction();

            // 1 解锁购买单
            $unlockRes = SheetUnionLogic::DoUnlockSheet($sheetsBusinessMap, $publicParams);
            if ($unlockRes->isFail())
            {
                DB::rollBack();

                return $unlockRes;
            }

            // 2 取消结算主单
            $cancelOrderRes = PayOrderModel::on()
                                           ->where([
                                                       'pay_order_code' => $payOrderCode,
                                                       'status'         => SheetStatusEnum::Unpaid->value,
                                                   ])
                                           ->update([
                                                        'status'    => SheetStatusEnum::Cancelled->value,
                                                        'cancel_by' => $userId,
                                                        'cancel_at' => getCurrentTimeWithMilliseconds(),
                                                    ]);
            if (empty($cancelOrderRes))
            {
                DB::rollBack();

                return self::Fail('取消结算单，更新结算主单失败', 601130);
            }

            // 3 取消结算子单
            $cancelSubOrderRes = PaySubOrderModel::on()
                                                 ->where([
                                                             'pay_order_code' => $payOrderCode,
                                                             'status'         => SheetStatusEnum::Unpaid->value,
                                                         ])
                                                 ->update([
                                                              'status'    => SheetStatusEnum::Cancelled->value,
                                                              'cancel_by' => $userId,
                                                              'cancel_at' => getCurrentTimeWithMilliseconds(),
                                                          ]);
            if (empty($cancelSubOrderRes) || $cancelSubOrderRes != count($paySubOrdersExtData))
            {
                DB::rollBack();

                return self::Fail('取消结算单，更新结算子单失败', 601130);
            }

            // 4 取消结算子单扩展
            $cancelSubOrderExtRes = PaySubOrderExtModel::on()
                                                       ->where([
                                                                   'pay_order_code' => $payOrderCode,
                                                                   'process_status' => PaySubOrderExtProcessStatusEnum::Unprocessed->value,
                                                                   'status'         => 1,
                                                               ])
                                                       ->update([
                                                                    'status' => 0,
                                                                ]);
            if (empty($cancelSubOrderExtRes) || $cancelSubOrderExtRes != count($paySubOrdersExtData))
            {
                DB::rollBack();

                return self::Fail('取消结算单，更新结算子单扩展失败', 601130);
            }

            DB::commit();

            return self::Success();
        } catch (Throwable $throwable)
        {
            DB::rollBack();

            Log::error(__CLASS__ . '::' . __METHOD__ . ' 取消结算单异常', [
                'code'    => $throwable->getCode(),
                'message' => $throwable->getMessage(),
                'file'    => $throwable->getFile(),
                'line'    => $throwable->getLine(),
                'trace'   => $throwable->getTraceAsString(),
            ]);

            return self::Fail('取消结算单异常', 601131);
        }
    }

    /**
     * 获取结算订单详情【来自业务订单的详情】
     *
     * @param string      $payOrderCode
     * @param int         $hospitalId
     * @param string|null $keyByCode
     *
     * @return LogicResult
     */
    public static function GetPayOrderItemDetail(
        string $payOrderCode, int $hospitalId, ?string $keyByCode = 'paySubOrderCode'
    ): LogicResult
    {
        if (empty($hospitalId))
        {
            return self::Fail('医院ID必选', 400);
        }
        if (empty($payOrderCode))
        {
            return self::Fail('结算单号必选', 400);
        }

        $paySubOrder = PaySubOrderModel::on()
                                       ->where([
                                                   'pay_order_code' => $payOrderCode,
                                                   'status'         => SheetStatusEnum::Paid->value,
                                               ])
                                       ->get();
        if ($paySubOrder->isEmpty())
        {
            return self::Fail('结算单子单不存在', 601003);
        }

        $orderBusinessMap = [];
        foreach ($paySubOrder as $value)
        {
            if (!isset($orderBusinessMap[$value['type']]))
            {
                $orderBusinessMap[$value['type']] = [];
            }

            $orderBusinessMap[$value['type']][] = $value['pay_sub_order_code'];
        }

        // 批量获取各个业务的结算详情
        $task = ConcurrentTask::new();
        foreach ($orderBusinessMap as $businessType => $paySubOrderCodes)
        {
            $logic = self::GetOrderLogicClass($businessType);
            if (!self::CheckOrderLogicIsSupportDetail($logic))
            {
                return self::Fail('结算单逻辑处理器注册异常', 601401);
            }

            $task->addTask($businessType,
                fn() => $logic::GetOrdersItemDetail(
                    paySubOrderCodes: $paySubOrderCodes,
                    hospitalId:       $hospitalId,
                    keyByCode:        $keyByCode
                ));
        }

        DB::disconnect();
        $getOrderItemDetailRes = $task->run();
        if (empty($getOrderItemDetailRes))
        {
            return self::Fail('业务订单获取失败', 501503);
        }

        $orderItemDetailRes = [];
        foreach ($getOrderItemDetailRes as $type => $result)
        {
            if ($result->isException())
            {
                Log::error(__CLASS__ . '::' . __METHOD__ . '结算单：获取业务订单详情异常', [
                    'code'    => $result->getException()
                                        ->getCode(),
                    'message' => $result->getException()
                                        ->getMessage(),
                    'file'    => $result->getException()
                                        ->getFile(),
                    'line'    => $result->getException()
                                        ->getLine(),
                    'trace'   => $result->getException()
                                        ->getTraceAsString(),
                ]);

                return self::Fail('获取业务订单详情异常', 501504);
            }

            $data = $result->getData();
            if (!is_array($data))
            {
                return self::Fail('获取业务订单详情结果异常', 501504);
            }

            $orderItemDetailRes[] = [
                'type' => [
                    'id'   => $type,
                    'name' => PaySubOrderTypeEnum::getDescription($type) ?? null,
                ],
                'data' => $data,
            ];
        }

        return self::Success($orderItemDetailRes);
    }

    /**
     * 获取结算单的收据数据
     *
     * @param string $payOrderCode
     *
     * @return LogicResult
     */
    public static function GetOrderReceiptData(string $payOrderCode): LogicResult
    {
        $payOrderValidRes = PayOrderHelper::GetValidPayOrder($payOrderCode);
        if ($payOrderValidRes->isFail())
        {
            return $payOrderValidRes;
        }
        $order = $payOrderValidRes->getData();

        if ($order['status'] != SheetStatusEnum::Paid->value)
        {
            return self::Fail('结算单未支付，无法获取收据', 601106);
        }

        $hospitalId = $order['hospital_id'];

        $payOrderRes = PayOrderHelper::FormatOrderListStructure(
            [$order],
            ['_hospitalId' => $hospitalId]
        );
        $payOrder    = !empty($payOrderRes) ? current($payOrderRes) : [];
        if (empty($payOrder))
        {
            return self::Fail('结算单不存在', 601100);
        }

        $orderDetailRes = self::GetPayOrderItemDetail($payOrderCode, $hospitalId, null);
        if ($orderDetailRes->isFail())
        {
            return $orderDetailRes;
        }
        $orderDetail = $orderDetailRes->getData();

        $hasRecharge = false;
        $detail      = [];
        foreach ($orderDetail as $businessDetail)
        {
            if (($businessDetail['type']['id'] ?? 0) == PaySubOrderTypeEnum::Recharge->value)
            {
                $hasRecharge = true;
            }

            {
                foreach ($businessDetail['data'] as $orderItems)
                {
                    foreach ($orderItems as $item)
                    {
                        $detail[] = [
                            'name'     => $item['itemName'] ?? '',
                            'price'    => $item['originPrice'] ?? '',
                            'quantity' => ($item['quantity'] ?? '') . ($item['unit'] ?? ''),
                            'total'    => $item['salePrice'] ?? '',
                        ];

                        if (($item['isSuit'] ?? 0) == 1 && ($item['isPrintItems'] ?? false) && isset($item['suitItems']) && is_array($item['suitItems']))
                        {
                            foreach ($item['suitItems'] as $suitItem)
                            {
                                $detail[] = [
                                    'name'     => $suitItem['itemName'] ?? '',
                                    'price'    => '--',
                                    'quantity' => ($suitItem['quantity'] ?? '') . ($suitItem['unit'] ?? ''),
                                    'total'    => '--',
                                ];
                            }
                        }
                    }
                }
            }
        }


        $hospitalRes = HospitalHelper::GetHospitalFullInfo($order['hospital_id'], true);
        if ($hospitalRes->isFail())
        {
            return $hospitalRes;
        }

        $memberBalance = null;
        if (!empty($order['member_id']))
        {
            $memberBalance = MemberBalanceHelper::MemberBalance($order['member_id']);
        }


        $result = [
            ...$payOrder,
            'items'         => $detail,
            'hasRecharge'   => $hasRecharge,
            'memberBalance' => $memberBalance,
            'hospital'      => $hospitalRes->getData(),
            'currentTime'   => date('Y-m-d H:i:s'),
        ];

        return self::Success($result);
    }

    /**
     * 检查逻辑类是否支持获取订单详情
     *
     * @param string|null $orderLogic
     *
     * @return bool
     */
    private static function CheckOrderLogicIsSupportDetail(?string $orderLogic): bool
    {
        if (!$orderLogic)
        {
            return false;
        }

        return is_subclass_of($orderLogic, OrderItemInterface::class);
    }

    /**
     * 获取业务订单逻辑类
     *
     * @param string $businessType
     *
     * @return string|null
     */
    private static function GetOrderLogicClass(string $businessType): ?string
    {
        return self::ORDER_LOGIC_MAP[$businessType] ?? null;
    }
}
