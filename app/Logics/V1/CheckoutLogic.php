<?php

namespace App\Logics\V1;

use Throwable;
use Illuminate\Support\Arr;
use App\Logics\Logic;
use App\Logics\LogicResult;
use App\Enums\CheckoutTypeEnum;
use App\Support\Checkout\CheckoutHelper;
use App\Support\PayMode\HospitalPayModeHelper;
use App\Support\PayMode\PayModeThirdHelper;
use App\Models\MemberModel;
use App\Models\PayModeModel;
use App\Models\PayChannelModel;
use App\Support\Checkout\CheckoutPriceHelper;
use App\Support\Balance\MemberBalanceHelper;
use App\Logics\V1\BuySheet\SheetUnionLogic;

class CheckoutLogic extends Logic
{
    /**
     * 单次批量结算的结算单数量限制
     */
    const int CHECKOUT_SHEET_NUM_LIMIT = 50;

    /**
     * 结算准备数据
     *
     * @param array $params
     * @param array $publicParams
     *
     * @return LogicResult
     */
    public static function CheckoutPrepare(array $params, array $publicParams): LogicResult
    {
        // 业务参数
        $sheets     = Arr::get($params, 'sheets', []);
        $totalPrice = trim(Arr::get($params, 'totalPrice', '0.00'));
        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        $userId     = intval(Arr::get($publicParams, '_userId'));

        if (empty($sheets))
        {
            return self::Fail('结算准备，缺少购买单必选参数', 400);
        }
        if (count($sheets) > self::CHECKOUT_SHEET_NUM_LIMIT)
        {
            return self::Fail('单次结算购买单数量超过限制(' . self::CHECKOUT_SHEET_NUM_LIMIT . ')', 600080);
        }
        if (empty($totalPrice) && $totalPrice < 0)
        {
            return self::Fail('结算准备，缺少总金额必选参数', 400);
        }
        if (empty($hospitalId))
        {
            return self::Fail('结算准备，缺少医院ID必选参数', 400);
        }
        if (empty($userId))
        {
            return self::Fail('结算准备，缺少当前登录用户ID必选参数', 400);
        }

        // 根据业务类型对购买单的编码进行分组
        $sheetsCheckRes = CheckoutHelper::GetValidSheetParams($sheets);
        if ($sheetsCheckRes->isFail())
        {
            return $sheetsCheckRes;
        }
        $sheetsBusinessMap = $sheetsCheckRes->getData();

        // 批量获取各个业务的结算详情
        $doCheckoutPrepareRes = SheetUnionLogic::DoCheckoutPrepare($sheetsBusinessMap, $publicParams);
        if ($doCheckoutPrepareRes->isFail())
        {
            return $doCheckoutPrepareRes;
        }

        $doCheckoutPrepareData = $doCheckoutPrepareRes->getData();

        //校验金额是否相同
        $calTotalPrice = '0.00';
        foreach ($doCheckoutPrepareData as $businessTypeData)
        {
            $calTotalPrice = numberAdd([$calTotalPrice, $businessTypeData['totalPrice']], 2);
        }
        if (bccomp($calTotalPrice, $totalPrice, 2) != 0)
        {
            return self::Fail('结算准备，总金额与传入的总金额不一致', 600103);
        }

        //从中提取会员信息
        $memberInfo = $doCheckoutPrepareData[0]['data'][0]['memberInfo'] ?? null;

        $memberUid = $memberInfo['uid'] ?? '';
        $memberId  = null;
        if (!empty($memberUid))
        {
            $member = MemberModel::getOneByUid($memberUid);
            if (empty($member))
            {
                return self::Fail('会员不存在', 30001);
            }

            $memberId = $member->id;
        }

        //支持的支付方式等参数
        $checkoutParams = self::GetCheckoutParams($sheetsBusinessMap,
                                                  $calTotalPrice,
                                                  $memberId,
                                                  $publicParams);

        $result = [
            'memberInfo' => $memberInfo,
            'data'       => $doCheckoutPrepareData,
            'totalPrice' => $calTotalPrice,
            ...$checkoutParams,
        ];

        return self::Success($result);
    }

    /**
     * 获取结算的参数
     *
     * @param array    $checkoutPrepareData
     * @param string   $totalPrice
     * @param int|null $memberId
     * @param          $publicParams
     *
     * @return array
     */
    private static function GetCheckoutParams(
        array $checkoutPrepareData, string $totalPrice, ?int $memberId, $publicParams
    ): array
    {
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));

        if (empty($checkoutPrepareData) || empty($hospitalId))
        {
            return [];
        }

        $businessTypes        = array_keys($checkoutPrepareData);
        $supportCheckoutTypes = CheckoutTypeEnum::GetSheetTypesSupportType($businessTypes);

        $memberBalance = null;
        if (!empty($memberId) && in_array(CheckoutTypeEnum::Balance->value, $supportCheckoutTypes))
        {
            $memberBalance = MemberBalanceHelper::MemberBalance($memberId);
        }

        //获取结算方式
        $payModeRes = HospitalPayModeHelper::GetHospitalPayChannelAndModel($hospitalId);
        if ($payModeRes->isFail())
        {
            return [];
        }

        //获取第三方结算方式
        $payModeThirdOptions = [];
        if (in_array(CheckoutTypeEnum::ThirdParty->value, $supportCheckoutTypes))
        {
            $payModeThirdOptions = PayModeThirdHelper::GetPayModeThirdOptions();
        }


        return [
            'checkoutTypes'       => $supportCheckoutTypes,
            'memberBalance'       => $memberBalance,
            'totalReducePrice'    => '0.00',
            'payPrice'            => $totalPrice,
            'payChannelsData'     => $payModeRes->getData('payChannelsData'),
            'payModeThirdOptions' => $payModeThirdOptions,
        ];
    }


    /**
     * 结算校验
     *
     * @param array $params
     * @param array $publicParams
     * @param bool  $isForCheckout 是否为结算校验 true:是 false:否
     *
     * @return LogicResult
     */
    public static function Checking(array $params, array $publicParams, bool $isForCheckout = false): LogicResult
    {
        //业务参数
        $sheets             = Arr::get($params, 'sheets', []);
        $totalPrice         = trim(Arr::get($params, 'totalPrice', '0.00'));
        $activityPrice      = trim(Arr::get($params, 'activityPrice', '0.00'));
        $couponPrice        = trim(Arr::get($params, 'couponPrice', '0.00'));
        $discountPrice      = trim(Arr::get($params, 'discountPrice', '0.00'));
        $thirdPartyPayPrice = trim(Arr::get($params, 'thirdPartyPayPrice', '0.00'));
        $balancePayPrice    = trim(Arr::get($params, 'balancePayPrice', '0.00'));
        $cashPayPrice       = trim(Arr::get($params, 'cashPayPrice', '0.00'));
        $discount           = Arr::get($params, 'discount', null);
        $thirdParty         = Arr::get($params, 'thirdParty', null);
        $balance            = Arr::get($params, 'balance', null);
        $cash               = Arr::get($params, 'cash', null);
        //公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));

        if (empty($hospitalId))
        {
            return self::Fail('结算校验，缺少医院ID必选参数', 400);
        }

        if (empty($sheets))
        {
            return self::Fail('结算校验，缺少购买单必选参数', 400);
        }
        if (empty($totalPrice) && $totalPrice < 0)
        {
            return self::Fail('结算校验，缺少总金额必选参数', 400);
        }
        if (empty($activityPrice) && $activityPrice < 0)
        {
            return self::Fail('结算校验，缺少活动优惠金额必选参数', 400);
        }
        if (empty($couponPrice) && $couponPrice < 0)
        {
            return self::Fail('结算校验，缺少代金券/现金券优惠金额必选参数', 400);
        }
        if (empty($discountPrice) && $discountPrice < 0)
        {
            return self::Fail('结算校验，缺少折扣优惠金额必选参数', 400);
        }
        if (empty($thirdPartyPayPrice) && $thirdPartyPayPrice < 0)
        {
            return self::Fail('结算校验，缺少第三方支付金额必选参数', 400);
        }
        if (empty($balancePayPrice) && $balancePayPrice < 0)
        {
            return self::Fail('结算校验，缺少余额支付金额必选参数', 400);
        }
        if (empty($cashPayPrice) && $cashPayPrice < 0)
        {
            return self::Fail('结算校验，缺少现付款支付金额必选参数', 400);
        }
        if (!empty($discount) && (!isset($discount['type']) || !isset($discount['rate']) || !isset($discount['price']) || !isset($discount['reason'])))
        {
            return self::Fail('结算校验，缺少折扣信息必选参数', 400);
        }
        if (!empty($thirdParty) && (!isset($thirdParty['type']) || !isset($thirdParty['price']) || !isset($thirdParty['remark'])))
        {
            return self::Fail('结算校验，缺少第三方支付信息必选参数', 400);
        }
        if (!empty($balance) && (!isset($balance['use']) || !isset($balance['price'])))
        {
            return self::Fail('结算校验，缺少余额信息必选参数', 400);
        }
        if (!empty($cash) && (!isset($cash['channel']) || !isset($cash['mode']) || !isset($cash['price'])))
        {
            return self::Fail('结算校验，缺少现付款信息必选参数', 400);
        }

        //特殊结算方式验证
        if ($activityPrice > 0 || $couponPrice > 0)
        {
            return self::Fail('结算校验，暂不支持活动优惠和代金券/现金券优惠', 600250);
        }

        //0元验证
        if (bccomp($totalPrice, '0.00', 2) < 1)
        {
            if (bccomp($activityPrice, 0, 2) > 0)
            {
                return self::Fail('结算校验，0元总金额，活动优惠金额不能大于0', 600250);
            }
            if (bccomp($couponPrice, 0, 2) > 0)
            {
                return self::Fail('结算校验，0元总金额，代金券/现金券优惠金额不能大于0', 600250);
            }
            if (bccomp($discountPrice, 0, 2) > 0)
            {
                return self::Fail('结算校验，0元总金额，折扣优惠金额不能大于0', 600250);
            }
            if (bccomp($thirdPartyPayPrice, 0, 2) > 0)
            {
                return self::Fail('结算校验，0元总金额，第三方支付金额不能大于0', 600250);
            }
            if (bccomp($balancePayPrice, 0, 2) > 0)
            {
                return self::Fail('结算校验，0元总金额，余额支付金额不能大于0', 600250);
            }
            if (bccomp($cashPayPrice, 0, 2) > 0)
            {
                return self::Fail('结算校验，0元总金额，现付款支付金额不能大于0', 600250);
            }
        }

        //先使用prepare获取基本验证
        $checkoutPrepareRes = self::CheckoutPrepare($params, $publicParams);
        if ($checkoutPrepareRes->isFail())
        {
            return self::Fail('结算基本数据校验失败', 600250);
        }
        $prepareData = $checkoutPrepareRes->getData();

        //价格验证
        $checkoutPriceRes = CheckoutPriceHelper::CalculateCheckoutPrice($prepareData, $params, $publicParams);
        if ($checkoutPriceRes->isFail())
        {
            return $checkoutPriceRes;
        }
        $checkoutPriceData = $checkoutPriceRes->getData();

        //支付方式，支付渠道获取
        $payChannelId = $cash['channel'] ?? 0;
        $payModeId    = $cash['mode'] ?? 0;
        $payChannel   = null;
        $payMode      = null;
        if (!empty($payChannelId) && !empty($payModeId))
        {
            $payChannel = PayChannelModel::getOne($payChannelId);
            $payMode    = PayModeModel::getOne($payModeId);

            if (empty($payChannel))
            {
                return self::Fail('支付渠道不存在', 600431);
            }
            if (empty($payMode))
            {
                return self::Fail('支付方式不存在', 600432);
            }
        }

        //TODO:未来加入对打折优惠的权限和边际验证

        //用户ID信息的获取
        //可用余额的再次验证
        $memberInfo = $prepareData['memberInfo'] ?? null;
        $memberUid  = $memberInfo['uid'] ?? '';
        if (!empty($memberUid))
        {
            $member = MemberModel::getOneByUid($memberUid);
            if (empty($member))
            {
                return self::Fail('会员不存在', 30001);
            }

            $memberId         = $member->id;
            $memberInfo['id'] = $memberId;

            $memberBalance = MemberBalanceHelper::MemberBalance($memberId);

            if (bccomp($memberBalance['balanceTotal'] ?? '0.00', $balancePayPrice, 2) < 0)
            {
                return self::Fail('会员余额不足', 600440);
            }
        }


        $result = [];

        if ($isForCheckout)
        {
            $result = [
                'memberInfo'       => $memberInfo,
                'sheets'           => $sheets,
                'prepareData'      => $prepareData,
                'checkoutTypeData' => [
                    'discount'   => $discount,
                    'thirdParty' => $thirdParty,
                    'balance'    => $balance,
                    'cash'       => $cash,
                ],
                'priceData'        => $checkoutPriceData,
                'payChannel'       => $payChannel,
                'payMode'          => $payMode,
            ];
        }


        return self::Success($result);
    }

    /**
     * 结算下单[与Checking同参数]
     *
     * 参数校验：调用self::Checking做一次前置校验
     * 生成支付单
     * 呼叫支付(如果是同步支付)
     *
     * @param array $params
     * @param array $publicParams
     *
     * @return LogicResult
     * @throws Throwable
     */
    public static function DoCheckout(array $params, array $publicParams): LogicResult
    {
        $checkingRes = self::Checking($params, $publicParams, true);
        if ($checkingRes->isFail())
        {
            return $checkingRes;
        }

        $checkingData = $checkingRes->getData();

        //结算下支付单
        $createPayOrderRes = PayOrderLogic::CreatePayOrder($checkingData, $publicParams);
        if ($createPayOrderRes->isFail())
        {
            return $createPayOrderRes;
        }
        $payOrderData = $createPayOrderRes->getData();

        //呼起支付
        $goPayRes = PayLogic::GoPay($payOrderData, $publicParams, true);
        if ($goPayRes->isFail())
        {
            return $goPayRes;
        }
        $goPayData = $goPayRes->getData();

        return self::Success($goPayData);
    }
}
