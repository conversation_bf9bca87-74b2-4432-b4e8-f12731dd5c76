<?php

namespace App\Logics\V1;

use DB;
use Arr;
use Log;
use Throwable;
use App\Support\Item\ItemHelper;
use App\Support\Stock\StockQuantityConversionHelper;
use App\Enums\PageEnum;
use App\Enums\StockReduceTypeEnum;
use App\Logics\Logic;
use App\Logics\LogicResult;
use App\Models\StockItemShelfModel;
use App\Models\StockItemShelfReduceModel;

/**
 * 库存减少操作逻辑类
 *
 * 主要处理各种出库场景的库存减少操作
 * 支持整散比换算、效期优先出库、散装优先策略
 */
class StockItemShelfReduceLogic extends Logic
{
    /**
     * 获取出库列表筛选项
     *
     * @return LogicResult
     */
    public static function GetListFilterOptions(): LogicResult
    {
        return self::Success([
                                 'typeOptions' => StockReduceTypeEnum::options(),
                             ]);
    }

    /**
     * 获取出库记录列表
     *
     * @param array $searchParams
     * @param array $publicParams
     *
     * @return LogicResult
     */
    public static function GetReduceStockRecord(array $searchParams, array $publicParams): LogicResult
    {
        if (empty($publicParams))
        {
            return self::Fail('获取出库记录，缺少公共必选参数', 400);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        if (empty($hospitalId))
        {
            return self::Fail('获取出库记录，缺少医院ID必选参数', 400);
        }

        // 业务参数处理
        $keywords      = trimWhitespace(Arr::get($searchParams, 'keywords', ''));
        $orderCode     = trimWhitespace(Arr::get($searchParams, 'orderCode', ''));
        $startDate     = trimWhitespace(Arr::get($searchParams, 'startDate', ''));
        $endDate       = trimWhitespace(Arr::get($searchParams, 'endDate', ''));
        $type          = Arr::get($searchParams, 'type');
        $createUserUid = trimWhitespace(Arr::get($searchParams, 'createUserUid', ''));
        $page          = intval(Arr::get($searchParams, 'page', PageEnum::DefaultPageIndex->value)) ?: PageEnum::DefaultPageIndex->value;
        $count         = intval(Arr::get($searchParams, 'count', PageEnum::DefaultPageSize->value)) ?: PageEnum::DefaultPageSize->value;

        // 日期格式验证
        if (!empty($startDate) && !checkDateIsValid($startDate))
        {
            return self::Fail('获取出库记录，开始日期格式错误', 400);
        }
        if (!empty($endDate) && !checkDateIsValid($endDate))
        {
            return self::Fail('获取出库记录，结束日期格式错误', 400);
        }

        // 构建查询参数
        $queryParams = [
            'hospitalId'    => $hospitalId,
            'keywords'      => $keywords,
            'orderCode'     => $orderCode,
            'startDate'     => $startDate,
            'endDate'       => $endDate,
            'type'          => $type,
            'createUserUid' => $createUserUid,
        ];

        // 获取出库记录列表
        $getReduceStockRecordRes = StockItemShelfReduceModel::getReduceStockRecordListData($queryParams, $page, $count);
        if (empty($getReduceStockRecordRes))
        {
            return self::Success(['total' => 0, 'data' => []]);
        }

        $totalCount            = $getReduceStockRecordRes['total'] ?? 0;
        $reduceStockRecordList = $getReduceStockRecordRes['data'] ?? [];
        if ($totalCount <= 0 || empty($reduceStockRecordList))
        {
            return self::Success(['total' => 0, 'data' => []]);
        }

        // 格式化返回数据
        $getFormatReduceStockRecordRes = self::FormatReduceStockRecordStructure($reduceStockRecordList);
        if ($getFormatReduceStockRecordRes->isFail())
        {
            return $getFormatReduceStockRecordRes;
        }

        return self::Success(['total' => $totalCount, 'data' => $getFormatReduceStockRecordRes->getData()]);
    }

    /**
     * 库存减少操作
     *
     * @param array $reduceStockParams 减少库存的参数
     * @param array $publicParams      公共参数，包含医院ID、操作人等
     *
     * @return LogicResult
     * @throws Throwable
     */
    public static function ReduceStockShelfQuantity(array $reduceStockParams, array $publicParams): LogicResult
    {
        if (empty($reduceStockParams))
        {
            return self::Fail('商品出库，缺少必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('商品出库，缺少公共必选参数', 400);
        }

        // 公共参数验证
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId', 0));
        $userId     = intval(Arr::get($publicParams, '_userId', 0));
        if (empty($hospitalId))
        {
            return self::Fail('商品出库，缺少医院ID必选参数', 400);
        }
        if (empty($userId))
        {
            return self::Fail('商品出库，缺少操作人ID必选参数', 400);
        }

        // 验证减少库存参数
        $validReduceStockParams = [];
        foreach ($reduceStockParams as $curReduceStockInfo)
        {
            $curCheckParamsRes = self::CheckReduceStockItemQuantityParams($curReduceStockInfo);
            if ($curCheckParamsRes->isFail())
            {
                return $curCheckParamsRes;
            }

            $validReduceStockParams[] = $curCheckParamsRes->getData();
        }

        // 无有效出库信息
        if (empty($validReduceStockParams))
        {
            return self::Fail('商品出库，商品信息全部无效', 45000);
        }

        // 获取减少库存商品信息
        $itemIds        = array_unique(array_filter(array_column($validReduceStockParams, 'itemId')));
        $getItemInfoRes = ItemLogic::GetItemFullInfo(itemIds: $itemIds, publicParams: $publicParams);
        if ($getItemInfoRes->isFail())
        {
            return $getItemInfoRes;
        }

        // 商品信息不完整
        $getItemInfoRes = array_column($getItemInfoRes->getData(), null, 'id');
        if (count($itemIds) != count($getItemInfoRes))
        {
            return self::Fail('商品出库，部分商品信息不存在', 45000);
        }

        // 获取商品加权价
        $getItemDailyPriceRes = StockItemDailyPriceLogic::GetItemNowDailyPrice($itemIds, $publicParams);
        if ($getItemDailyPriceRes->isFail())
        {
            return $getItemDailyPriceRes;
        }

        // 加权价不完整
        $getItemDailyPriceRes = $getItemDailyPriceRes->getData();
        if (count($itemIds) != count($getItemDailyPriceRes))
        {
            return self::Fail('商品出库，部分商品加权价不存在', 45000);
        }

        try
        {
            DB::beginTransaction();

            $returnReduceQuantityInfo = [];
            foreach ($validReduceStockParams as $curValidReduceStockInfo)
            {
                $curItemId   = $curValidReduceStockInfo['itemId'];
                $curItemInfo = $getItemInfoRes[$curItemId] ?? [];
                if (empty($curItemInfo))
                {
                    DB::rollBack();

                    return self::Fail("商品出库【{$curValidReduceStockInfo['itemBarcode']}】，商品信息不存在", 45000);
                }

                $curItemDailyPrice = $getItemDailyPriceRes[$curItemId] ?? [];
                if (empty($curItemDailyPrice))
                {
                    DB::rollBack();

                    return self::Fail("商品出库【{$curValidReduceStockInfo['itemBarcode']}】，商品加权价不存在", 45000);
                }

                $curItemInfo['stock_daily_price'] = $curItemDailyPrice;

                $getReduceStockItemShelfRes = self::ReduceStockItemShelf($curValidReduceStockInfo, $curItemInfo, $publicParams);
                if ($getReduceStockItemShelfRes->isFail())
                {
                    DB::rollBack();

                    return $getReduceStockItemShelfRes;
                }

                // 出库数量、价格信息
                $curReducePackQuantity = $getReduceStockItemShelfRes->getData('reducePackQuantity');
                $curPackDailyPrice     = $getReduceStockItemShelfRes->getData('packDailyPrice');
                $curReduceBulkQuantity = $getReduceStockItemShelfRes->getData('reduceBulkQuantity');
                $curBulkDailyPrice     = $getReduceStockItemShelfRes->getData('bulkDailyPrice');
                $curReduceDetailInfo   = $getReduceStockItemShelfRes->getData('reduceDetailInfo', []);
                if (!in_array($curValidReduceStockInfo['reduceType'], StockReduceTypeEnum::getLooseStockTypes()) && $curReducePackQuantity <= 0 && $curReduceBulkQuantity <= 0)
                {
                    DB::rollBack();

                    return self::Fail("商品出库【{$curValidReduceStockInfo['itemBarcode']}】，出库数量异常", 45001);
                }

                // 库存扣减明细，可能同一个商品存在多条出库（关联业务ID不同），所以返回时汇总
                if (!isset($returnReduceQuantityInfo[$curItemId]))
                {
                    $returnReduceQuantityInfo[$curItemId] = [
                        'reducePackQuantity' => $curReducePackQuantity,
                        'packDailyPrice'     => $curPackDailyPrice,
                        'reduceBulkQuantity' => $curReduceBulkQuantity,
                        'bulkDailyPrice'     => $curBulkDailyPrice,
                        'reduceDetailInfo'   => $curReduceDetailInfo,
                    ];
                }
                else
                {
                    $returnReduceQuantityInfo[$curItemId]['reducePackQuantity'] = numberAdd([
                                                                                                $returnReduceQuantityInfo[$curItemId]['reducePackQuantity'],
                                                                                                $curReducePackQuantity
                                                                                            ]);
                    $returnReduceQuantityInfo[$curItemId]['reduceBulkQuantity'] = numberAdd([
                                                                                                $returnReduceQuantityInfo[$curItemId]['reduceBulkQuantity'],
                                                                                                $curReduceBulkQuantity
                                                                                            ]);
                    $returnReduceQuantityInfo[$curItemId]['reduceDetailInfo']   = array_merge($returnReduceQuantityInfo[$curItemId]['reduceDetailInfo'], $curReduceDetailInfo);
                }
            }

            DB::commit();

            return self::Success($returnReduceQuantityInfo);

        } catch (Throwable $throwable)
        {
            DB::rollBack();

            Log::error(__CLASS__ . '::' . __METHOD__ . ' 库存减少异常', [
                'code'    => $throwable->getCode(),
                'message' => $throwable->getMessage(),
                'file'    => $throwable->getFile(),
                'line'    => $throwable->getLine(),
                'trace'   => $throwable->getTraceAsString(),
            ]);

            return self::Fail('库存减少异常', 44002);
        }
    }

    /**
     * 减少商品库存
     *
     * @param array $reduceStockInfo 减少库存信息
     * @param array $itemInfo        商品信息
     * @param array $publicParams    公共参数
     *
     * @return LogicResult
     * @throws Throwable
     */
    private static function ReduceStockItemShelf(array $reduceStockInfo, array $itemInfo, array $publicParams): LogicResult
    {
        if (empty($reduceStockInfo))
        {
            return self::Fail('扣除商品库存，缺少必选参数', 400);
        }
        if (empty($itemInfo))
        {
            return self::Fail('扣除商品库存，缺少商品信息', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('扣除商品库存，缺少公共必选参数', 400);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId', 0));
        $userId     = intval(Arr::get($publicParams, '_userId', 0));
        if (empty($hospitalId) || empty($userId))
        {
            return self::Fail('扣除商品库存，缺少公共必选参数', 400);
        }

        // 业务参数
        $itemId           = intval(Arr::get($reduceStockInfo, 'itemId', 0));
        $itemBarcode      = trimWhitespace(Arr::get($reduceStockInfo, 'itemBarcode', ''));
        $reduceType       = intval(Arr::get($reduceStockInfo, 'reduceType', 0));
        $reduceSubType    = intval(Arr::get($reduceStockInfo, 'reduceSubType', 0));
        $relationCode     = trimWhitespace(Arr::get($reduceStockInfo, 'relationCode', ''));
        $relationId       = intval(Arr::get($reduceStockInfo, 'relationId', 0));
        $relationDetailId = intval(Arr::get($reduceStockInfo, 'relationDetailId', 0));
        $packQuantity     = Arr::get($reduceStockInfo, 'packQuantity');
        $bulkQuantity     = Arr::get($reduceStockInfo, 'bulkQuantity');
        $shelfCode        = trimWhitespace(Arr::get($reduceStockInfo, 'shelfCode', ''));
        $produceDate      = Arr::get($reduceStockInfo, 'produceDate');
        $expireDate       = Arr::get($reduceStockInfo, 'expiredDate');
        $remark           = trimWhitespace(Arr::get($reduceStockInfo, 'remark', ''));

        // 计算需要出库的总散装数量
        $itemBulkRatio             = $itemInfo['bulk_ratio'];
        $outboundTotalBulkQuantity = StockQuantityConversionHelper::convertToTotalBulkQuantity($packQuantity, $bulkQuantity, $itemBulkRatio);
        if ($outboundTotalBulkQuantity <= 0)
        {
            return self::Fail("扣除商品库存【{$itemBarcode}】，出库数量错误", 45001);
        }

        // 商品加权价
        $itemPackDailyPrice = $itemInfo['stock_daily_price']['packPrice'];
        $itemBulkDailyPrice = $itemInfo['stock_daily_price']['bulkPrice'];

        // 获取该商品的有效库存（按效期排序），如果不需要效期的直接获取有效的
        $withFilterCondition = [
            'shelf_code'   => $shelfCode,
            'produce_date' => $produceDate,
            'expired_date' => $expireDate,
        ];

        $getStockListRes = StockItemShelfModel::getStockItemEffectiveStockDetail($hospitalId, [$itemId], $withFilterCondition);
        $getStockListRes = $getStockListRes[$itemId] ?? [];
        if (!in_array($reduceType, StockReduceTypeEnum::getLooseStockTypes()) && empty($getStockListRes))
        {
            return self::Fail("扣除商品库存【{$itemBarcode}】，无有效库存", 45002);
        }

        // 验证库存是否足够
        $effectiveBulkTotalQuantity = 0;
        foreach ($getStockListRes as $curStockInfo)
        {

            $effectiveBulkTotalQuantity = numberAdd([
                                                        $effectiveBulkTotalQuantity,
                                                        StockQuantityConversionHelper::convertToTotalBulkQuantity($curStockInfo['effective_pack_quantity'],
                                                                                                                  $curStockInfo['effective_bulk_quantity'],
                                                                                                                  $itemBulkRatio)
                                                    ]);

        }

        // 过滤库库存不足情况
        if (!in_array($reduceStockInfo['reduceType'], StockReduceTypeEnum::getLooseStockTypes()))
        {
            if ($effectiveBulkTotalQuantity < $outboundTotalBulkQuantity)
            {
                return self::Fail("扣除商品库存不足【{$itemBarcode}】，需要出{$outboundTotalBulkQuantity}散装，可用{$effectiveBulkTotalQuantity}散装", 45003);
            }
        }

        // 需要出库的总散装数量
        $remainTotalQuantity = $outboundTotalBulkQuantity;

        // 返回出库整装、散装数量
        $reducePackQuantity = 0;
        $reduceBulkQuantity = 0;
        $reduceDetailInfo   = [];

        try
        {
            DB::beginTransaction();

            // 库存扣减记录
            $insertStockItemShelfReduce = [];
            foreach ($getStockListRes as $curStockInfo)
            {
                // 如果已经满足出库数量，则提前退出循环
                if ($remainTotalQuantity <= 0)
                {
                    break;
                }

                // 当前行库存信息
                $curStockId          = $curStockInfo['id'];
                $curAvailablePackQty = $curStockInfo['effective_pack_quantity'] ?? 0;
                $curAvailableBulkQty = $curStockInfo['effective_bulk_quantity'] ?? 0;
                if (empty($curStockId) || ($curAvailablePackQty <= 0 && $curAvailableBulkQty <= 0))
                {
                    continue;
                }

                // 1.优先出当前行的散装库存
                $curUseBulkQuantity = min($remainTotalQuantity, $curAvailableBulkQty);

                // 2.剩余需要出库的总散装数量
                $remainTotalQuantity = numberSub([$remainTotalQuantity, $curUseBulkQuantity]);

                // 3.如果散装不够，拆分整装库存。记录使用整装数、实际使用整装中的散装数、拆分后加回的散装数
                $curUsePackQuantity         = 0;
                $curUseBulkQuantityFromPack = 0;
                $curUsePackAddBackBulkQty   = 0;

                // 散装不够时，拆分当前行的整装库存
                if ($remainTotalQuantity > 0 && $curAvailablePackQty > 0 && $itemBulkRatio > 0)
                {
                    // 计算需要多少个整装，转化成总共可提供的散装数
                    $curNeededPackQuantity = ceil($remainTotalQuantity / $itemBulkRatio);
                    $curUsePackQuantity    = min($curNeededPackQuantity, $curAvailablePackQty);
                    $curPackToBulkQuantity = numberMul([$curUsePackQuantity, $itemBulkRatio]);

                    // 剩余实际需要使用的散装数量
                    $curUseBulkQuantityFromPack = min($remainTotalQuantity, $curPackToBulkQuantity);

                    // 拆分后扣减使用的后，如果存在剩余的散装加回当前行散装库存
                    if ($curPackToBulkQuantity > $curUseBulkQuantityFromPack)
                    {
                        $curUsePackAddBackBulkQty = numberSub([$curPackToBulkQuantity, $curUseBulkQuantityFromPack]);
                    }

                    // 剩余需要出库的总散装数量
                    $remainTotalQuantity = numberSub([$remainTotalQuantity, $curUseBulkQuantityFromPack]);
                }

                // 如果当前行没有发生扣减，则跳过
                if ($curUseBulkQuantity <= 0 && $curUsePackQuantity <= 0)
                {
                    continue;
                }

                // 扣减整装和散装，同时加回拆分剩余的散装
                $curAffectedRows = StockItemShelfModel::on()
                                                      ->where(['id' => $curStockId])
                                                      ->whereRaw('effective_pack_quantity = ? and effective_pack_quantity - ? >= 0',
                                                                 [$curAvailablePackQty, $curUsePackQuantity])
                                                      ->whereRaw('effective_bulk_quantity = ? and effective_bulk_quantity - ? + ? >= 0',
                                                                 [$curAvailableBulkQty, $curUseBulkQuantity, $curUsePackAddBackBulkQty])
                                                      ->update([
                                                                   'effective_pack_quantity' => DB::raw('effective_pack_quantity - ' . $curUsePackQuantity),
                                                                   'effective_bulk_quantity' => DB::raw('effective_bulk_quantity - ' . $curUseBulkQuantity . ' + ' . $curUsePackAddBackBulkQty),
                                                               ]);
                if ($curAffectedRows <= 0)
                {
                    DB::rollBack();

                    return self::Fail("扣除商品库存【{$itemBarcode}】，更新失败", 45004);
                }

                // 准备并记录详细的出库日志
                $totalReducedBulk = numberAdd([$curUseBulkQuantity, $curUseBulkQuantityFromPack]);

                // 换算实际出库的整装和散装数量
                $recordQuantity     = StockQuantityConversionHelper::convertToPackAndBulkQuantity($totalReducedBulk, $itemBulkRatio);
                $recordPackQuantity = $recordQuantity['packQuantity'];
                $recordBulkQuantity = $recordQuantity['bulkQuantity'];

                // 记录库存扣减明细
                $insertStockItemShelfReduce[] = [
                    'type'                => $reduceType,
                    'sub_type'            => $reduceSubType,
                    'relation_code'       => $relationCode,
                    'relation_id'         => $relationId,
                    'relation_detail_id'  => $relationDetailId,
                    'stock_add_id'        => $curStockInfo['stock_item_shelf_add_id'],
                    'org_id'              => $curStockInfo['org_id'],
                    'brand_id'            => $curStockInfo['brand_id'],
                    'hospital_id'         => $curStockInfo['hospital_id'],
                    'warehouse_id'        => $curStockInfo['warehouse_id'],
                    'stock_item_shelf_id' => $curStockId,
                    'item_id'             => $itemId,
                    'item_barcode'        => $itemBarcode,
                    'pack_quantity'       => $recordPackQuantity,
                    'bulk_quantity'       => $recordBulkQuantity,
                    'produce_date'        => $curStockInfo['produce_date'],
                    'expired_date'        => $curStockInfo['expired_date'],
                    'shelf_life'          => $curStockInfo['shelf_life'],
                    'shelf_code'          => $curStockInfo['shelf_code'],
                    'pack_price'          => $recordPackQuantity > 0 ? $itemPackDailyPrice : 0,
                    'bulk_price'          => $recordBulkQuantity > 0 ? $itemBulkDailyPrice : 0,
                    'bulk_ratio'          => $itemBulkRatio,
                    'remark'              => $remark,
                    'created_by'          => $userId
                ];

                // 累计出库整装和散装数量
                $reducePackQuantity += $recordPackQuantity;
                $reduceBulkQuantity += $recordBulkQuantity;
                $reduceDetailInfo[] = [
                    'stockItemShelfId' => $curStockId,
                    'produceDate'      => $curStockInfo['produce_date'],
                    'expiredDate'      => $curStockInfo['expired_date'],
                    'packQuantity'     => $recordPackQuantity,
                    'bulkQuantity'     => $recordBulkQuantity,
                ];
            }

            // 过滤可缺货出库库存不足情况，如果最终需要扣除的数量大于0，说明库存不足
            if (!in_array($reduceType, StockReduceTypeEnum::getLooseStockTypes()) && $remainTotalQuantity > 0)
            {
                DB::rollBack();

                return self::Fail("扣除商品库存【{$itemBarcode}】，未出库完成，库存不足", 45002);
            }

            // 批量插入库存扣减记录
            if (!empty($insertStockItemShelfReduce))
            {
                StockItemShelfReduceModel::insert($insertStockItemShelfReduce);
            }

            DB::commit();

            return self::Success([
                                     'reducePackQuantity' => $reducePackQuantity,
                                     'packDailyPrice'     => $itemPackDailyPrice,
                                     'reduceBulkQuantity' => $reduceBulkQuantity,
                                     'bulkDailyPrice'     => $itemBulkDailyPrice,
                                     'reduceDetailInfo'   => $reduceDetailInfo,
                                 ]);
        } catch (Throwable $throwable)
        {
            DB::rollBack();

            Log::error(__CLASS__ . '::' . __METHOD__ . ' 扣除商品库存异常', [
                'code'    => $throwable->getCode(),
                'message' => $throwable->getMessage(),
                'file'    => $throwable->getFile(),
                'line'    => $throwable->getLine(),
                'trace'   => $throwable->getTraceAsString(),
            ]);

            return self::Fail('扣除商品库存失败', 45004);
        }
    }

    /**
     * 验证减少库存参数
     *
     * @param array $curReduceStockInfo
     *
     * @return LogicResult
     */
    private static function CheckReduceStockItemQuantityParams(array $curReduceStockInfo): LogicResult
    {
        $curItemId           = intval(Arr::get($curReduceStockInfo, 'itemId', 0));
        $curItemBarcode      = trimWhitespace(Arr::get($curReduceStockInfo, 'itemBarcode', ''));
        $curReduceType       = intval(Arr::get($curReduceStockInfo, 'reduceType', 0));
        $curReduceSubType    = intval(Arr::get($curReduceStockInfo, 'reduceSubType', 0));
        $curRelationCode     = trimWhitespace(Arr::get($curReduceStockInfo, 'relationCode', ''));
        $curRelationId       = intval(Arr::get($curReduceStockInfo, 'relationId', 0));
        $curRelationDetailId = intval(Arr::get($curReduceStockInfo, 'relationDetailId', 0));
        $curPackQuantity     = Arr::get($curReduceStockInfo, 'packQuantity', 0);
        $curBulkQuantity     = Arr::get($curReduceStockInfo, 'bulkQuantity', 0);
        $curShelfCode        = trimWhitespace(Arr::get($curReduceStockInfo, 'shelfCode', ''));
        $curProduceDate      = Arr::get($curReduceStockInfo, 'produceDate');
        $curExpiredDate      = Arr::get($curReduceStockInfo, 'expiredDate');
        $curRemark           = trimWhitespace(Arr::get($curReduceStockInfo, 'remark', ''));
        if (empty($curItemId) && empty($curItemBarcode))
        {
            return self::Fail('库存减少，商品条码错误', 45000);
        }

        $errorPrefix = '商品出库 【' . $curItemBarcode . '】';
        if ($curPackQuantity <= 0 && $curBulkQuantity <= 0)
        {
            return self::Fail($errorPrefix . '，出库整装数量和出库散装数量不能同时为0或负数', 44009);
        }
        if (empty($curReduceType) || StockReduceTypeEnum::notExists($curReduceType))
        {
            return self::Fail($errorPrefix . '，缺少类型错误', 45000);
        }
        if (StockReduceTypeEnum::typeIsMustSubType($curReduceType) && StockReduceTypeEnum::notExists($curReduceSubType))
        {
            return self::Fail($errorPrefix . '，缺少子类型错误', 45000);
        }
        if (empty($curRelationCode))
        {
            return self::Fail($errorPrefix . '，缺少关联业务单号', 45000);
        }
        if (empty($curRelationId))
        {
            return self::Fail($errorPrefix . '，缺少关联业务ID', 45000);
        }
        if (empty($curRelationDetailId))
        {
            return self::Fail($errorPrefix . '，缺少关联业务详情ID', 45000);
        }
        if (!empty($curProduceDate) && !checkDateIsValid($curProduceDate))
        {
            return self::Fail($errorPrefix . '，生产日期格式错误', 45000);
        }
        if (!empty($curExpiredDate) && !checkDateIsValid($curExpiredDate))
        {
            return self::Fail($errorPrefix . '，过期日期格式错误', 45000);
        }
        if (StockReduceTypeEnum::typeIsMustRemark($curReduceType) && empty($curRemark))
        {
            return self::Fail($errorPrefix . '，出库原因必填', 45000);
        }

        $returnStockItemInfo = [
            'itemId'           => $curItemId,
            'itemBarcode'      => $curItemBarcode,
            'reduceType'       => $curReduceType,
            'reduceSubType'    => $curReduceSubType,
            'relationCode'     => $curRelationCode,
            'relationId'       => $curRelationId,
            'relationDetailId' => $curRelationDetailId,
            'packQuantity'     => $curPackQuantity,
            'bulkQuantity'     => $curBulkQuantity,
            'shelfCode'        => $curShelfCode,
            'produceDate'      => $curProduceDate,
            'expiredDate'      => $curExpiredDate,
            'remark'           => $curRemark,
        ];

        return self::Success($returnStockItemInfo);
    }

    /**
     * 格式化出库记录
     *
     * @param array $reduceStockRecordList
     *
     * @return LogicResult
     */
    private static function FormatReduceStockRecordStructure(array $reduceStockRecordList): LogicResult
    {
        if (empty($reduceStockRecordList))
        {
            return self::Success();
        }

        $returnStockRecordList = [];
        foreach ($reduceStockRecordList as $curReduceRecord)
        {
            $returnStockRecordList[] = [
                'type'        => [
                    'id'   => $curReduceRecord['type'],
                    'name' => StockReduceTypeEnum::getDescription($curReduceRecord['type']),
                ],
                'itemUid'     => $curReduceRecord['item_uid'],
                'itemBarcode' => $curReduceRecord['item_barcode'],
                'itemName'    => ItemHelper::ItemDisplayName($curReduceRecord),
                'shelfLife'   => $curReduceRecord['shelf_life_day'] . '天',
                'produceDate' => $curReduceRecord['produce_date'],
                'expiredDate' => $curReduceRecord['expired_date'],
                'shelfCode'   => $curReduceRecord['shelf_code'],
                'quantity'    => StockQuantityConversionHelper::getStockQuantityStructure($curReduceRecord),
                'createUser'  => [
                    'uid'  => $curReduceRecord['user_uid'],
                    'name' => $curReduceRecord['user_name'],
                ],
                'remark'      => $curReduceRecord['remark'],
                'createTime'  => formatDisplayDateTime($curReduceRecord['created_at']),
            ];
        }

        return self::Success($returnStockRecordList);
    }
}
