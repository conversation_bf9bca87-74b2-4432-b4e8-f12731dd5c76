<?php

namespace App\Logics\V1;

use DB;
use Arr;
use Log;
use Throwable;
use App\Support\Stock\StockInventoryHelper;
use App\Support\Stock\StockQuantityConversionHelper;
use App\Logics\Logic;
use App\Logics\LogicResult;
use App\Enums\StockAddTypeEnum;
use App\Enums\StockReduceTypeEnum;
use App\Enums\BusinessCodePrefixEnum;
use App\Enums\StockInventorySourceEnum;
use App\Models\StockInventoryRecordDetailModel;
use App\Models\StockInventoryRecordDetailOperationModel;
use App\Models\StockInventoryRecordModel;

class StockInventoryLogic extends Logic
{
    /**
     * 获取有效盘点单记录
     *
     * @param int   $inventoryRecordId
     * @param array $publicParams
     * @param bool  $isInProgress
     *
     * @return LogicResult
     */
    private static function getValidInventoryRecord(int $inventoryRecordId, array $publicParams, bool $isInProgress = true): LogicResult
    {
        if (empty($inventoryRecordId))
        {
            return self::Fail('获取有效盘点记录，盘点单ID错误', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('获取有效盘点记录，缺少公共必选参数', 400);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        if (empty($hospitalId))
        {
            return self::Fail('获取有效盘点记录，缺少医院公共必选参数', 400);
        }

        $getWhere = [['id', '=', $inventoryRecordId], ['hospital_id', '=', $hospitalId]];
        if (!empty($isInProgress))
        {
            $getWhere[] = ['start_time', '<=', getNowDateTime()];
            $getWhere[] = ['end_time', '<', getNowDateTime()];
            $getWhere[] = ['is_before_end', '=', 0];
        }
        $getStockInventoryRes = StockInventoryRecordModel::getData(where: $getWhere);
        $getStockInventoryRes = !empty($getStockInventoryRes) ? current($getStockInventoryRes) : [];
        if (empty($getStockInventoryRes))
        {
            return self::Fail('盘点单不存在、已结束盘点', 46000);
        }

        return self::Success($getStockInventoryRes);
    }

    /**
     * 提交库存盘点
     *
     * @param array $inventoryParams
     * @param array $publicParams
     *
     * @return LogicResult
     * @throws Throwable
     */
    public static function submitStockInventory(array $inventoryParams, array $publicParams): LogicResult
    {
        if (empty($inventoryParams))
        {
            return self::Fail('库存盘点，缺少盘点数据参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('库存盘点，缺少公共必选参数', 400);
        }

        // 公共参数
        $hospitalId      = intval(Arr::get($publicParams, '_hospitalId'));
        $hospitalBrandId = intval(Arr::get($publicParams, '_hospitalBrandId'));
        $hospitalOrgId   = intval(Arr::get($publicParams, '_hospitalOrgId'));
        $userId          = intval(Arr::get($publicParams, '_userId'));
        if (empty($hospitalId) || empty($hospitalBrandId) || empty($hospitalOrgId) || empty($userId))
        {
            return self::Fail('库存盘点，缺少医院公共必选参数', 400);
        }

        // 业务参数
        $inventoryType     = intval(Arr::get($inventoryParams, 'inventoryType'));
        $inventoryRecordId = intval(Arr::get($inventoryParams, 'inventoryRecordId'));
        $itemId            = intval(Arr::get($inventoryParams, 'itemId'));
        $itemBarcode       = trimWhitespace(Arr::get($inventoryParams, 'itemBarcode'));
        $stockItemShelf    = Arr::get($inventoryParams, 'stock');
        if (empty($itemId) || empty($itemBarcode) || empty($stockItemShelf))
        {
            return self::Fail('库存盘点，缺少盘点商品库存信息', 400);
        }

        // 如果非单品临时盘点，则验证盘点单信息
        if (!StockInventorySourceEnum::getIsSingleItemTemporary($inventoryType))
        {
            if (empty($inventoryRecordId))
            {
                return self::Fail('库存盘点，盘点单ID错误', 400);
            }

            // 获取盘点单信息
            $getInventoryRes = self::getValidInventoryRecord($inventoryRecordId, $publicParams);
            if ($getInventoryRes->isFail())
            {
                return $getInventoryRes;
            }

            // 获取盘点商品信息
            $getInventoryDetailRes = StockInventoryRecordDetailModel::getInventoryRecordDetail($hospitalId, $inventoryRecordId, [$itemId]);
            $getInventoryDetailRes = !empty($getInventoryDetailRes[$itemId]) ? $getInventoryDetailRes[$itemId] : [];
            if (empty($getInventoryDetailRes))
            {
                return self::Fail('此商品不存在盘点单内，不可盘点', 46003);
            }

            // 盘点单ID、盘点单详情ID
            $inventoryRecordId       = $getInventoryRes->getData('id');
            $inventoryRecordCode     = $getInventoryRes->getData('inventory_code');
            $inventoryRecordDetailId = $getInventoryDetailRes['id'];
        }

        // 获取商品信息
        $getItemInfoRes = ItemLogic::GetItemFullInfo(itemIds: [$itemId], publicParams: $publicParams, withItemStock: true);
        if ($getItemInfoRes->isFail())
        {
            return $getItemInfoRes;
        }

        $getItemInfoRes = $getItemInfoRes->getData(0);
        if (empty($getItemInfoRes))
        {
            return self::Fail('获取盘点商品信息不存在', 33000);
        }

        // 验证盘点库存数据
        $getValidInventoryParamsRes = StockInventoryHelper::GetValidInventoryParams($getItemInfoRes, $stockItemShelf);
        if ($getValidInventoryParamsRes->isFail())
        {
            return $getValidInventoryParamsRes;
        }

        // 商品整散比
        $itemBulkRatio = $getItemInfoRes['bulk_ratio'];

        // 盘点商品、商品整散库存总数
        $shelfCodes                 = [];
        $inventoryPackTotalQuantity = 0;
        $inventoryBulkTotalQuantity = 0;

        // 盘点商品需要增、减、操作日志信息
        $addStockInfo             = [];
        $reduceStockInfo          = [];
        $stockInventoryOperateLog = [];

        foreach ($stockItemShelf as $curItemShelf)
        {
            $curItemBarcode = $curItemShelf['itemBarcode'];
            $curIsNew       = $curItemShelf['isAdd'];
            $curExpireDate  = $curItemShelf['expiredDate'];
            $curShelfCode   = $curItemShelf['shelfCode'];
            $curPackStock   = $curItemShelf['packStock'] ?: 0;
            $curBulkStock   = $curItemShelf['bulkStock'] ?: 0;
            if (empty($curShelfCode))
            {
                continue;
            }

            // 货位
            if (!in_array($curShelfCode, $shelfCodes))
            {
                $shelfCodes[] = $curShelfCode;
            }

            // 盘点商品整散库存总数
            $inventoryPackTotalQuantity = numberAdd([$inventoryPackTotalQuantity, $curPackStock]);
            $inventoryBulkTotalQuantity = numberAdd([$inventoryBulkTotalQuantity, $curBulkStock]);

            // 盘点日志
            $stockInventoryOperateLog[] = [
                'inventory_record_id'        => 0,
                'inventory_record_detail_id' => 0,
                'org_id'                     => $hospitalOrgId,
                'brand_id'                   => $hospitalBrandId,
                'hospital_id'                => $hospitalId,
                'item_id'                    => $itemId,
                'item_barcode'               => $curItemBarcode,
                'shelf_code'                 => $curShelfCode,
                'pack_quantity'              => $curItemShelf['packStock'],
                'bulk_quantity'              => $curItemShelf['bulkStock'],
                'produce_date'               => $curItemShelf['produceDate'] ?? null,
                'expired_date'               => $curItemShelf['expiredDate'] ?? null,
            ];

            // 获取当前货位、效期下库存
            $curItemStockRes = StockItemShelfLogic::GetEffectiveQuantity([$itemId], $publicParams, true, ['expired_date' => $curExpireDate, 'shelf_code' => $curShelfCode]);
            if ($curItemStockRes->isFail())
            {
                return $curItemStockRes;
            }

            // 如果当前货位+效期无库存，可以新增盘点行。反之如果有库存，不可重复新增盘点行
            $curItemStockRes = $curItemStockRes->getData($itemId);
            if (empty($curItemStockRes))
            {
                // 如果无库存，并且不是新增盘点行，则提示
                if (!$curIsNew)
                {
                    return self::Fail("货位:【{$curShelfCode}】效期:【{$curExpireDate}】，无有效库存，不需要盘点", 46004);
                }

                // 实际增加的增、散数量（优先整）
                $curAddTotalBulkQuantity = StockQuantityConversionHelper::convertToTotalBulkQuantity($curPackStock, $curBulkStock, $itemBulkRatio);
                $curAddInfo              = StockQuantityConversionHelper::convertToPackAndBulkQuantity($curAddTotalBulkQuantity, $itemBulkRatio);
                if ($curAddInfo['packQuantity'] <= 0 && $curAddInfo['bulkQuantity'] <= 0)
                {
                    return self::Fail("货位:【{$curShelfCode}】效期:【{$curExpireDate}】新增盘点行，整装、散装数量不可同时为0", 46004);
                }
                $addStockInfo[] = [
                    'itemBarcode'  => $curItemBarcode,
                    'packQuantity' => $curAddInfo['packQuantity'],
                    'bulkQuantity' => $curAddInfo['bulkQuantity'],
                    'shelfCode'    => $curShelfCode,
                    'expiredDate'  => $curExpireDate,
                ];
                continue;
            }
            else
            {
                if ($curIsNew)
                {
                    return self::Fail("货位:【{$curShelfCode}】效期:【{$curExpireDate}】，已存在有效库存，不可重复盘点", 46004);
                }
            }

            // 盘点数与库存数是否一致
            $curPackQuantity           = $curItemStockRes['packQuantity'];
            $curBulkQuantity           = $curItemStockRes['bulkQuantity'];
            $curTotalQuantity          = StockQuantityConversionHelper::convertToTotalBulkQuantity($curPackQuantity, $curBulkQuantity, $itemBulkRatio);
            $curInventoryTotalQuantity = StockQuantityConversionHelper::convertToTotalBulkQuantity($curPackStock, $curBulkStock, $itemBulkRatio);
            if ($curTotalQuantity == $curInventoryTotalQuantity)
            {
                continue;
            }

            // 库存数 - 盘点数，>0:盘亏；<0:盘盈
            $diffQuantity = numberSub([$curTotalQuantity, $curInventoryTotalQuantity]);
            if ($diffQuantity > 0)
            {
                $curReduceInfo     = StockQuantityConversionHelper::convertToPackAndBulkQuantity($diffQuantity, $itemBulkRatio);
                $reduceStockInfo[] = [
                    'itemBarcode'  => $curItemBarcode,
                    'packQuantity' => $curReduceInfo['packQuantity'],
                    'bulkQuantity' => $curReduceInfo['bulkQuantity'],
                    'shelfCode'    => $curShelfCode,
                    'expiredDate'  => $curExpireDate,
                ];
            }

            // 盘盈
            if ($diffQuantity < 0)
            {
                $curAddInfo     = StockQuantityConversionHelper::convertToPackAndBulkQuantity(abs($diffQuantity), $itemBulkRatio);
                $addStockInfo[] = [
                    'itemBarcode'  => $curItemBarcode,
                    'packQuantity' => $curAddInfo['packQuantity'],
                    'bulkQuantity' => $curAddInfo['bulkQuantity'],
                    'shelfCode'    => $curShelfCode,
                    'expiredDate'  => $curExpireDate,
                ];
            }
        }

        // 如果增减都为空，说明盘点库存的每行与实际库存一致，反之获取库存最终变动值（比如：减1个整、增加1个整，实际变化为0，不需要变动库存）
        $actualAddStock    = [];
        $actualReduceStock = [];
        if (!empty($addStockInfo) || !empty($reduceStockInfo))
        {
            $getActualInventoryStockRes = StockInventoryHelper::getActualInventoryStock($addStockInfo, $reduceStockInfo, $itemBulkRatio);
            if (empty($getActualInventoryStockRes))
            {
                return self::Fail('库存盘点，获取实际库存变动值失败', 46004);
            }

            // 实际库存变动（可能为空，此处不直接返回。是需要记录盘点日志，但是实际库存不变化）
            $actualAddStock    = $getActualInventoryStockRes['addStockInfo'] ?? [];
            $actualReduceStock = $getActualInventoryStockRes['reduceStockInfo'] ?? [];
        }

        // 数据错误
        if (empty($addStockInfo) && empty($reduceStockInfo) && empty($stockInventoryOperateLog))
        {
            return self::Fail('库存盘点，无有效数据', 46004);
        }

        // 货位是否有效
        $getShelfSlotInfoRes = WarehouseLogic::GetShelfSlotInfo($shelfCodes, $publicParams);
        if ($getShelfSlotInfoRes->isFail())
        {
            return $getShelfSlotInfoRes;
        }
        if (count($shelfCodes) != count($getShelfSlotInfoRes->getData()))
        {
            return self::Fail('库存盘点，部分货位不存在', 46004);
        }

        // 获取商品加权价
        $getItemDailyPriceRes = StockItemDailyPriceLogic::GetItemNowDailyPrice([$itemId], $publicParams);
        if ($getItemDailyPriceRes->isFail())
        {
            return $getItemDailyPriceRes;
        }

        $getItemDailyPriceRes = $getItemDailyPriceRes->getData($itemId);
        if (empty($getItemDailyPriceRes))
        {
            return self::Fail('库存盘点，商品加权价不存在', 46004);
        }

        try
        {
            DB::beginTransaction();

            // 初始化盘点单
            if (StockInventorySourceEnum::getIsSingleItemTemporary($inventoryType))
            {
                $getInitRes = self::InitTemporaryInventoryRecord($inventoryParams, $getItemInfoRes['stock_info'], $publicParams);
                if ($getInitRes->isFail())
                {
                    return $getInitRes;
                }

                $inventoryRecordId       = $getInitRes->getData('inventoryRecordId');
                $inventoryRecordCode     = $getInitRes->getData('inventoryRecordCode');
                $inventoryRecordDetailId = $getInitRes->getData('inventoryRecordDetailId');
            }

            // 盘点单ID、盘点单详情ID无效
            if (empty($inventoryRecordId) || empty($inventoryRecordCode) || empty($inventoryRecordDetailId))
            {
                return self::Fail('库存盘点，盘点单ID、盘点单详情ID错误', 46005);
            }

            // 盘盈
            if (!empty($actualAddStock))
            {
                $upAddStockInfo = [];
                foreach ($actualAddStock as $curAddStock)
                {
                    $upAddStockInfo[] = [
                        'itemId'           => $itemId,
                        'itemBarcode'      => $itemBarcode,
                        'addType'          => StockAddTypeEnum::Inventory->value,
                        'addSubType'       => 0,
                        'relationCode'     => $inventoryRecordCode,
                        'relationId'       => $inventoryRecordId,
                        'relationDetailId' => $inventoryRecordDetailId,
                        'shelfCode'        => $curAddStock['shelfCode'],
                        'packQuantity'     => $curAddStock['packQuantity'],
                        'bulkQuantity'     => $curAddStock['bulkQuantity'],
                        'packPrice'        => $curAddStock['packQuantity'] > 0 ? $getItemDailyPriceRes['packPrice'] : 0,
                        'bulkPrice'        => $curAddStock['bulkQuantity'] > 0 ? $getItemDailyPriceRes['bulkPrice'] : 0,
                        'produceDate'      => null,
                        'expiredDate'      => $curAddStock['expiredDate'],
                        'remark'           => StockAddTypeEnum::getDescription(StockAddTypeEnum::Inventory->value),
                    ];
                }
                $getAddStockRes = StockItemShelfAddLogic::AddStockShelfQuantity($upAddStockInfo, $publicParams);
                if ($getAddStockRes->isFail())
                {
                    return $getAddStockRes;
                }
            }

            // 盘亏
            if (!empty($actualReduceStock))
            {
                $upReduceStockInfo = [];
                foreach ($actualReduceStock as $curReduceStock)
                {
                    $upReduceStockInfo[] = [
                        'itemId'           => $itemId,
                        'itemBarcode'      => $itemBarcode,
                        'reduceType'       => StockReduceTypeEnum::Inventory->value,
                        'reduceSubType'    => 0,
                        'relationCode'     => $inventoryRecordCode,
                        'relationId'       => $inventoryRecordId,
                        'relationDetailId' => $inventoryRecordDetailId,
                        'shelfCode'        => $curReduceStock['shelfCode'],
                        'packQuantity'     => $curReduceStock['packQuantity'],
                        'bulkQuantity'     => $curReduceStock['bulkQuantity'],
                        'packPrice'        => 0,
                        'bulkPrice'        => 0,
                        'produceDate'      => null,
                        'expiredDate'      => $curReduceStock['expiredDate'],
                        'remark'           => StockReduceTypeEnum::getDescription(StockReduceTypeEnum::Inventory->value),
                    ];
                }
                $getReduceStockRes = StockItemShelfReduceLogic::ReduceStockShelfQuantity($upReduceStockInfo, $publicParams);
                if ($getReduceStockRes->isFail())
                {
                    return $getReduceStockRes;
                }
            }

            // 更新盘点详情状态
            StockInventoryRecordDetailModel::updateOne($inventoryRecordDetailId,
                                                       ['pack_quantity' => $inventoryPackTotalQuantity, 'bulk_quantity' => $inventoryBulkTotalQuantity, 'is_operated' => 1]);

            // 盘点单完成数、临盘单总数增加
            $curInventoryTotal        = 0;
            $curInventoryFinishTotals = 0;
            if (StockInventorySourceEnum::getIsSingleItemTemporary($inventoryType))
            {
                $curInventoryTotal        = 1;
                $curInventoryFinishTotals = 1;
            }
            if (!empty($getInventoryDetailRes) && empty($getInventoryDetailRes['is_operated']))
            {
                $curInventoryFinishTotals = 1;
            }
            StockInventoryRecordModel::updateOne($inventoryRecordId, [
                'totals'        => DB::raw('totals + ' . $curInventoryTotal),
                'finish_totals' => DB::raw('finish_totals + ' . $curInventoryFinishTotals),
            ]);

            // 盘点操作日志
            if (!empty($stockInventoryOperateLog))
            {
                foreach ($stockInventoryOperateLog as &$curLog)
                {
                    $curLog['inventory_record_id']        = $inventoryRecordId;
                    $curLog['inventory_record_detail_id'] = $inventoryRecordDetailId;
                }

                StockInventoryRecordDetailOperationModel::insert($stockInventoryOperateLog);
            }

            DB::commit();

            return self::Success();

        } catch (Throwable $throwable)
        {
            DB::rollBack();

            Log::error(__CLASS__ . '::' . __METHOD__ . ' 库存盘点异常', [
                'code'    => $throwable->getCode(),
                'message' => $throwable->getMessage(),
                'file'    => $throwable->getFile(),
                'line'    => $throwable->getLine(),
                'trace'   => $throwable->getTraceAsString(),
            ]);

            return self::Fail('库存盘点异常', 46005);
        }
    }

    /**
     * 初始化单品临时盘点单、盘点单商品详情
     *
     * @param array $inventoryParams
     * @param array $itemStockInfo
     * @param array $publicParams
     *
     * @return LogicResult
     * @throws Throwable
     */
    private static function InitTemporaryInventoryRecord(array $inventoryParams, array $itemStockInfo, array $publicParams): LogicResult
    {
        // 公共参数
        $hospitalId      = intval(Arr::get($publicParams, '_hospitalId'));
        $hospitalBrandId = intval(Arr::get($publicParams, '_hospitalBrandId'));
        $hospitalOrgId   = intval(Arr::get($publicParams, '_hospitalOrgId'));
        $userId          = intval(Arr::get($publicParams, '_userId'));
        if (empty($hospitalId) || empty($hospitalBrandId) || empty($hospitalOrgId) || empty($userId))
        {
            return self::Fail('初始化单品临时盘点单，缺少医院公共必选参数', 400);
        }

        // 业务参数
        $itemId      = intval(Arr::get($inventoryParams, 'itemId'));
        $itemBarcode = trimWhitespace(Arr::get($inventoryParams, 'itemBarcode'));
        if (empty($itemId) || empty($itemBarcode))
        {
            return self::Fail('初始化单品临时盘点单，缺少盘点业务参数', 400);
        }

        try
        {
            DB::beginTransaction();

            // 获取临时盘点单，是否存在。不存在则创建
            $getTemporaryInventoryRes = StockInventoryRecordModel::on()
                                                                 ->where([
                                                                             'hospital_id' => $hospitalId,
                                                                             'source'      => StockInventorySourceEnum::TEMPORARY_FIXED->value,
                                                                             'status'      => 2
                                                                         ])
                                                                 ->first();
            if (!empty($getTemporaryInventoryRes))
            {
                $inventoryRecordId   = $getTemporaryInventoryRes['id'];
                $inventoryRecordCode = $getTemporaryInventoryRes['inventory_code'];
            }
            else
            {
                // 不存在临时盘点单，则创建临时盘点单
                $inventoryRecordCode       = generateBusinessCodeNumber(BusinessCodePrefixEnum::PDDH);
                $insertInventoryRecordData = [
                    'uid'              => generateUUID(),
                    'inventory_code'   => $inventoryRecordCode,
                    'title'            => '单品临盘单',
                    'org_id'           => $hospitalOrgId,
                    'brand_id'         => $hospitalBrandId,
                    'hospital_id'      => $hospitalId,
                    'source'           => StockInventorySourceEnum::TEMPORARY_FIXED->value,
                    'status'           => 2,
                    'inventory_status' => 1,
                    'created_by'       => $userId,
                ];

                $inventoryRecordId = StockInventoryRecordModel::on()
                                                              ->insertGetId($insertInventoryRecordData);
            }

            // 生成盘点详情
            $inventoryRecordDetailId = StockInventoryRecordDetailModel::on()
                                                                      ->insertGetId([
                                                                                        'uid'                 => generateUUID(),
                                                                                        'inventory_record_id' => $inventoryRecordId,
                                                                                        'org_id'              => $hospitalOrgId,
                                                                                        'brand_id'            => $hospitalBrandId,
                                                                                        'hospital_id'         => $hospitalId,
                                                                                        'item_id'             => $itemId,
                                                                                        'item_barcode'        => $itemBarcode,
                                                                                        'source_type'         => 5,
                                                                                        'pack_total_quantity' => $itemStockInfo['packQuantity'] ?? 0,
                                                                                        'bulk_total_quantity' => $itemStockInfo['bulkQuantity'] ?? 0,
                                                                                        'created_by'          => $userId,
                                                                                    ]);

            DB::commit();

            return self::Success(['inventoryRecordId' => $inventoryRecordId, 'inventoryRecordCode' => $inventoryRecordCode, 'inventoryRecordDetailId' => $inventoryRecordDetailId]);
        } catch (Throwable $throwable)
        {
            DB::rollBack();
            Log::error(__CLASS__ . '::' . __METHOD__ . ' 初始化单品临时盘点单失败', [
                'code'    => $throwable->getCode(),
                'message' => $throwable->getMessage(),
                'file'    => $throwable->getFile(),
                'line'    => $throwable->getLine(),
                'trace'   => $throwable->getTraceAsString(),
            ]);

            return self::Fail('初始化单品临时盘点单失败', 46005);
        }
    }
}
