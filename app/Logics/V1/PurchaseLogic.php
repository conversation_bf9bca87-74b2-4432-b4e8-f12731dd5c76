<?php

namespace App\Logics\V1;

use DB;
use Log;
use Arr;
use Throwable;
use App\Facades\SearchFacade;
use App\Support\Item\ItemHelper;
use App\Support\Purchase\PurchaseHelper;
use App\Enums\PageEnum;
use App\Enums\PurchaseTypeEnum;
use App\Enums\BusinessCodePrefixEnum;
use App\Enums\PurchaseOrderStatusEnum;
use App\Enums\PurchaseOrderInboundStatusEnum;
use App\Enums\PurchaseOrderReceivedStatusEnum;
use App\Logics\Logic;
use App\Logics\LogicResult;
use App\Models\UsersModel;
use App\Models\HospitalModel;
use App\Models\ItemBrandModel;
use App\Models\PurchaseOrderModel;
use App\Models\PurchaseReceivedModel;
use App\Models\PurchaseSupplierModel;
use App\Models\PurchaseOrderItemModel;
use App\Models\PurchaseOrderOperationLogModel;
use App\Models\ItemSelfPurchaseWhitelistModel;

class PurchaseLogic extends Logic
{
    /*
     * 搜索商品数量限制
     */
    const int SEARCH_ITEM_LIMIT = 50;

    /**
     * 获取创建采购单用户
     *
     * @param array $publicParams
     *
     * @return LogicResult
     */
    public static function GetCreatePurchaseUserOptions(array $publicParams): LogicResult
    {
        if (empty($publicParams))
        {
            return self::Fail('获取创建采购单用户，缺少必选参数', 400);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        if (empty($hospitalId))
        {
            return self::Fail('获取创建采购单用户，缺少医院ID必选参数', 400);
        }

        $getPurchaseUserRes = PurchaseOrderModel::getData(fields: ['created_by'], where: ['hospital_id' => $hospitalId], group: 'created_by');
        if (empty($getPurchaseUserRes))
        {
            return self::Success();
        }

        $userIds    = array_unique(array_column($getPurchaseUserRes, 'created_by'));
        $getUserRes = UsersModel::getUserByIds($userIds);

        $returnUserOptions = [];
        foreach ($getUserRes as $curUser)
        {
            $returnUserOptions[] = [
                'uid'  => $curUser['uid'],
                'name' => $curUser['name'],
            ];
        }

        return self::Success($returnUserOptions);
    }

    /**
     * 获取创建采购单供应商
     *
     * @param array $publicParams
     *
     * @return LogicResult
     */
    public static function GetCreatePurchaseSupplierOptions(array $publicParams): LogicResult
    {
        if (empty($publicParams))
        {
            return self::Fail('获取创建采购单供应商，缺少必选参数', 400);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        if (empty($hospitalId))
        {
            return self::Fail('获取创建采购单供应商，缺少医院ID必选参数', 400);
        }

        $getPurchaseSupplierRes = PurchaseOrderModel::getData(fields: ['supplier_id'],
                                                              where : [['hospital_id', '=', $hospitalId], ['supplier_id', '>', 0]],
                                                              group : 'supplier_id');
        if (empty($getPurchaseSupplierRes))
        {
            return self::Success();
        }

        $supplierIds    = array_unique(array_column($getPurchaseSupplierRes, 'supplier_id'));
        $getSupplierRes = PurchaseSupplierModel::getManyByIds($supplierIds);

        $returnSupplierOptions = [];
        foreach ($getSupplierRes as $curSupplier)
        {
            $returnSupplierOptions[] = [
                'uid'  => $curSupplier['uid'],
                'name' => $curSupplier['name'],
            ];
        }

        return self::Success($returnSupplierOptions);
    }

    /**
     * 获取创建采购单调拨医院
     *
     * @param array $publicParams
     *
     * @return LogicResult
     */
    public static function GetPurchaseAllotHospitalOptions(array $publicParams): LogicResult
    {
        if (empty($publicParams))
        {
            return self::Fail('获取创建采购单调拨医院，缺少必选参数', 400);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        if (empty($hospitalId))
        {
            return self::Fail('获取创建采购单调拨医院，缺少医院ID必选参数', 400);
        }

        $getPurchaseAllotHospitalRes = PurchaseOrderModel::getData(fields: ['allot_hospital_id'],
                                                                   where : [['hospital_id', '=', $hospitalId], ['allot_hospital_id', '>', 0]],
                                                                   group : 'allot_hospital_id');
        if (empty($getPurchaseAllotHospitalRes))
        {
            return self::Success();
        }

        $allotHospitalIds = array_unique(array_column($getPurchaseAllotHospitalRes, 'allot_hospital_id'));
        $getHospitalRes   = HospitalModel::getManyByIds($allotHospitalIds);

        $returnAllotHospitalOptions = [];
        foreach ($getHospitalRes as $curHospital)
        {
            $returnAllotHospitalOptions[] = [
                'uid'  => $curHospital['uid'],
                'name' => $curHospital['alias_name'],
            ];
        }

        return self::Success($returnAllotHospitalOptions);
    }

    /**
     * 获取采购供应商
     *
     * @param int   $purchaseType
     * @param array $publicParams
     *
     * @return LogicResult
     */
    public static function GetPurchaseSupplier(int $purchaseType, array $publicParams): LogicResult
    {
        if (empty($purchaseType) || !in_array($purchaseType, PurchaseTypeEnum::values()))
        {
            return self::Fail('获取采购供应商，缺少采购类型必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('获取采购供应商，缺少必选参数', 400);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        if (empty($hospitalId))
        {
            return self::Fail('获取采购供应商，缺少医院ID必选参数', 400);
        }

        // 获取医院信息
        $getHospitalRes = HospitalLogic::GetHospitalBaseInfo(hospitalId: $hospitalId, withId: true);
        if ($getHospitalRes->isFail())
        {
            return $getHospitalRes;
        }

        // 医院所属城市ID
        $hospitalCityId = $getHospitalRes->getData('addressInfo.cityId', 0);

        // 获取供应商
        $getSupplierRes = PurchaseSupplierModel::getSupplierByPurchaseTypeAndCityId($purchaseType, $hospitalCityId);
        if (empty($getSupplierRes))
        {
            return self::Success(['supplierOptions' => []]);
        }

        $returnSupplierInfo = [];
        foreach ($getSupplierRes as $curInfo)
        {
            $returnSupplierInfo[] = [
                'uid'  => $curInfo['uid'],
                'name' => $curInfo['name'],
            ];
        }

        return self::Success(['supplierOptions' => $returnSupplierInfo]);
    }

    /**
     * 获取搜索商品
     *
     * @param array $searchParams
     * @param array $publicParams
     *
     * @return LogicResult
     */
    public static function GetSearchItems(array $searchParams, array $publicParams): LogicResult
    {
        if (empty($searchParams))
        {
            return self::Fail('获取搜索商品，缺少必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('获取搜索商品，缺少公共必选参数', 400);
        }

        // 公共参数
        $orgId      = intval(Arr::get($publicParams, '_hospitalOrgId'));
        $brandId    = intval(Arr::get($publicParams, '_hospitalBrandId'));
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        if (empty($orgId) || empty($brandId) || empty($hospitalId))
        {
            return self::Fail('获取搜索商品，缺少公共必选参数', 400);
        }

        // 业务参数
        $purchaseType    = intval(Arr::get($searchParams, 'purchaseType', 0));
        $supplierUid     = trim(Arr::get($searchParams, 'supplierUid', ''));
        $fromHospitalUid = trim(Arr::get($searchParams, 'fromHospitalUid', ''));
        $isPurchased     = intval(Arr::get($searchParams, 'isPurchased', 0));
        $brandUid        = trim(Arr::get($searchParams, 'brandUid', ''));
        $keywords        = trim(Arr::get($searchParams, 'keywords', ''));
        if (empty($purchaseType) || !in_array($purchaseType, PurchaseTypeEnum::values()))
        {
            return self::Fail('采购类型错误', 42000);
        }
        if (PurchaseTypeEnum::getPurchaseIsSelfPurchase($purchaseType) && empty($supplierUid))
        {
            return self::Fail('采购供应商选择错误', 42000);
        }
        if (PurchaseTypeEnum::getPurchaseIsTransfer($purchaseType) && empty($fromHospitalUid))
        {
            return self::Fail('调拨源门店选择错误', 42000);
        }
        if (PurchaseTypeEnum::getPurchaseIsGroupPurchase($purchaseType) && (!empty($supplierUid) || !empty($fromHospitalUid)))
        {
            return self::Fail('集采不可选择供应商或调拨源门店', 42000);
        }
        if (empty($keywords))
        {
            return self::Fail('搜索关键词错误', 42001);
        }

        // 获取品牌ID
        $withBrandId = 0;
        if (!empty($brandUid))
        {
            $getBrandRes = ItemBrandModel::getOneByUid($brandUid);
            if (!empty($getBrandRes))
            {
                $withBrandId = $getBrandRes['id'];
            }
        }

        // 指定包含采购记录
        $withPurchaseHospitalId = 0;
        if (!empty($isPurchased))
        {
            $withPurchaseHospitalId = $hospitalId;
        }

        // 自采情况下，先获取符合条件的自采白名单商品
        $selfPurchaseItemIds = [];
        if (PurchaseTypeEnum::getPurchaseIsSelfPurchase($purchaseType))
        {
            $getSupplierRes = PurchaseSupplierLogic::GetValidPurchaseSupplier(supplierUid: $supplierUid);
            if ($getSupplierRes->isFail())
            {
                return $getSupplierRes;
            }
            if ($getSupplierRes->getData('purchase_type', 0) != PurchaseTypeEnum::SelfPurchase->value)
            {
                return self::Fail('选择的采购供应商非自采类型', 42000);
            }

            $searchWhere             = [
                'orgId'       => $orgId,
                'hospitalId'  => $hospitalId,
                'supplierId'  => $getSupplierRes->getData('id', 0),
                'keywords'    => $keywords,
                'brandId'     => $withBrandId,
                'isPurchased' => $withPurchaseHospitalId,
            ];
            $getSelfPurchaseItemsRes = ItemSelfPurchaseWhitelistModel::getSelfPurchaseItemList($searchWhere);
            if (empty($getSelfPurchaseItemsRes))
            {
                return self::Success(['data' => []]);
            }

            $selfPurchaseItemIds = array_column($getSelfPurchaseItemsRes, 'id');
        }

        // 搜索商品
        $getSearchItemsRes = SearchFacade::searchPurchaseItems($keywords,
                                                               $withPurchaseHospitalId,
                                                               $selfPurchaseItemIds,
                                                               $withBrandId,
                                                               self::SEARCH_ITEM_LIMIT,
                                                               $orgId);


        // 搜索商品为空
        if (empty($getSearchItemsRes))
        {
            return self::Success(['data' => []]);
        }

        // 格式化商品信息
        $getFormatItemRes = SearchItemLogic::FormatItemInfoStructure($getSearchItemsRes,
                                                                     $publicParams,
                                                                     false,
                                                                     true);
        if ($getFormatItemRes->isFail())
        {
            return $getFormatItemRes;
        }

        return self::Success(['data' => $getFormatItemRes->getData()]);
    }

    /**
     * 获取采购单列表
     *
     * @param array $searchParams
     * @param array $publicParams
     *
     * @return LogicResult
     */
    public static function GetPurchaseLists(array $searchParams, array $publicParams): LogicResult
    {
        if (empty($publicParams))
        {
            return self::Fail('获取采购单列表，缺少公共必选参数', 400);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        if (empty($hospitalId))
        {
            return self::Fail('获取采购单列表，缺少公共必选参数', 400);
        }

        // 业务参数
        $iPage     = intval(Arr::get($searchParams, 'page', 0)) ?? PageEnum::DefaultPageIndex->value;
        $iPageSize = intval(Arr::get($searchParams, 'count', 0)) ?? PageEnum::DefaultPageSize->value;

        // 获取采购单列表
        $searchParams['hospitalId'] = $hospitalId;
        $getPurchaseListRes         = PurchaseOrderModel::getPurchaseOrderListData($searchParams, $iPage, $iPageSize);
        if (empty($getPurchaseListRes))
        {
            return self::Success(['total' => 0, 'data' => []]);
        }

        $totalCount      = $getPurchaseListRes['total'] ?? 0;
        $purchaseListRes = $getPurchaseListRes['data'] ?? [];
        if (empty($totalCount) || empty($purchaseListRes))
        {
            return self::Success(['total' => 0, 'data' => []]);
        }

        $getFormatPurchaseListRes = self::FormatPurchaseOrderStructure($purchaseListRes);
        if ($getFormatPurchaseListRes->isFail())
        {
            return $getFormatPurchaseListRes;
        }

        return self::Success(['total' => $totalCount, 'data' => $getFormatPurchaseListRes->getData()]);
    }

    /**
     * 获取有效采购单
     *
     * @param int   $purchaseOrderId
     * @param array $publicParams
     *
     * @return LogicResult
     */
    public static function GetValidPurchaseOrder(int $purchaseOrderId, array $publicParams): LogicResult
    {
        if (empty($purchaseOrderId))
        {
            return self::Fail('获取有效采购单，缺少采购单ID必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('获取有效采购单，缺少公共必选参数', 400);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        if (empty($hospitalId))
        {
            return self::Fail('获取有效采购单，缺少医院ID必选参数', 400);
        }

        // 获取采购单信息
        $getPurchaseOrderRes = PurchaseOrderModel::getData(where: ['id' => $purchaseOrderId, 'hospital_id' => $hospitalId,]);
        $getPurchaseOrderRes = $getPurchaseOrderRes ? current($getPurchaseOrderRes) : [];
        if (empty($getPurchaseOrderRes))
        {
            return self::Fail('采购单不存在或已失效', 42010);
        }

        return self::Success($getPurchaseOrderRes);
    }

    /**
     * 添加采购单
     *
     * @param       $addPurchaseParams
     * @param array $publicParams
     *
     * @return LogicResult
     * @throws Throwable
     */
    public static function AddPurchaseOrder($addPurchaseParams, array $publicParams): LogicResult
    {
        if (empty($addPurchaseParams))
        {
            return self::Fail('添加采购单，缺少必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('添加采购单，缺少公共必选参数', 400);
        }

        // 公共参数
        $orgId      = intval(Arr::get($publicParams, '_hospitalOrgId'));
        $brandId    = intval(Arr::get($publicParams, '_hospitalBrandId'));
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        $userId     = intval(Arr::get($publicParams, '_userId'));
        if (empty($orgId) || empty($brandId) || empty($hospitalId) || empty($userId))
        {
            return self::Fail('添加采购单，缺少医院相关必选参数', 400);
        }

        // 验证采购基本参数是否正确
        $getValidPurchaseParamsRes = PurchaseHelper::GetValidPurchaseParams($addPurchaseParams, $publicParams);
        if ($getValidPurchaseParamsRes->isFail())
        {
            return $getValidPurchaseParamsRes;
        }

        // 获取验证后的采购参数
        $getValidPurchaseParamsRes = $getValidPurchaseParamsRes->getData();
        $purchaseType              = $getValidPurchaseParamsRes['purchaseType'];
        $supplierId                = $getValidPurchaseParamsRes['supplierId'];
        $allocationHospitalId      = $getValidPurchaseParamsRes['allocationHospitalId'];
        $purchaseItems             = $getValidPurchaseParamsRes['items'];
        $purchaseRemark            = $getValidPurchaseParamsRes['remark'];
        $purchaseTotalPrice        = $getValidPurchaseParamsRes['totalPrice'];
        $submitType                = $getValidPurchaseParamsRes['submitType'];
        if (empty($purchaseType) || empty($purchaseItems) || empty($purchaseRemark))
        {
            return self::Fail('添加采购单，验证采购单信息异常', 42007);
        }
        if (empty($supplierId) && empty($allocationHospitalId))
        {
            return self::Fail('添加采购单，缺少供应商或调拨源门店ID', 42000);
        }

        try
        {
            DB::beginTransaction();

            // 创建采购单
            $purchaseCode       = generateBusinessCodeNumber(BusinessCodePrefixEnum::CGDH);
            $insertPurchaseData = [
                'purchase_code'     => $purchaseCode,
                'purchase_type'     => $purchaseType,
                'supplier_id'       => $supplierId,
                'allot_hospital_id' => $allocationHospitalId,
                'org_id'            => $orgId,
                'brand_id'          => $brandId,
                'hospital_id'       => $hospitalId,
                'purchase_price'    => $purchaseTotalPrice,
                'status'            => $submitType,
                'remark'            => $purchaseRemark,
                'created_by'        => $userId,
            ];

            // 如果创建既提交审核记录提交审核人、提交审核时间
            if ($submitType == PurchaseOrderStatusEnum::Pending->value)
            {
                $insertPurchaseData['submitted_by'] = $userId;
                $insertPurchaseData['submitted_at'] = getCurrentTimeWithMilliseconds();
            }

            $purchaseOrderId = PurchaseOrderModel::insertOne($insertPurchaseData);

            // 添加采购单商品
            $getInsertPurchaseItemRes = self::InsertPurchaseItem(['purchaseOrderId' => $purchaseOrderId, 'purchaseCode' => $purchaseCode],
                                                                 $purchaseItems,
                                                                 $publicParams);
            if ($getInsertPurchaseItemRes->isFail())
            {
                DB::rollBack();

                return $getInsertPurchaseItemRes;
            }

            DB::commit();

            return self::Success(['purchaseCode' => $purchaseCode]);
        } catch (Throwable $throwable)
        {
            DB::rollBack();

            Log::error(__CLASS__ . '::' . __METHOD__ . ' 添加采购单异常', [
                'code'    => $throwable->getCode(),
                'message' => $throwable->getMessage(),
                'file'    => $throwable->getFile(),
                'line'    => $throwable->getLine(),
                'trace'   => $throwable->getTraceAsString(),
            ]);

            return self::Fail('添加采购单异常', 42009);
        }
    }

    /**
     * 编辑采购单
     *
     * @param int   $purchaseOrderId
     * @param array $editPurchaseParams
     * @param array $publicParams
     *
     * @return LogicResult
     * @throws Throwable
     */
    public static function EditPurchaseOrder(int $purchaseOrderId, array $editPurchaseParams, array $publicParams): LogicResult
    {
        if (empty($purchaseOrderId))
        {
            return self::Fail('编辑采购单，缺少采购单ID必选参数', 400);
        }
        if (empty($editPurchaseParams))
        {
            return self::Fail('编辑采购单，缺少采购单必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('编辑采购单，缺少公共必选参数', 400);
        }

        // 公共参数
        $userId = intval(Arr::get($publicParams, '_userId'));
        if (empty($userId))
        {
            return self::Fail('编辑采购单，缺少医院相关必选参数', 400);
        }

        // 获取采购单信息
        $getPurchaseOrderRes = self::GetValidPurchaseOrder($purchaseOrderId, $publicParams);
        if ($getPurchaseOrderRes->isFail())
        {
            return $getPurchaseOrderRes;
        }

        // 获取采购单商品明细
        $getPurchaseOrderItemRes = PurchaseOrderItemModel::getData(where: ['purchase_order_id' => $purchaseOrderId, 'status' => 1]);
        if (empty($getPurchaseOrderItemRes))
        {
            return self::Fail('采购单商品不存在', 42011);
        }

        // 获取采购单商品信息，用于如果修改的话记录名称等
        $oldItemIds                = array_column($getPurchaseOrderItemRes, 'item_id');
        $getOldPurchaseItemInfoRes = ItemLogic::GetItemFullInfo(itemIds: $oldItemIds, itemStatus: [], publicParams: $publicParams);
        if ($getOldPurchaseItemInfoRes->isFail())
        {
            return $getOldPurchaseItemInfoRes;
        }

        $getOldPurchaseItemInfoRes = array_column($getOldPurchaseItemInfoRes->getData(), null, 'id');

        // 验证采购单是否可以编辑
        $getPurchaseOrderRes = $getPurchaseOrderRes->getData();
        $getCheckEditAbleRes = self::CheckEditOrDeleteAblePurchaseOrder($getPurchaseOrderRes, $publicParams);
        if ($getCheckEditAbleRes->isFail())
        {
            return $getCheckEditAbleRes;
        }

        // 验证采购基本参数是否正确
        $getValidPurchaseParamsRes = PurchaseHelper::GetValidPurchaseParams($editPurchaseParams, $publicParams, $getPurchaseOrderRes, $getPurchaseOrderItemRes);
        if ($getValidPurchaseParamsRes->isFail())
        {
            return $getValidPurchaseParamsRes;
        }

        // 获取验证后的采购参数
        $getValidPurchaseParamsRes = $getValidPurchaseParamsRes->getData();
        $purchaseType              = $getValidPurchaseParamsRes['purchaseType'];
        $supplierId                = $getValidPurchaseParamsRes['supplierId'];
        $allocationHospitalId      = $getValidPurchaseParamsRes['allocationHospitalId'];
        $purchaseItems             = $getValidPurchaseParamsRes['items'];
        $purchaseRemark            = $getValidPurchaseParamsRes['remark'];
        $purchaseTotalPrice        = $getValidPurchaseParamsRes['totalPrice'];
        $submitType                = $getValidPurchaseParamsRes['submitType'];
        if (empty($purchaseType) || empty($purchaseItems) || empty($purchaseRemark))
        {
            return self::Fail('添加采购单，验证采购单信息异常', 42007);
        }
        if (empty($supplierId) && empty($allocationHospitalId))
        {
            return self::Fail('添加采购单，缺少供应商或调拨源门店ID', 42000);
        }

        // 采购类型不可修改
        if ($purchaseType != $getPurchaseOrderRes['purchase_type'])
        {
            return self::Fail('采购类型不可修改', 42014);
        }

        // 采购单供应商、调拨门店不可修改
        if ($supplierId != $getPurchaseOrderRes['supplier_id'] || $allocationHospitalId != $getPurchaseOrderRes['allot_hospital_id'])
        {
            return self::Fail('采购供应商或调拨源门店不可修改', 42014);
        }

        // 采购单相关操作的日志说明
        $operationDesc = [];

        // 更新采购单
        $updatePurchaseOrderData = [];
        if ($purchaseRemark != $getPurchaseOrderRes['remark'])
        {
            $updatePurchaseOrderData['remark'] = $purchaseRemark;
            $operationDesc[]                   = "采购单备注由：{$getPurchaseOrderRes['remark']} 修改为：$purchaseRemark";
        }
        if (bccomp($purchaseTotalPrice, $getPurchaseOrderRes['purchase_price'], 4) !== 0)
        {
            $updatePurchaseOrderData['purchase_price'] = $purchaseTotalPrice;
            $operationDesc[]                           = "采购单金额由：{$getPurchaseOrderRes['purchase_price']} 修改为：$purchaseTotalPrice";
        }
        if ($submitType != $getPurchaseOrderRes['status'])
        {
            $updatePurchaseOrderData['status'] = $submitType;
            $operationDesc[]                   = "采购单状态由：{$getPurchaseOrderRes['status']} 修改为：$submitType";

            // 如果直接提交审核，修改提审状态
            if ($submitType == PurchaseOrderStatusEnum::Pending->value)
            {
                $updatePurchaseOrderData['submitted_by'] = $userId;
                $updatePurchaseOrderData['submitted_at'] = getCurrentTimeWithMilliseconds();

                $operationDesc[] = "采购单提审核人由：{$getPurchaseOrderRes['submitted_by']} 修改为：$userId";
            }
            else
            {
                $updatePurchaseOrderData['submitted_by'] = 0;
                $updatePurchaseOrderData['submitted_at'] = null;
            }
        }

        // 更新采购单商品
        $insertPurchaseItemData = [];
        $updatePurchaseItemData = [];
        $deletePurchaseItemData = [];
        foreach ($getPurchaseOrderItemRes as $oldKey => $oldPurchaseItem)
        {
            foreach ($purchaseItems as $newKey => $newPurchaseItem)
            {
                // 采购单商品在表中的UID
                $curPurchaseItemUid = $newPurchaseItem['uid'] ?? '';

                // 如果uid为空，则代表是新增的记录
                if (empty($curPurchaseItemUid))
                {
                    $insertPurchaseItemData[] = $newPurchaseItem;
                    unset($purchaseItems[$newKey]);
                    continue;
                }

                // 如果uid不一致，则代表不是同一个商品
                if ($oldPurchaseItem['uid'] != $curPurchaseItemUid)
                {
                    continue;
                }

                // 采购单商品的主信息
                $curPurchaseItemInfo = $newPurchaseItem['itemInfo'] ?? [];
                if (empty($curPurchaseItemInfo))
                {
                    return self::Fail('采购单商品信息不存在', 42011);
                }

                // 如果是同一个商品，商品ID是否同一个，防止错误
                if ($oldPurchaseItem['item_id'] != $curPurchaseItemInfo['id'])
                {
                    return self::Fail('提交采购单商品与存在的商品ID不一致', 42011);
                }

                // 对比是否存在修改采购的信息：散装数量、散装价格、整装数量、整装价格
                $curDiffInfo = PurchaseHelper::ComparePurchaseItemChange($oldPurchaseItem, $newPurchaseItem);
                if (!empty($curDiffInfo))
                {
                    // 如果更新数据、更新数据日志不完整则说明错误
                    [$curUpData, $curUpLog] = $curDiffInfo;
                    if (!empty($curUpData) && !empty($curUpLog))
                    {
                        $updatePurchaseItemData[] = ['where' => ['id' => $oldPurchaseItem['id']], 'update' => $curUpData, 'log' => $curUpLog];
                    }
                }

                unset($purchaseItems[$newKey]);
                unset($getPurchaseOrderItemRes[$oldKey]);
            }
        }

        // 新提交的采购单商品如果存在，则代表说明存在找不到的商品。1、提交新商品uid为空，属于新增，已经删除掉；2、如果是修改旧的商品，记录修改信息后会删除掉；3、剩余的不属于新增、也不属于修改的
        if (!empty($purchaseItems))
        {
            return self::Fail('采购单商品信息异常，请重试', 42011);
        }

        // 如果旧的采购单还存在商品，则需要删除掉
        if (!empty($getPurchaseOrderItemRes))
        {
            $deletePurchaseItemData = array_column($getPurchaseOrderItemRes, null, 'id');
        }

        // 无任何更改
        if (empty($updatePurchaseOrderData) && empty($insertPurchaseItemData) && empty($updatePurchaseItemData) && empty($deletePurchaseItemData))
        {
            return self::Success();
        }

        try
        {
            DB::beginTransaction();

            // 更新采购单
            if (!empty($updatePurchaseOrderData))
            {
                PurchaseOrderModel::updateOne($purchaseOrderId, $updatePurchaseOrderData);
            }

            // 添加采购单商品
            if (!empty($insertPurchaseItemData))
            {
                $getInsertPurchaseItemRes = self::InsertPurchaseItem(['purchaseOrderId' => $purchaseOrderId, 'purchaseCode' => $getPurchaseOrderRes['purchase_code']],
                                                                     $insertPurchaseItemData,
                                                                     $publicParams);
                if ($getInsertPurchaseItemRes->isFail())
                {
                    DB::rollBack();

                    return $getInsertPurchaseItemRes;
                }
            }

            // 更新采购单商品
            foreach ($updatePurchaseItemData as $curUpdateData)
            {
                PurchaseOrderItemModel::updateOne($curUpdateData['where']['id'], $curUpdateData['update']);

                // 添加日志
                $operationDesc = array_merge($operationDesc, [$curUpdateData['log']]);
            }

            // 删除采购单商品
            if (!empty($deletePurchaseItemData))
            {
                PurchaseOrderItemModel::on()
                                      ->whereIn('id', array_keys($deletePurchaseItemData))
                                      ->update(['status' => 0]);

                // 添加日志
                array_walk($deletePurchaseItemData, function ($item) use (&$operationDesc, $getOldPurchaseItemInfoRes) {
                    $delItemId     = $item['item_id'];
                    $delItemName   = $getOldPurchaseItemInfoRes[$delItemId]['item_display_name'] ?? '';
                    $operationDesc = array_merge($operationDesc, ["删除商品：【{$delItemId}】$delItemName"]);
                });
            }

            // 记录日志
            $insertOperationData = [
                'purchase_order_id'   => $purchaseOrderId,
                'purchase_code'       => $getPurchaseOrderRes['purchase_code'],
                'operation_desc'      => json_encode($operationDesc, JSON_UNESCAPED_UNICODE),
                'operation_user_id'   => $userId,
                'operation_user_name' => UsersModel::getOneUserNameById($userId),
                'operation_channel'   => 1,
            ];
            PurchaseOrderOperationLogModel::insert($insertOperationData);

            DB::commit();

            return self::Success();
        } catch (Throwable $throwable)
        {
            DB::rollBack();

            Log::error(__CLASS__ . '::' . __METHOD__ . ' 编辑采购单异常', [
                'code'    => $throwable->getCode(),
                'message' => $throwable->getMessage(),
                'file'    => $throwable->getFile(),
                'line'    => $throwable->getLine(),
                'trace'   => $throwable->getTraceAsString(),
            ]);

            return self::Fail('编辑采购单异常', 42010);
        }
    }

    /**
     * 获取采购单详情
     *
     * @param int   $purchaseOrderId
     * @param array $publicParams
     *
     * @return LogicResult
     */
    public static function GetPurchaseOrderDetail(int $purchaseOrderId, array $publicParams): LogicResult
    {
        if (empty($purchaseOrderId))
        {
            return self::Fail('获取采购单详情，缺少采购单ID必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('获取采购单详情，缺少公共必选参数', 400);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        if (empty($hospitalId))
        {
            return self::Fail('获取采购单详情，缺少公共医院ID必选参数', 400);
        }

        // 获取采购单信息
        $getPurchaseOrderRes = PurchaseOrderModel::getPurchaseOrderListData(['purchaseOrderId' => $purchaseOrderId, 'hospitalId' => $hospitalId]);
        $getPurchaseOrderRes = !empty($getPurchaseOrderRes['data']) ? current($getPurchaseOrderRes['data']) : [];
        if (empty($getPurchaseOrderRes))
        {
            return self::Fail('采购单不存在', 42010);
        }

        // 获取采购单商品明细
        $getPurchaseOrderItemRes = PurchaseOrderItemModel::getData(where: ['purchase_order_id' => $purchaseOrderId, 'status' => 1]);
        if (empty($getPurchaseOrderItemRes))
        {
            return self::Fail('采购单商品不存在', 42011);
        }

        // 获取采购单商品信息
        $itemIds                = array_column($getPurchaseOrderItemRes, 'item_id');
        $getPurchaseItemInfoRes = ItemLogic::GetItemFullInfo(itemIds: $itemIds, itemStatus: [], publicParams: $publicParams, withItemPrice: true, withItemStock: true);
        if ($getPurchaseItemInfoRes->isFail())
        {
            return $getPurchaseItemInfoRes;
        }

        // 格式化采购单商品信息
        $getFormatPurchaseOrderRes = self::FormatPurchaseOrderStructure([$getPurchaseOrderRes]);
        if ($getFormatPurchaseOrderRes->isFail())
        {
            return $getFormatPurchaseOrderRes;
        }

        // 格式化采购单商品信息
        $getFormatPurchaseItemRes = self::FormatPurchaseItemStructure($getPurchaseOrderItemRes, $getPurchaseItemInfoRes->getData());
        if ($getFormatPurchaseItemRes->isFail())
        {
            return $getFormatPurchaseItemRes;
        }

        $getFormatPurchaseOrderRes          = current($getFormatPurchaseOrderRes->getData());
        $getFormatPurchaseOrderRes['items'] = $getFormatPurchaseItemRes->getData();

        return self::Success($getFormatPurchaseOrderRes);
    }

    /**
     * 删除采购单
     *
     * @param int   $purchaseOrderId
     * @param array $publicParams
     *
     * @return LogicResult
     * @throws Throwable
     */
    public static function DeletePurchaseOrder(int $purchaseOrderId, array $publicParams): LogicResult
    {
        if (empty($purchaseOrderId))
        {
            return self::Fail('删除采购单，缺少采购单ID必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('删除采购单，缺少公共必选参数', 400);
        }

        // 公共参数
        $userId = intval(Arr::get($publicParams, '_userId'));
        if (empty($userId))
        {
            return self::Fail('删除采购单，缺少用户ID必选参数', 400);
        }

        // 获取采购单信息
        $getPurchaseOrderRes = self::GetValidPurchaseOrder($purchaseOrderId, $publicParams);
        if ($getPurchaseOrderRes->isFail())
        {
            return $getPurchaseOrderRes;
        }

        // 验证是否可删除
        $getPurchaseOrderRes   = $getPurchaseOrderRes->getData();
        $purchaseOrderCode     = $getPurchaseOrderRes['purchase_code'];
        $getCheckDeleteAbleRes = self::CheckEditOrDeleteAblePurchaseOrder($getPurchaseOrderRes, $publicParams);
        if ($getCheckDeleteAbleRes->isFail())
        {
            return $getCheckDeleteAbleRes;
        }

        try
        {
            DB::beginTransaction();

            // 删除采购单
            PurchaseOrderModel::updateOne($purchaseOrderId, ['status' => PurchaseOrderStatusEnum::Cancelled->value, 'deleted_at' => getCurrentTimeWithMilliseconds()]);

            // 记录日志
            $operationDesc       = ["删除采购单：$purchaseOrderCode"];
            $insertOperationData = [
                'purchase_order_id'   => $purchaseOrderId,
                'purchase_code'       => $purchaseOrderCode,
                'operation_desc'      => json_encode($operationDesc, JSON_UNESCAPED_UNICODE),
                'operation_user_id'   => $userId,
                'operation_user_name' => UsersModel::getOneUserNameById($userId),
                'operation_channel'   => 1,
            ];
            PurchaseOrderOperationLogModel::insert($insertOperationData);

            DB::commit();

            return self::Success();
        } catch (Throwable $throwable)
        {
            DB::rollBack();

            Log::error(__CLASS__ . '::' . __METHOD__ . ' 删除采购单异常', [
                'code'    => $throwable->getCode(),
                'message' => $throwable->getMessage(),
                'file'    => $throwable->getFile(),
                'line'    => $throwable->getLine(),
                'trace'   => $throwable->getTraceAsString(),
            ]);

            return self::Fail('删除采购单异常', 42015);
        }
    }

    /**
     * 写入采购单商品
     *
     * @param array $purchaseOrderInfo
     * @param array $purchaseItems
     * @param array $publicParams
     *
     * @return LogicResult
     * @throws Throwable
     */
    private static function InsertPurchaseItem(array $purchaseOrderInfo, array $purchaseItems, array $publicParams): LogicResult
    {
        if (empty($purchaseOrderInfo))
        {
            return self::Fail('添加采购单商品，缺少采购单主信息参数', 400);
        }
        if (empty($purchaseItems))
        {
            return self::Fail('添加采购单商品，缺少采购单商品必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('添加采购单商品，缺少公共必选参数', 400);
        }

        // 公共参数
        $userId = intval(Arr::get($publicParams, '_userId'));
        if (empty($userId))
        {
            return self::Fail('添加采购单商品，缺少用户ID必选参数', 400);
        }

        // 业务参数
        $purchaseOrderId = intval(Arr::get($purchaseOrderInfo, 'purchaseOrderId', 0));
        $purchaseCode    = Arr::get($purchaseOrderInfo, 'purchaseCode', '');
        if (empty($purchaseOrderId) || empty($purchaseCode))
        {
            return self::Fail('添加采购单商品，缺少采购单ID、采购单号必选参数', 400);
        }

        // 获取采购商品现有库存
        $itemIds                 = array_unique(array_filter(array_column($purchaseItems, 'itemId')));
        $getEffectiveQuantityRes = StockItemShelfLogic::GetEffectiveQuantity($itemIds, $publicParams);
        if ($getEffectiveQuantityRes->isFail())
        {
            return $getEffectiveQuantityRes;
        }
        $getEffectiveQuantityRes = $getEffectiveQuantityRes->getData();

        $insertPurchaseItemData = [];
        foreach ($purchaseItems as $curPurchaseItem)
        {
            // 当前采购商品的信息、库存信息
            $curItemInfo        = $curPurchaseItem['itemInfo'] ?? [];
            $curItemBarcodeInfo = $curItemInfo['item_barcode_info'] ?? [];
            if (empty($curItemInfo) || empty($curItemBarcodeInfo))
            {
                return self::Fail('写入采购单，采购商品、商品条码信息不存在', 42007);
            }

            $tmpPurchaseItemData = [
                'uid'                              => generateUUID(),
                'purchase_order_id'                => $purchaseOrderId,
                'purchase_code'                    => $purchaseCode,
                'item_id'                          => $curItemInfo['id'],
                'item_barcode'                     => $curItemBarcodeInfo['item_barcode'],
                'item_bulk_ratio'                  => $curItemInfo['bulk_ratio'],
                'pack_purchase_price'              => $curPurchaseItem['packPurchasePrice'],
                'bulk_purchase_price'              => $curPurchaseItem['bulkPurchasePrice'],
                'pack_quantity'                    => $curPurchaseItem['packQuantity'],
                'bulk_quantity'                    => $curPurchaseItem['bulkQuantity'],
                'purchase_pack_effective_quantity' => $getEffectiveQuantityRes['packQuantity'] ?? 0,
                'purchase_bulk_effective_quantity' => $getEffectiveQuantityRes['bulkQuantity'] ?? 0,
                'bulk_avg_price'                   => $curPurchaseItem['bulkAvgPrice'] ?? 0,
            ];

            $insertPurchaseItemData[] = $tmpPurchaseItemData;
        }

        try
        {
            DB::beginTransaction();

            // 添加采购单商品
            PurchaseOrderItemModel::insert($insertPurchaseItemData);

            // 记录日志
            $operationDesc = [];
            array_walk($purchaseItems, function ($item) use (&$operationDesc) {
                $operationDesc[] = "添加商品：【{$item['itemInfo']['id']}】{$item['itemName']}";
            });


            $insertOperationData = [
                'purchase_order_id'   => $purchaseOrderId,
                'purchase_code'       => $purchaseCode,
                'operation_desc'      => json_encode($operationDesc, JSON_UNESCAPED_UNICODE),
                'operation_user_id'   => $userId,
                'operation_user_name' => UsersModel::getOneUserNameById($userId),
                'operation_channel'   => 1,
            ];
            PurchaseOrderOperationLogModel::insert($insertOperationData);

            DB::commit();

            return self::Success();
        } catch (Throwable $throwable)
        {
            DB::rollBack();

            Log::error(__CLASS__ . '::' . __METHOD__ . ' 添加采购单商品异常', [
                'code'    => $throwable->getCode(),
                'message' => $throwable->getMessage(),
                'file'    => $throwable->getFile(),
                'line'    => $throwable->getLine(),
                'trace'   => $throwable->getTraceAsString(),
            ]);

            return self::Fail('添加采购单商品异常', 42009);
        }
    }

    /**
     * 验证采购单是否可编辑
     *
     * @param array $purchaseOrderInfo
     * @param array $publicParams
     *
     * @return LogicResult
     */
    private static function CheckEditOrDeleteAblePurchaseOrder(array $purchaseOrderInfo, array $publicParams): LogicResult
    {
        if (empty($purchaseOrderInfo))
        {
            return self::Fail('验证采购单是否可操作，缺少采购单信息必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('验证采购单是否可操作，缺少公共必选参数', 400);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        if (empty($hospitalId))
        {
            return self::Fail('验证采购单是否可操作，缺少医院ID必选参数', 400);
        }

        if (!in_array($purchaseOrderInfo['status'], [PurchaseOrderStatusEnum::Draft->value, PurchaseOrderStatusEnum::Rejected->value]))
        {
            return self::Fail('采购单状态非草稿、驳回，不可操作', 42012);
        }
        if ($purchaseOrderInfo['hospital_id'] != $hospitalId)
        {
            return self::Fail('采购单医院不一致，不可操作', 42012);
        }

        return self::Success();
    }

    /**
     * 更新采购单入库信息
     *
     * @param int        $purchaseOrderId  采购单ID
     * @param int|string $thisInboundPrice 本次入库金额
     * @param array      $publicParams
     *
     * @return LogicResult
     */
    public static function UpdatePurchaseOrderInbound(int $purchaseOrderId, int|string $thisInboundPrice, array $publicParams): LogicResult
    {
        if (empty($purchaseOrderId))
        {
            return self::Fail('更新采购单入库状态，缺少采购单ID必选参数', 400);
        }
        if ($thisInboundPrice < 0)
        {
            return self::Fail('更新采购单入库状态，入库金额必选参数错误', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('更新采购单入库状态，缺少公共必选参数', 400);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        if (empty($hospitalId))
        {
            return self::Fail('更新采购单入库状态，缺少医院ID必选参数', 400);
        }

        // 获取采购单信息
        $getPurchaseOrderRes = self::GetValidPurchaseOrder($purchaseOrderId, $publicParams);
        if ($getPurchaseOrderRes->isFail())
        {
            return $getPurchaseOrderRes;
        }

        $getPurchaseOrderRes = $getPurchaseOrderRes->getData();

        // 获取采购单对应的到货单，未签收完成的数量、签收总金额
        $notAllInboundCount = PurchaseReceivedModel::getTotalNumber(where  : ['purchase_order_id' => $purchaseOrderId, 'status' => 1],
                                                                    whereIn: [
                                                                                 'inbound_status' => [
                                                                                     PurchaseOrderInboundStatusEnum::NotInbound->value,
                                                                                     PurchaseOrderInboundStatusEnum::PartiallyInbound->value
                                                                                 ]
                                                                             ]);

        // 如果采购单全部签收、并且没有未完成的入库，则更新采购单入库完成
        if ($notAllInboundCount <= 0 && $getPurchaseOrderRes['received_status'] == PurchaseOrderReceivedStatusEnum::FullyReceived->value)
        {
            $upInboundStatus       = PurchaseOrderInboundStatusEnum::FullyInbound->value;
            $upInboundCompleteTime = getCurrentTimeWithMilliseconds();
        }
        else
        {
            $upInboundStatus       = PurchaseOrderInboundStatusEnum::PartiallyInbound->value;
            $upInboundCompleteTime = null;
        }

        PurchaseOrderModel::on()
                          ->where(['id' => $purchaseOrderId])
                          ->whereRaw('inbound_price + ? <= received_price', [$thisInboundPrice])
                          ->update([
                                       'inbound_price'       => DB::raw('inbound_price + ' . $thisInboundPrice),
                                       'inbound_status'      => $upInboundStatus,
                                       'inbound_complete_at' => $upInboundCompleteTime
                                   ]);

        return self::Success();
    }

    /**
     * 格式化采购单列表
     *
     * @param array $purchaseOrderList
     *
     * @return LogicResult
     */
    private static function FormatPurchaseOrderStructure(array $purchaseOrderList): LogicResult
    {
        if (empty($purchaseOrderList))
        {
            return self::Success();
        }

        $returnPurchaseOrderList = [];
        foreach ($purchaseOrderList as $curPurchaseOrder)
        {
            $tmpPurchaseOrder = [
                'purchaseCode'    => $curPurchaseOrder['purchase_code'],
                'purchaseType'    => [
                    'id'   => $curPurchaseOrder['purchase_type'],
                    'name' => PurchaseTypeEnum::getDescription($curPurchaseOrder['purchase_type']),
                ],
                'supplierName'    => self::FormatPurchaseSupplierName($curPurchaseOrder)
                                         ->getData('supplierName', ''),
                'supplier'        => [
                    'uid'  => $curPurchaseOrder['supplier_uid'],
                    'name' => $curPurchaseOrder['supplier_name'],
                ],
                'fromHospital'    => [
                    'uid'  => $curPurchaseOrder['hospital_uid'],
                    'name' => $curPurchaseOrder['hospital_alias_name'],
                ],
                'purchasePrice'   => $curPurchaseOrder['purchase_price'] <= 0 ? '' : formatDisplayNumber($curPurchaseOrder['purchase_price'], 4),
                'receivedInPrice' => $curPurchaseOrder['received_price'] <= 0 ? '' : formatDisplayNumber($curPurchaseOrder['received_price'], 4),
                'inboundPrice'    => $curPurchaseOrder['inbound_price'] <= 0 ? '' : formatDisplayNumber($curPurchaseOrder['inbound_price'], 4),
                'purchaseStatus'  => [
                    'id'   => $curPurchaseOrder['status'],
                    'name' => PurchaseOrderStatusEnum::getDescription($curPurchaseOrder['status']),
                ],
                'receivedStatus'  => null,
                'createUser'      => [
                    'uid'  => $curPurchaseOrder['user_uid'],
                    'name' => $curPurchaseOrder['user_name'],
                ],
                'createTime'      => formatDisplayDateTime($curPurchaseOrder['created_at']),
                'remark'          => $curPurchaseOrder['remark'],
                'rejectReason'    => $curPurchaseOrder['reject_reason'],
            ];

            // 已审核通过，返回签收状态
            if ($curPurchaseOrder['status'] == PurchaseOrderStatusEnum::Approved->value)
            {
                $tmpPurchaseOrder['receivedStatus'] = [
                    'id'   => $curPurchaseOrder['received_status'],
                    'name' => PurchaseOrderReceivedStatusEnum::getDescription($curPurchaseOrder['received_status']),
                ];
            }

            $returnPurchaseOrderList[] = $tmpPurchaseOrder;
        }

        return self::Success($returnPurchaseOrderList);
    }

    public static function FormatPurchaseSupplierName(array $purchaseInfo): LogicResult
    {
        if (empty($purchaseInfo))
        {
            return self::Success();
        }

        $supplierName = PurchaseTypeEnum::getPurchaseIsTransfer($purchaseInfo['purchase_type']) ? $purchaseInfo['hospital_alias_name'] : $purchaseInfo['supplier_name'];

        return self::Success(['supplierName' => $supplierName]);
    }

    /**
     * 格式化采购单商品信息
     *
     * @param array $purchaseItemList
     * @param array $itemInfoList
     *
     * @return LogicResult
     */
    private static function FormatPurchaseItemStructure(array $purchaseItemList, array $itemInfoList): LogicResult
    {
        if (empty($purchaseItemList) || empty($itemInfoList))
        {
            return self::Success();
        }

        // 按照商品ID数组
        $itemInfoList = array_column($itemInfoList, null, 'id');

        $returnPurchaseItemList = [];
        foreach ($purchaseItemList as $curPurchaseItem)
        {
            // 商品信息
            $curItemInfo = $itemInfoList[$curPurchaseItem['item_id']] ?? [];
            if (empty($curItemInfo))
            {
                continue;
            }

            $tmpPurchaseItem = [
                'uid'                           => $curPurchaseItem['uid'],
                'itemUid'                       => $curItemInfo['uid'],
                'itemBarcode'                   => $curPurchaseItem['item_barcode'],
                'itemName'                      => ItemHelper::ItemDisplayName($curItemInfo),
                'packPurchasePrice'             => null,
                'bulkPurchasePrice'             => null,
                'packQuantity'                  => formatDisplayNumber($curPurchaseItem['pack_quantity']),
                'bulkQuantity'                  => formatDisplayNumber($curPurchaseItem['bulk_quantity']),
                'purchasePackEffectiveQuantity' => formatDisplayNumber($curPurchaseItem['purchase_pack_effective_quantity']),
                'purchaseBulkEffectiveQuantity' => formatDisplayNumber($curPurchaseItem['purchase_bulk_effective_quantity']),
                'receivedPackQuantity'          => formatDisplayNumber($curPurchaseItem['received_pack_quantity']),
                'receivedBulkQuantity'          => formatDisplayNumber($curPurchaseItem['received_bulk_quantity']),
                'isGift'                        => $curPurchaseItem['is_gift'],
                'packTotalPrice'                => null,
                'bulkTotalPrice'                => null,
                'totalPrice'                    => null,
                'isReceivedCompleted'           => 0,
                'itemInfo'                      => ItemHelper::FormatItemInfoStructure($curItemInfo),
            ];

            // 如果是赠品，不显示价格
            if (!empty($curPurchaseItem['is_gift']))
            {
                $returnPurchaseItemList[] = $tmpPurchaseItem;
                continue;
            }

            // 是否到货完成
            if ($curPurchaseItem['received_pack_quantity'] >= $curPurchaseItem['pack_quantity'] && $curPurchaseItem['received_bulk_quantity'] >= $curPurchaseItem['bulk_quantity'])
            {
                $tmpPurchaseItem['isReceivedCompleted'] = 1;
            }

            // 如果存在价格，重新计算价格并返回。如果不存在价格，要返回null，不可返回0。返回0前端展示为0，返回null，则不展示。集采未审核通过前可能不存在价格
            if ($curPurchaseItem['pack_purchase_price'] > 0)
            {
                $tmpPurchaseItem['packPurchasePrice'] = formatDisplayNumber($curPurchaseItem['pack_purchase_price'], 4);
                $tmpPurchaseItem['packTotalPrice']    = formatDisplayNumber(numberMul([$curPurchaseItem['pack_purchase_price'], $curPurchaseItem['pack_quantity']], 4), 4);
            }
            if ($curPurchaseItem['bulk_purchase_price'] > 0)
            {
                $tmpPurchaseItem['bulkPurchasePrice'] = formatDisplayNumber($curPurchaseItem['bulk_purchase_price'], 4);
                $tmpPurchaseItem['bulkTotalPrice']    = formatDisplayNumber(numberMul([$curPurchaseItem['bulk_purchase_price'], $curPurchaseItem['bulk_quantity']], 4), 4);
            }
            if ($tmpPurchaseItem['packTotalPrice'] > 0 || $tmpPurchaseItem['bulkTotalPrice'] > 0)
            {
                $tmpPurchaseItem['totalPrice'] = formatDisplayNumber(numberAdd([$tmpPurchaseItem['packTotalPrice'], $tmpPurchaseItem['bulkTotalPrice']], 4), 4);
            }

            $returnPurchaseItemList[] = $tmpPurchaseItem;
        }

        return self::Success($returnPurchaseItemList);
    }
}
