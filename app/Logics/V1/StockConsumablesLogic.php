<?php

namespace App\Logics\V1;

use DB;
use Arr;
use Log;
use Throwable;
use App\Support\Item\ItemHelper;
use App\Facades\SearchFacade;
use App\Support\Stock\StockConsumablesHelper;
use App\Enums\PageEnum;
use App\Enums\StockReduceTypeEnum;
use App\Enums\BusinessCodePrefixEnum;
use App\Enums\StockOutboundStatusEnum;
use App\Logics\Logic;
use App\Logics\LogicResult;
use App\Models\UsersModel;
use App\Models\StockConsumablesReceiveOrderModel;
use App\Models\StockConsumablesReceiveOrderItemModel;
use App\Models\StockConsumablesReceiveOrderOperationLogModel;

class StockConsumablesLogic extends Logic
{
    /**
     * 获取创建耗材领用单用户
     *
     * @param array $publicParams
     *
     * @return LogicResult
     */
    public static function GetCreateConsumablesUserOptions(array $publicParams): LogicResult
    {
        if (empty($publicParams))
        {
            return self::Fail('获取创建耗材领用单用户，缺少必选参数', 400);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        if (empty($hospitalId))
        {
            return self::Fail('获取创建耗材领用单用户，缺少医院ID必选参数', 400);
        }

        $getStockConsumablesUserRes = StockConsumablesReceiveOrderModel::getData(fields: ['created_by'], where: ['hospital_id' => $hospitalId], group: 'created_by');
        if (empty($getStockConsumablesUserRes))
        {
            return self::Success();
        }

        $userIds    = array_unique(array_column($getStockConsumablesUserRes, 'created_by'));
        $getUserRes = UsersModel::getUserByIds($userIds);

        $returnUserOptions = [];
        foreach ($getUserRes as $curUser)
        {
            $returnUserOptions[] = [
                'uid'  => $curUser['uid'],
                'name' => $curUser['name'],
            ];
        }

        return self::Success($returnUserOptions);
    }

    /**
     * 搜索耗材商品
     *
     * @param array $searchParams
     * @param array $publicParams
     *
     * @return LogicResult
     */
    public static function GetSearchItems(array $searchParams, array $publicParams): LogicResult
    {
        if (empty($searchParams))
        {
            return self::Fail('获取搜索商品，缺少必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('获取搜索商品，缺少公共必选参数', 400);
        }

        // 公共参数
        $orgId      = intval(Arr::get($publicParams, '_hospitalOrgId'));
        $brandId    = intval(Arr::get($publicParams, '_hospitalBrandId'));
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        if (empty($orgId) || empty($brandId) || empty($hospitalId))
        {
            return self::Fail('获取搜索商品，缺少公共必选参数', 400);
        }

        // 业务参数
        $keywords = trimWhitespace(Arr::get($searchParams, 'keywords', ''));
        if (empty($keywords))
        {
            return self::Fail('搜索关键词错误', 42001);
        }

        // 搜索商品
        $getSearchItemsRes = SearchFacade::searchConsumablesItems($keywords, $hospitalId, orgId: $orgId);
        if (empty($getSearchItemsRes))
        {
            return self::Success(['data' => []]);
        }

        // 格式化商品信息
        $getFormatItemRes = SearchItemLogic::FormatItemInfoStructure($getSearchItemsRes, $publicParams, true, true);
        if ($getFormatItemRes->isFail())
        {
            return $getFormatItemRes;
        }

        $getFormatItemRes = $getFormatItemRes->getData();

        // 获取商品库存
        $arrItemIds          = array_column($getSearchItemsRes, 'id');
        $getStockQuantityRes = StockItemShelfLogic::GetEffectiveQuantity($arrItemIds, $publicParams);
        if ($getStockQuantityRes->isFail())
        {
            return $getStockQuantityRes;
        }

        // 商品编码为key
        $getStockQuantityRes = array_column($getStockQuantityRes->getData(), null, 'itemBarcode');

        foreach ($getFormatItemRes as &$curSearchItem)
        {
            if ($curSearchItem['packPrice'] <= 0 || $curSearchItem['bulkPrice'] <= 0)
            {
                $curSearchItem['disable']     = true;
                $curSearchItem['disableTips'] = '价格无效，不可用';
                continue;
            }

            // 商品无库存，禁用
            $curItemStock = $getStockQuantityRes[$curSearchItem['itemBarcode']] ?? [];
            if (empty($curItemStock) || ($curItemStock['packQuantity'] <= 0 && $curItemStock['bulkQuantity'] <= 0))
            {
                $curSearchItem['disable']     = true;
                $curSearchItem['disableTips'] = '库存不足，不可用';
            }
        }

        return self::Success(['data' => $getFormatItemRes]);
    }

    /**
     * 获取耗材领用单列表
     *
     * @param array $searchParams
     * @param array $publicParams
     *
     * @return LogicResult
     */
    public static function GetConsumablesReceiveLists(array $searchParams, array $publicParams): LogicResult
    {
        if (empty($publicParams))
        {
            return self::Fail('获取耗材领用单列表，缺少公共必选参数', 400);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        if (empty($hospitalId))
        {
            return self::Fail('获取耗材领用单列表，缺少医院ID必选参数', 400);
        }

        // 业务参数
        $keywords       = trimWhitespace(Arr::get($searchParams, 'keywords', ''));
        $startDate      = trimWhitespace(Arr::get($searchParams, 'startDate', ''));
        $endDate        = trimWhitespace(Arr::get($searchParams, 'endDate', ''));
        $createUserUid  = Arr::get($searchParams, 'createUserUid', '');
        $status         = Arr::get($searchParams, 'status');
        $outboundStatus = Arr::get($searchParams, 'outboundStatus');
        $iPage          = intval(Arr::get($searchParams, 'page', 0)) ?? PageEnum::DefaultPageIndex->value;
        $iPageSize      = intval(Arr::get($searchParams, 'count', 0)) ?? PageEnum::DefaultPageSize->value;
        if (!empty($startDate) && !checkDateIsValid($startDate))
        {
            return self::Fail('获取耗材领用单列表，开始日期格式错误', 400);
        }
        if (!empty($endDate) && !checkDateIsValid($endDate))
        {
            return self::Fail('获取耗材领用单列表，结束日期格式错误', 400);
        }

        // 构建查询参数
        $queryParams = [
            'hospitalId'     => $hospitalId,
            'keywords'       => $keywords,
            'startDate'      => $startDate,
            'endDate'        => $endDate,
            'createUserUid'  => $createUserUid,
            'status'         => $status,
            'outboundStatus' => $outboundStatus,
        ];

        // 获取耗材单列表
        $getConsumablesListRes = StockConsumablesReceiveOrderModel::getStockConsumablesReceiveOrderListData($queryParams, $iPage, $iPageSize);
        if (empty($getConsumablesListRes))
        {
            return self::Success(['total' => 0, 'data' => []]);
        }

        $totalCount         = $getConsumablesListRes['total'] ?? 0;
        $consumablesListRes = $getConsumablesListRes['data'] ?? [];
        if (empty($totalCount) || empty($consumablesListRes))
        {
            return self::Success(['total' => 0, 'data' => []]);
        }

        $getFormatConsumablesListRes = self::FormatConsumablesOrderStructure($consumablesListRes);
        if ($getFormatConsumablesListRes->isFail())
        {
            return $getFormatConsumablesListRes;
        }

        return self::Success(['total' => $totalCount, 'data' => $getFormatConsumablesListRes->getData()]);
    }

    /**
     * 获取有效耗材领用单
     *
     * @param int   $receiveOrderId
     * @param array $publicParams
     *
     * @return LogicResult
     */
    public static function GetValidConsumablesReceiveOrder(int $receiveOrderId, array $publicParams): LogicResult
    {
        if (empty($receiveOrderId))
        {
            return self::Fail('获取有效耗材领用单，缺少领用单ID必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('获取有效耗材领用单，缺少公共必选参数', 400);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        if (empty($hospitalId))
        {
            return self::Fail('获取有效耗材领用单，缺少医院ID必选参数', 400);
        }

        // 获取耗材单信息
        $getStockConsumablesRes = StockConsumablesReceiveOrderModel::getData(where: ['id' => $receiveOrderId, 'hospital_id' => $hospitalId]);
        $getStockConsumablesRes = $getStockConsumablesRes ? current($getStockConsumablesRes) : [];
        if (empty($getStockConsumablesRes))
        {
            return self::Fail('耗材领用单不存在或已失效', 44000);
        }

        return self::Success($getStockConsumablesRes);
    }

    /**
     * 添加耗材领用单
     *
     * @param array $addConsumablesParams
     * @param array $publicParams
     *
     * @return LogicResult
     * @throws Throwable
     */
    public static function AddConsumablesReceiveOrder(array $addConsumablesParams, array $publicParams): LogicResult
    {
        if (empty($addConsumablesParams))
        {
            return self::Fail('添加耗材领用单，缺少必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('添加耗材领用单，缺少公共必选参数', 400);
        }

        // 公共参数
        $orgId      = intval(Arr::get($publicParams, '_hospitalOrgId'));
        $brandId    = intval(Arr::get($publicParams, '_hospitalBrandId'));
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        $userId     = intval(Arr::get($publicParams, '_userId'));
        if (empty($orgId) || empty($brandId) || empty($hospitalId) || empty($userId))
        {
            return self::Fail('添加耗材领用单，缺少医院相关必选参数', 400);
        }

        // 验证领用耗材基本参数是否正确
        $getValidConsumablesParamsRes = StockConsumablesHelper::GetValidConsumablesParams($addConsumablesParams, $publicParams);
        if ($getValidConsumablesParamsRes->isFail())
        {
            return $getValidConsumablesParamsRes;
        }

        // 获取验证后的领用耗材参数
        $consumablesItems  = $getValidConsumablesParamsRes->getData('items', []);
        $receiveTotalPrice = $getValidConsumablesParamsRes->getData('receiveTotalPrice', 0);
        $remark            = $getValidConsumablesParamsRes->getData('remark', '');

        try
        {
            DB::beginTransaction();

            // 创建耗材领用单
            $receiveCode                  = generateBusinessCodeNumber(BusinessCodePrefixEnum::HCLY);
            $insertReceiveConsumablesData = [
                'receive_code'  => $receiveCode,
                'org_id'        => $orgId,
                'brand_id'      => $brandId,
                'hospital_id'   => $hospitalId,
                'receive_price' => $receiveTotalPrice,
                'remark'        => $remark,
                'created_by'    => $userId,
            ];
            $receiveOrderId               = StockConsumablesReceiveOrderModel::insertOne($insertReceiveConsumablesData);

            // 添加耗材领用单商品
            $getInsertConsumablesItemRes = self::InsertConsumablesItem(['receiveOrderId' => $receiveOrderId, 'receiveCode' => $receiveCode], $consumablesItems, $publicParams);
            if ($getInsertConsumablesItemRes->isFail())
            {
                DB::rollBack();

                return $getInsertConsumablesItemRes;
            }

            DB::commit();

            return self::Success();
        } catch (Throwable $throwable)
        {
            DB::rollBack();
            Log::error(__CLASS__ . '::' . __METHOD__ . ' 添加耗材领用单异常', [
                'code'    => $throwable->getCode(),
                'message' => $throwable->getMessage(),
                'file'    => $throwable->getFile(),
                'line'    => $throwable->getLine(),
                'trace'   => $throwable->getTraceAsString(),
            ]);

            return self::Fail('添加耗材领用单异常', 44023);
        }
    }

    /**
     * 编辑耗材领用单
     *
     * @param int   $receiveOrderId
     * @param array $editConsumablesParams
     * @param array $publicParams
     *
     * @return LogicResult
     * @throws Throwable
     */
    public static function EditConsumablesReceiveOrder(int $receiveOrderId, array $editConsumablesParams, array $publicParams): LogicResult
    {
        if (empty($receiveOrderId))
        {
            return self::Fail('编辑耗材领用单，缺少领用单ID必选参数', 400);
        }
        if (empty($editConsumablesParams))
        {
            return self::Fail('编辑耗材领用单，缺少领用单必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('编辑耗材领用单，缺少公共必选参数', 400);
        }

        // 公共参数
        $userId = intval(Arr::get($publicParams, '_userId'));
        if (empty($userId))
        {
            return self::Fail('编辑耗材领用单，缺少用户ID必选参数', 400);
        }

        // 获取耗材领用单信息
        $getConsumablesOrderRes = self::GetValidConsumablesReceiveOrder($receiveOrderId, $publicParams);
        if ($getConsumablesOrderRes->isFail())
        {
            return $getConsumablesOrderRes;
        }
        $getConsumablesOrderRes = $getConsumablesOrderRes->getData();

        // 获取耗材领用单商品明细
        $getConsumablesOrderItemRes = StockConsumablesReceiveOrderItemModel::getData(where: ['receive_order_id' => $receiveOrderId, 'status' => 1]);
        if (empty($getConsumablesOrderItemRes))
        {
            return self::Fail('耗材领用单商品不存在', 44001);
        }

        // 获取耗材领用单商品信息，用于如果修改的话记录名称等
        $oldItemIds                   = array_column($getConsumablesOrderItemRes, 'item_id');
        $getOldConsumablesItemInfoRes = ItemLogic::GetItemFullInfo(itemIds: $oldItemIds, itemStatus: [], publicParams: $publicParams);
        if ($getOldConsumablesItemInfoRes->isFail())
        {
            return $getOldConsumablesItemInfoRes;
        }
        $getOldConsumablesItemInfoRes = array_column($getOldConsumablesItemInfoRes->getData(), null, 'id');

        // 验证耗材领用单是否可以编辑
        $getCheckEditAbleRes = self::CheckEditAbleConsumablesReceiveOrder($getConsumablesOrderRes, $publicParams);
        if ($getCheckEditAbleRes->isFail())
        {
            return $getCheckEditAbleRes;
        }

        // 验证耗材单基本参数是否正确
        $getValidConsumablesParamsRes = StockConsumablesHelper::GetValidConsumablesParams($editConsumablesParams, $publicParams, $getConsumablesOrderItemRes);
        if ($getValidConsumablesParamsRes->isFail())
        {
            return $getValidConsumablesParamsRes;
        }

        // 获取验证后的领用耗材参数
        $consumablesItems  = $getValidConsumablesParamsRes->getData('items', []);
        $receiveTotalPrice = $getValidConsumablesParamsRes->getData('receiveTotalPrice', 0);
        $remark            = $getValidConsumablesParamsRes->getData('remark', '');

        // 领用单相关操作的日志说明
        $operationDesc = [];

        // 更新领用单信息
        $updateConsumablesOrderData = [];
        if ($remark != $getConsumablesOrderRes['remark'])
        {
            $updateConsumablesOrderData['remark'] = $remark;
            $operationDesc[]                      = "领用说明由：{$getConsumablesOrderRes['remark']} 修改为：$remark";
        }
        if (bccomp($receiveTotalPrice, $getConsumablesOrderRes['receive_price'], 4) !== 0)
        {
            $updateConsumablesOrderData['receive_price'] = $receiveTotalPrice;
            $operationDesc[]                             = "领用单金额由：{$getConsumablesOrderRes['receive_price']} 修改为：$receiveTotalPrice";
        }

        // 更新耗材领用单商品
        $insertConsumablesItemData = [];
        $updateConsumablesItemData = [];
        $deleteConsumablesItemData = [];
        foreach ($getConsumablesOrderItemRes as $oldKey => $oldConsumablesItem)
        {
            foreach ($consumablesItems as $newKey => $newConsumablesItem)
            {
                // 领用单商品在表中的UID
                $curConsumablesItemUid = $newConsumablesItem['uid'] ?? '';

                // 如果uid为空，则代表是新增的记录
                if (empty($curConsumablesItemUid))
                {
                    $insertConsumablesItemData[] = $newConsumablesItem;
                    unset($consumablesItems[$newKey]);
                    continue;
                }

                // 如果uid不一致，则代表不是同一个商品
                if ($oldConsumablesItem['uid'] != $curConsumablesItemUid)
                {
                    continue;
                }

                // 领用单商品的主信息
                $curConsumablesItemInfo = $newConsumablesItem['itemInfo'] ?? [];
                if (empty($curConsumablesItemInfo))
                {
                    return self::Fail('耗材商品信息不存在', 44022);
                }

                // 如果是同一个商品，商品ID是否同一个，防止错误
                if ($oldConsumablesItem['item_id'] != $curConsumablesItemInfo['id'])
                {
                    return self::Fail('提交领用单商品与存在的商品ID不一致', 44022);
                }

                // 对比是否存在修改耗材领用商品的信息：散装数量、散装价格、整装数量、整装价格
                $curDiffInfo = StockConsumablesHelper::CompareConsumablesItemChange($oldConsumablesItem, $newConsumablesItem);
                if (!empty($curDiffInfo))
                {
                    // 如果更新数据、更新数据日志不完整则说明错误
                    [$curUpData, $curUpLog] = $curDiffInfo;
                    if (!empty($curUpData) && !empty($curUpLog))
                    {
                        $updateConsumablesItemData[] = ['where' => ['id' => $oldConsumablesItem['id']], 'update' => $curUpData, 'log' => $curUpLog];
                    }
                }

                unset($consumablesItems[$newKey]);
                unset($getConsumablesOrderItemRes[$oldKey]);
            }
        }

        // 新提交的领用单商品如果存在，则代表说明存在找不到的商品。1、提交新商品uid为空，属于新增，已经删除掉；2、如果是修改旧的商品，记录修改信息后会删除掉；3、剩余的不属于新增、也不属于修改的
        if (!empty($consumablesItems))
        {
            return self::Fail('耗材单商品信息异常，请重试', 44022);
        }

        // 如果旧的耗材单还存在商品，则需要删除掉
        if (!empty($getConsumablesItemRes))
        {
            $deleteConsumablesItemData = array_column($getConsumablesItemRes, null, 'id');
        }

        // 无任何更改
        if (empty($updateConsumablesOrderData) && empty($insertConsumablesItemData) && empty($updateConsumablesItemData) && empty($deleteConsumablesItemData))
        {
            return self::Success();
        }

        try
        {
            DB::beginTransaction();

            // 更新领用单
            if (!empty($updateConsumablesOrderData))
            {
                StockConsumablesReceiveOrderModel::updateOne($receiveOrderId, $updateConsumablesOrderData);
            }

            // 添加耗材领用单商品
            if (!empty($insertConsumablesItemData))
            {
                $getInsertConsumablesItemRes = self::InsertConsumablesItem(['receiveOrderId' => $receiveOrderId, 'receiveCode' => $getConsumablesOrderRes['receive_code']],
                                                                           $insertConsumablesItemData,
                                                                           $publicParams);
                if ($getInsertConsumablesItemRes->isFail())
                {
                    DB::rollBack();

                    return $getInsertConsumablesItemRes;
                }
            }

            // 更新耗材单商品
            foreach ($updateConsumablesItemData as $curUpdateData)
            {
                StockConsumablesReceiveOrderItemModel::updateOne($curUpdateData['where']['id'], $curUpdateData['update']);

                // 添加日志
                $operationDesc = array_merge($operationDesc, [$curUpdateData['log']]);
            }

            // 删除耗材单商品
            if (!empty($deleteConsumablesItemData))
            {
                StockConsumablesReceiveOrderItemModel::on()
                                                     ->whereIn('id', array_keys($deleteConsumablesItemData))
                                                     ->update(['status' => 0]);

                // 添加日志
                array_walk($deleteConsumablesItemData, function ($item) use (&$operationDesc, $getOldConsumablesItemInfoRes) {
                    $delItemId     = $item['item_id'];
                    $delItemName   = $getOldConsumablesItemInfoRes[$delItemId]['item_display_name'] ?? '';
                    $operationDesc = array_merge($operationDesc, ["删除商品：【{$delItemId}】$delItemName"]);
                });
            }

            // 记录日志
            $insertOperationData = [
                'receive_order_id'    => $receiveOrderId,
                'receive_order_code'  => $getConsumablesOrderRes['receive_code'],
                'operation_desc'      => json_encode($operationDesc, JSON_UNESCAPED_UNICODE),
                'operation_user_id'   => $userId,
                'operation_user_name' => UsersModel::getOneUserNameById($userId),
                'operation_channel'   => 1,
            ];
            StockConsumablesReceiveOrderOperationLogModel::insert($insertOperationData);

            DB::commit();

            return self::Success();
        } catch (Throwable $throwable)
        {
            DB::rollBack();
            Log::error(__CLASS__ . '::' . __METHOD__ . ' 编辑耗材领用单异常',
                       [
                           'code'    => $throwable->getCode(),
                           'message' => $throwable->getMessage(),
                           'file'    => $throwable->getFile(),
                           'line'    => $throwable->getLine(),
                           'trace'   => $throwable->getTraceAsString(),
                       ]);

            return self::Fail('编辑耗材领用单异常', 44023);
        }
    }

    /**
     * 获取耗材单详情
     *
     * @param int   $receiveOrderId
     * @param array $publicParams
     *
     * @return LogicResult
     */
    public static function GetConsumablesReceiveOrderDetail(int $receiveOrderId, array $publicParams): LogicResult
    {
        if (empty($receiveOrderId))
        {
            return self::Fail('获取耗材领用单详情，缺少领用单ID必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('获取耗材领用单详情，缺少公共必选参数', 400);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        if (empty($hospitalId))
        {
            return self::Fail('获取耗材领用单详情，缺少公共医院ID必选参数', 400);
        }

        // 获取耗材领用单信息
        $getConsumablesOrderRes = StockConsumablesReceiveOrderModel::getStockConsumablesReceiveOrderListData([
                                                                                                                 'receiveOrderId' => $receiveOrderId,
                                                                                                                 'hospitalId'     => $hospitalId
                                                                                                             ]);
        $getConsumablesOrderRes = !empty($getConsumablesOrderRes['data']) ? current($getConsumablesOrderRes['data']) : [];
        if (empty($getConsumablesOrderRes))
        {
            return self::Fail('耗材领用单不存在', 44000);
        }

        // 获取耗材领用单商品明细
        $getConsumablesOrderItemRes = StockConsumablesReceiveOrderItemModel::getData(where: ['receive_order_id' => $receiveOrderId, 'status' => 1]);
        if (empty($getConsumablesOrderItemRes))
        {
            return self::Fail('耗材领用单商品不存在', 44001);
        }

        // 获取耗材领用单商品信息
        $itemIds                   = array_column($getConsumablesOrderItemRes, 'item_id');
        $getConsumablesItemInfoRes = ItemLogic::GetItemFullInfo(itemIds: $itemIds, itemStatus: [], publicParams: $publicParams);
        if ($getConsumablesItemInfoRes->isFail())
        {
            return $getConsumablesItemInfoRes;
        }

        // 格式化耗材领用单信息
        $getFormatConsumablesOrderRes = self::FormatConsumablesOrderStructure([$getConsumablesOrderRes]);
        if ($getFormatConsumablesOrderRes->isFail())
        {
            return $getFormatConsumablesOrderRes;
        }

        // 格式化耗材单商品信息
        $getFormatConsumablesOrderItemRes = self::FormatConsumablesOrderItemStructure($getConsumablesOrderItemRes, $getConsumablesItemInfoRes->getData());
        if ($getFormatConsumablesOrderItemRes->isFail())
        {
            return $getFormatConsumablesOrderItemRes;
        }

        $getFormatConsumablesOrderRes          = current($getFormatConsumablesOrderRes->getData());
        $getFormatConsumablesOrderRes['items'] = $getFormatConsumablesOrderItemRes->getData();

        return self::Success($getFormatConsumablesOrderRes);
    }

    /**
     * 耗材领用出库
     *
     * @param int   $receiveOrderId
     * @param array $publicParams
     *
     * @return LogicResult
     * @throws Throwable
     */
    public static function ReceiveOrderOutbound(int $receiveOrderId, array $publicParams): LogicResult
    {
        if (empty($receiveOrderId))
        {
            return self::Fail('耗材领用出库，缺少领用单ID必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('耗材领用出库，缺少公共必选参数', 400);
        }

        // 获取耗材领用单信息
        $getConsumablesOrderRes = self::GetValidConsumablesReceiveOrder($receiveOrderId, $publicParams);
        if ($getConsumablesOrderRes->isFail())
        {
            return $getConsumablesOrderRes;
        }

        // 是否可出库
        $getConsumablesOrderRes = $getConsumablesOrderRes->getData();
        if ($getConsumablesOrderRes['outbound_status'] == StockOutboundStatusEnum::FullyOutbound->value)
        {
            return self::Fail('耗材领用单已全部出库，不可再次出库', 44002);
        }

        // 获取耗材领用单商品明细
        $getConsumablesOrderItemRes = StockConsumablesReceiveOrderItemModel::getData(where: [
                                                                                                'receive_order_id'     => $receiveOrderId,
                                                                                                'is_outbound_complete' => 0,
                                                                                                'status'               => 1
                                                                                            ]);
        if (empty($getConsumablesOrderItemRes))
        {
            return self::Fail('耗材领用单商品不存在', 44001);
        }

        $outboundItemData = [];
        foreach ($getConsumablesOrderItemRes as $curConsumablesItem)
        {
            if (empty($curConsumablesItem['item_id']) || empty($curConsumablesItem['item_barcode']))
            {
                return self::Fail('耗材领用出库，商品信息异常', 44001);
            }
            if ($curConsumablesItem['pack_quantity'] <= 0 && $curConsumablesItem['bulk_quantity'] <= 0)
            {
                return self::Fail('耗材领用出库，商品数量异常', 44001);
            }

            $outboundItemData[] = [
                'itemId'           => $curConsumablesItem['item_id'],
                'itemBarcode'      => $curConsumablesItem['item_barcode'],
                'packQuantity'     => numberSub([$curConsumablesItem['pack_quantity'], $curConsumablesItem['outbound_pack_quantity']]),
                'bulkQuantity'     => numberSub([$curConsumablesItem['bulk_quantity'], $curConsumablesItem['outbound_bulk_quantity']]),
                'relationCode'     => $getConsumablesOrderRes['receive_code'],
                'relationId'       => $receiveOrderId,
                'relationDetailId' => $curConsumablesItem['id'],
                'reduceType'       => StockReduceTypeEnum::ConsumablesUse->value,
                'remark'           => '耗材领用出库',
            ];
        }

        // 无有效耗材商品
        if (empty($outboundItemData))
        {
            return self::Fail('耗材领用出库，无有效耗材商品需出库', 44001);
        }

        try
        {
            DB::beginTransaction();

            // 耗材出库
            $getReduceStockRes = StockItemShelfReduceLogic::ReduceStockShelfQuantity($outboundItemData, $publicParams);
            if ($getReduceStockRes->isFail())
            {
                DB::rollBack();

                return $getReduceStockRes;
            }

            $outboundTotalPrice = 0;
            foreach ($getConsumablesOrderItemRes as $curConsumablesItem)
            {
                $curReduceInfo = $getReduceStockRes->getData($curConsumablesItem['item_id'], []);
                if (empty($curReduceInfo))
                {
                    DB::rollBack();

                    return self::Fail("耗材领用出库，商品【{$curConsumablesItem['item_barcode']}】出库失败", 45004);
                }

                // 更新耗材领用单商品出库信息，此处更新为全部出库完成。目前出库是全部出。
                $upOutboundData = [
                    'outbound_pack_quantity' => $curReduceInfo['reducePackQuantity'],
                    'outbound_bulk_quantity' => $curReduceInfo['reduceBulkQuantity'],
                    'is_outbound_complete'   => 1,
                ];
                StockConsumablesReceiveOrderItemModel::updateOne($curConsumablesItem['id'], $upOutboundData);

                // 累计出库总金额
                $outboundTotalPrice = numberAdd([
                                                    $outboundTotalPrice,
                                                    numberMul([$curReduceInfo['reducePackQuantity'], $curReduceInfo['packDailyPrice']], 4),
                                                    numberMul([$curReduceInfo['reduceBulkQuantity'], $curReduceInfo['bulkDailyPrice']], 4)
                                                ], 4);
            }

            // 更新领用单出库状态
            StockConsumablesReceiveOrderModel::updateOne($receiveOrderId,
                                                         [
                                                             'outbound_price'       => $outboundTotalPrice,
                                                             'outbound_status'      => StockOutboundStatusEnum::FullyOutbound->value,
                                                             'outbound_complete_at' => getCurrentTimeWithMilliseconds()
                                                         ]);

            DB::commit();

            return self::Success();
        } catch (Throwable $throwable)
        {
            DB::rollBack();
            Log::error(__CLASS__ . '::' . __METHOD__ . ' 耗材领用出库异常', [
                'code'    => $throwable->getCode(),
                'message' => $throwable->getMessage(),
                'file'    => $throwable->getFile(),
                'line'    => $throwable->getLine(),
                'trace'   => $throwable->getTraceAsString(),
            ]);

            return self::Fail('耗材领用出库异常', 45004);
        }
    }

    /**
     * 删除耗材领用单
     *
     * @param int   $receiveOrderId
     * @param array $publicParams
     *
     * @return LogicResult
     * @throws Throwable
     */
    public static function DeleteConsumablesReceiveOrder(int $receiveOrderId, array $publicParams): LogicResult
    {
        if (empty($receiveOrderId))
        {
            return self::Fail('删除耗材领用单，缺少耗材单ID必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('删除耗材领用单，缺少公共必选参数', 400);
        }

        // 公共参数
        $userId = intval(Arr::get($publicParams, '_userId'));
        if (empty($userId))
        {
            return self::Fail('删除耗材领用单，缺少用户ID必选参数', 400);
        }

        // 获取耗材领用单信息
        $getConsumablesOrderRes = self::GetValidConsumablesReceiveOrder($receiveOrderId, $publicParams);
        if ($getConsumablesOrderRes->isFail())
        {
            return $getConsumablesOrderRes;
        }

        $getConsumablesOrderRes = $getConsumablesOrderRes->getData();
        $receiveOrderCode       = $getConsumablesOrderRes['receive_code'];

        // 验证耗材单单是否可以删除
        $getCheckEditAbleRes = self::CheckEditAbleConsumablesReceiveOrder($getConsumablesOrderRes, $publicParams);
        if ($getCheckEditAbleRes->isFail())
        {
            return $getCheckEditAbleRes;
        }

        try
        {
            DB::beginTransaction();

            // 删除退货单
            StockConsumablesReceiveOrderModel::updateOne($receiveOrderId, ['status' => 0, 'deleted_at' => getCurrentTimeWithMilliseconds()]);

            // 记录日志
            $operationDesc       = ["删除耗材领用单：$receiveOrderCode"];
            $insertOperationData = [
                'receive_order_id'    => $receiveOrderId,
                'receive_order_code'  => $receiveOrderCode,
                'operation_desc'      => json_encode($operationDesc, JSON_UNESCAPED_UNICODE),
                'operation_user_id'   => $userId,
                'operation_user_name' => UsersModel::getOneUserNameById($userId),
                'operation_channel'   => 1,
            ];
            StockConsumablesReceiveOrderOperationLogModel::insert($insertOperationData);

            DB::commit();

            return self::Success();
        } catch (Throwable $throwable)
        {
            DB::rollBack();

            Log::error(__CLASS__ . '::' . __METHOD__ . ' 删除耗材领用单异常', [
                'code'    => $throwable->getCode(),
                'message' => $throwable->getMessage(),
                'file'    => $throwable->getFile(),
                'line'    => $throwable->getLine(),
                'trace'   => $throwable->getTraceAsString(),
            ]);

            return self::Fail('删除耗材领用单异常', 44025);
        }
    }

    /**
     * 添加耗材领用单商品
     *
     * @param array $receiveOrderInfo
     * @param array $consumablesItems
     * @param array $publicParams
     *
     * @return LogicResult
     * @throws Throwable
     */
    private static function InsertConsumablesItem(array $receiveOrderInfo, array $consumablesItems, array $publicParams): LogicResult
    {
        if (empty($receiveOrderInfo))
        {
            return self::Fail('添加耗材领用单商品，缺少领用单主信息参数', 400);
        }
        if (empty($consumablesItems))
        {
            return self::Fail('添加耗材领用单商品，缺少领用单商品必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('添加耗材领用单商品，缺少公共必选参数', 400);
        }

        // 公共参数
        $userId = intval(Arr::get($publicParams, '_userId'));
        if (empty($userId))
        {
            return self::Fail('添加耗材领用单商品，缺少用户ID必选参数', 400);
        }

        // 业务参数
        $receiveOrderId   = intval(Arr::get($receiveOrderInfo, 'receiveOrderId', 0));
        $receiveOrderCode = Arr::get($receiveOrderInfo, 'receiveCode', '');
        if (empty($receiveOrderId) || empty($receiveOrderCode))
        {
            return self::Fail('添加耗材领用单商品，缺少领用单ID、领用单号必选参数', 400);
        }

        $insertConsumablesItemData = [];
        foreach ($consumablesItems as $curConsumablesItem)
        {
            $insertConsumablesItemData[] = [
                'uid'              => generateUUID(),
                'receive_order_id' => $receiveOrderId,
                'item_id'          => $curConsumablesItem['itemId'],
                'item_barcode'     => $curConsumablesItem['itemBarcode'],
                'item_bulk_ratio'  => $curConsumablesItem['itemInfo']['bulk_ratio'] ?? 1,
                'pack_price'       => $curConsumablesItem['packPrice'],
                'bulk_price'       => $curConsumablesItem['bulkPrice'],
                'pack_quantity'    => $curConsumablesItem['packQuantity'],
                'bulk_quantity'    => $curConsumablesItem['bulkQuantity'],
            ];
        }

        try
        {
            DB::beginTransaction();

            // 添加商品
            StockConsumablesReceiveOrderItemModel::insert($insertConsumablesItemData);

            // 记录日志
            $operationDesc = [];
            array_walk($consumablesItems, function ($item) use (&$operationDesc) {
                $operationDesc[] = "添加商品：【{$item['itemInfo']['id']}】{$item['itemName']}";
            });

            $insertOperationData = [
                'receive_order_id'    => $receiveOrderId,
                'receive_order_code'  => $receiveOrderCode,
                'operation_desc'      => json_encode($operationDesc, JSON_UNESCAPED_UNICODE),
                'operation_user_id'   => $userId,
                'operation_user_name' => UsersModel::getOneUserNameById($userId),
                'operation_channel'   => 1,
            ];
            StockConsumablesReceiveOrderOperationLogModel::insert($insertOperationData);

            DB::commit();

            return self::Success();
        } catch (Throwable $throwable)
        {
            DB::rollBack();
            Log::error(__CLASS__ . '::' . __METHOD__ . ' 添加耗材领用单商品异常', [
                'code'    => $throwable->getCode(),
                'message' => $throwable->getMessage(),
                'file'    => $throwable->getFile(),
                'line'    => $throwable->getLine(),
                'trace'   => $throwable->getTraceAsString(),
            ]);

            return self::Fail('添加耗材领用单商品异常', 44023);
        }
    }

    /**
     * 验证耗材领用单是否可编辑
     *
     * @param array $consumablesReceiveOrderInfo
     * @param array $publicParams
     *
     * @return LogicResult
     */
    private static function CheckEditAbleConsumablesReceiveOrder(array $consumablesReceiveOrderInfo, array $publicParams): LogicResult
    {
        if (empty($consumablesReceiveOrderInfo))
        {
            return self::Fail('验证耗材领用单是否可操作，缺少耗材单信息必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('验证耗材领用单是否可操作，缺少公共必选参数', 400);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        if (empty($hospitalId))
        {
            return self::Fail('验证耗材领用单是否可操作，缺少医院ID必选参数', 400);
        }

        if ($consumablesReceiveOrderInfo['status'] != 1)
        {
            return self::Fail('领用单已作废或无效，不可操作', 44024);
        }
        if ($consumablesReceiveOrderInfo['hospital_id'] != $hospitalId)
        {
            return self::Fail('领用单单医院不一致，不可操作', 44024);
        }

        return self::Success();
    }

    /**
     * 格式化耗材领用单列表
     *
     * @param array $consumablesOrderList
     *
     * @return LogicResult
     */
    private static function FormatConsumablesOrderStructure(array $consumablesOrderList): LogicResult
    {
        if (empty($consumablesOrderList))
        {
            return self::Success();
        }

        $returnConsumablesOrderList = [];
        foreach ($consumablesOrderList as $curConsumablesOrder)
        {
            $returnConsumablesOrderList[] = [
                'receiveCode'    => $curConsumablesOrder['receive_code'],
                'receivePrice'   => formatDisplayNumber($curConsumablesOrder['receive_price'], 4),
                'outboundPrice'  => formatDisplayNumber($curConsumablesOrder['outbound_price'], 4),
                'status'         => [
                    'id'   => $curConsumablesOrder['status'],
                    'name' => $curConsumablesOrder['status'] == 1 ? '正常' : '作废',
                ],
                'outboundStatus' => [
                    'id'   => $curConsumablesOrder['outbound_status'],
                    'name' => StockOutboundStatusEnum::getDescription($curConsumablesOrder['outbound_status']),
                ],
                'createUser'     => [
                    'uid'  => $curConsumablesOrder['user_uid'],
                    'name' => $curConsumablesOrder['user_name'],
                ],
                'createTime'     => formatDisplayDateTime($curConsumablesOrder['created_at']),
                'remark'         => $curConsumablesOrder['remark'],
            ];
        }

        return self::Success($returnConsumablesOrderList);
    }

    /**
     * 格式化耗材领用单商品列表
     *
     * @param array $consumablesItemList
     * @param array $itemInfoList
     *
     * @return LogicResult
     */
    private static function FormatConsumablesOrderItemStructure(array $consumablesItemList, array $itemInfoList): LogicResult
    {
        if (empty($consumablesItemList) || empty($itemInfoList))
        {
            return self::Success();
        }

        // 按照商品ID数组
        $itemInfoList = array_column($itemInfoList, null, 'id');

        // 按照商品ID数组
        $itemInfoList = array_column($itemInfoList, null, 'id');

        $returnConsumablesItemList = [];
        foreach ($consumablesItemList as $curConsumablesItem)
        {
            // 商品信息
            $curItemInfo = $itemInfoList[$curConsumablesItem['item_id']] ?? [];
            if (empty($curItemInfo))
            {
                continue;
            }

            $returnConsumablesItemList[] = [
                'uid'                  => $curConsumablesItem['uid'],
                'itemUid'              => $curItemInfo['uid'],
                'itemBarcode'          => $curConsumablesItem['item_barcode'],
                'itemName'             => ItemHelper::ItemDisplayName($curItemInfo),
                'packQuantity'         => formatDisplayNumber($curConsumablesItem['pack_quantity']),
                'bulkQuantity'         => formatDisplayNumber($curConsumablesItem['bulk_quantity']),
                'outboundPackQuantity' => formatDisplayNumber($curConsumablesItem['outbound_pack_quantity']),
                'outboundBulkQuantity' => formatDisplayNumber($curConsumablesItem['outbound_bulk_quantity']),
                'isOutboundComplete'   => $curConsumablesItem['is_outbound_complete'],
                'itemInfo'             => ItemHelper::FormatItemInfoStructure($curItemInfo)
            ];
        }

        return self::Success($returnConsumablesItemList);
    }
}
