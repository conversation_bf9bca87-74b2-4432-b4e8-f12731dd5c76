<?php

namespace App\Logics\V1;

use App\Enums\RedisKeyEnum;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Logics\Logic;
use App\Logics\LogicResult;
use App\Enums\LoginTypeEnum;
use App\Enums\UserSmsTypeEnum;
use App\Facades\TokenFacade;
use App\Models\UserLoginLogModel;
use Illuminate\Support\Facades\Redis;
use App\Support\Mqtt\MqttHisTopicHelper;
use Throwable;

class LoginLogic extends Logic
{
    //TODO:临时方案，需要删除
    private const string MENU_JSON = <<<menu
[
  {
    "id": "1000",
    "name": "首页",
    "path": "/",
    "icon": "IconFluentMdl2Home",
    "type": "menu",
    "children": [
      {
        "id": "1001",
        "name": "系统首页",
        "path": "/dashboard",
        "icon": "House",
        "type": "menu",
        "children": []
      }
    ]
  },
  {
    "id": "2000",
    "name": "前台接待",
    "path": "/reception",
    "icon": "IconFluentMdl2ContactHeart",
    "type": "menu",
    "children": [
      {
        "id": "2001",
        "name": "接待收银",
        "path": "/reception/reception",
        "icon": "Money",
        "type": "menu",
        "children": []
      },
      {
        "id": "2002",
        "name": "收款记录",
        "path": "/reception/orders",
        "icon": "DocumentChecked",
        "type": "menu",
        "children": []
      },
      {
        "id": "2003",
        "name": "退款记录",
        "path": "/reception/refunds",
        "icon": "DocumentRemove",
        "type": "menu",
        "children": []
      },
      {
        "id": "2004",
        "name": "客户管理",
        "path": "/reception/customer",
        "icon": "User",
        "type": "menu",
        "children": []
      },
      {
        "id": "2005",
        "name": "预约管理",
        "path": "/reception/appointments",
        "icon": "AlarmClock",
        "type": "menu",
        "children": []
      },
      {
        "id": "2006",
        "name": "转诊单",
        "path": "/reception/transfer",
        "icon": "Switch",
        "type": "menu",
        "children": []
      }
    ]
  },
  {
    "id": "3000",
    "name": "医生诊疗",
    "path": "/medical",
    "icon": "IconFluentMdl2Medical",
    "type": "menu",
    "children": [
      {
        "id": "3001",
        "name": "门诊工作台",
        "path": "/medical/outpatient",
        "icon": "Notification",
        "type": "menu",
        "children": []
      },
      {
        "id": "3002",
        "name": "住院工作台",
        "path": "/medical/inpatient",
        "icon": "Connection",
        "type": "menu",
        "children": []
      },
      {
        "id": "3003",
        "name": "病历管理",
        "path": "/medical/case",
        "icon": "Files",
        "type": "menu",
        "children": []
      }
    ]
  },
  {
    "id": "4000",
    "name": "检测中心",
    "path": "/lab",
    "icon": "IconFluentMdl2TestBeaker",
    "type": "menu",
    "children": [
      {
        "id": "4001",
        "name": "化验检测",
        "path": "/lab/test",
        "icon": "MagicStick",
        "type": "menu",
        "children": []
      },
      {
        "id": "4002",
        "name": "影像检测",
        "path": "/lab/image",
        "icon": "Camera",
        "type": "menu",
        "children": []
      }
    ]
  },
  {
    "id": "5000",
    "name": "处置中心",
    "path": "/treatment",
    "icon": "IconFluentMdl2Vaccination",
    "type": "menu",
    "children": [
      {
        "id": "5001",
        "name": "待执行处置",
        "path": "/treatment/task",
        "icon": "List",
        "type": "menu",
        "children": []
      },
      {
        "id": "5002",
        "name": "已执行处置",
        "path": "/treatment/record",
        "icon": "Checked",
        "type": "menu",
        "children": []
      }
    ]
  },
  {
    "id": "6000",
    "name": "美容洗澡",
    "path": "/beauty",
    "icon": "IconFluentMdl2Cut",
    "type": "menu",
    "children": [
      {
        "id": "6001",
        "name": "服务开单",
        "path": "/beauty/service",
        "icon": "Scissor",
        "type": "menu",
        "children": []
      },
      {
        "id": "6002",
        "name": "服务记录",
        "path": "/beauty/record",
        "icon": "Checked",
        "type": "menu",
        "children": []
      }
    ]
  },
  {
    "id": "7000",
    "name": "住院管理",
    "path": "/inpatient",
    "icon": "IconFluentMdl2Hotel",
    "type": "menu",
    "children": [
      {
        "id": "7001",
        "name": "住院概览",
        "path": "/inpatient/summary",
        "icon": "FullScreen",
        "type": "menu",
        "children": []
      },
      {
        "id": "7002",
        "name": "住院监护",
        "path": "/inpatient/monitor",
        "icon": "View",
        "type": "menu",
        "children": []
      },
      {
        "id": "7003",
        "name": "住院部管理",
        "path": "/inpatient/manage",
        "icon": "Grid",
        "type": "menu",
        "children": []
      }
    ]
  },
  {
    "id": "8000",
    "name": "药房仓储",
    "path": "/stock",
    "icon": "IconFluentMdl2Pill",
    "type": "menu",
    "children": [
      {
        "id": "8001",
        "name": "拣货出库",
        "path": "/stock/picking",
        "icon": "ShoppingCartFull",
        "type": "menu",
        "children": []
      },
      {
        "id": "8002",
        "name": "耗材出库",
        "path": "/stock/consumables",
        "icon": "ToiletPaper",
        "type": "menu",
        "children": []
      },
      {
        "id": "8003",
        "name": "调拨出库",
        "path": "/stock/transfer",
        "icon": "Switch",
        "type": "menu",
        "children": []
      },
      {
        "id": "8004",
        "name": "退货出库",
        "path": "/stock/refund",
        "icon": "DocumentRemove",
        "type": "menu",
        "children": []
      },
      {
        "id": "8005",
        "name": "采购管理",
        "path": "/stock/purchase",
        "icon": "EditPen",
        "type": "menu",
        "children": []
      },
      {
        "id": "8006",
        "name": "到货签收",
        "path": "/stock/receive",
        "icon": "Finished",
        "type": "menu",
        "children": []
      },
      {
        "id": "8007",
        "name": "到货入库",
        "path": "/stock/inbound",
        "icon": "FolderAdd",
        "type": "menu",
        "children": []
      },
      {
        "id": "8008",
        "name": "库存查询",
        "path": "/stock/query",
        "icon": "Search",
        "type": "menu",
        "children": []
      },
      {
        "id": "8009",
        "name": "药房盘点",
        "path": "/stock/check",
        "icon": "DocumentChecked",
        "type": "menu",
        "children": []
      },
      {
        "id": "8010",
        "name": "药房货位管理",
        "path": "/stock/warehouse",
        "icon": "Menu",
        "type": "menu",
        "children": []
      },
      {
        "id": "8011",
        "name": "出⼊库记录",
        "path": "/stock/record",
        "icon": "Checked",
        "type": "menu",
        "children": []
      }
    ]
  },
  {
    "id": "9000",
    "name": "经营报表",
    "path": "/report",
    "icon": "IconFluentMdl2Chart",
    "type": "menu",
    "children": [
      {
        "id": "9001",
        "name": "营业日报表",
        "path": "/report/daily",
        "icon": "PieChart",
        "type": "menu",
        "children": []
      },
      {
        "id": "9002",
        "name": "业绩统计报表",
        "path": "/report/performance",
        "icon": "PieChart",
        "type": "menu",
        "children": []
      },
      {
        "id": "9003",
        "name": "医疗组成报表",
        "path": "/report/medical",
        "icon": "PieChart",
        "type": "menu",
        "children": []
      },
      {
        "id": "9004",
        "name": "个人产出报表",
        "path": "/report/individual",
        "icon": "PieChart",
        "type": "menu",
        "children": []
      },
      {
        "id": "9005",
        "name": "药房仓储报表",
        "path": "/report/customer",
        "icon": "PieChart",
        "type": "menu",
        "children": []
      }
    ]
  },
  {
    "id": "10000",
    "name": "员工管理",
    "path": "/users",
    "icon": "IconFluentMdl2PlayerSettings",
    "type": "menu",
    "children": [
      {
        "id": "10001",
        "name": "员工管理",
        "path": "/users/employee",
        "icon": "User",
        "type": "menu",
        "children": []
      },
      {
        "id": "10002",
        "name": "排班管理",
        "path": "/users/schedule",
        "icon": "Calendar",
        "type": "menu",
        "children": []
      }
    ]
  },
  {
    "id": "11000",
    "name": "经营管理",
    "path": "/manage",
    "icon": "IconFluentMdl2EditNote",
    "type": "menu",
    "children": [
      {
        "id": "11001",
        "name": "项目价格管理",
        "path": "/manage/price",
        "icon": "PriceTag",
        "type": "menu",
        "children": []
      },
      {
        "id": "11002",
        "name": "处方模板管理",
        "path": "/manage/recipe",
        "icon": "Files",
        "type": "menu",
        "children": []
      },
      {
        "id": "11003",
        "name": "化验耗材管理",
        "path": "/manage/consumables",
        "icon": "Filter",
        "type": "menu",
        "children": []
      },
      {
        "id": "11004",
        "name": "医疗文书管理",
        "path": "/manage/document",
        "icon": "Files",
        "type": "menu",
        "children": []
      }
    ]
  },
  {
    "id": "12000",
    "name": "系统设置",
    "path": "/system",
    "icon": "IconFluentMdl2Settings",
    "type": "menu",
    "children": []
  }
]
menu;

    /**
     * 验证码登录
     *
     * @param array $params
     * @param array $publicParams
     *
     * @return LogicResult
     * @throws Throwable
     */
    public static function LoginFromCode(array $params, array $publicParams): LogicResult
    {
        $phone      = trim(Arr::get($params, 'phone', ''));
        $verifyCode = trim(Arr::get($params, 'verifyCode', ''));
        $ip         = trim(Arr::get($params, 'ip', ''));


        if ($phone == '' && $verifyCode == '')
        {
            return self::Fail('验证码登录，缺少必选参数', 400);
        }

        if (!checkValidCellphone($phone))
        {
            return self::Fail('用户手机号格式错误', 10000);
        }

        $verifyData       = [
            'phone' => $phone,
            'type'  => UserSmsTypeEnum::Login->value,
            'code'  => $verifyCode,
        ];
        $verifyCodeResult = UserVerifySmsLogic::CheckVerifyCode($verifyData, $publicParams);
        if ($verifyCodeResult->isFail())
        {
            return $verifyCodeResult;
        }

        //用户是否存在
        $userGetResult = UserLogic::GetUser(phone: $phone, withId: true);
        $isNewUser     = 0;
        if ($userGetResult->isFail())
        {
            //新增用户
            $addUserResult = UserLogic::AddUser([
                                                    'phone' => $phone,
                                                ]);
            if ($addUserResult->isFail())
            {
                return self::Fail('验证码登录时用户新增失败', $addUserResult->getCode());
            }

            $isNewUser     = 1;
            $userGetResult = UserLogic::GetUser(phone: $phone, withId: true, onWritePdo: true);
            if ($userGetResult->isFail())
            {
                return self::Fail('验证码登录时新增用户后再次获取用户失败', $userGetResult->getCode());
            }
        }

        $userAvailableHospitals = HospitalUserLogic::GetUserAvailableHospitals(userId       : $userGetResult->getData('id'),
                                                                               withRecommend: true);
        if ($userAvailableHospitals->isFail())
        {
            return $userAvailableHospitals;
        }

        $data = [
            'userId'    => $userGetResult->getData('id'),
            'userUid'   => $userGetResult->getData('uid'),
            'loginType' => LoginTypeEnum::Code->value,
            'ip'        => $ip,
            'params'    => ['phone' => $phone, 'code' => $verifyCode, 'isNewUser' => $isNewUser],
        ];

        $loginResult = self::DoLogin($data, $publicParams);
        if ($loginResult->isFail())
        {
            return $loginResult;
        }

        $loginInfo = [
            'token'      => $loginResult->getData('token'),
            'firstLogin' => false,//TODO:判断是否的第一次登录该系统，主要判断是否完成了某些初始化工作
            'user'       => $userGetResult->getData(except: ['id']),
            'hospitals'  => $userAvailableHospitals->getData('hospitals'),
        ];

        return self::Success($loginInfo);
    }

    /**
     * 密码登录
     *
     * @param array $params
     * @param array $publicParams
     *
     * @return LogicResult
     * @throws Throwable
     */
    public static function LoginFromPassword(array $params, array $publicParams): LogicResult
    {
        $phone    = trim(Arr::get($params, 'phone', ''));
        $password = trim(Arr::get($params, 'password', ''));
        $ip       = trim(Arr::get($params, 'ip', ''));


        if ($phone == '' && $password == '')
        {
            return self::Fail('密码登录，缺少必选参数', 400);
        }

        if (!checkValidCellphone($phone))
        {
            return self::Fail('用户手机号格式错误', 10000);
        }

        //用户是否存在
        $userGetResult = UserLogic::GetUser(phone: $phone, withId: true, withPassword: true);
        if ($userGetResult->isFail())
        {
            return $userGetResult;
        }

        //验证密码错误限制是否满足
        $checkPasswordLimitResult = self::CheckPasswordLimit($phone);
        if ($checkPasswordLimitResult->isFail())
        {
            return $checkPasswordLimitResult;
        }

        //验证密码
        if (password_verify($password, $userGetResult->getData('password')) === false)
        {
            //记录错误次数
            self::AddPasswordErrorTimes($phone);

            return self::Fail('登录失败，密码错误', 10191);
        }

        $userAvailableHospitals = HospitalUserLogic::GetUserAvailableHospitals(userId       : $userGetResult->getData('id'),
                                                                               withRecommend: true);
        if ($userAvailableHospitals->isFail())
        {
            return $userAvailableHospitals;
        }

        $data = [
            'userId'    => $userGetResult->getData('id'),
            'userUid'   => $userGetResult->getData('uid'),
            'loginType' => LoginTypeEnum::Password->value,
            'ip'        => $ip,
            'params'    => ['phone' => $phone, 'password' => '', 'isNewUser' => 0],
        ];

        $loginResult = self::DoLogin($data, $publicParams);
        if ($loginResult->isFail())
        {
            return $loginResult;
        }

        $loginInfo = [
            'token'      => $loginResult->getData('token'),
            'firstLogin' => false,//TODO:判断是否的第一次登录该系统，主要判断是否完成了某些初始化工作
            'user'       => $userGetResult->getData(except: ['id']),
            'hospitals'  => $userAvailableHospitals->getData('hospitals'),
        ];

        return self::Success($loginInfo);
    }

    /**
     * 登录
     *
     * 创建会话
     * 记录登录日志
     *
     * @param array $params
     * @param array $publicParams
     *
     * @return LogicResult
     * @throws Throwable
     */
    public static function DoLogin(array $params, array $publicParams): LogicResult
    {
        $userId          = intval(Arr::get($params, 'userId', 0));//用户本人账户ID
        $userUid         = trim(Arr::get($params, 'userUid', ''));//用户本人账户UID
        $hospitalId      = intval(Arr::get($params, 'hospitalId', 0));
        $hospitalUid     = trim(Arr::get($params, 'hospitalUid', ''));
        $hospitalUserId  = intval(Arr::get($params, 'hospitalUserId', 0));//用户在当前医院下的账户ID，记录入医院信息的用户ID，必须用此ID
        $hospitalUserUid = trim(Arr::get($params, 'hospitalUserUid', ''));//用户在当前医院下的账户UID
        $loginType       = intval(Arr::get($params, 'loginType', 0));
        $ip              = trim(Arr::get($params, 'ip', ''));
        $payload         = Arr::get($params, 'payload', []);//合并到session存储,key-value
        $loginParams     = Arr::get($params, 'params', []);//记录登录时的附件信息到登录日志

        if ($userId <= 0 || $userUid == '' || !LoginTypeEnum::exists($loginType))
        {
            return self::Fail('用户登录缺少必选参数', 400);
        }

        if (($hospitalId > 0 && $hospitalUid == '') || ($hospitalId <= 0 && $hospitalUid != ''))
        {
            return self::Fail('用户登录缺少必选参数，医院ID不完整', 400);
        }
        if (($hospitalUserId > 0 && $hospitalUserUid == '') || ($hospitalUserId <= 0 && $hospitalUserUid != ''))
        {
            return self::Fail('用户登录缺少必选参数，医院用户ID不完整', 400);
        }

        $sessionData = [
            ...$payload,
            'userId'          => $userId,//登录后，必须设置
            'userUid'         => $userUid,//登录后，必须设置
            'hospitalUserId'  => $hospitalUserId,//登录并选择医院后，必须设置
            'hospitalUserUid' => $hospitalUserUid,//登录并选择医院后，必须设置
            'hospitalId'      => $hospitalId,//登录并选择医院后，必须设置
            'hospitalUid'     => $hospitalUid,//登录并选择医院后，必须设置
            'version'         => trim(Arr::get($publicParams, 'version', '')),
            'ip'              => $ip,
            'loginLogId'      => 0,//记录本次登录记录ID，用于回写后续选择的医院
        ];

        $loginLogData = [
            'token'       => '',
            'user_id'     => $userId,
            'hospital_id' => $hospitalId,
            'params'      => json_encode($loginParams),
            'ip'          => $ip,
            'login_type'  => $loginType,
        ];

        try
        {
            DB::beginTransaction();

            //生成新会话
            $newToken = TokenFacade::start(uid: $userId, data: $sessionData);
            if (!$newToken)
            {
                DB::rollBack();

                return self::Fail('创建会话失败', 10190);
            }

            $loginLogData['token'] = $newToken;

            //插入登录记录
            $insertLogId = UserLoginLogModel::insertOne($loginLogData);
            if ($insertLogId <= 0)
            {
                DB::rollBack();
                Log::error(__CLASS__ . '::' . __METHOD__ . ' insert user login log fail', $loginLogData);

                return self::Fail('登录记录写入失败', 10190);
            }

            //更新会话本次登录记录ID值
            TokenFacade::push($newToken, 'loginLogId', $insertLogId);

            //TODO:发布登录成功的事件

            DB::commit();

            return self::Success(['token' => $newToken]);
        } catch (Throwable $throwable)
        {
            DB::rollBack();
            Log::error(__CLASS__ . '::' . __METHOD__ . ' cause exception', [
                'code'    => $throwable->getCode(),
                'message' => $throwable->getMessage(),
                'file'    => $throwable->getFile(),
                'line'    => $throwable->getLine(),
                'trace'   => $throwable->getTraceAsString(),
            ]);

            return self::Fail('登录异常', 10190);
        }
    }

    /**
     * 登录后选择医院
     *
     * 记入session
     * 修改login_log表中的医院ID
     *
     * @param array $params
     * @param array $publicParams
     *
     * @return LogicResult
     * @throws Throwable
     */
    public static function LoginChooseHospital(array $params, array $publicParams): LogicResult
    {
        $hospitalUid = trim(Arr::get($params, 'hospitalUid', ''));

        $userId = Arr::has($params, 'userId') ? intval(Arr::get($params, 'userId')) : intval(Arr::get($publicParams,
                                                                                                      '_userId'));

        $token = Arr::has($params, 'token') ? trim(Arr::get($params, 'token')) : trim(Arr::get($publicParams, 'token'));

        if ($hospitalUid == '' || $userId <= 0 || $token == '')
        {
            return self::Fail('登录选择医院，缺少必选参数', 400);
        }

        $currentHospital = HospitalLogic::GetHospitalBaseInfo(uid: $hospitalUid, withId: true);
        if ($currentHospital->isFail())
        {
            return $currentHospital;
        }

        $currentHospitalUser = HospitalUserLogic::GetUserHospitalUser(userId    : $userId,
                                                                      hospitalId: $currentHospital->getData('id'),
                                                                      withId    : true);
        if ($currentHospitalUser->isFail())
        {
            return $currentHospitalUser;
        }

        try
        {
            DB::beginTransaction();

            $currentLoginLogId = TokenFacade::get($token, 'loginLogId', 0);
            if ($currentLoginLogId <= 0)
            {
                DB::rollBack();

                return self::Fail('登录选择医院失败，获取会话中loginLogId失败', 10196);
            }

            //修改会话中存储的信息
            $chooseResult = TokenFacade::put($token,
                                             [
                                                 'hospitalId'        => $currentHospital->getData('id'),
                                                 'hospitalUid'       => $currentHospital->getData('uid'),
                                                 'hospitalOrgId'     => $currentHospital->getData('orgId'),
                                                 'hospitalOrgUid'    => $currentHospital->getData('orgUid'),
                                                 'hospitalBrandId'   => $currentHospital->getData('brandId'),
                                                 'hospitalBrandCode' => $currentHospital->getData('brandCode'),
                                                 'hospitalUserId'    => $currentHospitalUser->getData('id'),
                                                 'hospitalUserUid'   => $currentHospitalUser->getData('uid'),
                                             ]);
            if (!$chooseResult)
            {
                DB::rollBack();

                return self::Fail('登录选择医院失败', 10196);
            }

            //修改登录日志
            UserLoginLogModel::updateOne($currentLoginLogId, ['hospital_id' => $currentHospital->getData('id')]);

            //TODO:发布登录选择医院成功的事件

            DB::commit();

            return self::Success([
                                     'hospital'    => $currentHospital->getData(except: ['id']),
                                     'menus'       => json_decode(self::MENU_JSON, true),//TODO:临时方案，需要删除
                                     'permissions' => [],
                                 ]);
        } catch (Throwable $throwable)
        {
            DB::rollBack();
            Log::error(__CLASS__ . '::' . __METHOD__ . ' cause exception', [
                'code'    => $throwable->getCode(),
                'message' => $throwable->getMessage(),
                'file'    => $throwable->getFile(),
                'line'    => $throwable->getLine(),
                'trace'   => $throwable->getTraceAsString(),
            ]);

            return self::Fail('登录选择医院异常', 10196);
        }
    }

    /**
     * 获取登录状态
     *
     * @param array $params
     * @param array $publicParams
     *
     * @return LogicResult
     */
    public static function LoginStatus(array $params, array $publicParams): LogicResult
    {
        $token = Arr::has($params, 'token') ? trim(Arr::get($params, 'token')) : trim(Arr::get($publicParams, 'token'));

        $userId         = intval(Arr::get($publicParams, '_userId'));
        $hospitalId     = intval(Arr::get($publicParams, '_hospitalId'));
        $hospitalUserId = intval(Arr::get($publicParams, '_hospitalUserId'));

        if ($token == '')
        {
            return self::Fail('获取登录状态，缺少必选参数', 400);
        }

        if ($userId <= 0)
        {
            return self::Fail('用户未登录', 1000);
        }

        if ($hospitalId <= 0 || $hospitalUserId <= 0)
        {
            return self::Fail('登录未完成，未选择医院', 1001);
        }

        $userResult = UserLogic::GetUser(userId: $userId);
        if ($userResult->isFail())
        {
            return $userResult;
        }

        $hospitalResult = HospitalLogic::GetHospitalBaseInfo(hospitalId: $hospitalId);
        if ($hospitalResult->isFail())
        {
            return $hospitalResult;
        }

        return self::Success([
	                             'user'        => $userResult->getData(),
	                             'hospital'    => $hospitalResult->getData(),
	                             'menus'       => json_decode(self::MENU_JSON, true),//TODO:临时方案，需要删除
	                             'permissions' => [],
	                             'topics'      => MqttHisTopicHelper::GetHisUserSubscribeTopics($publicParams),
                             ]);
    }

    /**
     * 退出登录
     *
     * @param array $params
     * @param array $publicParams
     *
     * @return LogicResult
     */
    public static function Logout(array $params, array $publicParams): LogicResult
    {
        $token = Arr::has($params, 'token') ? trim(Arr::get($params, 'token')) : trim(Arr::get($publicParams, 'token'));

        if ($token == '')
        {
            return self::Fail('退出登录，缺少必选参数', 400);
        }

        //修改登录日志
        UserLoginLogModel::updateOneByToken($token, ['logout_at' => getCurrentTimeWithMilliseconds()]);

        $result = TokenFacade::destroy($token);

        if (!$result)
        {
            return self::Fail('10199', 10199);
        }

        //TODO:发布退出登录成功的事件

        return self::Success();
    }

    /**
     * 验证密码限制是否满足
     *
     * @param string $phone
     *
     * @return LogicResult
     */
    private static function CheckPasswordLimit(string $phone): LogicResult
    {
        $passwordErrorTimes = Redis::connection(config('setting.user.redis_connection', 'user'))
                                   ->get(RedisKeyEnum::GetUserPasswordErrorKey($phone));
        \Log::debug('err', [$passwordErrorTimes]);
        if (intval($passwordErrorTimes) >= config('setting.user.password_error_times', 10))
        {
            return self::Fail('密码错误次数太多，请稍后再试', 10160);
        }

        return self::Success();
    }

    /**
     * 增加密码错误次数记录
     *
     * @param string $phone
     *
     * @return array
     */
    private static function AddPasswordErrorTimes(string $phone): array
    {
        return Redis::connection(config('setting.user.redis_connection', 'user'))
                    ->transaction(function (\Redis $redis) use ($phone) {
                        $redis->incr(RedisKeyEnum::GetUserPasswordErrorKey($phone), 1);
                        $redis->expire(RedisKeyEnum::GetUserPasswordErrorKey($phone),
                                       config('setting.user.password_error_duration', 3600));
                    });
    }

}
