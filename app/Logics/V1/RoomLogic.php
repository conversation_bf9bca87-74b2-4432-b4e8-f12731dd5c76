<?php

namespace App\Logics\V1;

use Arr;
use Throwable;
use App\Logics\Logic;
use App\Logics\LogicResult;
use App\Models\RoomsModel;
use App\Models\RoomsBedsModel;

class RoomLogic extends Logic
{
    /**
     * 获取住院部信息
     *
     * @param int $hospitalId
     *
     * @return LogicResult
     */
    public static function GetRoomsOptions(int $hospitalId): LogicResult
    {
        if (empty($hospitalId))
        {
            return self::Fail('获取医院住院部，缺少医院ID必选参数', 400);
        }

        // 获取住院部信息
        $getRoomsRes = RoomsModel::getData(where: ['hospital_id' => $hospitalId, 'status' => 1]);
        if (empty($getRoomsRes))
        {
            return self::Success(['roomsOptions' => []]);
        }

        $returnRoomsInfo = [];
        foreach ($getRoomsRes as $curInfo)
        {
            $returnRoomsInfo[] = [
                'id'   => $curInfo['id'],
                'name' => $curInfo['name'],
            ];
        }

        return self::Success(['roomsOptions' => $returnRoomsInfo]);
    }

    /**
     * 获取住院部、床位关系列表
     *
     * @param array $publicParams
     *
     * @return LogicResult
     */
    public static function GetRoomManageList(array $publicParams): LogicResult
    {
        if (empty($publicParams))
        {
            return self::Fail('获取住院部列表，缺少公共必选参数', 400);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        if (empty($hospitalId))
        {
            return self::Fail('获取住院部列表，缺少医院ID必选参数', 400);
        }

        $getRoomsRes = RoomsModel::getData(where: ['hospital_id' => $hospitalId, 'status' => 1]);
        if (empty($getRoomsRes))
        {
            return self::Success(['data' => []]);
        }

        $roomIds       = array_column($getRoomsRes, 'id');
        $getRoomBedRes = RoomsBedsModel::getData(whereIn: ['room_id' => $roomIds]);

        $returnRoomList = [];
        foreach ($getRoomsRes as $curRoom)
        {
            $tmpRoomInfo = [
                'roomUid'  => $curRoom['uid'],
                'roomName' => $curRoom['name'],
                'bedList'  => [],
            ];

            foreach ($getRoomBedRes as $curBed)
            {
                if ($curBed['room_id'] != $curRoom['id'])
                {
                    continue;
                }

                $curBedStatus = null;
                if (empty($curBed['status']))
                {
                    $curBedStatus = [
                        'id'   => 2,
                        'name' => '禁用',
                    ];
                }
                else
                {
                    if (!empty($curBed['used']))
                    {
                        $curBedStatus = [
                            'id'   => 1,
                            'name' => '占用',
                        ];
                    }
                    else
                    {
                        $curBedStatus = [
                            'id'   => 0,
                            'name' => '空闲',
                        ];
                    }
                }

                $tmpRoomInfo['bedList'][] = [
                    'bedUid'    => $curBed['uid'],
                    'bedName'   => $curBed['name'],
                    'bedStatus' => $curBedStatus,
                ];
            }

            $returnRoomList[] = $tmpRoomInfo;
        }

        return self::Success(['data' => $returnRoomList]);
    }

    /**
     * 获取有效住院部信息
     *
     * @param int   $roomId
     * @param array $publicParams
     *
     * @return LogicResult
     */
    public static function GetValidRoomById(int $roomId, array $publicParams): LogicResult
    {
        if (empty($roomId))
        {
            return self::Fail('获取有效住院部信息，缺少住院部ID必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('获取有效住院部信息，缺少公共必选参数', 400);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        if (empty($hospitalId))
        {
            return self::Fail('获取有效住院部信息，缺少医院ID必选参数', 400);
        }

        // 验证住院部是否存在
        $getRoomRes = RoomsModel::getData(where: [
                                                     'id'          => $roomId,
                                                     'hospital_id' => $hospitalId,
                                                     'status'      => 1
                                                 ]);
        $getRoomRes = $getRoomRes ? current($getRoomRes) : [];
        if (empty($getRoomRes))
        {
            return self::Fail('获取有效住院部信息，住院部不存在或已失效', 37000);
        }

        return self::Success($getRoomRes);
    }

    /**
     * 新增住院部
     *
     * @param array $addRoomParams
     * @param array $publicParams
     *
     * @return LogicResult
     * @throws Throwable
     */
    public static function AddRoom(array $addRoomParams, array $publicParams): LogicResult
    {
        if (empty($addRoomParams))
        {
            return self::Fail('新增住院部，缺少住院部必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('新增住院部，缺少公共必选参数', 400);
        }

        // 公共参数
        $hospitalOrgId   = intval(Arr::get($publicParams, '_hospitalOrgId'));
        $hospitalBrandId = intval(Arr::get($publicParams, '_hospitalBrandId'));
        $hospitalId      = intval(Arr::get($publicParams, '_hospitalId'));
        $userId          = intval(Arr::get($publicParams, '_userId'));
        if (empty($hospitalId) || empty($hospitalOrgId) || empty($hospitalBrandId) || empty($userId))
        {
            return self::Fail('新增住院部，缺少必选参数', 400);
        }

        // 业务参数
        $roomName = trimWhitespace($addRoomParams['name'] ?? '');
        if (empty($roomName))
        {
            return self::Fail('新增住院部，缺少住院部名称必选参数', 400);
        }

        // 检查住院部名称是否已存在
        $getExistRoomRes = RoomsModel::getData(where: ['name' => $roomName, 'hospital_id' => $hospitalId, 'status' => 1]);
        if (!empty($getExistRoomRes))
        {
            return self::Fail('住院部名称已存在', 37010);
        }


        // 插入住院部数据
        $insertRoomData = [
            'uid'         => generateUUID(),
            'name'        => $roomName,
            'org_id'      => $hospitalOrgId,
            'brand_id'    => $hospitalBrandId,
            'hospital_id' => $hospitalId,
            'status'      => 1,
            'created_by'  => $userId,
        ];
        RoomsModel::insertOne($insertRoomData);

        return self::Success();
    }

    /**
     * 编辑住院部
     *
     * @param int   $roomId
     * @param array $editRoomParams
     * @param array $publicParams
     *
     * @return LogicResult
     */
    public static function EditRoom(int $roomId, array $editRoomParams, array $publicParams): LogicResult
    {
        if (empty($roomId) || empty($editRoomParams))
        {
            return self::Fail('编辑住院部，缺少住院部必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('编辑住院部，缺少公共必选参数', 400);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        if (empty($hospitalId))
        {
            return self::Fail('编辑住院部，缺少医院ID必选参数', 400);
        }

        // 业务参数
        $roomName = trimWhitespace($editRoomParams['name'] ?? '');
        if (empty($roomName))
        {
            return self::Fail('编辑住院部，缺少住院部名称必选参数', 400);
        }

        // 验证住院部是否存在
        $getRoomRes = self::GetValidRoomById($roomId, $publicParams);
        if ($getRoomRes->isFail())
        {
            return $getRoomRes;
        }

        // 编辑住院部
        $updateRoomData = [
            'name' => $roomName,
        ];
        RoomsModel::updateOne($roomId, $updateRoomData);

        return self::Success();
    }

    /**
     * 删除住院部
     *
     * @param int   $roomId
     * @param array $publicParams
     *
     * @return LogicResult
     */
    public static function DeleteRoom(int $roomId, array $publicParams): LogicResult
    {
        if (empty($roomId))
        {
            return self::Fail('删除住院部，缺少住院部ID必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('删除住院部，缺少公共必选参数', 400);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        $userId     = intval(Arr::get($publicParams, '_userId'));
        if (empty($hospitalId) || empty($userId))
        {
            return self::Fail('删除住院部，缺少用户ID必选参数', 400);
        }

        // 验证住院部是否存在
        $getRoomRes = self::GetValidRoomById($roomId, $publicParams);
        if ($getRoomRes->isFail())
        {
            return $getRoomRes;
        }

        // 检查住院部下是否存在正在使用的床位
        $getUsedBedsTotalRes = RoomsBedsModel::getTotalNumber(['room_id' => $roomId, 'status' => 1, 'used' => 1]);
        if ($getUsedBedsTotalRes > 0)
        {
            return self::Fail('当前住院部存在正在使用的床位，不可删除', 37002);
        }

        // 删除住院部
        RoomsModel::updateOne($roomId, ['deleted_by' => $userId, 'deleted_at' => getCurrentTimeWithMilliseconds(), 'status' => 0]);

        return self::Success();
    }
}
