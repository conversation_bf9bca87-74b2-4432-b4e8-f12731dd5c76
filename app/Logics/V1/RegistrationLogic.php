<?php

namespace App\Logics\V1;

use DB;
use Arr;
use Log;
use Exception;
use Throwable;
use App\Logics\Logic;
use App\Logics\LogicResult;
use App\Enums\TransferStatusEnum;
use App\Enums\InpatientStatusEnum;
use App\Enums\OutpatientStatusEnum;
use App\Enums\RegistrationTypeEnum;
use App\Enums\RegistrationModeEnum;
use App\Enums\BusinessCodePrefixEnum;
use App\Enums\RegistrationSourceEnum;
use App\Enums\RegistrationStatusEnum;
use App\Enums\PetWeightSourceTypeEnum;
use App\Enums\RegistrationDoctorSelectedTypeEnum;
use App\Models\UsersModel;
use App\Models\InpatientModel;
use App\Models\TransfersModel;
use App\Models\OutpatientModel;
use App\Models\HospitalUserModel;
use App\Models\PetVitalSignModel;
use App\Models\RegistrationsModel;
use App\Models\RegistrationTypeModel;
use App\Models\ItemRegistrationModel;
use App\Models\RegistrationReasonModel;
use App\Models\RegistrationSourceModel;
use App\Models\RegistrationPetRecordModel;
use App\Support\Registration\RegistrationHelper;
use App\Support\Mqtt\MqttPublishMessageHelper;

class RegistrationLogic extends Logic
{
    /**
     * 获取医院挂号时可选择的用户
     *
     * @param int   $hospitalId
     * @param array $roleIds
     *
     * @return LogicResult
     */
    public static function GetHospitalRegistrationUsers(int $hospitalId, array $roleIds = []): LogicResult
    {
        if (empty($hospitalId))
        {
            return self::Fail('获取医院下医院用户，缺少必选参数', 400);
        }

        // TODO 获取医院下医生角色
        $getHospitalDoctorRes = HospitalUserModel::getHospitalUsers($hospitalId);
        if (empty($getHospitalDoctorRes))
        {
            return self::Success();
        }

        $getHospitalDoctorRes = $getHospitalDoctorRes->toArray();
        $userIds              = array_column($getHospitalDoctorRes, 'user_id');
        if (empty($userIds))
        {
            return self::Success();
        }

        // 获取医生等待接诊数
        $getDoctorOutpatientTotalInfoRes = OutpatientModel::getDoctorOutpatientTotalInfo($hospitalId, $userIds);

        $returnDoctorList = [];
        foreach ($getHospitalDoctorRes as $doctorInfo)
        {
            $curDoctorOutpatientTotalInfo = $getDoctorOutpatientTotalInfoRes[$doctorInfo['user_id']] ?? [];

            $returnDoctorList[] = [
                'uid'            => $doctorInfo['user_uid'],
                'doctorName'     => $doctorInfo['user_name'],
                'workTitle'      => $doctorInfo['work_title'],
                'waitingCount'   => $curDoctorOutpatientTotalInfo['waiting_count'] ?? 0,
                'treatmentCount' => ($curDoctorOutpatientTotalInfo['in_treatment_count'] ?? 0) + ($curDoctorOutpatientTotalInfo['stop_treatment_count'] ?? 0),
                'totalCount'     => $curDoctorOutpatientTotalInfo['total_count'] ?? 0,
            ];
        }

        return self::Success(['doctorList' => $returnDoctorList]);
    }

    /**
     * 获取会员宠物挂号列表
     *
     * @param int $memberId
     * @param int $hospitalId
     *
     * @return LogicResult
     * @throws Exception
     */
    public static function GetRegistrationPetList(int $memberId, int $hospitalId): LogicResult
    {
        if (empty($memberId) || empty($hospitalId))
        {
            return self::Fail('获取会员挂号宠物列表，缺少必选参数', 400);
        }

        // 获取有效会员信息
        $getMemberRes = MemberLogic::GetValidMemberByIdOrUid($memberId);
        if ($getMemberRes->isFail())
        {
            return $getMemberRes;
        }

        // 用户ID
        $memberId = $getMemberRes->getData('id', 0);

        // 获取用户宠物列表
        $getMemberPetListRes = PetLogic::SearchMemberPetList($memberId);
        if ($getMemberPetListRes->isFail())
        {
            return $getMemberPetListRes;
        }

        $memberPetLists = $getMemberPetListRes->getData('data', []);
        if (empty($memberPetLists))
        {
            return self::Success();
        }

        // 获取宠物有效的门诊记录
        $petIds              = array_column($memberPetLists, 'id');
        $getOutpatientPetRes = OutpatientModel::getOutpatientByPetId($hospitalId,
                                                                     $memberId,
                                                                     $petIds,
                                                                     OutpatientStatusEnum::VALID_STATUS);

        // 获取宠物有效的住院记录
        $getInpatientPetRes = InpatientModel::getInpatientByPetId($hospitalId,
                                                                  $memberId,
                                                                  $petIds,
                                                                  [InpatientStatusEnum::Inpatient->value]);

        foreach ($memberPetLists as $petKey => $petInfo)
        {
            $petId = $petInfo['id'];

            // 宠物默认挂号状态，0:未挂号；1:已挂号；2:住院中
            $curPetRegistrationStatus     = 0;
            $curPetRegistrationStatusName = '未挂号';
            $curPetDoctorName             = '';
            $curPetDoctorWorkTitle        = '';
            $curPetRegistrationTime       = '';

            // 当前宠物是否存在有效挂号
            $curPetOutpatientInfo = $getOutpatientPetRes->where('pet_id', $petId)
                                                        ->first();
            if (!empty($curPetOutpatientInfo))
            {
                $curPetRegistrationStatus     = 1;
                $curPetRegistrationStatusName = OutpatientStatusEnum::getDescription($curPetOutpatientInfo->status);
                $curPetDoctorName             = $curPetOutpatientInfo->doctor_name;
                $curPetDoctorWorkTitle        = $curPetOutpatientInfo->work_title;
                $curPetRegistrationTime       = formatDisplayDateTime($curPetOutpatientInfo->registration_time);
            }
            else
            {
                // 当前宠物是否存在有效住院
                $curPetInpatientInfo = $getInpatientPetRes->where('pet_id', $petId)
                                                          ->first();
                if (!empty($curPetInpatientInfo))
                {
                    $curPetRegistrationStatus     = 2;
                    $curPetRegistrationStatusName = InpatientStatusEnum::getDescription($curPetInpatientInfo->status);
                    $curPetDoctorName             = $curPetInpatientInfo->doctor_name;
                    $curPetDoctorWorkTitle        = $curPetInpatientInfo->work_title;
                    $curPetRegistrationTime       = formatDisplayDateTime($curPetInpatientInfo->registration_time);
                }
            }

            // 宠物存在挂号的基本信息
            $memberPetLists[$petKey]['registrationStatus'] = $curPetRegistrationStatus;
            $memberPetLists[$petKey]['registrationInfo']   = [
                'formatStatusName' => $curPetRegistrationStatusName,
                'workTitle'        => $curPetDoctorWorkTitle,
                'doctorName'       => $curPetDoctorName,
                'registrationTime' => $curPetRegistrationTime,
            ];
        }

        return self::Success(['petLists' => $memberPetLists]);
    }

    /**
     * 获取挂号优惠
     *
     * @param int   $memberId
     * @param int   $petId
     * @param int   $registrationReasonId
     * @param int   $registrationTypeId
     * @param array $publicParams
     * @param bool  $withItemId
     *
     * @return LogicResult
     */
    public static function GetRegistrationDiscount(int $memberId, int $petId, int $registrationReasonId, int $registrationTypeId, array $publicParams, bool $withItemId = false): LogicResult
    {
        if (empty($memberId) || empty($petId))
        {
            return self::Fail('挂号优惠，缺少人宠必选参数', 400);
        }
        if (empty($registrationReasonId) || empty($registrationTypeId))
        {
            return self::Fail('挂号优惠，缺少原因类型参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('挂号优惠，缺少公共参数', 400);
        }

        // 公共参数
        $hospitalOrgId = intval(Arr::get($publicParams, '_hospitalOrgId'));
        $hospitalId    = intval(Arr::get($publicParams, '_hospitalId'));

        // 获取有效会员信息
        $getMemberInfoRes = MemberLogic::GetValidMemberByIdOrUid($memberId);
        if ($getMemberInfoRes->isFail())
        {
            return $getMemberInfoRes;
        }

        // 获取挂号宠物信息
        $getPetInfoRes = PetLogic::GetValidPetByIdOrUid($petId);
        if ($getPetInfoRes->isFail())
        {
            return $getPetInfoRes;
        }

        $petMemberId = $getPetInfoRes->getData('member_id', 0);
        if ($petMemberId != $memberId)
        {
            return self::Fail('挂号宠物不属于当前用户', 32000);
        }

        // 当前医院是否可操作此用户
        $getAllowedBrandIdsRes = HospitalUserPermissionLogic::CheckHospitalMemberOperationPermission($hospitalId, $memberId);
        if ($getAllowedBrandIdsRes->isFail())
        {
            return $getAllowedBrandIdsRes;
        }

        // 挂号原因是否存在
        $getRegistrationReasonInfoRes = RegistrationReasonModel::getOne($registrationReasonId);
        if (empty($getRegistrationReasonInfoRes) || empty($getRegistrationReasonInfoRes['status']))
        {
            return self::Fail('挂号原因不存在', 35001);
        }

        // 挂号类型是否存在
        $getRegistrationTypeInfoRes = RegistrationTypeModel::getOne($registrationTypeId);
        if (empty($getRegistrationTypeInfoRes) || empty($getRegistrationTypeInfoRes['status']))
        {
            return self::Fail('挂号类型不存在', 35002);
        }

        // 根据挂号类型:出诊、复诊，自动匹配挂号商品
        $getItemRegistrationInfoRes = ItemRegistrationModel::getRegistrationItemByType(hospitalOrgId     : $hospitalOrgId,
                                                                                       registrationTypeId: $registrationTypeId,
                                                                                       registrationTime  : date('H:i:s'));

        if ($getItemRegistrationInfoRes->isEmpty())
        {
            return self::Fail('挂号商品不存在', 35005);
        }
        if ($getItemRegistrationInfoRes->count() > 1)
        {
            return self::Fail('挂号商品无效，存在多条', 35005);
        }

        // 挂号费用
        $getItemRegistrationInfoRes = $getItemRegistrationInfoRes->first();
        $registrationItemId         = $getItemRegistrationInfoRes['id'];
        $salePrice                  = $getItemRegistrationInfoRes['sale_price'];
        $payPrice                   = $salePrice;

        // 返回结构
        $returnData = ['salePrice' => $salePrice, 'dealPrice' => $payPrice, 'payPrice' => $payPrice, 'discountPrice' => 0, 'discountReason' => ''];
        if (!empty($withItemId))
        {
            $returnData['itemId'] = $registrationItemId;
        }

        // 如果挂号费为0元，则不显示优惠项
        if (bccomp($payPrice, '0', 2) != 1)
        {
            return self::Success($returnData);
        }

        // 免费号情况下减免掉挂号费
        if (!empty($getRegistrationReasonInfoRes['is_free']))
        {
            $discountPrice = $payPrice;
            $payPrice      = numberSub([$payPrice, $discountPrice]);

            // 优惠信息
            $returnData['payPrice']       = $payPrice;
            $returnData['dealPrice']      = $payPrice;
            $returnData['discountPrice']  = $discountPrice;
            $returnData['discountReason'] = '免挂号费';
        }

        return self::Success($returnData);
    }

    /**
     * 添加挂号
     *
     * @param int   $memberId
     * @param int   $petId
     * @param array $registrationParams 挂号参数
     * @param array $publicParams
     *
     * @return LogicResult
     * @throws Throwable
     */
    public static function AddRegistration(int $memberId, int $petId, array $registrationParams, array $publicParams): LogicResult
    {
        if (empty($memberId) || empty($petId))
        {
            return self::Fail('添加挂号记录，缺少人宠必选参数', 400);
        }
        if (empty($registrationParams) || empty($publicParams))
        {
            return self::Fail('添加挂号记录，缺少必选参数', 400);
        }

        // 公共参数
        $hospitalOrgId   = intval(Arr::get($publicParams, '_hospitalOrgId'));
        $hospitalBrandId = intval(Arr::get($publicParams, '_hospitalBrandId'));
        $hospitalId      = intval(Arr::get($publicParams, '_hospitalId'));
        $createdById     = intval(Arr::get($publicParams, '_userId'));
        if (empty($hospitalOrgId) || empty($hospitalBrandId) || empty($hospitalId) || empty($createdById))
        {
            return self::Fail('公共参数，缺少必选参数', 400);
        }

        // 业务参数
        $petWeight                   = Arr::get($registrationParams, 'petWeight', 0);
        $registrationSourceId        = intval(Arr::get($registrationParams, 'registrationSourceId', 1));
        $registrationReasonId        = intval(Arr::get($registrationParams, 'registrationReasonId', 0));
        $registrationTypeId          = intval(Arr::get($registrationParams, 'registrationTypeId', 1));
        $registrationDoctorUid       = trim(Arr::get($registrationParams, 'registrationDoctorUid', ''));
        $registrationBeautyDoctorUid = trim(Arr::get($registrationParams, 'beautyDoctorUid', ''));
        $registrationMode            = intval(Arr::get($registrationParams, 'registrationMode', 1));
        $registrationTransferCode    = trim(Arr::get($registrationParams, 'transferCode', ''));

        // 获取有效会员信息
        $getMemberInfoRes = MemberLogic::GetValidMemberByIdOrUid($memberId);
        if ($getMemberInfoRes->isFail())
        {
            return $getMemberInfoRes;
        }

        // 获取挂号宠物信息
        $getPetInfoRes = PetLogic::GetValidPetByIdOrUid($petId);
        if ($getPetInfoRes->isFail())
        {
            return $getPetInfoRes;
        }

        $getPetInfoRes = $getPetInfoRes->getData();
        $petMemberId   = $getPetInfoRes['member_id'];
        if ($petMemberId != $memberId)
        {
            return self::Fail('挂号宠物不属于当前用户', 32000);
        }

        // 挂号来源是否存在
        $getRegistrationSourceInfoRes = RegistrationSourceModel::getOne($registrationSourceId);
        if (empty($getRegistrationSourceInfoRes) || empty($getRegistrationSourceInfoRes['status']))
        {
            return self::Fail('挂号来源不存在', 3500);
        }

        // 如果选择了挂号医生，验证医生是否有效并可以选择
        $selectedDoctorId = 0;
        if (!empty($registrationDoctorUid))
        {
            // 医生是否存在
            $getDoctorInfoRes = UsersModel::getOneByUid($registrationDoctorUid);
            if (empty($getDoctorInfoRes))
            {
                return self::Fail('挂号医生不存在', 35003);
            }

            // TODO 是否有医生角色
            $getHospitalRelationDoctorInfoRes = HospitalUserModel::getHospitalUsers($hospitalId,
                                                                                    [$getDoctorInfoRes['id']]);
            if ($getHospitalRelationDoctorInfoRes->isEmpty())
            {
                return self::Fail('挂号医生不存在', 35003);
            }

            // 医生UID对应的ID
            $selectedDoctorId = $getHospitalRelationDoctorInfoRes->first()->user_id;
        }

        // 如果选择了美容医生，验证医生是否有效并可以选择
        $selectedBeautyDoctorId = 0;
        if (!empty($registrationBeautyDoctorUid))
        {
            // 医生是否存在
            $getDoctorInfoRes = UsersModel::getOneByUid($registrationBeautyDoctorUid);
            if (empty($getDoctorInfoRes))
            {
                return self::Fail('美容医生不存在', 35003);
            }

            // TODO 是否有美容师角色
            $getHospitalRelationDoctorInfoRes = HospitalUserModel::getHospitalUsers($hospitalId,
                                                                                    [$getDoctorInfoRes['id']]);
            if ($getHospitalRelationDoctorInfoRes->isEmpty())
            {
                return self::Fail('美容医生不存在', 35003);
            }

            // 医生UID对应的ID
            $selectedBeautyDoctorId = $getHospitalRelationDoctorInfoRes->first()->user_id;
        }

        // 挂号方式
        if (empty($registrationMode) || !in_array($registrationMode, RegistrationModeEnum::values()))
        {
            return self::Fail('挂号方式无效', 35004);
        }

        // 院外转诊挂号
        $getTransferRes = [];
        if (!empty($registrationTransferCode))
        {
            $getTransferRes = TransfersModel::getData(where: ['transfer_code' => $registrationTransferCode]);
            $getTransferRes = $getTransferRes ? current($getTransferRes) : [];
            if (empty($getTransferRes))
            {
                return self::Fail('转诊单不存在，不可操作', 40301);
            }
            if ($getTransferRes['transfer_hospital_id'] != $hospitalId)
            {
                return self::Fail('转诊单非本院接收，不可操作', 40312);
            }
            if ($getTransferRes['status'] != TransferStatusEnum::Waiting->value)
            {
                return self::Fail('转诊单状态非等待办理，不可操作', 40313);
            }
            if ($getTransferRes['member_id'] != $memberId)
            {
                return self::Fail('转诊单用户与当前用户不一致，不可操作', 40314);
            }
            if ($getTransferRes['pet_id'] != $petId)
            {
                return self::Fail('转诊单宠物与当前宠物不一致，不可操作', 40315);
            }

            // 如果是院外转诊的话，修改挂号来源->转院挂号、挂号类型->转诊
            if ($getTransferRes['reason_id'] == RegistrationSourceEnum::TransferHospital->value)
            {
                $registrationSourceId = RegistrationSourceEnum::TransferHospital->value;
                $registrationTypeId   = RegistrationTypeEnum::TransferVisit->value;
            }
        }

        // 获取挂号优惠信息
        $getRegistrationDiscountRes = self::GetRegistrationDiscount($memberId, $petId, $registrationReasonId, $registrationTypeId, $publicParams, true);
        if ($getRegistrationDiscountRes->isFail())
        {
            return $getRegistrationDiscountRes;
        }

        // 优惠信息
        $itemId        = $getRegistrationDiscountRes->getData('itemId');
        $salePrice     = $getRegistrationDiscountRes->getData('salePrice');
        $dealPrice     = $getRegistrationDiscountRes->getData('dealPrice');
        $payPrice      = $getRegistrationDiscountRes->getData('payPrice');
        $discountPrice = $getRegistrationDiscountRes->getData('discountPrice');
        if (empty($itemId))
        {
            return self::Fail('挂号商品不存在', 35005);
        }

        // 是否存在有效未使用的挂号
        $haveValidRegistrationTotal = RegistrationsModel::getTotalNumber([
                                                                             'hospital_id' => $hospitalId,
                                                                             'member_id'   => $memberId,
                                                                             'pet_id'      => $petId,
                                                                             'status'      => RegistrationStatusEnum::Unused->value,
                                                                         ], onWritePdo: true);

        if (!empty($haveValidRegistrationTotal))
        {
            return self::Fail('当前宠物存在未使用挂号', 35007);
        }

        // 当前用户宠物是否有未完结的门诊
        $getPetHaveValidOutpatientRes = OutpatientModel::getOutpatientByPetId($hospitalId,
                                                                              $memberId,
                                                                              [$petId],
                                                                              OutpatientStatusEnum::VALID_STATUS,
                                                                              true);
        if ($getPetHaveValidOutpatientRes->isNotEmpty())
        {
            return self::Fail('当前宠物存在未完结门诊', 35008);
        }

        // 当前用户宠物是否有未完结的住院
        $getPetHaveValidInpatientRes = InpatientModel::getInpatientByPetId($hospitalId,
                                                                           $memberId,
                                                                           [$petId],
                                                                           [InpatientStatusEnum::Inpatient->value],
                                                                           true);
        if ($getPetHaveValidInpatientRes->isNotEmpty())
        {
            return self::Fail('当前宠物存在未完结住院', 35009);
        }

        // 获取当前医院今日挂号总数，记录当前挂号的顺序
        $getTodayRegistrationCount = RegistrationsModel::getTotalNumber([
                                                                            'hospital_id'       => $hospitalId,
                                                                            'registration_date' => date('Y-m-d')
                                                                        ]);

        try
        {
            // 挂号医生选择模式
            $selectedType = 0;
            if ($registrationTypeId == RegistrationTypeEnum::TransferVisit->value && $selectedDoctorId)
            {
                $selectedType = RegistrationDoctorSelectedTypeEnum::TransferWithDoctor->value;
            }
            elseif ($selectedDoctorId)
            {
                $selectedType = RegistrationDoctorSelectedTypeEnum::FrontDesk->value;
            }

            // 写入挂号信息
            $insertRegistrationData = [
                'uid'                  => generateUUID(),
                'registration_code'    => generateBusinessCodeNumber(BusinessCodePrefixEnum::MZGH),
                'member_id'            => $memberId,
                'org_id'               => $hospitalOrgId,
                'brand_id'             => $hospitalBrandId,
                'hospital_id'          => $hospitalId,
                'source_id'            => $registrationSourceId,
                'reason_id'            => $registrationReasonId,
                'type_id'              => $registrationTypeId,
                'doctor_id'            => $selectedDoctorId,
                'doctor_selected_type' => $selectedType,
                'beauty_doctor_id'     => $selectedBeautyDoctorId,
                'pet_id'               => $petId,
                'mode'                 => $registrationMode,
                'registration_date'    => date('Y-m-d'),
                'registration_number'  => $getTodayRegistrationCount + 1,
                'item_id'              => $itemId,
                'sale_price'           => $salePrice,
                'reduce_price'         => $discountPrice,
                'deal_price'           => $dealPrice,
                'pay_price'            => $payPrice,
                'status'               => 2,
                'created_by'           => $createdById,
            ];

            // 挂号时宠物基本信息
            $insertRegistrationPetData = [
                'registration_id'  => 0,
                'record_number'    => $getPetInfoRes['record_number'],
                'weight'           => sprintf("%.2f", $getPetInfoRes['weight']),
                'gender'           => $getPetInfoRes['gender'],
                'birthday'         => $getPetInfoRes['birthday'],
                'sterile_status'   => $getPetInfoRes['sterile_status'],
                'sterile_date'     => $getPetInfoRes['sterile_date'],
                'prevent_status'   => $getPetInfoRes['prevent_status'],
                'prevent_date'     => $getPetInfoRes['prevent_date'],
                'deworming_status' => $getPetInfoRes['deworming_status'],
                'deworming_date'   => $getPetInfoRes['deworming_date'],
                'irritate_remark'  => $getPetInfoRes['irritate_remark'],
            ];

            // 写入门诊记录
            $insertOutpatientData = [
                'outpatient_code' => generateBusinessCodeNumber(BusinessCodePrefixEnum::MZJL),
                'member_id'       => $memberId,
                'pet_id'          => $petId,
                'org_id'          => $hospitalOrgId,
                'brand_id'        => $hospitalBrandId,
                'hospital_id'     => $hospitalId,
                'registration_id' => 0,
                'doctor_id'       => $selectedDoctorId,
            ];

            // 写入宠物体况信息
            $insertPetVitalSignData = [
                'member_id'       => $memberId,
                'pet_id'          => $petId,
                'registration_id' => 0,
                'weight'          => sprintf("%.2f", $petWeight),
                'birthday'        => $getPetInfoRes['birthday'],
            ];

            // 开始写入数据
            $isBegin = true;
            DB::beginTransaction();

            // 写入挂号基本数据
            $lastRegistrationId = RegistrationsModel::insertOne($insertRegistrationData);
            $insertRegistrationData['id'] = $lastRegistrationId;

            // TODO 挂号并支付，生成订单

            // 写入挂号宠物快照信息
            $insertRegistrationPetData['registration_id'] = $lastRegistrationId;
            RegistrationPetRecordModel::insertOne($insertRegistrationPetData);

            // 写入挂号填写宠物体重变动记录
            if ($petWeight > 0)
            {
                $editPetWeightParams = [
                    'weight'       => sprintf("%.2f", $petWeight),
                    'relationType' => PetWeightSourceTypeEnum::Registration->value,
                    'relationId'   => $lastRegistrationId,
                ];
                $getEditPetWeightRes = PetLogic::EditPetWeight($getPetInfoRes['id'],
                                                               $editPetWeightParams,
                                                               $publicParams);
                if ($getEditPetWeightRes->isFail())
                {
                    DB::rollBack();

                    return $getEditPetWeightRes;
                }
            }

            // 写入宠物体况信息
            $insertPetVitalSignData['registration_id'] = $lastRegistrationId;
            PetVitalSignModel::insertOne($insertPetVitalSignData);

            // 写入门诊记录
            $insertOutpatientData['registration_id'] = $lastRegistrationId;
            OutpatientModel::insertOne($insertOutpatientData);

            // 如果是转诊，回写相关数据
            if (!empty($getTransferRes))
            {
                $upData = [
                    'transfer_registration_id' => $lastRegistrationId,
                    'status'                   => TransferStatusEnum::Success->value
                ];
                if ($selectedDoctorId > 0)
                {
                    $upData['transfer_doctor_id'] = $selectedDoctorId;
                }

                TransfersModel::updateOne($getTransferRes['id'], $upData);
            }

            DB::commit();

            MqttPublishMessageHelper::PublishRegistrationAddMessage($insertRegistrationData, $publicParams);

            return self::Success([
                                     'registrationId'   => $lastRegistrationId,
                                     'registrationCode' => $insertRegistrationData['registration_code'],
                                     'outpatientCode'   => $insertOutpatientData['outpatient_code'],
                                     'sheetCode'        => $insertRegistrationData['registration_code'],//购买单统一单号字段
                                     'totalPrice'       => $insertRegistrationData['sale_price'],//购买单下单时金额
                                 ]);
        } catch (Throwable $throwable)
        {
            !empty($isBegin) && DB::rollBack();

            Log::error('挂号失败', [
                'code'    => $throwable->getCode(),
                'message' => $throwable->getMessage(),
                'file'    => $throwable->getFile(),
                'line'    => $throwable->getLine(),
                'trace'   => $throwable->getTraceAsString(),
            ]);

            return self::Fail('添加挂号记录失败', 35010);
        }
    }

    /**
     * 挂号详情
     *
     * @param array $params
     * @param array $publicParams
     *
     * @return LogicResult
     */
    public static function GetDetail(array $params, array $publicParams): LogicResult
    {
        // 业务参数
        $registrationId   = intval(Arr::get($params, 'registrationId', 0));
        $registrationUid  = trim(Arr::get($params, 'registrationUid', ''));
        $registrationCode = trim(Arr::get($params, 'registrationCode', ''));
        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));

        if (empty($registrationCode) && empty($registrationUid) && empty($registrationId))
        {
            return self::Fail('获取挂号单详情，缺少单据ID/UID/编码必选参数', 400);
        }
        if (empty($hospitalId))
        {
            return self::Fail('获取挂号单详情，缺少医院ID必选参数', 400);
        }

        $registrationRes = RegistrationHelper::GetValidRegistration(registrationCode: $registrationCode);
        if ($registrationRes->isFail())
        {
            return $registrationRes;
        }
        $registration = $registrationRes->getData();

        $detailsRes = RegistrationHelper::FormatDetailStructure([$registration], $publicParams);
        if ($detailsRes->isFail())
        {
            return $detailsRes;
        }
        $detailRes  = $detailsRes->getData();
        $detailData = is_array($detailRes) && !empty($detailRes) ? current($detailRes) : null;
        if (empty($detailData))
        {
            return self::Fail('挂号单不存在', 35011);
        }

        return self::Success($detailData);
    }

    /**
     * 删除挂号单
     *
     * @param array $params
     * @param array $publicParams
     *
     * @return LogicResult
     */
    public static function DeleteRegistration(array $params, array $publicParams): LogicResult
    {
        // 业务参数
        $registrationId   = intval(Arr::get($params, 'registrationId', 0));
        $registrationUid  = trim(Arr::get($params, 'registrationUid', ''));
        $registrationCode = trim(Arr::get($params, 'registrationCode', ''));
        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        $userId     = intval(Arr::get($publicParams, '_userId'));

        if (empty($registrationCode) && empty($registrationUid) && empty($registrationId))
        {
            return self::Fail('删除挂号单，缺少单据ID/UID/编码必选参数', 400);
        }
        if (empty($hospitalId))
        {
            return self::Fail('删除挂号单，缺少医院ID必选参数', 400);
        }
        if (empty($userId))
        {
            return self::Fail('删除挂号单，缺少登录用户ID必选参数', 400);
        }

        $registrationRes = RegistrationHelper::GetValidRegistration(registrationCode: $registrationCode);
        if ($registrationRes->isFail())
        {
            return $registrationRes;
        }
        $registration   = $registrationRes->getData();
        $registrationId = $registration['id'];

        // 是否可删除
        $checkDeleteRes = self::CheckRegistrationDelete($registration, $publicParams);
        if ($checkDeleteRes->isFail())
        {
            return $checkDeleteRes;
        }

        //TODO:其他验证

        $updateData = [
            'cancel_by' => $userId,
            'cancel_at' => getCurrentTimeWithMilliseconds(),
        ];

        $updateRes = RegistrationsModel::DoSoftDelete($registrationId,
                                                      RegistrationStatusEnum::Invalid->value,
                                                      RegistrationStatusEnum::Unused->value,
                                                      $updateData);

        if (empty($updateRes))
        {
            return self::Fail('挂号单删除失败', 35101);
        }

        return self::Success();
    }

    public static function CheckRegistrationDelete(array $registration, array $publicParams): LogicResult
    {
        return RegistrationHelper::CheckRegistrationDelete($registration, $publicParams);
    }
}
