<?php

namespace App\Logics\V1;

use DB;
use Arr;
use Log;
use Throwable;
use App\Enums\DateEnum;
use App\Enums\StockAddTypeEnum;
use App\Logics\Logic;
use App\Logics\LogicResult;
use App\Models\StockItemShelfAddModel;
use App\Models\StockItemDailyPriceModel;

class StockItemDailyPriceLogic extends Logic
{
    /**
     * 获取商品当前加权价
     *
     * @param array $itemIds
     * @param array $publicParams 公共参数
     *
     * @return LogicResult
     */
    public static function GetItemNowDailyPrice(array $itemIds, array $publicParams): LogicResult
    {
        if (empty($itemIds))
        {
            return self::Fail('获取商品加权价，缺少商品ID必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('获取商品加权价，缺少公共必选参数', 400);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        if (empty($hospitalId))
        {
            return self::Fail('获取商品加权价，缺少医院ID必选参数', 400);
        }

        // 获取商品当前加权价
        $getItemDailyPriceRes = StockItemDailyPriceModel::getData(where   : [
                                                                                ['hospital_id', '=', $hospitalId],
                                                                                ['start_time', '<=', getNowDateTime()],
                                                                                ['end_time', '>', getNowDateTime()]
                                                                            ],
                                                                  whereIn : ['item_id' => $itemIds],
                                                                  group   : 'item_id',
                                                                  orderBys: ['id' => 'desc'],
                                                                  keyBy   : 'item_id');
        if (empty($getItemDailyPriceRes))
        {
            return self::Success();
        }

        $returnItemDailyPrice = [];
        foreach ($getItemDailyPriceRes as $curItemPriceInfo)
        {
            $returnItemDailyPrice[$curItemPriceInfo['item_id']] = [
                'id'        => $curItemPriceInfo['id'],
                'itemId'    => $curItemPriceInfo['item_id'],
                'packPrice' => $curItemPriceInfo['pack_price'],
                'bulkPrice' => $curItemPriceInfo['bulk_price'],
                'startTime' => $curItemPriceInfo['start_time'],
                'endTime'   => $curItemPriceInfo['end_time'],
            ];
        }

        return self::Success($returnItemDailyPrice);
    }

    /**
     * 更新医院商品每日加权价
     *
     * @param int   $stockItemShelfAddId 库存动态加记录ID
     * @param array $publicParams        公共参数
     *
     * @return LogicResult
     * @throws Throwable
     */
    public static function UpdateHospitalItemDailyPrice(int $stockItemShelfAddId, array $publicParams): LogicResult
    {
        if (empty($stockItemShelfAddId))
        {
            return self::Fail('更新商品加权价，缺少库存动态加记录ID', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('更新商品加权价，缺少公共参数', 400);
        }

        // 公共参数提取
        $orgId      = intval(Arr::get($publicParams, '_hospitalOrgId'));
        $brandId    = intval(Arr::get($publicParams, '_hospitalBrandId'));
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        if (empty($orgId) || empty($brandId) || empty($hospitalId))
        {
            return self::Fail('更新商品加权价，缺少公共必选参数', 400);
        }

        // 获取入库记录详情
        $getStockAddRecordRes = StockItemShelfAddModel::on()
                                                      ->useWritePdo()
                                                      ->where(['id' => $stockItemShelfAddId, 'hospital_id' => $hospitalId, 'status' => 1])
                                                      ->first();
        if (empty($getStockAddRecordRes))
        {
            return self::Fail('更新商品加权价，库存动态加记录不存在', 43004);
        }
        if (!in_array($getStockAddRecordRes['type'], StockAddTypeEnum::getNeedWeightedPrice()))
        {
            return self::Success();
        }
        if ($getStockAddRecordRes['pack_quantity'] <= 0 && $getStockAddRecordRes['bulk_quantity'] <= 0)
        {
            return self::Fail('更新商品加权价，整装、散装入库数量不能同时为0', 43005);
        }

        // 入库商品ID商品
        $itemId       = $getStockAddRecordRes['item_id'];
        $packQuantity = $getStockAddRecordRes['pack_quantity'];
        $bulkQuantity = $getStockAddRecordRes['bulk_quantity'];
        $packPrice    = $getStockAddRecordRes['pack_price'];
        $bulkPrice    = $getStockAddRecordRes['bulk_price'];
        $bulkRatio    = $getStockAddRecordRes['bulk_ratio'];

        // 现有整散库存、价格
        $beforeStockPackQuantity = 0;
        $beforeStockBulkQuantity = 0;
        $beforeStockPackPrice    = 0;
        $beforeStockBulkPrice    = 0;

        // 获取现有商品库存数量
        $getEffectiveQuantityRes = StockItemShelfLogic::GetEffectiveQuantity([$itemId], $publicParams);
        if ($getEffectiveQuantityRes->isFail())
        {
            return $getEffectiveQuantityRes;
        }

        $getEffectiveQuantityRes = $getEffectiveQuantityRes->getData($itemId, []);
        if (!empty($getEffectiveQuantityRes))
        {
            // 现有库存，减去本次入库数量。因为先入库后更新成本价
            $beforeStockPackQuantity = numberSub([$getEffectiveQuantityRes['packQuantity'], $packQuantity]);
            $beforeStockBulkQuantity = numberSub([$getEffectiveQuantityRes['bulkQuantity'], $bulkQuantity]);
            if ($beforeStockPackQuantity < 0 && $beforeStockBulkQuantity < 0)
            {
                return self::Fail('更新商品加权价，整装、散装库存数小于0', 43005);
            }
        }

        // 获取现有商品加权成本价
        $getStockPriceRes = self::GetItemNowDailyPrice([$itemId], $publicParams);
        if ($getStockPriceRes->isFail())
        {
            return $getStockPriceRes;
        }

        $getStockPriceRes = $getStockPriceRes->getData($itemId, []);
        if (is_array($getStockPriceRes) && !empty($getStockPriceRes))
        {
            $beforeStockPackPrice = $getStockPriceRes['packPrice'];
            $beforeStockBulkPrice = $getStockPriceRes['bulkPrice'];
        }

        // 现有库存总价值
        $beforeTotalPackPrice = numberMul([$beforeStockPackQuantity, $beforeStockPackPrice], 4);
        $beforeTotalBulkPrice = numberMul([$beforeStockBulkQuantity, $beforeStockBulkPrice], 4);

        /**
         * 计算包含现有库存的加权价公式：
         * 新散装加权价 = ((现有整装数*现有整装价格) + (现有散装数*现有散装价格) + (入库整装数*入库整装价格) + (入库散装数*入库散装价格)) / ((现有整装数+入库整装数)*整散比 + 现有散装数 + 入库散装数)
         */

        // 入库整、散总价值
        $curTotalPackPrice = numberMul([$packQuantity, $packPrice], 4);
        $curTotalBulkPrice = numberMul([$bulkQuantity, $bulkPrice], 4);

        // 总价值
        $totalPrice = numberAdd([$beforeTotalPackPrice, $beforeTotalBulkPrice, $curTotalPackPrice, $curTotalBulkPrice], 4);

        // 散装库存总数量，存在的库存 + 入库库存（以散装为基准）
        $totalBulkQuantity = numberAdd([
                                           numberMul([$beforeStockPackQuantity * $bulkRatio]),
                                           $beforeStockBulkQuantity,
                                           numberMul([$packQuantity, $bulkRatio]),
                                           $bulkQuantity
                                       ]);
        if ($totalBulkQuantity <= 0)
        {
            return self::Fail('更新商品加权价，总散装数量计算错误', 43006);
        }

        // 计算新的散装加权价
        $bulkWeightedPrice = numberDiv([$totalPrice, $totalBulkQuantity], 4);
        if ($bulkWeightedPrice <= 0)
        {
            return self::Fail('更新商品加权价，散装加权价计算错误', 43006);
        }

        // 计算新的整装加权价 = 散装加权价 * 整散比
        $packWeightedPrice = numberMul([$bulkWeightedPrice, $bulkRatio], 4);
        if ($packWeightedPrice <= 0)
        {
            return self::Fail('更新商品加权价，整装加权价计算错误', 43006);
        }

        // 判断加权价格是否有变化，无变化不需要更新
        if (bccomp($bulkWeightedPrice, $beforeStockBulkPrice, 4) == 0 && bccomp($packWeightedPrice, $beforeStockPackPrice, 4) == 0)
        {
            return self::Success();
        }

        try
        {
            $nowDateTime    = getNowDateTime();
            $beforeDateTime = date('Y-m-d H:i:s', time() - 1);

            DB::beginTransaction();

            // 更新加权价结束时间为当前时间-1秒
            if (!empty($getStockPriceRes))
            {
                $affectedRows = StockItemDailyPriceModel::on()
                                                        ->where(['id' => $getStockPriceRes['id']])
                                                        ->update(['end_time' => $beforeDateTime]);
                if ($affectedRows <= 0)
                {
                    DB::rollBack();

                    return self::Fail('更新商品加权价，更新结束时间失败', 43006);
                }
            }

            // 插入新的加权价记录
            StockItemDailyPriceModel::insertOne([
                                                    'org_id'      => $orgId,
                                                    'brand_id'    => $brandId,
                                                    'hospital_id' => $hospitalId,
                                                    'item_id'     => $itemId,
                                                    'pack_price'  => $packWeightedPrice,
                                                    'bulk_price'  => $bulkWeightedPrice,
                                                    'bulk_ratio'  => $bulkRatio,
                                                    'start_time'  => $nowDateTime,
                                                    'end_time'    => DateEnum::LongDateTime->value,
                                                ]);

            DB::commit();

            return self::Success();
        } catch (Throwable $throwable)
        {
            DB::rollBack();

            Log::error(__CLASS__ . '::' . __METHOD__ . ' 更新商品加权价异常', [
                'code'    => $throwable->getCode(),
                'message' => $throwable->getMessage(),
                'file'    => $throwable->getFile(),
                'line'    => $throwable->getLine(),
                'trace'   => $throwable->getTraceAsString(),
            ]);

            return self::Fail('更新商品加权价异常', 43006);
        }
    }
}
