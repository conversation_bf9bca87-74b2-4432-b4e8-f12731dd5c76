<?php

namespace App\Logics\V1;

use Arr;
use DB;
use Log;
use Throwable;
use App\Logics\Logic;
use App\Logics\LogicResult;
use App\Enums\TestOrImageStatusEnum;
use App\Enums\TestOrImagePaidStatusEnum;
use App\Enums\TestOrImageResultTypeEnum;
use App\Enums\HospitalUserByBusinessEnum;
use App\Models\ImagesModel;
use App\Models\RecipeItemModel;
use App\Models\HospitalUserModel;
use App\Models\ImagesTesterModel;
use App\Models\ImagesResultsModel;
use App\Models\ImagesResultsFilesModel;
use App\Models\ImagesResultsImageModel;
use App\Models\ImagesResultsReportModel;
use App\Models\ImagesReportTemplatesModel;
use App\Models\ImagesRelationTemplatesModel;
use App\Models\ImagesResultsReportImageModel;

class ImageLogic extends Logic
{
    /**
     * 影像列表筛选项
     *
     * @param array $publicParams
     *
     * @return LogicResult
     */
    public static function GetImageFilterOptions(array $publicParams): LogicResult
    {
        if (empty($publicParams))
        {
            return self::Fail('获取影像列表筛选项，缺少必选参数', 400);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        if (empty($hospitalId))
        {
            return self::Fail('获取影像列表筛选项，缺少医院ID必选参数', 400);
        }

        // 获取医院内医生角色
        $getHospitalDoctorRes = HospitalUserLogic::GetHospitalUsersByBusiness(HospitalUserByBusinessEnum::Doctor->value,
                                                                              $publicParams);

        // 影像检测状态
        $getImageStatusOptions = TestOrImageStatusEnum::options();

        // 影像付款状态
        $getImagePaidStatusOptions = TestOrImagePaidStatusEnum::options();

        // 检测员录入状态
        $testerStatusOptions = [
            [
                'id'   => 0,
                'name' => '未录入',
            ],
            [
                'id'   => 1,
                'name' => '已录入',
            ],
        ];

        return self::Success([
                                 'doctorOptions'          => $getHospitalDoctorRes->getData('data', []),
                                 'imageStatusOptions'     => $getImageStatusOptions,
                                 'imagePaidStatusOptions' => $getImagePaidStatusOptions,
                                 'testerStatusOptions'    => $testerStatusOptions,
                             ]);
    }

    /**
     * 获取影像列表
     *
     * @param array $searchParams
     * @param array $publicParams
     * @param int   $iPage
     * @param int   $iPageSize
     *
     * @return LogicResult
     */
    public static function GetImageList(array $searchParams, array $publicParams, int $iPage, int $iPageSize): LogicResult
    {
        if (empty($publicParams))
        {
            return self::Fail('检测列表，缺少必选参数', 400);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        if (empty($hospitalId))
        {
            return self::Fail('检测列表，缺少医院ID必选参数', 400);
        }

        // 获取当前医院下影像列表
        $searchParams['hospitalId'] = $hospitalId;
        $getImageListRes            = ImagesModel::getImageListData($searchParams, $iPage, $iPageSize);
        if (empty($getImageListRes))
        {
            return self::Success(['total' => 0, 'data' => []]);
        }

        $totalCount   = $getImageListRes['total'] ?? 0;
        $imageListRes = $getImageListRes['data'] ?? [];
        if (empty($totalCount) || empty($imageListRes))
        {
            return self::Success(['total' => 0, 'data' => []]);
        }

        // 格式化影像信息
        $getFormatImageRes = self::FormatImageInfo($imageListRes);
        if ($getFormatImageRes->isFail())
        {
            return $getFormatImageRes;
        }

        return self::Success(['total' => $totalCount, 'data' => $getFormatImageRes->getData()]);
    }

    /**
     * 获取有效影像信息
     *
     * @param int      $imageId
     * @param array    $publicParams
     * @param bool     $withHospitalId
     * @param int|null $imageStatus
     *
     * @return LogicResult
     */
    public static function GetValidImage(int $imageId, array $publicParams, bool $withHospitalId = true, ?int $imageStatus = 1): LogicResult
    {
        if (empty($imageId))
        {
            return self::Fail('获取影像，缺少影像ID必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('获取影像，缺少公共必选参数', 400);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        if (empty($hospitalId))
        {
            return self::Fail('获取影像，缺少医院ID必选参数', 400);
        }

        // 根据影像编码查询影像记录
        $getWhere = ['id' => $imageId];
        if (!empty($withHospitalId))
        {
            $getWhere['hospital_id'] = $hospitalId;
        }

        $getImageRes = ImagesModel::getData(where: $getWhere);
        $getImageRes = $getImageRes ? current($getImageRes) : [];
        if (empty($getImageRes))
        {
            return self::Fail('影像不存在', 40101);
        }
        if (!empty($imageStatus) && $getImageRes['status'] != $imageStatus)
        {
            return self::Fail('影像状态已发生变化，不可操作', 40000);
        }

        return self::Success($getImageRes);
    }

    /**
     * 验证影像是否可操作：开始检测、录入检测员、填写报告结果
     * 1、化验项有效
     * 2、病历未结束（操作人可以是非诊断医生）
     *
     * @param int   $imageId
     * @param array $publicParams
     *
     * @return LogicResult
     */
    public static function GetCheckImageOperable(int $imageId, array $publicParams): LogicResult
    {
        if (empty($imageId))
        {
            return self::Fail('验证影像操作，缺少影像ID必选参数', 400);
        }

        $getTestRes = self::GetValidImage($imageId, $publicParams);
        if ($getTestRes->isFail())
        {
            return $getTestRes;
        }

        $getTestRes = $getTestRes->getData();

        // 获取关联的病历信息
        $getCaseRes = CaseLogic::GetValidCaseById($getTestRes['case_id'], $publicParams, withDoctorId: false);
        if ($getCaseRes->isFail())
        {
            return $getCaseRes;
        }

        // 病历是否已经结束
        $getCaseRes = $getCaseRes->getData();
        if (!empty($getCaseRes['finished']))
        {
            return self::Fail('关联病历已结束诊断，不可操作', 40006);
        }

        return self::Success($getTestRes);
    }

    /**
     * 获取影像详情
     *
     * @param int   $imageId
     * @param array $publicParams
     *
     * @return LogicResult
     */
    public static function GetImageDetail(int $imageId, array $publicParams): LogicResult
    {
        if (empty($imageId))
        {
            return self::Fail('获取影像详情，缺少影像ID必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('获取影像详情，缺少公共必选参数', 400);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        $doctorId   = intval(Arr::get($publicParams, '_userId'));
        if (empty($hospitalId))
        {
            return self::Fail('获取影像详情，缺少医院ID必选参数', 400);
        }
        if (empty($doctorId))
        {
            return self::Fail('获取影像详情，缺少医生ID必选参数', 400);
        }

        // 获取影像信息
        $getImageInfoRes = ImagesModel::getImageListData(['imageId' => $imageId, 'hospitalId' => $hospitalId]);
        if (empty($getImageInfoRes) || empty($getImageInfoRes['data']))
        {
            return self::Fail('查看的影像不存在', 40101);
        }

        $getImageInfoRes = current($getImageInfoRes['data']);

        // 格式化影像信息
        $getFormatImageRes = self::FormatImageInfo([$getImageInfoRes]);
        if ($getFormatImageRes->isFail())
        {
            return $getFormatImageRes;
        }

        // 验证影像关联的病历信息
        $caseId            = $getImageInfoRes['case_id'];
        $getFormatImageRes = current($getFormatImageRes->getData());
        $getCaseRes        = CaseLogic::GetValidCaseById($caseId, $publicParams, false, false);
        if ($getCaseRes->isFail())
        {
            return $getCaseRes;
        }

        // 化验项非作废，并且未完结病历，并且是本医院和当前医生的病历才可以编辑
        $getCaseRes = $getCaseRes->getData();
        if (!empty($getImageInfoRes['status']) && empty($getCaseRes['finished']) && $getCaseRes['hospital_id'] == $hospitalId && $getCaseRes['doctor_id'] == $doctorId)
        {
            $getFormatImageRes['editAble'] = true;
        }

        // 获取影像相关结果
        $getImageResultRes = self::GetImageResultByImageIds([$imageId], $publicParams);
        if ($getImageResultRes->isFail())
        {
            return $getImageResultRes;
        }

        $getImageResultRes = $getImageResultRes->getData();
        if (!empty($getImageResultRes[$imageId]))
        {
            $getFormatImageRes['result'] = $getImageResultRes[$imageId];
        }

        return self::Success($getFormatImageRes);
    }

    /**
     * 开始检测
     *
     * @param int   $imageId
     * @param array $testerIds
     * @param array $publicParams
     *
     * @return LogicResult
     * @throws Throwable
     */
    public static function StartTest(int $imageId, array $testerIds, array $publicParams): LogicResult
    {
        if (empty($imageId))
        {
            return self::Fail('开始检测影像，缺少影像ID参数', 400);
        }
        if (empty($testerIds))
        {
            return self::Fail('开始检测影像，至少需要一个检测员', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('开始检测影像，缺少必选参数', 400);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        $userId     = Arr::get($publicParams, '_userId');
        if (empty($hospitalId))
        {
            return self::Fail('开始检测影像，缺少医院ID必选参数', 400);
        }
        if (empty($userId))
        {
            return self::Fail('开始检测影像，缺少用户ID必选参数', 400);
        }

        // 根据影像编码查询影像记录
        $getImageRes = self::GetCheckImageOperable($imageId, $publicParams);
        if ($getImageRes->isFail())
        {
            return $getImageRes;
        }

        // 已经开始检测、存在结果状态
        $getImageRes = $getImageRes->getData();
        if ($getImageRes['start_status'] == 1)
        {
            return self::Fail('影像已开始检测，不可重复开始', 40103);
        }
        if ($getImageRes['result_status'] == 1)
        {
            return self::Fail('影像已存在结果，不可重复开始', 40104);
        }

        // 开始检测
        try
        {
            DB::beginTransaction();

            // 录入检测员
            $verifyTesterIdsRes = self::ImageFollowTester($imageId, $testerIds, $publicParams);
            if ($verifyTesterIdsRes->isFail())
            {
                DB::rollBack();

                return $verifyTesterIdsRes;
            }

            // 更新影像为开始检测状态
            ImagesModel::updateOne($imageId, ['start_status' => 1, 'start_date' => getCurrentTimeWithMilliseconds()]);

            // 增加处方商品化验使用数量
            RecipeItemModel::on()
                           ->where(['uid' => $getImageRes['recipe_item_uid']])
                           ->whereColumn('used_quantity', '<', 'quantity')
                           ->increment('used_quantity');

            DB::commit();

            return self::Success();
        } catch (Throwable $throwable)
        {
            DB::rollBack();

            Log::error(__CLASS__ . '::' . __METHOD__ . ' 开始检测影像异常', [
                'code'    => $throwable->getCode(),
                'message' => $throwable->getMessage(),
                'file'    => $throwable->getFile(),
                'line'    => $throwable->getLine(),
                'trace'   => $throwable->getTraceAsString(),
            ]);

            return self::Fail('开始检测影像异常', 40114);
        }
    }

    /**
     * 录入影像检测员
     *
     * @param int   $imageId
     * @param array $testerIds
     * @param array $publicParams
     *
     * @return LogicResult
     * @throws Throwable
     */
    public static function ImageFollowTester(int $imageId, array $testerIds, array $publicParams): LogicResult
    {
        if (empty($imageId))
        {
            return self::Fail('录入影像检测员，缺少影像ID必选参数', 400);
        }
        if (empty($testerIds))
        {
            return self::Fail('录入影像检测员，缺少检测员必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('录入影像检测员，缺少公共参数', 400);
        }

        $testerIds = array_filter($testerIds);
        if (empty($testerIds))
        {
            return self::Fail('影像检测员必选', 400);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        if (empty($hospitalId))
        {
            return self::Fail('录入影像检测员，缺少医院ID必选参数', 400);
        }

        // 获取影像信息
        $getImageRes = self::GetCheckImageOperable($imageId, $publicParams);
        if ($getImageRes->isFail())
        {
            return $getImageRes;
        }

        // 影像关联处方ID、商品ID
        $getImageRes   = $getImageRes->getData();
        $imageRecipeId = $getImageRes['recipe_id'];
        $imageItemId   = $getImageRes['item_id'];

        // 验证检测员是否存在
        $getTesterRes = HospitalUserModel::getHospitalUsers($hospitalId, $testerIds);
        if ($getTesterRes->isEmpty())
        {
            return self::Fail('影像检测员不存在', 40102);
        }

        // 部分检测员不存在
        $getTesterIds  = $getTesterRes->pluck('user_id')
                                      ->unique()
                                      ->toArray();
        $diffTesterIds = array_diff($testerIds, $getTesterIds);
        if (!empty($diffTesterIds))
        {
            return self::Fail('部分影像检测员不存在', 40102);
        }

        // 获取该影像存在的检测员
        $getImageTesterRes = ImagesTesterModel::getData(where   : ['image_id' => $imageId],
                                                        orderBys: ['tester_level' => 'asc'],
                                                        keyBy   : 'tester_level');


        $insertData = [];
        $updateData = [];
        foreach ($testerIds as $level => $userId)
        {
            // 当前级别是否存在检测员
            $curOldTesterInfo = $getImageTesterRes[$level] ?? [];

            // 无效检测员
            if (empty($userId) && (empty($curOldTesterInfo) || empty($curOldTesterInfo['tester_status'])))
            {
                continue;
            }

            // 修改旧的检测员记录
            if (!empty($curOldTesterInfo))
            {
                $updateData[] = [
                    'where' => ['id' => $curOldTesterInfo['id']],
                    'data'  => [
                        'user_id'       => $userId,
                        'tester_status' => $userId > 0 ? 1 : 0,
                    ],
                ];
            }
            else
            {
                // 新增当前级别与检测员
                $insertData[] = [
                    'image_id'      => $imageId,
                    'recipe_id'     => $imageRecipeId,
                    'item_id'       => $imageItemId,
                    'user_id'       => $userId,
                    'tester_level'  => $level,
                    'tester_status' => 1,
                ];
            }

            unset($getImageTesterRes[$level]);
        }

        // 旧的检测员存在的修改为无效
        if (!empty($getImageTesterRes))
        {
            $updateData = array_merge($updateData, array_map(function ($item) {
                return [
                    'where' => ['id' => $item['id']],
                    'data'  => [
                        'tester_status' => 0,
                    ],
                ];
            }, $getImageTesterRes));
        }

        // 没有需要更新的数据
        if (empty($insertData) && empty($updateData))
        {
            return self::Success();
        }

        try
        {
            DB::beginTransaction();

            if (!empty($insertData))
            {
                ImagesTesterModel::insert($insertData);
            }

            if (!empty($updateData))
            {
                foreach ($updateData as $updateItem)
                {
                    ImagesTesterModel::updateOne($updateItem['where']['id'], $updateItem['data']);
                }
            }

            // 更新影像标记有检测员
            if ($getImageRes['is_tester'] != 1)
            {
                ImagesModel::updateOne($imageId, ['is_tester' => 1]);
            }

            DB::commit();
        } catch (Throwable $throwable)
        {
            DB::rollBack();

            Log::error(__CLASS__ . '::' . __METHOD__ . ' 录入影像检测员异常', [
                'code'    => $throwable->getCode(),
                'message' => $throwable->getMessage(),
                'file'    => $throwable->getFile(),
                'line'    => $throwable->getLine(),
                'trace'   => $throwable->getTraceAsString(),
            ]);

            return self::Fail('录入影像检测员异常', 40114);
        }

        return self::Success();
    }

    /**
     * 保存影像结果
     *
     * @param int   $imageId
     * @param array $resultParams
     * @param array $publicParams
     *
     * @return LogicResult
     * @throws Throwable
     */
    public static function SaveImageResult(int $imageId, array $resultParams, array $publicParams): LogicResult
    {
        if (empty($imageId))
        {
            return self::Fail('保存影像结果，缺少影像ID必选参数', 400);
        }
        if (empty($resultParams))
        {
            return self::Fail('保存影像结果，缺少结果必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('保存影像结果，缺少公共参数', 400);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        $userId     = intval(Arr::get($publicParams, '_userId'));
        if (empty($hospitalId))
        {
            return self::Fail('保存影像结果，缺少医院ID必选参数', 400);
        }
        if (empty($userId))
        {
            return self::Fail('保存影像结果，缺少用户ID必选参数', 400);
        }

        // 业务参数
        $resultType = Arr::get($resultParams, 'resultType', '');
        if (empty($resultType) || !TestOrImageResultTypeEnum::exists($resultType))
        {
            return self::Fail('保存影像结果，结果类型参数错误', 400);
        }

        // 获取影像信息
        $getImageRes = self::GetCheckImageOperable($imageId, $publicParams);
        if ($getImageRes->isFail())
        {
            return $getImageRes;
        }

        // 影像状态是否可以录入结果
        $getImageRes = $getImageRes->getData();
        if ($getImageRes['start_status'] != 1)
        {
            return self::Fail('影像未开始检测，不可录入结果', 40105);
        }

        // 获取关联的病历信息
        $getCaseRes = CaseLogic::GetValidCaseById($getImageRes['case_id'], $publicParams);
        if ($getCaseRes->isFail())
        {
            return $getCaseRes;
        }

        // 病历是否已经结束
        $getCaseRes = $getCaseRes->getData();
        if (!empty($getCaseRes['finished']))
        {
            return self::Fail('病历已结束诊断，不可录入结果', 40106);
        }

        try
        {
            DB::beginTransaction();

            // 匹配填报结果类型并保存 @formatter:off
            match ($resultType)
            {
                'text'            => $getDealRes = self::SaveImageResultDesc($imageId, $resultParams, $publicParams),
                'image'           => $getDealRes = self::SaveImageResultImage($imageId, $resultParams, $publicParams),
                'file'            => $getDealRes = self::SaveImageResultFile($imageId, $resultParams, $publicParams),
                'templateList'    => $getDealRes = self::SaveImageResultTemplateList($imageId, $getImageRes['item_id'], $resultParams, $publicParams),
                'templateDetail' => $getDealRes = self::SaveImageResultTemplateDetail($imageId, $getImageRes['item_id'], $resultParams, $publicParams),
            };
            // @formatter:on

            if ($getDealRes->isFail())
            {
                DB::rollBack();

                return $getDealRes;
            }

            // 更新影像结果状态为有结果
            if ($getImageRes['result_status'] != 1)
            {
                ImagesModel::updateOne($imageId, ['result_status' => 1]);
            }

            DB::commit();

            return self::Success();
        } catch (Throwable $throwable)
        {
            DB::rollBack();

            Log::error(__CLASS__ . '::' . __METHOD__ . ' [统一保存处]保存影像结果异常', [
                'code'    => $throwable->getCode(),
                'message' => $throwable->getMessage(),
                'file'    => $throwable->getFile(),
                'line'    => $throwable->getLine(),
                'trace'   => $throwable->getTraceAsString(),
            ]);

            return self::Fail('[统一保存处]保存影像结果异常', 40113);
        }
    }

    /**
     * 保存影像结果-文字
     *
     * @param int   $imageId
     * @param array $resultDescParams
     * @param array $publicParams
     *
     * @return LogicResult
     * @throws Throwable
     */
    private static function SaveImageResultDesc(int $imageId, array $resultDescParams, array $publicParams): LogicResult
    {
        if (empty($imageId))
        {
            return self::Fail('保存影像结果，缺少影像ID必选参数', 400);
        }
        if (empty($resultDescParams))
        {
            return self::Fail('保存影像结果，缺少文字结果必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('保存影像结果，缺少公共参数', 400);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        $userId     = intval(Arr::get($publicParams, '_userId'));
        if (empty($hospitalId))
        {
            return self::Fail('保存影像结果，缺少医院ID必选参数', 400);
        }
        if (empty($userId))
        {
            return self::Fail('保存影像结果，缺少用户ID必选参数', 400);
        }

        // 业务参数不存在
        if (!isset($resultDescParams['textResult']))
        {
            return self::Fail('保存影像结果，缺少文字结果必选参数', 400);
        }

        // 文字结果
        $resultDesc = trim($resultDescParams['textResult']);

        // 获取现有的影像文字结果
        $getDescResultRes = ImagesResultsModel::getData(where: ['image_id' => $imageId, 'status' => 1]);

        // 无任何修改
        if (empty($getDescResultRes) && empty($resultDesc))
        {
            return self::Success();
        }

        // 旧的文字结果id
        $oldResultDescId = 0;
        if (!empty($getDescResultRes))
        {
            $oldResultDescId = current($getDescResultRes)['id'];

            // 如果新的结果为空，那么直接把文字结果记录给修改无效
            if (empty($resultDesc))
            {
                $newDescData = [
                    'status' => 0,
                ];
            }
            else
            {
                $newDescData = [
                    'result_desc' => addslashes($resultDesc),
                ];
            }
        }
        else
        {
            $newDescData = [
                'image_id'    => $imageId,
                'result_desc' => addslashes($resultDesc),
                'created_by'  => $userId,
            ];
        }

        try
        {
            DB::beginTransaction();

            if (!empty($oldResultDescId))
            {
                ImagesResultsModel::updateOne($oldResultDescId, $newDescData);
            }
            else
            {
                ImagesResultsModel::insertOne($newDescData);
            }

            DB::commit();

            return self::Success();
        } catch (Throwable $throwable)
        {
            DB::rollBack();

            Log::error(__CLASS__ . '::' . __METHOD__ . ' 保存影像结果-文字结果异常', [
                'code'    => $throwable->getCode(),
                'message' => $throwable->getMessage(),
                'file'    => $throwable->getFile(),
                'line'    => $throwable->getLine(),
                'trace'   => $throwable->getTraceAsString(),
            ]);

            return self::Fail('保存影像结果异常', 40113);
        }
    }

    /**
     * 保存影像结果-图片
     *
     * @param int   $imageId
     * @param array $resultImageParams
     * @param array $publicParams
     *
     * @return LogicResult
     * @throws Throwable
     */
    private static function SaveImageResultImage(int $imageId, array $resultImageParams, array $publicParams): LogicResult
    {
        if (empty($imageId))
        {
            return self::Fail('保存影像结果，缺少影像ID必选参数', 400);
        }
        if (empty($resultImageParams))
        {
            return self::Fail('保存影像结果，缺少图片结果必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('保存影像结果，缺少公共参数', 400);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        $userId     = intval(Arr::get($publicParams, '_userId'));
        if (empty($hospitalId))
        {
            return self::Fail('保存影像结果，缺少医院ID必选参数', 400);
        }
        if (empty($userId))
        {
            return self::Fail('保存影像结果，缺少用户ID必选参数', 400);
        }

        // 业务参数不存在
        if (!isset($resultImageParams['imageList']))
        {
            return self::Fail('保存影像结果，缺少图片结果必选参数', 400);
        }

        // 业务参数, 影像结果图片
        $newImageResultList = Arr::get($resultImageParams, 'imageList', []);

        // 获取现有的影像图片结果
        $getImageResultRes = ImagesResultsImageModel::getData(where: ['image_id' => $imageId, 'status' => 1],
                                                              keyBy: 'id');

        // 影像新的图片结果
        $insertImageData = [];
        $deleteImageIds  = [];
        foreach ($newImageResultList as $imageItem)
        {
            // id为0则代表新增，反之代表图片结果id
            $curImageId = $imageItem['id'] ?? 0;
            if (empty($curImageId))
            {
                $insertImageData[] = [
                    'image_id'   => $imageId,
                    'image_name' => $imageItem['name'],
                    'image_url'  => $imageItem['url'],
                    'created_by' => $userId,
                ];
                continue;
            }

            // 旧的图片结果还存在，过滤
            if (!empty($getImageResultRes[$curImageId]))
            {
                unset($getImageResultRes[$curImageId]);
            }
        }

        // 旧的图片结果还存在多余，说明新的结果不存在，删除
        if (!empty($getImageResultRes))
        {
            $deleteImageIds = array_keys($getImageResultRes);
        }

        try
        {
            DB::beginTransaction();

            // 新增图片结果
            if (!empty($insertImageData))
            {
                ImagesResultsImageModel::insert($insertImageData);
            }

            // 删除图片结果
            if (!empty($deleteImageIds))
            {
                ImagesResultsImageModel::on()
                                       ->whereIn('id', $deleteImageIds)
                                       ->update([
                                                    'status'     => 0,
                                                    'deleted_by' => $userId,
                                                    'deleted_at' => getCurrentTimeWithMilliseconds()
                                                ]);
            }

            DB::commit();

            return self::Success();
        } catch (Throwable $throwable)
        {
            DB::rollBack();

            Log::error(__CLASS__ . '::' . __METHOD__ . ' 保存影像结果-图片结果异常', [
                'code'    => $throwable->getCode(),
                'message' => $throwable->getMessage(),
                'file'    => $throwable->getFile(),
                'line'    => $throwable->getLine(),
                'trace'   => $throwable->getTraceAsString(),
            ]);

            return self::Fail('保存影像结果异常', 40113);
        }
    }

    /**
     * 保存影像结果-文件
     *
     * @param int   $imageId
     * @param array $resultFileParams
     * @param array $publicParams
     *
     * @return LogicResult
     * @throws Throwable
     */
    private static function SaveImageResultFile(int $imageId, array $resultFileParams, array $publicParams): LogicResult
    {
        if (empty($imageId))
        {
            return self::Fail('保存影像结果，缺少影像ID必选参数', 400);
        }
        if (empty($resultFileParams))
        {
            return self::Fail('保存影像结果，缺少结果必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('保存影像结果，缺少公共参数', 400);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        $userId     = intval(Arr::get($publicParams, '_userId'));
        if (empty($hospitalId))
        {
            return self::Fail('保存影像结果，缺少医院ID必选参数', 400);
        }
        if (empty($userId))
        {
            return self::Fail('保存影像结果，缺少用户ID必选参数', 400);
        }

        // 业务参数不存在
        if (!isset($resultFileParams['fileList']))
        {
            return self::Fail('保存影像结果，缺少文件结果必选参数', 400);
        }

        // 业务参数, 影像文件结果
        $newFileResultList = Arr::get($resultFileParams, 'fileList', []);

        // 获取现有的影像文件结果
        $getFileResultRes = ImagesResultsFilesModel::getData(where: ['image_id' => $imageId, 'status' => 1],
                                                             keyBy: 'id');

        // 影像新的文件结果
        $insertFileData = [];
        $deleteFileIds  = [];
        foreach ($newFileResultList as $fileItem)
        {
            // id为0则代表新增，反之代表文件结果id
            $curFileId = $fileItem['id'] ?? 0;
            if (empty($curFileId))
            {
                $insertFileData[] = [
                    'image_id'   => $imageId,
                    'file_name'  => $fileItem['name'],
                    'file_url'   => $fileItem['url'],
                    'created_by' => $userId,
                ];
                continue;
            }

            // 旧的文件结果还存在，过滤
            if (!empty($getFileResultRes[$curFileId]))
            {
                unset($getFileResultRes[$curFileId]);
            }
        }

        // 旧的文件结果还存在多余，说明新的结果不存在，删除
        if (!empty($getFileResultRes))
        {
            $deleteFileIds = array_keys($getFileResultRes);
        }

        try
        {
            DB::beginTransaction();

            // 新增文件结果
            if (!empty($insertFileData))
            {
                ImagesResultsFilesModel::insert($insertFileData);
            }

            // 删除文件结果
            if (!empty($deleteFileIds))
            {
                ImagesResultsFilesModel::on()
                                       ->whereIn('id', $deleteFileIds)
                                       ->update([
                                                    'status'     => 0,
                                                    'deleted_by' => $userId,
                                                    'deleted_at' => getCurrentTimeWithMilliseconds()
                                                ]);
            }

            DB::commit();

            return self::Success();
        } catch (Throwable $throwable)
        {
            DB::rollBack();

            Log::error(__CLASS__ . '::' . __METHOD__ . ' 保存影像结果-文件结果异常', [
                'code'    => $throwable->getCode(),
                'message' => $throwable->getMessage(),
                'file'    => $throwable->getFile(),
                'line'    => $throwable->getLine(),
                'trace'   => $throwable->getTraceAsString(),
            ]);

            return self::Fail('保存影像结果异常', 40113);
        }
    }

    /**
     * 保存影像结果-模版列表
     *
     * @param int   $imageId
     * @param int   $imageItemId
     * @param array $resultTemplateListParams
     * @param array $publicParams
     *
     * @return LogicResult
     * @throws Throwable
     */
    public static function SaveImageResultTemplateList(int $imageId, int $imageItemId, array $resultTemplateListParams, array $publicParams): LogicResult
    {
        if (empty($imageId))
        {
            return self::Fail('保存影像结果，缺少影像ID必选参数', 400);
        }
        if (empty($imageItemId))
        {
            return self::Fail('保存影像结果，缺少影像商品ID必选参数', 400);
        }
        if (empty($resultTemplateListParams))
        {
            return self::Fail('保存影像结果，缺少结果必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('保存影像结果，缺少公共参数', 400);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        $userId     = intval(Arr::get($publicParams, '_userId'));
        if (empty($hospitalId))
        {
            return self::Fail('保存影像结果，缺少医院ID必选参数', 400);
        }
        if (empty($userId))
        {
            return self::Fail('保存影像结果，缺少用户ID必选参数', 400);
        }

        // 业务参数不存在
        if (!isset($resultTemplateListParams['templateList']))
        {
            return self::Fail('保存影像结果，缺少模版报告必选参数', 400);
        }

        // 业务参数, 报告单结果
        $newTemplateList = Arr::get($resultTemplateListParams, 'templateList', []);

        // uid字段为空则说明是新增的，新增需要填写模版报告的详情信息。通过结果类型为“模版详情”保存。
        if (array_any($newTemplateList, fn($templateParam) => empty($templateParam['uid'])))
        {
            return self::Fail('新增模版报告结果，请在右侧详情页填写后保存', 40114);
        }

        // 获取现有的影像模版
        $getOldResultRes = ImagesResultsReportModel::getData(where: ['image_id' => $imageId, 'status' => 1],
                                                             keyBy: 'id');

        foreach ($getOldResultRes as $oldKey => $oldResult)
        {
            foreach ($newTemplateList as $newKey => $newResult)
            {
                if ($oldResult['uid'] == $newResult['uid'])
                {
                    unset($getOldResultRes[$oldKey]);
                    unset($newTemplateList[$newKey]);
                    break;
                }
            }
        }

        // 无任何修改
        if (empty($getOldResultRes) && empty($newTemplateList))
        {
            return self::Success();
        }

        // 不可通过此保存结果方式来新增新的影像模版
        if (!empty($newTemplateList))
        {
            return self::Fail('新增模版报告结果，请在右侧详情页填写后保存', 40114);
        }

        // 旧的模版结果还存在多余，说明新的结果不存在，删除
        $deleteTemplateIds = [];
        if (!empty($getOldResultRes))
        {
            $deleteTemplateIds = array_keys($getOldResultRes);
        }
        if (empty($deleteTemplateIds))
        {
            return self::Success();
        }

        try
        {
            DB::beginTransaction();

            // 删除模版
            ImagesResultsReportModel::on()
                                    ->whereIn('id', $deleteTemplateIds)
                                    ->update([
                                                 'status'     => 0,
                                                 'deleted_by' => $userId,
                                                 'deleted_at' => getCurrentTimeWithMilliseconds()
                                             ]);

            // 删除模版图片详情
            ImagesResultsReportImageModel::on()
                                         ->whereIn('report_result_id', $deleteTemplateIds)
                                         ->update([
                                                      'status'     => 0,
                                                      'deleted_by' => $userId,
                                                      'deleted_at' => getCurrentTimeWithMilliseconds()
                                                  ]);

            DB::commit();
        } catch (Throwable $throwable)
        {
            DB::rollBack();

            Log::error(__CLASS__ . '::' . __METHOD__ . ' 保存影像结果-模版列表异常', [
                'code'    => $throwable->getCode(),
                'message' => $throwable->getMessage(),
                'file'    => $throwable->getFile(),
                'line'    => $throwable->getLine(),
                'trace'   => $throwable->getTraceAsString(),
            ]);

            return self::Fail('保存影像结果异常', 40113);
        }

        return self::Success();
    }

    /**
     * 保存影像结果-模版详情
     *
     * @param int   $imageId
     * @param int   $imageItemId
     * @param array $resultTemplateInfoParams
     * @param array $publicParams
     *
     * @return LogicResult
     * @throws Throwable
     */
    private static function SaveImageResultTemplateDetail(int $imageId, int $imageItemId, array $resultTemplateInfoParams, array $publicParams): LogicResult
    {
        if (empty($imageId))
        {
            return self::Fail('保存影像结果，缺少影像ID必选参数', 400);
        }
        if (empty($imageItemId))
        {
            return self::Fail('保存影像结果，缺少影像商品ID必选参数', 400);
        }
        if (empty($resultTemplateInfoParams))
        {
            return self::Fail('保存影像结果，缺少结果必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('保存影像结果，缺少公共参数', 400);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        $userId     = intval(Arr::get($publicParams, '_userId'));
        if (empty($hospitalId))
        {
            return self::Fail('保存影像结果，缺少医院ID必选参数', 400);
        }
        if (empty($userId))
        {
            return self::Fail('保存影像结果，缺少用户ID必选参数', 400);
        }

        // 业务参数不存在
        if (!isset($resultTemplateInfoParams['templateInfo']))
        {
            return self::Fail('保存影像结果，缺少模版详情必选参数', 400);
        }

        // 业务参数-模版信息
        $templateInfoParams = $resultTemplateInfoParams['templateInfo'];
        $newTemplateUid     = Arr::get($templateInfoParams, 'template.uid', '');
        if (empty($newTemplateUid))
        {
            return self::Fail('保存影像结果，缺少影像结果模版必选参数', 400);
        }

        // 业务参数-模版结果信息
        $newReportResultUid    = Arr::get($templateInfoParams, 'result.uid', '');
        $newTemplatePosition   = Arr::get($templateInfoParams, 'result.position', '');
        $newTemplateFindings   = Arr::get($templateInfoParams, 'result.findings', '');
        $newTemplateImpression = Arr::get($templateInfoParams, 'result.impression', '');
        $newTemplatePictures   = Arr::get($templateInfoParams, 'result.pictures', []);

        // 获取影像信息
        $getImageRes = self::GetValidImage($imageId, $publicParams);
        if ($getImageRes->isFail())
        {
            return $getImageRes;
        }

        // 病历ID
        $caseId = $getImageRes->getData('case_id', 0);

        // 获取模版信息
        $getItemRelationTemplateRes = ImagesRelationTemplatesModel::getImageItemRelationTemplates(itemId         : $imageItemId,
                                                                                                  templateUids   : [$newTemplateUid],
                                                                                                  withValidStatus: false);
        $getItemRelationTemplateRes = $getItemRelationTemplateRes ? current($getItemRelationTemplateRes) : [];
        if (empty($getItemRelationTemplateRes))
        {
            return self::Fail('影像报告模版不存在', 40107);
        }

        // 模版信息
        $templateId               = $getItemRelationTemplateRes['template_id'];
        $templateStatus           = $getItemRelationTemplateRes['template_status'];
        $templateIsNeedPosition   = $getItemRelationTemplateRes['template_is_need_position'];
        $templateIsNeedFindings   = $getItemRelationTemplateRes['template_is_need_findings'];
        $templateIsNeedImpression = $getItemRelationTemplateRes['template_is_need_impression'];
        $templateIsNeedPictures   = $getItemRelationTemplateRes['template_is_need_pictures'];

        // 验证检查部位必填时的情况
        if (!empty($templateIsNeedPosition) && empty($newTemplatePosition))
        {
            return self::Fail('影像结果模版，检查部位必填', 40108);
        }

        // 验证检查超声描述/表现/症状
        if (!empty($templateIsNeedFindings) && empty($newTemplateFindings))
        {
            return self::Fail('影像结果模版，检查超声描述/表现/症状必填', 40109);
        }

        // 验证检查超声提示/意见/建议
        if (!empty($templateIsNeedImpression) && empty($newTemplateImpression))
        {
            return self::Fail('影像结果模版，检查超声提示/意见/建议必填', 40110);
        }

        // 验证检查超声图片
        if (!empty($templateIsNeedPictures) && empty($newTemplatePictures))
        {
            return self::Fail('影像结果模版，检查超声图片必传', 40111);
        }

        // 获取存在的影像报告结果，编辑情况下：根据结果UID获取，如果新增：根据模版ID和影像ID获取是否存在
        $getWhere = ['image_id' => $imageId, 'template_id' => $templateId, 'status' => 1];
        if (!empty($newReportResultUid))
        {
            $getWhere['uid']   = $newReportResultUid;
            $getImageReportRes = ImagesResultsReportModel::getData(where: $getWhere);
            $getImageReportRes = $getImageReportRes ? current($getImageReportRes) : [];
            if (empty($getImageReportRes))
            {
                return self::Fail('影像结果报告单不存在', 40107);
            }
        }
        else
        {
            $getImageReportRes = ImagesResultsReportModel::getData(where: $getWhere);
            $getImageReportRes = $getImageReportRes ? current($getImageReportRes) : [];

            // 如果已经存在此模版报告，那么限制不可以在新增此模版报告
            if (!empty($getImageReportRes))
            {
                return self::Fail('此模版报告已经存在，不可重复新增，请修改存在的报告单结果', 40114);
            }
        }

        // 获取存在的影像报告结果-图片
        if (!empty($getImageReportRes))
        {
            $getWhere               = [
                'report_result_id' => $getImageReportRes['id'],
                'image_id'         => $imageId,
                'template_id'      => $templateId,
                'status'           => 1
            ];
            $getImageReportImageRes = ImagesResultsReportImageModel::getData(where: $getWhere, keyBy: 'id');
        }

        // 如果此模版为新使用，验证模版状态、影像关联模版状态是否有效。如果此模版存在结果，那么不判断模版状态是否有效
        if (empty($getImageReportRes) && empty($getImageReportImageRes) && (empty($templateStatus) || empty($getItemRelationTemplateRes['status'])))
        {
            return self::Fail('影像结果模版已失效，不可使用', 40107);
        }

        // 如果影像存在报告单结果，设置报告单更新的信息。反之不存在则作为新增报告单
        $oldReportResultId = 0;
        if (!empty($getImageReportRes))
        {
            // 存在的报告单结果id
            $oldReportResultId = $getImageReportRes['id'];

            // 设置要更新的报告单信息
            $reportResult = [
                'position'   => $newTemplatePosition,
                'findings'   => $newTemplateFindings,
                'impression' => $newTemplateImpression,
            ];
        }
        else
        {
            // 设置要新增的报告单信息
            $reportResult = [
                'uid'         => generateUUID(),
                'case_id'     => $caseId,
                'image_id'    => $imageId,
                'template_id' => $templateId,
                'position'    => $newTemplatePosition,
                'findings'    => $newTemplateFindings,
                'impression'  => $newTemplateImpression,
                'created_by'  => $userId,
            ];
        }

        // 更新影像报告图片结果
        $insertReportImageData = [];
        $deleteReportImageIds  = [];
        foreach ($newTemplatePictures as $newPictureItem)
        {
            // id为0则代表新增，反之代表图片结果id
            $curPictureId = $newPictureItem['id'] ?? 0;
            if (empty($curPictureId))
            {
                $insertReportImageData[] = [
                    'case_id'     => $caseId,
                    'image_id'    => $imageId,
                    'template_id' => $templateId,
                    'image_name'  => $newPictureItem['name'],
                    'image_url'   => $newPictureItem['url'],
                    'created_by'  => $userId,
                ];
                continue;
            }

            // 旧的图片结果还存在，过滤
            if (!empty($getImageReportImageRes[$curPictureId]))
            {
                unset($getImageReportImageRes[$curPictureId]);
            }
        }

        // 旧的文件结果还存在多余，说明新的结果不存在，删除
        if (!empty($getImageReportImageRes))
        {
            $deleteReportImageIds = array_keys($getImageReportImageRes);
        }

        // 无任何修改
        if (empty($reportResult) && empty($insertReportImageData) && empty($deleteReportImageIds))
        {
            return self::Fail('影像结果模版报告，数据有误', 40112);
        }

        try
        {
            DB::beginTransaction();

            // 更新报告单
            if (!empty($oldReportResultId))
            {
                ImagesResultsReportModel::updateOne($oldReportResultId, $reportResult);
            }
            else
            {
                $oldReportResultId = ImagesResultsReportModel::insertOne($reportResult);
            }

            // 新增报告单图片
            if (!empty($insertReportImageData))
            {
                foreach ($insertReportImageData as $key => $insertItem)
                {
                    $insertReportImageData[$key]['report_result_id'] = $oldReportResultId;
                }

                ImagesResultsReportImageModel::insert($insertReportImageData);
            }

            // 删除报告单图片
            if (!empty($deleteReportImageIds))
            {
                ImagesResultsReportImageModel::on()
                                             ->whereIn('id', $deleteReportImageIds)
                                             ->update([
                                                          'status'     => 0,
                                                          'deleted_by' => $userId,
                                                          'deleted_at' => getCurrentTimeWithMilliseconds()
                                                      ]);
            }

            DB::commit();

            return self::Success();
        } catch (Throwable $throwable)
        {
            DB::rollBack();

            Log::error(__CLASS__ . '::' . __METHOD__ . ' 保存影像结果-模版详情异常', [
                'code'    => $throwable->getCode(),
                'message' => $throwable->getMessage(),
                'file'    => $throwable->getFile(),
                'line'    => $throwable->getLine(),
                'trace'   => $throwable->getTraceAsString(),
            ]);

            return self::Fail('保存影像结果异常', 40113);
        }
    }

    /**
     * 获取影像关联可用的模版列表
     *
     * @param int   $imageId
     * @param array $publicParams
     *
     * @return LogicResult
     */
    public static function GetImageRelationTemplateList(int $imageId, array $publicParams): LogicResult
    {
        if (empty($imageId))
        {
            return self::Fail('获取影像关联模版，缺少影像ID必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('获取影像关联模版，缺少公共必选参数', 400);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        if (empty($hospitalId))
        {
            return self::Fail('获取影像关联模版，缺少医院ID必选参数', 400);
        }

        // 获取影像信息
        $getImageRes = self::GetValidImage($imageId, $publicParams, true, null);
        if ($getImageRes->isFail())
        {
            return $getImageRes;
        }

        // 获取影像关联的模版
        $itemId                 = $getImageRes->getData('item_id', 0);
        $getRelationTemplateRes = ImagesRelationTemplatesModel::getImageItemRelationTemplates($itemId);
        if (empty($getRelationTemplateRes))
        {
            return self::Success(['total' => 0, 'data' => []]);
        }

        // 获取已存在的模版结果，如果已经存在不可以在新增此模版。可以编辑存在的报告单
        $getExistTemplateRes = ImagesResultsReportModel::getData(where: ['image_id' => $imageId, 'status' => 1],
                                                                 keyBy: 'template_id');

        $returnResult = [];
        foreach ($getRelationTemplateRes as $relationInfo)
        {
            $returnResult[] = [
                'uid'     => $relationInfo['template_uid'],
                'name'    => $relationInfo['template_name'],
                'useAble' => empty($getExistTemplateRes[$relationInfo['template_id']]),
            ];
        }

        return self::Success(['total' => count($returnResult), 'data' => $returnResult]);
    }

    /**
     * 获取影像关联模版报告单结果
     *
     * @param int   $imageId
     * @param array $templateInfo
     * @param array $publicParams
     *
     * @return LogicResult
     */
    public static function GetImageTemplateReport(int $imageId, array $templateInfo, array $publicParams): LogicResult
    {
        if (empty($imageId))
        {
            return self::Fail('获取影像关联模版报告单，缺少影像ID必选参数', 400);
        }
        if (empty($templateInfo))
        {
            return self::Fail('获取影像关联模版报告单，缺少报告单ID必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('获取影像关联模版报告单，缺少公共必选参数', 400);
        }

        // 业务参数
        $reportResultId = Arr::get($templateInfo, 'reportResultId', 0);
        $templateId     = Arr::get($templateInfo, 'templateId', 0);
        if (empty($reportResultId) && empty($templateId))
        {
            return self::Fail('获取影像项关联模版报告单，缺少报告单结果ID、模版ID必选参数', 400);
        }

        // 获取影像信息
        $getImageRes = self::GetValidImage($imageId, $publicParams, false, null);
        if ($getImageRes->isFail())
        {
            return $getImageRes;
        }

        // 获取结果信息
        $getImageReportRes      = [];
        $getImageReportImageRes = [];
        if (!empty($reportResultId))
        {
            $getImageReportRes = ImagesResultsReportModel::getData(where: [
                                                                              'id'       => $reportResultId,
                                                                              'image_id' => $imageId,
                                                                              'status'   => 1
                                                                          ]);
            $getImageReportRes = $getImageReportRes ? current($getImageReportRes) : [];
            if (empty($getImageReportRes))
            {
                return self::Fail('影像结果报告单不存在', 40107);
            }

            // 模版ID
            $templateId = $getImageReportRes['template_id'];

            // 影像报告单结果-图片
            $getImageReportImageRes = ImagesResultsReportImageModel::getData(where: [
                                                                                        'report_result_id' => $reportResultId,
                                                                                        'status'           => 1
                                                                                    ]);
        }

        // 获取结果关联的模版信息
        $getTemplateRes = [];
        if (!empty($templateId))
        {
            $getTemplateRes = ImagesReportTemplatesModel::getOne($templateId);
        }
        if (empty($getTemplateRes))
        {
            return self::Fail('影像结果报告单关联的模版不存在', 40107);
        }

        // 报告单结果图片信息
        $reportImageResult = [];
        foreach ($getImageReportImageRes as $getTestReportDetailResItem)
        {
            $reportImageResult[] = [
                'id'   => $getTestReportDetailResItem['id'],
                'name' => $getTestReportDetailResItem['image_name'],
                'url'  => realPicturePath($getTestReportDetailResItem['image_url']),
            ];
        }

        $returnData = [
            'imageCode' => $getImageRes->getData('image_code'),
            'template'  => [
                'uid'  => $getTemplateRes['uid'],
                'name' => $getTemplateRes['name'],
            ],
            'result'    => [
                'uid'              => $getImageReportRes['uid'] ?? '',
                'isNeedPosition'   => $getTemplateRes['is_need_position'],
                'positionName'     => $getTemplateRes['position_name'],
                'position'         => $getImageReportRes['position'] ?? '',
                'isNeedFindings'   => $getTemplateRes['is_need_findings'],
                'findingsName'     => $getTemplateRes['findings_name'],
                'findings'         => $getImageReportRes['findings'] ?? '',
                'isNeedImpression' => $getTemplateRes['is_need_impression'],
                'impressionName'   => $getTemplateRes['impression_name'],
                'impressionLimit'  => $getTemplateRes['impression_limit'],
                'impression'       => $getImageReportRes['impression'] ?? '',
                'isNeedPictures'   => $getTemplateRes['is_need_pictures'],
                'picturesLimitMin' => $getTemplateRes['pictures_limit_min'],
                'picturesLimitMax' => $getTemplateRes['pictures_limit_max'],
                'pictures'         => $reportImageResult,
            ],
        ];

        return self::Success($returnData);
    }

    /**
     * 批量获取影像结果
     *
     * @param array $imageIds
     * @param array $publicParams
     * @param bool  $withReportResult 是否需要返回报告结果
     *
     * @return LogicResult
     */
    public static function GetImageResultByImageIds(array $imageIds, array $publicParams, bool $withReportResult = false): LogicResult
    {
        if (empty($imageIds))
        {
            return self::Fail('获取影像结果，缺少影像ID必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('获取影像结果，缺少公共必选参数', 400);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        if (empty($hospitalId))
        {
            return self::Fail('获取影像结果，缺少医院ID必选参数', 400);
        }

        // 获取影像项目信息
        $getImageInfoRes = ImagesModel::getImageItemInfoByImageIds($imageIds);
        if (empty($getImageInfoRes))
        {
            return self::Fail('获取化验结果，化验项目不存在', 40000);
        }

        // 获取影像文字结果
        $getDescResultRes = ImagesResultsModel::getData(where: ['status' => 1], whereIn: ['image_id' => $imageIds]);
        $getDescResultRes = array_column($getDescResultRes, 'result_desc', 'image_id');

        // 获取影像图片结果
        $getImageResultRes = ImagesResultsImageModel::getData(where  : ['status' => 1],
                                                              whereIn: ['image_id' => $imageIds]);

        // 获取影像文件结果
        $getFileResultRes = ImagesResultsFilesModel::getData(where  : ['status' => 1],
                                                             whereIn: ['image_id' => $imageIds]);

        // 获取影像报告结果
        $getReportResultRes = ImagesResultsReportModel::getData(where  : ['status' => 1],
                                                                whereIn: ['image_id' => $imageIds]);

        // 获取影像报告模版
        if (!empty($getReportResultRes))
        {
            $templateIds = array_unique(array_filter(array_column($getReportResultRes, 'template_id')));
            if (!empty($templateIds))
            {
                $getTemplateRes = ImagesReportTemplatesModel::getData(whereIn: ['id' => $templateIds], keyBy: 'id');
            }
        }

        $returnResult = [];
        foreach ($imageIds as $imageId)
        {
            // 影像项目信息
            $curImageInfo = [];
            foreach ($getImageInfoRes as $imageInfo)
            {
                if ($imageInfo['id'] != $imageId)
                {
                    continue;
                }

                $curImageInfo = [
                    'imageCode'  => $imageInfo['image_code'],
                    'item'       => [
                        'uid'  => $imageInfo['item_uid'],
                        'name' => $imageInfo['item_display_name'],
                        'unit' => $imageInfo['item_use_unit'],
                    ],
                    'createTime' => formatDisplayDateTime($imageInfo['created_at']),
                    'remark'     => $imageInfo['remark'] ?? '',
                ];
            }

            // 文字结果
            $curTextResult = $getDescResultRes[$imageId]['result_desc'] ?? '';

            // 图片结果
            $curImageResult = [];
            foreach ($getImageResultRes as $imageResult)
            {
                if ($imageResult['image_id'] != $imageId)
                {
                    continue;
                }

                $curImageResult[] = [
                    'id'   => $imageResult['id'],
                    'name' => $imageResult['image_name'],
                    'url'  => realPicturePath($imageResult['image_url']),
                ];
            }

            // 文件结果
            $curFileResult = [];
            foreach ($getFileResultRes as $fileResult)
            {
                if ($fileResult['image_id'] != $imageId)
                {
                    continue;
                }

                $curFileResult[] = [
                    'id'   => $fileResult['id'],
                    'name' => $fileResult['file_name'],
                    'url'  => realPicturePath($fileResult['file_url']),
                ];
            }

            // 模版结果
            $curReportResult       = [];
            $curReportResultDetail = [];
            foreach ($getReportResultRes as $reportResult)
            {
                if ($reportResult['image_id'] != $imageId)
                {
                    continue;
                }

                // 关联的模版信息
                $curTemplateInfo = $getTemplateRes[$reportResult['template_id']] ?? [];
                if (empty($curTemplateInfo))
                {
                    continue;
                }

                $curReportResult[] = [
                    'template' => [
                        'uid'  => $curTemplateInfo['uid'] ?? '',
                        'name' => $curTemplateInfo['name'] ?? '',
                    ],
                    'result'   => [
                        'uid'      => $reportResult['uid'] ?? '',
                        'createAt' => $reportResult['created_at'],
                    ]
                ];

                // 获取报告单详情
                if (!empty($withReportResult))
                {
                    $getReportDetailRes = self::GetImageTemplateReport($imageId,
                                                                       ['reportResultId' => $reportResult['id']],
                                                                       $publicParams);
                    if ($getReportDetailRes->isFail())
                    {
                        continue;
                    }

                    $curReportResultDetail[] = $getReportDetailRes->getData();
                }
            }

            $returnResult[$imageId] = [
                'imageInfo'      => $curImageInfo,
                'textResult'     => $curTextResult,
                'imageList'      => $curImageResult,
                'fileList'       => $curFileResult,
                'templateList'   => $curReportResult,
                'templateDetail' => $curReportResultDetail,
            ];
        }

        return self::Success($returnResult);
    }

    /**
     * 格式化影像信息展示
     *
     * @param array $imageLists
     *
     * @return LogicResult
     */
    private static function FormatImageInfo(array $imageLists): LogicResult
    {
        if (empty($imageLists))
        {
            return self::Success();
        }

        // 获取影像关联的宠物基础信息、宠物对应宠物主基础信息
        $petIds = array_unique(array_column($imageLists, 'pet_id'));
        if (!empty($petIds))
        {
            $getPetRes = PetLogic::GetPetBaseInfoByPetIds($petIds);
            if ($getPetRes->isFail())
            {
                return $getPetRes;
            }

            $getPetRes = $getPetRes->getData();
        }

        // 获取影像关联的检测员
        $imageIds     = array_unique(array_column($imageLists, 'id'));
        $getTesterRes = ImagesTesterModel::getImageTester($imageIds);

        $returnImageList = [];
        foreach ($imageLists as $item)
        {
            // 影像关联会员信息
            $curImageMemberInfo = $getPetRes[$item['pet_id']]['memberInfo'] ?? [];

            // 影像关联宠物信息
            $curImagePetInfo = $getPetRes[$item['pet_id']]['petInfo'] ?? [];

            // 影像关联检测员
            $curTesterInfo = [];
            foreach ($getTesterRes as $testerInfo)
            {
                if ($testerInfo['image_id'] != $item['id'])
                {
                    continue;
                }

                $tmpTesterInfo = [
                    'uid'   => $testerInfo['user_uid'],
                    'name'  => $testerInfo['user_name'],
                    'level' => $testerInfo['tester_level'],
                ];

                $curTesterInfo[] = $tmpTesterInfo;
            }

            $tmpImageItem = [
                'imageCode'  => $item['image_code'],
                'memberInfo' => $curImageMemberInfo,
                'petInfo'    => $curImagePetInfo,
                'doctor'     => [
                    'uid'  => $item['doctor_uid'],
                    'name' => $item['doctor_name'],
                ],
                'item'       => [
                    'uid'      => $item['item_uid'],
                    'name'     => $item['item_display_name'],
                    'itemName' => $item['item_name'],
                    'unit'     => $item['item_use_unit'],
                ],
                'quantity'   => 1,
                'price'      => formatDisplayNumber($item['price']),
                'status'     => TestOrImageStatusEnum::FormatTestOrImageStatus($item),
                'payStatus'  => [
                    'id'   => $item['is_paid'],
                    'name' => TestOrImagePaidStatusEnum::getDescription($item['is_paid']),
                ],
                'createTime' => formatDisplayDateTime($item['created_at']),
                'tester'     => $curTesterInfo,
                'editAble'   => false, // 默认不可编辑，在影像详情中如果可编辑的话验证通过为true。其它业务处不可编辑
                'remark'     => $item['remark'] ?? '',
            ];

            $returnImageList[] = $tmpImageItem;
        }

        return self::Success($returnImageList);
    }
}
