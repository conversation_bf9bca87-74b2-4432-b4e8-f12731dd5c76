<?php

namespace App\Logics\V1;

use Throwable;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Logics\Logic;
use App\Logics\LogicResult;
use App\Enums\MemberBalanceOperateTypeEnum;
use App\Enums\MemberBalanceOperateResourceTypeEnum;
use App\Models\MemberBalanceModel;
use App\Enums\MemberBalanceRechargedTypeEnum;
use App\Enums\SheetStatusEnum;
use App\Models\BalanceRechargeOrderModel;
use App\Models\MemberBalanceRechargedModel;
use App\Models\MemberBalanceOperatedModel;

class MemberBalanceLogic extends Logic
{
    /**
     * 订单使用会员余额
     *
     * 更新余额批次
     * 更新用户余额
     * 记录操作日志
     *
     * @param int    $memberId
     * @param string $useBalanceAmount
     * @param string $resourceType
     * @param int    $resourceId
     * @param int    $resourceOrderCode
     * @param array  $publicParams
     *
     * @return LogicResult
     * @throws Throwable
     */
    public static function UseMemberBalance(
        int   $memberId, string $useBalanceAmount, string $resourceType, int $resourceId, int $resourceOrderCode,
        array $publicParams
    ): LogicResult
    {
        if (empty($memberId) || empty($useBalanceAmount) || empty($resourceType) || empty($resourceId) || empty($resourceOrderCode)
        )
        {
            return self::Fail('使用会员余额，缺少必选参数', 400);
        }

        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        $orgId      = intval(Arr::get($publicParams, '_hospitalOrgId'));
        $brandId    = intval(Arr::get($publicParams, '_hospitalBrandId'));

        if (empty($hospitalId) || empty($orgId) || empty($brandId))
        {
            return self::Fail('使用会员余额，缺少必选参数', 400);
        }

        $useBalance = $useBalanceAmount;

        $params = [
            '$memberId'          => $memberId,
            '$useBalanceAmount'  => $useBalanceAmount,
            '$resourceType'      => $resourceType,
            '$resourceId'        => $resourceId,
            '$resourceOrderCode' => $resourceOrderCode,
        ];

        try
        {
            DB::beginTransaction();

            // 1 获取用户余额, 共享锁可防止选中的数据列被篡改，直到事务被提交为止
            $memberBalance = MemberBalanceModel::on()
                                               ->where('member_id', $memberId)
                                               ->where('is_frozen', 0)
                                               ->sharedLock()
                                               ->first();
            if (empty($memberBalance))
            {
                DB::rollBack();

                return self::Fail('会员余额不存在或已冻结', 602000);
            }

            $balanceId       = $memberBalance['id'];
            $balanceRecharge = $memberBalance['balance_recharge'];
            $balanceGift     = $memberBalance['balance_gift'];
            $balanceTotal    = bcadd($balanceRecharge, $balanceGift, 2);

            // 真实账户和赠送账户需要扣减的金额
            $useBalanceRecharge = 0;
            $useBalanceGift     = 0;

            // 用户余额不足
            if (bccomp($balanceTotal, $useBalance, 2) < 0)
            {
                DB::rollBack();

                return self::Fail('会员余额不足', 602001);
            }

            // 2 寻找需要扣减的批次 更新扣减的批次可使用的余额 记录余额使用的批次。共享锁可防止选中的数据列被篡改，直到事务被提交为止
            $rechargeBatch = MemberBalanceRechargedModel::on()
                                                        ->where(['member_id' => $memberId])
                                                        ->where(function ($query) {
                                                            $query->where('balance_recharge_usable', '>', 0)
                                                                  ->orWhere('balance_gift_usable', '>', 0);
                                                        })
                                                        ->orderBy('id', 'asc')
                                                        ->sharedLock()
                                                        ->get()
                                                        ->all();

            if (empty($rechargeBatch))
            {
                DB::rollBack();

                Log::channel('balance')
                   ->warning('会员余额充值批次余额不足',
                             [
                                 ...$params,
                                 '$memberBalance' => $memberBalance,
                                 '$useBalance'    => $useBalance,
                             ]);

                return self::Fail('会员余额充值批次余额不足', 602002);
            }

            // 3 从各个余额批次依次扣减，先使用一个批次的充值余额、再使用赠送余额，然后再使用下一个批次
            foreach ($rechargeBatch as $value)
            {
                $rechargedId           = $value['id'];
                $balanceRechargeUsable = $value['balance_recharge_usable'];
                $balanceGiftUsable     = $value['balance_gift_usable'];

                // 本批次需要扣减的余额
                $deductRechargeAmount = 0;
                $deductGiftAmount     = 0;

                if ($useBalance > 0 && $balanceRechargeUsable > 0)
                {
                    // 本次真实余额账户够扣,全部从本批次扣除,否则将本批次扣光
                    $deductRechargeAmount = bccomp($balanceRechargeUsable,
                                                   $useBalance,
                                                   2) >= 0 ? $useBalance : $balanceRechargeUsable;
                    // 剩余需要扣减的真实账户的余额
                    $useBalance = bcsub($useBalance, $deductRechargeAmount, 2);
                }

                if ($useBalance > 0 && $balanceGiftUsable > 0)
                {
                    // 本次赠送余额账户够扣,全部从本批次扣除,否则将本批次扣光
                    $deductGiftAmount = bccomp($balanceGiftUsable,
                                               $useBalance,
                                               2) >= 0 ? $useBalance : $balanceGiftUsable;
                    // 剩余需要扣减的赠送账户的余额
                    $useBalance = bcsub($useBalance, $deductGiftAmount, 2);
                }

                $useBalanceRecharge = bcadd($useBalanceRecharge, $deductRechargeAmount, 2);
                $useBalanceGift     = bcadd($useBalanceGift, $deductGiftAmount, 2);

                // 本批次不需要扣减
                if ($deductRechargeAmount == 0 && $deductGiftAmount == 0)
                {
                    continue;
                }

                if ($deductRechargeAmount > 0 || $deductGiftAmount > 0)
                {
                    $updateRet = MemberBalanceRechargedModel::on()
                                                            ->where('id', '=', $rechargedId)
                                                            ->when($deductRechargeAmount > 0,
                                                                function ($query) use ($deductRechargeAmount) {
                                                                    $query->where('balance_recharge_usable',
                                                                                  '>=',
                                                                                  $deductRechargeAmount);
                                                                })
                                                            ->when($deductGiftAmount > 0,
                                                                function ($query) use ($deductGiftAmount) {
                                                                    $query->where('balance_gift_usable',
                                                                                  '>=',
                                                                                  $deductGiftAmount);
                                                                })
                                                            ->update(array_filter([
                                                                                      'balance_recharge_usable' => $deductRechargeAmount > 0
                                                                                          ? DB::raw("balance_recharge_usable - {$deductRechargeAmount}")
                                                                                          : null,
                                                                                      'balance_gift_usable'     => $deductGiftAmount > 0
                                                                                          ? DB::raw("balance_gift_usable - {$deductGiftAmount}")
                                                                                          : null,

                                                                                  ]));

                    if (!$updateRet)
                    {
                        DB::rollBack();

                        Log::channel('balance')
                           ->error('使用会员余额更新余额批次失败',
                                   [
                                       ...$params,
                                       'recharge_id'             => $rechargedId,
                                       'balance_recharge_usable' => $deductRechargeAmount,
                                       'balance_gift_usable'     => $deductGiftAmount
                                   ]);

                        return self::Fail('使用会员余额更新余额批次失败', 602020);
                    }
                }

                $operateData = [
                    'uid'                     => generateUUID(),
                    'org_id'                  => $orgId,
                    'brand_id'                => $brandId,
                    'hospital_id'             => $hospitalId,
                    'member_id'               => $memberId,
                    'source_platform'         => config('setting.system.platform_type_id'),
                    'resource_type'           => $resourceType,
                    'resource_id'             => $resourceId,
                    'resource_sub_id'         => 0,
                    'resource_order_code'     => $resourceOrderCode,
                    'recharged_id'            => $rechargedId,
                    'type'                    => MemberBalanceOperateTypeEnum::Decrease->value, //操作类型 1加 2减
                    'change_balance_recharge' => bcmul($deductRechargeAmount, - 1, 2),
                    'change_balance_gift'     => bcmul($deductGiftAmount, - 1, 2),
                ];

                $insertRet = MemberBalanceOperatedModel::insertOne($operateData);
                if (!$insertRet)
                {
                    DB::rollBack();

                    Log::channel('balance')
                       ->error('使用会员余额记录操作日志失败',
                               [
                                   ...$params,
                                   '$operateData' => $operateData
                               ]);

                    return self::Fail('使用会员余额记录操作日志失败', 602021);
                }
            }

            // 4 如果还有余额未扣减完，说明批次扣减错误，用户账户余额异常
            if ($useBalance > 0)
            {
                DB::rollBack();

                Log::channel('balance')
                   ->error('使用会员余额扣减余额异常',
                           [
                               ...$params,
                               '$memberBalance' => $memberBalance,
                               '$useBalance'    => $useBalance,
                           ]);

                return self::Fail('使用会员余额扣减余额异常', 602090);
            }


            if ($useBalanceRecharge > 0 || $useBalanceGift > 0)
            {
                $updateRet = MemberBalanceModel::on()
                                               ->where('id', '=', $balanceId)
                                               ->when($useBalanceRecharge > 0,
                                                   function ($query) use ($useBalanceRecharge) {
                                                       $query->where('balance_recharge', '>=', $useBalanceRecharge);
                                                   })
                                               ->when($useBalanceGift > 0,
                                                   function ($query) use ($useBalanceGift) {
                                                       $query->where('balance_gift', '>=', $useBalanceGift);
                                                   })
                                               ->update(array_filter([
                                                                         'balance_recharge' => $useBalanceRecharge > 0
                                                                             ? DB::raw("balance_recharge - {$useBalanceRecharge}")
                                                                             : null,
                                                                         'balance_gift'     => $useBalanceGift > 0
                                                                             ? DB::raw("balance_gift - {$useBalanceGift}")
                                                                             : null,

                                                                     ]));

                if (!$updateRet)
                {
                    DB::rollBack();

                    Log::channel('balance')
                       ->error('使用会员余额更新会员余额失败',
                               [
                                   ...$params,
                                   '$balanceId'          => $balanceId,
                                   '$useBalanceRecharge' => $useBalanceRecharge,
                                   '$useBalanceGift'     => $useBalanceGift,
                               ]);

                    return self::Fail('使用会员余额更新会员余额失败', 602022);
                }
            }

            DB::commit();

            return self::Success([
                                     'useBalanceTotal'    => $useBalanceAmount,
                                     'useBalanceRecharge' => $useBalanceRecharge,
                                     'useBalanceGift'     => $useBalanceGift
                                 ]);

        } catch (Throwable $throwable)
        {
            DB::rollBack();

            Log::error(__CLASS__ . '::' . __METHOD__ . ' 使用会员余额异常', [
                'code'    => $throwable->getCode(),
                'message' => $throwable->getMessage(),
                'file'    => $throwable->getFile(),
                'line'    => $throwable->getLine(),
                'trace'   => $throwable->getTraceAsString(),
            ]);

            Log::channel('balance')
               ->error('使用会员余额异常', $params);

            return self::Fail('使用会员余额异常', 602091);
        }
    }

    /**
     * 根据余额充值订单充值会员余额
     *
     * 创建充值记录
     * 记录操作日志
     * 会员账户充值
     *
     * @param int    $balanceRechargeOrderId
     * @param string $balanceRechargeOrderCode
     * @param int    $hospitalId
     *
     * @return LogicResult
     * @throws Throwable
     */
    public static function RechargeMemberBalance(
        int $balanceRechargeOrderId, string $balanceRechargeOrderCode, int $hospitalId
    ): LogicResult
    {
        if (empty($balanceRechargeOrderId) && empty($balanceRechargeOrderCode))
        {
            return self::Fail('充值会员余额，缺少必选参数', 400);
        }

        $order = BalanceRechargeOrderModel::GetOrderByCodeOrId($balanceRechargeOrderCode,
                                                               $balanceRechargeOrderId,
                                                               $hospitalId);
        if (empty($order))
        {
            return self::Fail('充值会员余额，充值订单不存在', 500120);
        }
        if ($order['status'] != SheetStatusEnum::Paid->value)
        {
            return self::Fail('充值会员余额，充值订单状态错误', 500121);
        }

        $memberId = $order['member_id'];
        $orgId    = $order['org_id'];

        $balanceRechargeTotal = bcmul($order['balance_recharge'], $order['quantity'], 2);
        $balanceGiftTotal     = bcmul($order['balance_gift'], $order['quantity'], 2);

        $rechargedData = [
            'org_id'                  => $order['org_id'],
            'brand_id'                => $order['brand_id'],
            'hospital_id'             => $order['hospital_id'],
            'member_id'               => $order['member_id'],
            'type'                    => MemberBalanceRechargedTypeEnum::NotInit->value,
            'recharge_order_id'       => $order['id'],
            'balance_recharge'        => $balanceRechargeTotal,
            'balance_gift'            => $balanceGiftTotal,
            'balance_recharge_usable' => $balanceRechargeTotal,
            'balance_gift_usable'     => $balanceGiftTotal,
        ];

        $operateData = [
            'uid'                     => generateUUID(),
            'org_id'                  => $order['org_id'],
            'brand_id'                => $order['brand_id'],
            'hospital_id'             => $order['hospital_id'],
            'member_id'               => $order['member_id'],
            'source_platform'         => config('setting.system.platform_type_id'),
            'resource_type'           => MemberBalanceOperateResourceTypeEnum::Recharge->value,
            'resource_id'             => $order['org_id'],
            'resource_sub_id'         => 0,
            'resource_order_code'     => $order['order_code'],
            'recharged_id'            => 0,//插入时更新
            'type'                    => MemberBalanceOperateTypeEnum::Increase->value, //操作类型 1加 2减
            'change_balance_recharge' => $balanceRechargeTotal,
            'change_balance_gift'     => $balanceGiftTotal,
        ];

        $params = [
            '$memberId'                 => $memberId,
            '$hospitalId'               => $hospitalId,
            '$balanceRechargeOrderId'   => $balanceRechargeOrderId,
            '$balanceRechargeOrderCode' => $balanceRechargeOrderCode,
            '$balanceRechargeTotal'     => $balanceRechargeTotal,
            '$balanceGiftTotal'         => $balanceGiftTotal,
        ];

        try
        {
            DB::beginTransaction();

            // 1 获取用户余额, 共享锁可防止选中的数据列被篡改，直到事务被提交为止
            $memberBalance = MemberBalanceModel::on()
                                               ->where('member_id', $memberId)
                                               ->sharedLock()
                                               ->first();

            $newMemberBalanceData = [];
            if (empty($memberBalance))
            {
                $newMemberBalanceData = [
                    'uid'              => generateUUID(),
                    'org_id'           => $orgId,
                    'member_id'        => $memberId,
                    'balance_recharge' => $balanceRechargeTotal,
                    'balance_gift'     => $balanceGiftTotal,
                    'is_frozen'        => 0,
                ];
            }

            // 2 创建充值批次记录
            $rechargedId = MemberBalanceRechargedModel::insertOne($rechargedData);
            if (empty($rechargedId))
            {
                DB::rollBack();

                Log::channel('balance')
                   ->error('充值会员余额，创建充值批次记录失败', [
                       ...$params,
                       '$rechargedData' => $rechargedData,
                   ]);

                return self::Fail('充值会员余额，创建充值批次记录失败', 500122);
            }

            // 3 创建余额变动记录
            $operateData['recharged_id'] = $rechargedId;
            $insertRet                   = MemberBalanceOperatedModel::insertOne($operateData);
            if (!$insertRet)
            {
                DB::rollBack();

                Log::channel('balance')
                   ->error('充值会员余额，创建余额变动记录失败', [
                       ...$params,
                       '$operateData' => $operateData,
                   ]);

                return self::Fail('充值会员余额，创建余额变动记录失败', 500123);
            }

            // 4 创建或修改会员余额
            if (!empty($memberBalance))
            {
                $updateRet = MemberBalanceModel::on()
                                               ->where('id', '=', $memberBalance['id'])
                                               ->update([
                                                            'balance_recharge' => DB::raw("balance_recharge + {$balanceRechargeTotal}"),
                                                            'balance_gift'     => DB::raw("balance_gift + {$balanceGiftTotal}"),
                                                        ]);
                if (!$updateRet)
                {
                    DB::rollBack();

                    Log::channel('balance')
                       ->error('充值会员余额，更新会员余额失败', [
                           ...$params,
                           '$memberBalance' => $memberBalance,
                       ]);

                    return self::Fail('充值会员余额，更新会员余额失败', 500124);
                }
            }
            else
            {
                $insertRet = MemberBalanceModel::insertOne($newMemberBalanceData);
                if (!$insertRet)
                {
                    DB::rollBack();

                    Log::channel('balance')
                       ->error('充值会员余额，创建会员余额失败', [
                           ...$params,
                           '$newMemberBalanceData' => $newMemberBalanceData,
                       ]);

                    return self::Fail('充值会员余额，创建会员余额失败', 500125);
                }
            }

            DB::commit();

            return self::Success();

        } catch (Throwable $throwable)
        {
            DB::rollBack();

            Log::error(__CLASS__ . '::' . __METHOD__ . ' 充值会员余额异常', [
                'code'    => $throwable->getCode(),
                'message' => $throwable->getMessage(),
                'file'    => $throwable->getFile(),
                'line'    => $throwable->getLine(),
                'trace'   => $throwable->getTraceAsString(),
            ]);

            Log::channel('balance')
               ->error('充值会员余额异常', $params);

            return self::Fail('充值会员余额异常', 500129);
        }
    }
}
