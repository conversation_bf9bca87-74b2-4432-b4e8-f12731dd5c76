<?php

namespace App\Logics\V1;

use Illuminate\Support\Arr;
use App\Logics\Logic;
use App\Logics\LogicResult;
use App\Models\HospitalCloudPrintersModel;
use App\Enums\CloudPrinterStatusEnum;
use App\Enums\CloudPrinterLabelTypeEnum;
use App\Enums\CloudPrinterReceiptTypeEnum;
use App\Services\GPrinter\GPrinter;
use App\Logics\CloudPrinterTemplate\ItemBarcodeLabelTemplate;
use App\Logics\CloudPrinterTemplate\ShelfCodeLabelTemplate;
use App\Logics\CloudPrinterTemplate\CashierReceiptTemplate;
use App\Logics\V1\PayOrderLogic;

class CloudPrinterLogic extends Logic
{
    const int MAX_LABEL_PRINT_COPIES = 10;//单个标签最大打印次数
    const int MAX_RECEIPT_PRINT_COPIES = 2;//单个小票最大打印次数

    /**
     * 新增医院云打印机
     *
     * @param array $params
     * @param array $publicParams
     *
     * @return LogicResult
     * @throws \Exception
     */
    public static function AddHospitalPrinter(array $params, array $publicParams): LogicResult
    {
        $deviceId   = trim(Arr::get($params, 'deviceId', ''));
        $deviceName = trim(Arr::get($params, 'deviceName', ''));
        $orgId      = intval(Arr::get($publicParams, '_hospitalOrgId'));
        $brandId    = intval(Arr::get($publicParams, '_hospitalBrandId'));
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));

        if (!$deviceId)
        {
            return self::Fail('打印机设备ID不能为空', 400);
        }
        if (!$deviceName)
        {
            return self::Fail('打印机设备名称不能为空', 400);
        }
        if (!$hospitalId || !$orgId || !$brandId)
        {
            return self::Fail('缺少公共必选参数', 400);
        }

        $printer = HospitalCloudPrintersModel::getOneByDeviceId($deviceId);
        if (!empty($printer))
        {
            return self::Fail('云打印机已存在', 29001);
        }

        $cloudAddResult = GPrinter::addDevice($deviceId, $deviceName, env('GPRINTER_DEFAULT_GROUP_ID'));
        if (!$cloudAddResult || !isset($cloudAddResult['code']) || $cloudAddResult['code'] != 1)
        {
            return self::Fail($cloudAddResult['msg'] ?? '添加打印机失败', 29005);
        }

        HospitalCloudPrintersModel::insertOne([
                                                  'device_id'     => $deviceId,
                                                  'device_name'   => $deviceName,
                                                  'group_id'      => 0,
                                                  'org_id'        => $orgId,
                                                  'brand_id'      => $brandId,
                                                  'hospital_id'   => $hospitalId,
                                                  'device_status' => 0,
                                                  'device_online' => 0,
                                                  'created_at'    => date('Y-m-d H:i:s'),
                                              ]);


        return self::Success();
    }

    /**
     * 编辑医院云打印机
     *
     * @param array $params
     * @param array $publicParams
     *
     * @return LogicResult
     * @throws \Exception
     */
    public static function EditHospitalPrinter(array $params, array $publicParams): LogicResult
    {
        $deviceId   = trim(Arr::get($params, 'deviceId', ''));
        $deviceName = trim(Arr::get($params, 'deviceName', ''));
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));

        if (!$deviceId)
        {
            return self::Fail('打印机设备ID不能为空', 400);
        }
        if (!$deviceName)
        {
            return self::Fail('打印机设备名称不能为空', 400);
        }
        if (!$hospitalId)
        {
            return self::Fail('缺少公共必选参数', 400);
        }

        $printer = HospitalCloudPrintersModel::getOneByDeviceId($deviceId, $hospitalId);
        if (empty($printer))
        {
            return self::Fail('云打印机不存在', 29000);
        }

        $cloudEditResult = GPrinter::editDevice($deviceId, $deviceName);
        if (!$cloudEditResult || !isset($cloudEditResult['code']) || $cloudEditResult['code'] != 1)
        {
            return self::Fail($cloudEditResult['msg'] ?? '编辑打印机失败', 29006);
        }

        HospitalCloudPrintersModel::updateOne($printer->id, ['device_name' => $deviceName]);

        return self::Success();
    }

    /**
     * 医院下的打印机列表
     *
     * @param array $publicParams
     *
     * @return LogicResult
     */
    public static function HospitalPrinterList(array $publicParams): LogicResult
    {
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));

        $printers = HospitalCloudPrintersModel::getData(where: ['hospital_id' => $hospitalId, 'status' => 1]);

        $result = [];
        foreach ($printers as $printer)
        {
            $result[] = self::BuildPrinterStruct($printer);
        }

        return self::Success($result);
    }

    /**
     * 刷新打印机状态
     *
     * @param array $params
     * @param array $publicParams
     *
     * @return LogicResult
     * @throws \Exception
     */
    public static function RefreshPrinterStatus(array $params, array $publicParams): LogicResult
    {
        $deviceId   = trim(Arr::get($params, 'deviceId', ''));
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));

        if (!$deviceId)
        {
            return self::Fail('打印机设备ID不能为空', 400);
        }

        $printer = HospitalCloudPrintersModel::getOneByDeviceId($deviceId, $hospitalId);
        if (empty($printer))
        {
            return self::Fail('云打印机不存在', 29000);
        }

        $res = GPrinter::getStatus($deviceId);
        if (!$res || !isset($res['code']) || !isset($res['statusList']) || $res['code'] != 1)
        {
            return self::Fail('刷新打印机状态失败', 29010);
        }

        $statusList = $res['statusList'];
        if (empty($statusList) || !isset($statusList[0]))
        {
            return self::Fail('刷新打印机状态失败', 29010);
        }

        $status = $statusList[0];
        HospitalCloudPrintersModel::updateOne($printer->id, [
                                                              'device_status'  => $status['status'],
                                                              'device_online'  => $status['online'],
                                                              'device_outtime' => $status['outtime'] ? date('Y-m-d H:i:s',
                                                                                                            strtotime($status['outtime'])) : null,
                                                          ]
        );

        return self::Success();
    }

    /**
     * 打印标签
     *
     * @param array $params
     * @param array $publicParams
     *
     * @return LogicResult
     * @throws \Exception
     */
    public static function PrintLabel(array $params, array $publicParams): LogicResult
    {
        $deviceId   = trim(Arr::get($params, 'deviceId', ''));
        $copies     = intval(Arr::get($params, 'copies', 1));
        $type       = trim(Arr::get($params, 'type', ''));
        $content    = Arr::get($params, 'content', []);
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));

        if (!$deviceId)
        {
            return self::Fail('打印机设备ID不能为空', 400);
        }
        if (!$copies)
        {
            return self::Fail('打印份数不能为空', 400);
        }
        if (!$type)
        {
            return self::Fail('标签类型不能为空', 400);
        }
        if (!$hospitalId)
        {
            return self::Fail('缺少公共必选参数', 400);
        }

        if ($copies > self::MAX_LABEL_PRINT_COPIES)
        {
            return self::Fail('打印份数不能超过10', 400);
        }
        if (!CloudPrinterLabelTypeEnum::exists($type))
        {
            return self::Fail('标签类型不存在', 400);
        }

        //根据打印类型验证打印内容
        if ($type == CloudPrinterLabelTypeEnum::itemBarcode->value)
        {
            if (empty($content['barcode']) || empty($content['name']))
            {
                return self::Fail('商品条码标签内容不完整', 400);
            }
        }
        elseif ($type == CloudPrinterLabelTypeEnum::shelfCode->value)
        {
            if (empty($content))
            {
                return self::Fail('货位码标签内容不完整', 400);
            }
        }

        $printer = HospitalCloudPrintersModel::getOneByDeviceId($deviceId, $hospitalId);
        if (empty($printer))
        {
            return self::Fail('云打印机不存在', 29000);
        }

        $printCommand = match ($type)
        {
            CloudPrinterLabelTypeEnum::itemBarcode->value => self::BuildItemBarcodePrintCommand($content, $copies),
            CloudPrinterLabelTypeEnum::shelfCode->value => self::BuildShelfCodePrintCommand($content, $copies),
            default => '',
        };

        if (empty($printCommand))
        {
            return self::Fail('打印指令构建失败', 29090);
        }

        $printRes = GPrinter::print($deviceId, $printCommand);
        if (!$printRes || !isset($printRes['code']) || $printRes['code'] != 0)
        {
            return self::Fail($printRes['msg'] ?? '打印失败', 29091);
        }

        return self::Success();
    }

    /**
     * 打印小票
     *
     * @param array $params
     * @param array $publicParams
     *
     * @return LogicResult
     * @throws \Exception
     */
    public static function PrintReceipt(array $params, array $publicParams): LogicResult
    {
        $deviceId   = trim(Arr::get($params, 'deviceId', ''));
        $copies     = intval(Arr::get($params, 'copies', 1));
        $type       = trim(Arr::get($params, 'type', ''));
        $content    = Arr::get($params, 'content', []);
        $isReprint  = boolval(Arr::get($params, 'isReprint', false));
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));

        if (!$deviceId)
        {
            return self::Fail('打印机设备ID不能为空', 400);
        }
        if (!$copies)
        {
            return self::Fail('打印份数不能为空', 400);
        }
        if (!$type)
        {
            return self::Fail('小票类型不能为空', 400);
        }
        if (!$hospitalId)
        {
            return self::Fail('缺少公共必选参数', 400);
        }

        if ($copies > self::MAX_RECEIPT_PRINT_COPIES)
        {
            return self::Fail('打印份数不能超过2', 400);
        }
        if (!CloudPrinterReceiptTypeEnum::exists($type))
        {
            return self::Fail('小票类型不存在', 400);
        }

        //根据打印类型验证打印内容
        if ($type == CloudPrinterReceiptTypeEnum::cashierReceipt->value)
        {
            if (empty($content['payOrderCode']) || empty($content['printType']))
            {
                return self::Fail('收银小票打印内容不完整', 400);
            }
        }

        $printer = HospitalCloudPrintersModel::getOneByDeviceId($deviceId, $hospitalId);
        if (empty($printer))
        {
            return self::Fail('云打印机不存在', 29000);
        }

        $printCommandRes = match ($type)
        {
            CloudPrinterReceiptTypeEnum::cashierReceipt->value => self::BuildCashierReceiptPrintCommand($content,
                                                                                                        $copies,
                                                                                                        $isReprint),
            default => self::Fail('小票类型不存在', 400),
        };
        if ($printCommandRes->isFail())
        {
            return $printCommandRes;
        }
        $printCommand = $printCommandRes->getData('template');

        if (empty($printCommand))
        {
            return self::Fail('打印指令构建失败', 29090);
        }

        $printRes = GPrinter::print($deviceId, $printCommand);
        if (!$printRes || !isset($printRes['code']) || $printRes['code'] != 0)
        {
            return self::Fail($printRes['msg'] ?? '打印失败', 29091);
        }

        return self::Success();
    }

    /**
     * 构建打印货位码指令
     *
     * @param array $content
     * @param int   $copies
     *
     * @return string
     */
    private static function BuildShelfCodePrintCommand(array $content, int $copies = 1): string
    {
        $shelfCode = [];
        foreach ($content as $code)
        {
            $shelfCode[] = [
                'code'     => $code,
                'quantity' => $copies,
            ];
        }

        return new ShelfCodeLabelTemplate()
            ->setPrintTime()
            ->setShelfCods($shelfCode)
            ->build();
    }

    /**
     * 构建打印商品条码指令
     *
     * @param array $content
     * @param int   $copies
     *
     * @return string
     */
    private static function BuildItemBarcodePrintCommand(array $content, int $copies = 1): string
    {
        return new ItemBarcodeLabelTemplate()
            ->setPrintTime()
            ->setPrintCount($copies)
            ->setBarcode($content['barcode'])
            ->setContent($content['name'])
            ->build();
    }

    /**
     * 构建打印收银小票指令
     *
     * @param array $content
     * @param int   $copies
     * @param bool  $isReprint
     *
     * @return LogicResult
     */
    private static function BuildCashierReceiptPrintCommand(array $content, int $copies = 1, bool $isReprint = false
    ): LogicResult
    {
        $payOrderCode = $content['payOrderCode'] ?? '';
        $printType    = $content['printType'] ?? '';
        if (empty($payOrderCode) || empty($printType))
        {
            return self::Fail('打印收银小票内容不完整', 400);
        }

        $receiptBaseRes = PayOrderLogic::GetOrderReceiptData($payOrderCode);
        if ($receiptBaseRes->isFail())
        {
            return $receiptBaseRes;
        }
        $receiptBase = $receiptBaseRes->getData();

        $receiptBuilder = new CashierReceiptTemplate()
            ->setHospitalName($receiptBase['hospital']['showName'] ?? '')
            ->setHospitalPhone($receiptBase['hospital']['phone'] ?? '')
            ->setHospitalAddress($receiptBase['hospital']['address']['full'] ?? '')
            ->setOperator('张三')
            ->setTime($receiptBase['orderTime'] ?? '')
            ->setOrderCode($receiptBase['payOrderCode'] ?? '')
            ->setMemberName($receiptBase['memberInfo'] ? ($receiptBase['memberInfo']['name'] ?? '') : '')
            ->setMemberPhone($receiptBase['memberInfo'] ? ($receiptBase['memberInfo']['phone'] ?? '') : '')
            ->setPetName($receiptBase['cashierUser'] ? ($receiptBase['cashierUser']['name'] ?? '') : '')
            ->setMemberBalance($receiptBase['memberBalance'] ? ($receiptBase['memberBalance']['balanceTotal'] ?? '') : '')
            ->setItems($receiptBase['items'] ?? [])
            ->setTotalPrice($receiptBase['salePrice'] ?? '')
            ->setTotalCouponPrice($receiptBase['couponPriceTotal'] ?? '')
            ->setTotalDiscountPrice($receiptBase['reducePriceTotal'] ?? '')
            ->setTotalPaymentPrice($receiptBase['payPriceTotal'] ?? '')
            ->setTotalThirdPartyPrice($receiptBase['thirdPartyPrice'] ?? '')
            ->setTotalRechargeBalance($receiptBase['balanceRechargePrice'] ?? '')
            ->setTotalGiftBalance($receiptBase['balanceGiftPrice'] ?? '')
            ->setTotalPayPrice($receiptBase['payPrice'] ?? '');

        if ($receiptBase['hasRecharge'])
        {
            $receiptBuilder->setRechargeTips('温馨提示：请当面核对金额，储值赠送金额属于给客户的优惠福利，当发生储值退款时，赠送部分理应不予退款。');
        }

        if ($isReprint)
        {
            $receiptBuilder->setReprint(true);
        }

        if (!empty($receiptBase['hospital']['wechatUrl']))
        {
            $receiptBuilder->setQrcode($receiptBase['hospital']['wechatUrl']);
        }

        return self::Success(['template' => $receiptBuilder->build()]);
    }

    /**
     * 构建打印机结构体
     *
     * @param array $printer
     *
     * @return array
     */
    private static function BuildPrinterStruct(array $printer): array
    {
        return [
            'deviceId'      => $printer['device_id'],
            'deviceName'    => $printer['device_name'],
            'deviceStatus'  => [
                'id'   => $printer['device_status'],
                'name' => CloudPrinterStatusEnum::getDescription($printer['device_status']),
            ],
            'deviceOnline'  => [
                'id'   => $printer['device_online'],
                'name' => $printer['device_online'] == 1 ? '在线' : '离线',
            ],
            'deviceOuttime' => $printer['device_outtime'],
        ];
    }
}
