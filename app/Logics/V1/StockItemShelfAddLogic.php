<?php

namespace App\Logics\V1;

use DB;
use Arr;
use Log;
use Throwable;
use Exception;
use App\Support\Item\ItemHelper;
use App\Support\Stock\StockQuantityConversionHelper;
use App\Logics\Logic;
use App\Logics\LogicResult;
use App\Enums\PageEnum;
use App\Enums\StockAddTypeEnum;
use App\Models\StockItemShelfModel;
use App\Models\StockItemShelfAddModel;
use App\Models\StockWarehouseShelfModel;

class StockItemShelfAddLogic extends Logic
{
    /**
     * 获取入库类型筛选项
     *
     * @return LogicResult
     */
    public static function GetListFilterOptions(): LogicResult
    {
        return self::Success([
                                 'typeOptions' => StockAddTypeEnum::options(),
                             ]);
    }

    /**
     * 获取入库记录列表
     *
     * @param array $searchParams
     * @param array $publicParams
     *
     * @return LogicResult
     */
    public static function GetAddStockRecord(array $searchParams, array $publicParams): LogicResult
    {
        if (empty($publicParams))
        {
            return self::Fail('获取入库记录，缺少公共必选参数', 400);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        if (empty($hospitalId))
        {
            return self::Fail('获取入库记录，缺少医院ID必选参数', 400);
        }

        // 业务参数处理
        $keywords      = trimWhitespace(Arr::get($searchParams, 'keywords', ''));
        $orderCode     = trimWhitespace(Arr::get($searchParams, 'orderCode', ''));
        $startDate     = trimWhitespace(Arr::get($searchParams, 'startDate', ''));
        $endDate       = trimWhitespace(Arr::get($searchParams, 'endDate', ''));
        $type          = Arr::get($searchParams, 'type');
        $createUserUid = trimWhitespace(Arr::get($searchParams, 'createUserUid', ''));
        $page          = intval(Arr::get($searchParams, 'page', PageEnum::DefaultPageIndex->value)) ?: PageEnum::DefaultPageIndex->value;
        $count         = intval(Arr::get($searchParams, 'count', PageEnum::DefaultPageSize->value)) ?: PageEnum::DefaultPageSize->value;

        // 日期格式验证
        if (!empty($startDate) && !checkDateIsValid($startDate))
        {
            return self::Fail('获取入库记录，开始日期格式错误', 400);
        }
        if (!empty($endDate) && !checkDateIsValid($endDate))
        {
            return self::Fail('获取入库记录，结束日期格式错误', 400);
        }

        // 构建查询参数
        $queryParams = [
            'hospitalId'    => $hospitalId,
            'keywords'      => $keywords,
            'orderCode'     => $orderCode,
            'startDate'     => $startDate,
            'endDate'       => $endDate,
            'type'          => $type,
            'createUserUid' => $createUserUid,
        ];

        // 获取入库记录列表
        $getAddStockRecordRes = StockItemShelfAddModel::getAddStockRecordListData($queryParams, $page, $count);
        if (empty($getAddStockRecordRes))
        {
            return self::Success(['total' => 0, 'data' => []]);
        }

        $totalCount         = $getAddStockRecordRes['total'] ?? 0;
        $addStockRecordList = $getAddStockRecordRes['data'] ?? [];
        if ($totalCount <= 0 || empty($addStockRecordList))
        {
            return self::Success(['total' => 0, 'data' => []]);
        }

        // 格式化返回数据
        $getFormatAddStockRecordRes = self::FormatAddStockRecordStructure($addStockRecordList);
        if ($getFormatAddStockRecordRes->isFail())
        {
            return $getFormatAddStockRecordRes;
        }

        return self::Success(['total' => $totalCount, 'data' => $getFormatAddStockRecordRes->getData()]);
    }

    /**
     * 商品入库操作
     *
     * @param array $addStockParams 需要增加库存的一些必要参数
     * @param array $publicParams   公共参数，包含医院ID、操作人等
     *
     * @return LogicResult
     * @throws Throwable
     */
    public static function AddStockShelfQuantity(array $addStockParams, array $publicParams): LogicResult
    {
        if (empty($addStockParams))
        {
            return self::Fail('商品入库，缺少必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('商品入库，缺少公共必选参数', 400);
        }

        // 公共参数验证
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId', 0));
        $userId     = intval(Arr::get($publicParams, '_userId', 0));
        if (empty($hospitalId))
        {
            return self::Fail('商品入库，缺少医院ID必选参数', 400);
        }
        if (empty($userId))
        {
            return self::Fail('商品入库，缺少操作人ID必选参数', 400);
        }

        // 获取入库商品信息
        $itemIds        = array_unique(array_filter(array_column($addStockParams, 'itemId')));
        $getItemInfoRes = ItemLogic::GetItemFullInfo(itemIds: $itemIds, publicParams: $publicParams, withItemStock: true);
        if ($getItemInfoRes->isFail())
        {
            return $getItemInfoRes;
        }

        // 入库商品信息不完整
        $getItemInfoRes = array_column($getItemInfoRes->getData(), null, 'id');
        if (count($itemIds) != count($getItemInfoRes))
        {
            return self::Fail('商品入库，部分商品信息不存在', 43000);
        }

        // 入库货位信息
        $shelfCodes          = array_unique(array_filter(array_column($addStockParams, 'shelfCode')));
        $getShelfCodeInfoRes = StockWarehouseShelfModel::getData(where: ['hospital_id' => $hospitalId], whereIn: ['shelf_code' => $shelfCodes], keyBy: 'shelf_code');
        if (empty($getShelfCodeInfoRes))
        {
            return self::Fail('商品入库，货位信息不存在', 43001);
        }
        if (count($shelfCodes) != count($getShelfCodeInfoRes))
        {
            return self::Fail('商品入库，部分货位信息不存在', 43001);
        }

        $validAddStockParams = [];
        foreach ($addStockParams as $curAddStockInfo)
        {
            $curCheckParamsRes = self::CheckAddStockItemQuantityParams($curAddStockInfo);
            if ($curCheckParamsRes->isFail())
            {
                return $curCheckParamsRes;
            }

            // 接受验证后的参数
            $curItemId           = $curCheckParamsRes->getData('itemId');
            $curItemBarcode      = $curCheckParamsRes->getData('itemBarcode');
            $curAddType          = $curCheckParamsRes->getData('addType');
            $curAddSubType       = $curCheckParamsRes->getData('addSubType');
            $curRelationCode     = $curCheckParamsRes->getData('relationCode');
            $curRelationId       = $curCheckParamsRes->getData('relationId');
            $curRelationDetailId = $curCheckParamsRes->getData('relationDetailId');
            $curShelfCode        = $curCheckParamsRes->getData('shelfCode');
            $curPackQuantity     = $curCheckParamsRes->getData('packQuantity');
            $curPackPrice        = $curCheckParamsRes->getData('packPrice');
            $curBulkQuantity     = $curCheckParamsRes->getData('bulkQuantity');
            $curBulkPrice        = $curCheckParamsRes->getData('bulkPrice');
            $curRemark           = $curCheckParamsRes->getData('remark');

            // 当前入库商品信息
            $curItemInfo = $getItemInfoRes[$curItemId] ?? [];
            if (empty($curItemInfo))
            {
                return self::Fail('商品入库，商品信息不存在', 43002);
            }

            // 当前入库商品信息条码是否正确
            $curItemBarcodeInfo = $curItemInfo['item_barcode_info'] ?? [];
            if (empty($curItemBarcodeInfo) || $curItemBarcodeInfo['item_barcode'] != $curItemBarcode)
            {
                return self::Fail('商品入库 【' . $curItemBarcode . '】，入库条码与商品条码不匹配', 43002);
            }

            // 当前入库商品货位信息
            $curShelfInfo = $getShelfCodeInfoRes[$curShelfCode] ?? [];
            if (empty($curShelfInfo))
            {
                return self::Fail('商品入库 【' . $curItemBarcode . '】，货位信息不存在', 43002);
            }

            // 获取商品入库效期
            $getAddStockExpirationDateRes = self::GetAddStockItemProduceDateAndExpireDate($curAddStockInfo, $curItemInfo);
            if ($getAddStockExpirationDateRes->isFail())
            {
                return $getAddStockExpirationDateRes;
            }

            // 当前入库商品实际效期
            [$curActualProduceDate, $curActualExpireDate] = $getAddStockExpirationDateRes->getData();

            $validAddStockParams[] = [
                'itemId'                => $curItemId,
                'itemBarcode'           => $curItemBarcode,
                'bulkRatio'             => $curItemInfo['bulk_ratio'],
                'addType'               => $curAddType,
                'addSubType'            => $curAddSubType,
                'relationCode'          => $curRelationCode,
                'relationId'            => $curRelationId,
                'relationDetailId'      => $curRelationDetailId,
                'warehouseId'           => $curShelfInfo['warehouse_id'],
                'shelfLife'             => $curItemInfo['shelf_life'],
                'shelfCode'             => $curShelfCode,
                'packQuantity'          => $curPackQuantity,
                'packPrice'             => $curPackPrice,
                'bulkQuantity'          => $curBulkQuantity,
                'bulkPrice'             => $curBulkPrice,
                'effectivePackQuantity' => $curPackQuantity,
                'effectiveBulkQuantity' => $curBulkQuantity,
                'produceDate'           => $curActualProduceDate,
                'expiredDate'           => $curActualExpireDate,
                'remark'                => $curRemark,
            ];
        }

        // 无有效入库信息
        if (empty($validAddStockParams))
        {
            return self::Fail('商品入库，入库商品信息全部无效', 43002);
        }

        try
        {
            DB::beginTransaction();

            foreach ($validAddStockParams as $curValidAddStockInfo)
            {
                $getAddStockItemShelfRes = self::AddStockItemShelf($curValidAddStockInfo, $publicParams);
                if ($getAddStockItemShelfRes->isFail())
                {
                    DB::rollBack();

                    throw new Exception($getAddStockItemShelfRes->getMessage(), $getAddStockItemShelfRes->getCode());
                }
            }

            DB::commit();

            return self::Success();

        } catch (Throwable $throwable)
        {
            DB::rollBack();

            Log::error(__CLASS__ . '::' . __METHOD__ . ' 商品库存入库异常', [
                'code'    => $throwable->getCode(),
                'message' => $throwable->getMessage(),
                'file'    => $throwable->getFile(),
                'line'    => $throwable->getLine(),
                'trace'   => $throwable->getTraceAsString(),
            ]);

            return self::Fail('商品库存入库异常', 43003);
        }
    }

    /**
     * 添加商品库存
     *
     * @param array $addStockInfo
     * @param array $publicParams
     *
     * @return LogicResult
     * @throws Throwable
     */
    private static function AddStockItemShelf(array $addStockInfo, array $publicParams): LogicResult
    {
        if (empty($addStockInfo))
        {
            return self::Fail('增加商品库存，缺少必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('增加商品库存，缺少公共必选参数', 400);
        }

        // 公共参数
        $orgId      = intval(Arr::get($publicParams, '_hospitalOrgId', 0));
        $brandId    = intval(Arr::get($publicParams, '_hospitalBrandId', 0));
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId', 0));
        $userId     = intval(Arr::get($publicParams, '_userId', 0));
        if (empty($hospitalId))
        {
            return self::Fail('增加商品库存，缺少医院ID必选参数', 400);
        }

        try
        {
            DB::beginTransaction();

            // 库存入库
            $insertStockData   = [
                'org_id'                  => $orgId,
                'brand_id'                => $brandId,
                'hospital_id'             => $hospitalId,
                'warehouse_id'            => $addStockInfo['warehouseId'],
                'item_id'                 => $addStockInfo['itemId'],
                'item_barcode'            => $addStockInfo['itemBarcode'],
                'bulk_ratio'              => $addStockInfo['bulkRatio'],
                'pack_quantity'           => $addStockInfo['packQuantity'],
                'bulk_quantity'           => $addStockInfo['bulkQuantity'],
                'effective_pack_quantity' => $addStockInfo['effectivePackQuantity'],
                'effective_bulk_quantity' => $addStockInfo['effectiveBulkQuantity'],
                'produce_date'            => $addStockInfo['produceDate'] ?: null,
                'expired_date'            => $addStockInfo['expiredDate'] ?: null,
                'shelf_life'              => $addStockInfo['shelfLife'],
                'shelf_code'              => $addStockInfo['shelfCode'],
                'created_by'              => $userId,
            ];
            $insertItemShelfId = StockItemShelfModel::insertOne($insertStockData);

            // 库存入库记录
            $insertStockAddData    = [
                'type'                => $addStockInfo['addType'],
                'sub_type'            => $addStockInfo['addSubType'],
                'relation_code'       => $addStockInfo['relationCode'],
                'relation_id'         => $addStockInfo['relationId'],
                'relation_detail_id'  => $addStockInfo['relationDetailId'],
                'org_id'              => $orgId,
                'brand_id'            => $brandId,
                'hospital_id'         => $hospitalId,
                'warehouse_id'        => $addStockInfo['warehouseId'],
                'stock_item_shelf_id' => $insertItemShelfId,
                'item_id'             => $addStockInfo['itemId'],
                'item_barcode'        => $addStockInfo['itemBarcode'],
                'pack_quantity'       => $addStockInfo['packQuantity'],
                'bulk_quantity'       => $addStockInfo['bulkQuantity'],
                'produce_date'        => $addStockInfo['produceDate'] ?: null,
                'expired_date'        => $addStockInfo['expiredDate'] ?: null,
                'shelf_life'          => $addStockInfo['shelfLife'],
                'shelf_code'          => $addStockInfo['shelfCode'],
                'pack_price'          => $addStockInfo['packQuantity'] > 0 ? $addStockInfo['packPrice'] : 0,
                'bulk_price'          => $addStockInfo['bulkQuantity'] > 0 ? $addStockInfo['bulkPrice'] : 0,
                'bulk_ratio'          => $addStockInfo['bulkRatio'],
                'remark'              => $addStockInfo['remark'] ?? '',
                'created_by'          => $userId,
            ];
            $insertStockShelfAddId = StockItemShelfAddModel::insertOne($insertStockAddData);

            // 更新库存表，记录入库明细ID
            StockItemShelfModel::updateOne($insertItemShelfId, ['stock_item_shelf_add_id' => $insertStockShelfAddId]);

            // 商品上架完成后，进行入库移动加权
            $getUpdateDailyPriceRes = StockItemDailyPriceLogic::UpdateHospitalItemDailyPrice($insertStockShelfAddId, $publicParams);
            if ($getUpdateDailyPriceRes->isFail())
            {
                DB::rollBack();

                return $getUpdateDailyPriceRes;
            }

            DB::commit();

            return self::Success();
        } catch (Throwable $throwable)
        {
            DB::rollBack();

            Log::error(__CLASS__ . '::' . __METHOD__ . ' 商品库存入库异常', [
                'code'    => $throwable->getCode(),
                'message' => $throwable->getMessage(),
                'file'    => $throwable->getFile(),
                'line'    => $throwable->getLine(),
                'trace'   => $throwable->getTraceAsString(),
            ]);

            return self::Fail('增加商品库存异常', 43003);
        }
    }

    /**
     * 获取入库商品效期
     *
     * @param array $addStockInfo
     * @param array $itemInfo
     *
     * @return LogicResult
     */
    public static function GetAddStockItemProduceDateAndExpireDate(array $addStockInfo, array $itemInfo): LogicResult
    {
        if (empty($addStockInfo) || empty($itemInfo))
        {
            return self::Fail('获取入库商品效期信息，缺少必选参数', 400);
        }

        // 初始化变量
        $produceDate = '';
        $expireDate  = '';

        // 无须效期管理
        if (empty($itemInfo['is_shelf_life']))
        {
            return self::Success([$produceDate, $expireDate]);
        }

        // 生产日期、过期日期
        $stockItemProduceDate = $addStockInfo['produceDate'] ?? '';
        $stockItemExpireDate  = $addStockInfo['expiredDate'] ?? '';

        // 效期格式验证
        $shelfLife = trimWhitespace((string) ($itemInfo['shelf_life'] ?? ''));
        if (!preg_match('/^\d{2}-\d{2}-\d{2}$/', $shelfLife))
        {
            return self::Fail('入库商品 【' . $addStockInfo['itemBarcode'] . '】，商品保质期格式错误', 43002);
        }

        // 商品需要效期管理，但是无保值时长，过期日期必填
        if ($itemInfo['shelf_life'] == '00-00-00' && (empty($stockItemExpireDate) || !checkDateIsValid($stockItemExpireDate)))
        {
            return self::Fail('入库商品 【' . $addStockInfo['itemBarcode'] . '】，商品未设置保值时长，过期日期必填', 43002);
        }

        // 生产日期和过期日期不能同时为空
        if (empty($stockItemProduceDate) && empty($stockItemExpireDate))
        {
            return self::Fail('入库商品 【' . $addStockInfo['itemBarcode'] . '】，生产日期和过期日期不能同时为空', 43002);
        }

        // 验证生产日期
        $nowUnix = time();
        if (!empty($stockItemProduceDate))
        {
            if (!checkDateIsValid($stockItemProduceDate))
            {
                return self::Fail('入库商品 【' . $addStockInfo['itemBarcode'] . '】，生产日期格式错误', 43002);
            }

            // 入库验证生产日期
            if (strtotime($stockItemProduceDate) >= $nowUnix)
            {
                return self::Fail('入库商品 【' . $addStockInfo['itemBarcode'] . '】，生产日期不能大于当前日期', 43002);
            }

            // 合法的生产日期
            $produceDate = $stockItemProduceDate;
        }

        // 验证过期日期
        if (!empty($stockItemExpireDate))
        {
            if (!checkDateIsValid($stockItemExpireDate))
            {
                return self::Fail('入库商品 【' . $addStockInfo['itemBarcode'] . '】，过期日期格式错误', 43002);
            }

            // 入库校验过期日期
            if (strtotime($stockItemExpireDate) <= $nowUnix)
            {
                return self::Fail('入库商品 【' . $addStockInfo['itemBarcode'] . '】，过期日期不能小于当前日期', 43002);
            }

            // 合法的过期日期
            $expireDate = $stockItemExpireDate;
        }

        // 商品质保时长
        [$year, $month, $day] = explode('-', $shelfLife);

        // 根据生产日期计算 过期日期
        if (!empty($stockItemProduceDate) && empty($stockItemExpireDate))
        {
            $addTime    = '+' . (int) $year . ' year +' . (int) $month . ' month +' . (int) $day . ' days';
            $expireDate = date('Y-m-d', strtotime($addTime, strtotime($stockItemProduceDate)));

            // 根据生产日期和效期计算得到过期时间, 如小于当前时间,提示
            if (strtotime($expireDate) <= $nowUnix)
            {
                return self::Fail('入库商品 【' . $addStockInfo['itemBarcode'] . '】，根据生产日期和保质期计算得到的过期日期小于当前日期', 43002);
            }
        }

        // 根据过期日期计算生产日期
        if (!empty($stockItemExpireDate) && empty($stockItemProduceDate))
        {
            $reduceTime  = '-' . (int) $year . ' year -' . (int) $month . ' month -' . (int) $day . ' days';
            $produceDate = date('Y-m-d', strtotime($reduceTime, strtotime($stockItemExpireDate)));
            if (strtotime($produceDate) > $nowUnix)
            {
                $produceDate = date("Y-m-d", $nowUnix);
            }
        }

        return self::Success([$produceDate, $expireDate]);
    }

    /**
     * 验证增加库存参数
     *
     * @param array $curAddStockInfo
     *
     * @return LogicResult
     */
    private static function CheckAddStockItemQuantityParams(array $curAddStockInfo): LogicResult
    {
        $curItemId           = intval(Arr::get($curAddStockInfo, 'itemId', 0));
        $curItemBarcode      = trimWhitespace(Arr::get($curAddStockInfo, 'itemBarcode', ''));
        $curAddType          = intval(Arr::get($curAddStockInfo, 'addType', 0));
        $curAddSubType       = intval(Arr::get($curAddStockInfo, 'addSubType', 0));
        $curRelationCode     = trimWhitespace(Arr::get($curAddStockInfo, 'relationCode', ''));
        $curRelationId       = intval(Arr::get($curAddStockInfo, 'relationId', 0));
        $curRelationDetailId = intval(Arr::get($curAddStockInfo, 'relationDetailId', 0));
        $curShelfCode        = trimWhitespace(Arr::get($curAddStockInfo, 'shelfCode', ''));
        $curPackQuantity     = Arr::get($curAddStockInfo, 'packQuantity', 0);
        $curPackPrice        = Arr::get($curAddStockInfo, 'packPrice', 0);
        $curBulkQuantity     = Arr::get($curAddStockInfo, 'bulkQuantity', 0);
        $curBulkPrice        = Arr::get($curAddStockInfo, 'bulkPrice', 0);
        $curProduceDate      = Arr::get($curAddStockInfo, 'produceDate');
        $curExpiredDate      = Arr::get($curAddStockInfo, 'expiredDate');
        $curRemark           = trimWhitespace(Arr::get($curAddStockInfo, 'remark', ''));
        if (empty($curItemId) && empty($curItemBarcode))
        {
            return self::Fail('商品入库，商品条码错误', 43002);
        }

        $errorPrefix = '商品入库 【' . $curItemBarcode . '】';
        if (empty($curAddType) || StockAddTypeEnum::notExists($curAddType))
        {
            return self::Fail($errorPrefix . '，入库类型错误', 43002);
        }
        if (StockAddTypeEnum::typeIsMustSubType($curAddType) && StockAddTypeEnum::notExists($curAddSubType))
        {
            return self::Fail($errorPrefix . '，入库子类型错误', 43002);
        }
        if (empty($curRelationCode))
        {
            return self::Fail($errorPrefix . '，入库关联单据号错误', 43002);
        }
        if (empty($curRelationId))
        {
            return self::Fail($errorPrefix . '，入库关联ID错误', 43002);
        }
        if (empty($curRelationDetailId))
        {
            return self::Fail($errorPrefix . '，入库关联明细ID错误', 43002);
        }
        if (empty($curShelfCode))
        {
            return self::Fail($errorPrefix . '，入库货架编码错误', 43002);
        }
        if ($curPackQuantity <= 0 && $curBulkQuantity <= 0)
        {
            return self::Fail($errorPrefix . '，入库整装数量和入库散装数量不能同时为0或负数', 43002);
        }
        if ($curPackQuantity > 0 && $curPackPrice < 0)
        {
            return self::Fail($errorPrefix . '，入库整装数量大于0时，入库整装价格不可小于0', 43002);
        }
        if ($curPackPrice > 0 && $curPackQuantity <= 0)
        {
            return self::Fail($errorPrefix . '，入库整装价格大于0时，入库整装数量不可小于等于0', 43002);
        }
        if ($curBulkQuantity > 0 && $curBulkPrice < 0)
        {
            return self::Fail($errorPrefix . '，入库散装数量大于0时，入库散装价格不可小于0', 43002);
        }
        if ($curBulkPrice > 0 && $curBulkQuantity <= 0)
        {
            return self::Fail($errorPrefix . '，入库散装价格大于0时，入库散装数量不可小于等于0', 43002);
        }
        if (StockAddTypeEnum::typeIsMustRemark($curAddType) && empty($curRemark))
        {
            return self::Fail($errorPrefix . '，入库类型需要填写备注', 43002);
        }

        $returnStockItemInfo = [
            'itemId'           => $curItemId,
            'itemBarcode'      => $curItemBarcode,
            'addType'          => $curAddType,
            'addSubType'       => $curAddSubType,
            'relationCode'     => $curRelationCode,
            'relationId'       => $curRelationId,
            'relationDetailId' => $curRelationDetailId,
            'shelfCode'        => $curShelfCode,
            'packQuantity'     => $curPackQuantity,
            'packPrice'        => $curPackPrice,
            'bulkQuantity'     => $curBulkQuantity,
            'bulkPrice'        => $curBulkPrice,
            'produceDate'      => $curProduceDate,
            'expiredDate'      => $curExpiredDate,
            'remark'           => $curRemark,
        ];

        return self::Success($returnStockItemInfo);
    }

    /**
     * 格式化入库记录
     *
     * @param array $addStockRecordList
     *
     * @return LogicResult
     */
    private static function FormatAddStockRecordStructure(array $addStockRecordList): LogicResult
    {
        if (empty($addStockRecordList))
        {
            return self::Success();
        }

        $returnStockRecordList = [];
        foreach ($addStockRecordList as $curAddRecord)
        {
            $returnStockRecordList[] = [
                'type'        => [
                    'id'   => $curAddRecord['type'],
                    'name' => StockAddTypeEnum::getDescription($curAddRecord['type']),
                ],
                'itemUid'     => $curAddRecord['item_uid'],
                'itemBarcode' => $curAddRecord['item_barcode'],
                'itemName'    => ItemHelper::ItemDisplayName($curAddRecord),
                'shelfLife'   => $curAddRecord['shelf_life_day'] . '天',
                'produceDate' => $curAddRecord['produce_date'],
                'expiredDate' => $curAddRecord['expired_date'],
                'shelfCode'   => $curAddRecord['shelf_code'],
                'quantity'    => StockQuantityConversionHelper::getStockQuantityStructure($curAddRecord),
                'createUser'  => [
                    'uid'  => $curAddRecord['user_uid'],
                    'name' => $curAddRecord['user_name'],
                ],
                'remark'      => $curAddRecord['remark'],
                'createTime'  => formatDisplayDateTime($curAddRecord['created_at']),
            ];
        }

        return self::Success($returnStockRecordList);
    }
}
