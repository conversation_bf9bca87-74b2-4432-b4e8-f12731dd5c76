<?php

namespace App\Logics\V1;

use Arr;
use App\Logics\Logic;
use App\Logics\LogicResult;
use App\Models\RecipesStockOccupyModel;

class RecipeStockOccupyLogic extends Logic
{
    /**
     * 删除处方占用库存
     *
     * @param int   $recipeId
     * @param array $publicParams
     *
     * @return LogicResult
     */
    public static function DeleteRecipeStockOccupy(int $recipeId, array $publicParams): LogicResult
    {
        if (empty($recipeId))
        {
            return self::Fail('删除处方占用，缺少处方ID', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('删除处方占用，缺少公共参数', 400);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        $userId     = intval(Arr::get($publicParams, '_userId'));
        if (empty($hospitalId))
        {
            return self::Fail('删除处方占用，缺少医院ID必选参数', 400);
        }
        if (empty($userId))
        {
            return self::Fail('删除处方占用，缺少医生ID必选参数', 400);
        }

        // 是否存在占用
        $getOccupyTotalRes = RecipesStockOccupyModel::getTotalNumber(where: ['recipe_id' => $recipeId, 'status' => 1]);
        if ($getOccupyTotalRes <= 0)
        {
            return self::Success();
        }

        // 删除处方占用库存
        RecipesStockOccupyModel::on()
                               ->where(['recipe_id' => $recipeId])
                               ->update(['status' => 0, 'deleted_by' => $userId, 'deleted_at' => getCurrentTimeWithMilliseconds()]);

        return self::Success();
    }
}
