<?php

namespace App\Logics\V1;

use DB;
use Log;
use Arr;
use Throwable;
use App\Logics\Logic;
use App\Logics\LogicResult;
use App\Enums\TransferStatusEnum;
use App\Enums\CaseSourceTypeEnum;
use App\Enums\RegistrationTypeEnum;
use App\Enums\TreatmentOutcomeEnum;
use App\Enums\OutpatientStatusEnum;
use App\Enums\BusinessCodePrefixEnum;
use App\Enums\RegistrationStatusEnum;
use App\Enums\RegistrationDoctorSelectedTypeEnum;
use App\Models\CasesModel;
use App\Models\TransfersModel;
use App\Models\MemberPetsModel;
use App\Models\OutpatientModel;
use App\Models\PetVitalSignModel;
use App\Models\RegistrationsModel;
use App\Models\RegistrationTypeModel;
use App\Models\RegistrationReasonModel;
use App\Models\RegistrationPetRecordModel;
use App\Models\OutpatientChangeStatusLogModel;

class OutpatientLogic extends Logic
{
    /**
     * 获取有效门诊信息
     *
     * @param int   $outpatientId
     * @param array $publicParams
     * @param bool  $withHospitalId
     * @param bool  $withDoctorId
     *
     * @return LogicResult
     */
    public static function GetValidOutpatientById(int $outpatientId, array $publicParams, bool $withHospitalId = true, bool $withDoctorId = true): LogicResult
    {
        if (empty($outpatientId))
        {
            return self::Fail('获取有效门诊信息，缺少门诊ID必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('获取有效门诊信息，缺少公共必选参数', 400);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        $doctorId   = intval(Arr::get($publicParams, '_userId'));
        if (empty($hospitalId))
        {
            return self::Fail('获取有效门诊信息，缺少医院ID必选参数', 400);
        }
        if (empty($doctorId))
        {
            return self::Fail('获取有效门诊信息，缺少医生ID必选参数', 400);
        }

        // 获取有效门诊信息
        $where = [
            'id'      => $outpatientId,
            'invalid' => 0,
        ];
        if (!empty($withHospitalId))
        {
            $where['hospital_id'] = $hospitalId;
        }
        if (!empty($withDoctorId))
        {
            $where['doctor_id'] = $doctorId;
        }

        $getOutpatientRes = OutpatientModel::getData(where: $where);
        if (empty($getOutpatientRes))
        {
            return self::Fail('门诊不存在', 36000);
        }

        return self::Success(current($getOutpatientRes));
    }

    /**
     * 获取正在诊断的门诊信息
     *
     * @param int   $outpatientId
     * @param array $publicParams
     * @param bool  $withHospitalId
     * @param bool  $withDoctorId
     *
     * @return LogicResult
     */
    public static function GetUnderWayOutpatientById(int $outpatientId, array $publicParams, bool $withHospitalId = true, bool $withDoctorId = true): LogicResult
    {
        if (empty($outpatientId))
        {
            return self::Fail('获取正在进行门诊信息，缺少门诊ID必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('获取正在进行门诊信息，缺少公共必选参数', 400);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        $doctorId   = intval(Arr::get($publicParams, '_userId'));
        if (empty($hospitalId))
        {
            return self::Fail('获取正在进行门诊信息，缺少医院ID必选参数', 400);
        }
        if (empty($doctorId))
        {
            return self::Fail('获取正在进行门诊信息，缺少医生ID必选参数', 400);
        }

        // 获取门诊信息
        $getValidOutpatientRes = self::GetValidOutpatientById($outpatientId,
                                                              $publicParams,
                                                              $withHospitalId,
                                                              $withDoctorId);
        if ($getValidOutpatientRes->isFail())
        {
            return $getValidOutpatientRes;
        }

        $getOutpatientRes = $getValidOutpatientRes->getData();
        if ($getOutpatientRes['status'] != OutpatientStatusEnum::InTreatment->value)
        {
            return self::Fail('门诊状态非诊断中，不可操作', 36001);
        }

        return self::Success($getOutpatientRes);
    }

    /**
     * 获取门诊数量
     *
     * @param int $hospitalId
     * @param int $doctorId
     *
     * @return LogicResult
     */
    public static function GetOutpatientTotal(int $hospitalId, int $doctorId): LogicResult
    {
        if (empty($hospitalId) || empty($doctorId))
        {
            return self::Fail('获取门诊数量，缺少必选参数', 400);
        }

        // 获取医院下医生门诊数量
        $getDoctorOutpatientTotalInfoRes = OutpatientModel::getDoctorOutpatientTotalInfo($hospitalId, [$doctorId]);

        // 获取医院下未选择医生的门诊数量
        $getUnselectedDoctorOutpatientTotalInfoRes = OutpatientModel::getDoctorOutpatientTotalInfo($hospitalId, [0]);

        if (empty($getDoctorOutpatientTotalInfoRes) && empty($getUnselectedDoctorOutpatientTotalInfoRes))
        {
            return self::Success(['inTotal' => 0, 'stopTotal' => 0]);
        }

        $inOutpatientTotal   = 0;
        $stopOutpatientTotal = 0;
        foreach ($getDoctorOutpatientTotalInfoRes as $outpatientInfo)
        {
            $inOutpatientTotal   += $outpatientInfo['in_treatment_count'] + $outpatientInfo['waiting_count'];
            $stopOutpatientTotal += $outpatientInfo['stop_treatment_count'];
        }

        foreach ($getUnselectedDoctorOutpatientTotalInfoRes as $outpatientInfo)
        {
            $inOutpatientTotal   += $outpatientInfo['in_treatment_count'] + $outpatientInfo['waiting_count'];
            $stopOutpatientTotal += $outpatientInfo['stop_treatment_count'];
        }

        return self::Success([
                                 'inTotal'   => $inOutpatientTotal,
                                 'stopTotal' => $stopOutpatientTotal,
                             ]);
    }

    /**
     * 获取门诊-候诊列表
     *
     * @param int $hospitalId
     * @param int $doctorId
     *
     * @return LogicResult
     */
    public static function GetUnderwayList(int $hospitalId, int $doctorId): LogicResult
    {
        if (empty($hospitalId) || empty($doctorId))
        {
            return self::Fail('获取门诊列表，缺少必选参数', 400);
        }

        // 获取门诊数量
        $getOutpatientTotalRes = self::GetOutpatientTotal($hospitalId, $doctorId);
        if ($getOutpatientTotalRes->isFail())
        {
            return $getOutpatientTotalRes;
        }

        // 无任何候诊列表
        $inTotal = $getOutpatientTotalRes->getData('inTotal', 0);
        if ($inTotal <= 0)
        {
            return self::Success(['data' => []]);
        }

        // 获取门诊数据
        $getOutpatientListRes = OutpatientModel::getOutpatientByDoctorId($hospitalId,
                                                                         [0, $doctorId],
                                                                         [
                                                                             OutpatientStatusEnum::Waiting->value,
                                                                             OutpatientStatusEnum::InTreatment->value
                                                                         ]);
        if ($getOutpatientListRes->isEmpty())
        {
            return self::Success(['data' => []]);
        }

        $getOutpatientListRes = $getOutpatientListRes->toArray();

        // 获取挂号数据
        $registrationIds        = array_column($getOutpatientListRes, 'registration_id');
        $getRegistrationListRes = RegistrationsModel::getData(where  : ['hospital_id' => $hospitalId],
                                                              whereIn: ['id' => $registrationIds],
                                                              keyBy  : 'id');
        if (empty($getRegistrationListRes))
        {
            return self::Success(['data' => []]);
        }

        // 获取宠物数据
        $petIds    = array_unique(array_column($getRegistrationListRes, 'pet_id'));
        $getPetRes = PetLogic::GetPetBaseInfoByPetIds($petIds);
        if ($getPetRes->isFail())
        {
            return $getPetRes;
        }

        $getPetRes = $getPetRes->getData();

        // 获取挂号原因
        $reasonIds                = array_unique(array_column($getRegistrationListRes, 'reason_id'));
        $getRegistrationReasonRes = RegistrationReasonModel::getData(whereIn: ['id' => $reasonIds], keyBy: 'id');

        // 获取挂号类型
        $typeIds                    = array_unique(array_column($getRegistrationListRes, 'type_id'));
        $getRegistrationTypeListRes = RegistrationTypeModel::getData(whereIn: ['id' => $typeIds], keyBy: 'id');

        $returnOutpatientList = [];
        foreach ($getOutpatientListRes as $outpatientInfo)
        {
            $curRegistrationInfo = $getRegistrationListRes[$outpatientInfo['registration_id']] ?? [];
            if (empty($curRegistrationInfo))
            {
                continue;
            }

            $curPetInfo    = $getPetRes[$curRegistrationInfo['pet_id']]['petInfo'] ?? [];
            $curReasonInfo = $getRegistrationReasonRes[$curRegistrationInfo['reason_id']] ?? [];
            $curTypeInfo   = $getRegistrationTypeListRes[$curRegistrationInfo['type_id']] ?? [];

            $tmpOutpatientInfo = [
                'outpatientCode'         => $outpatientInfo['outpatient_code'],
                'outpatientStatus'       => $outpatientInfo['status'],
                'outpatientStatusFormat' => OutpatientStatusEnum::getDescription($outpatientInfo['status']),
                'doctorSelectedType'     => $curRegistrationInfo['doctor_selected_type'],
                'registrationInfo'       => [
                    'registrationTime' => formatDisplayDateTime($curRegistrationInfo['created_at']),
                    'reason'           => [
                        'id'   => $curReasonInfo['id'] ?? 0,
                        'name' => $curReasonInfo['name'] ?? '',
                    ],
                    'type'             => [
                        'id'   => $curTypeInfo['id'] ?? 0,
                        'name' => $curTypeInfo['name'] ?? '',
                    ]
                ],
                'petInfo'                => $curPetInfo
            ];

            $returnOutpatientList[] = $tmpOutpatientInfo;
        }

        return self::Success(['data' => $returnOutpatientList]);
    }

    /**
     * 获取门诊-暂停列表
     *
     * @param int $hospitalId
     * @param int $doctorId
     *
     * @return LogicResult
     */
    public static function GetSuspendList(int $hospitalId, int $doctorId): LogicResult
    {
        if (empty($hospitalId) || empty($doctorId))
        {
            return self::Fail('获取门诊列表，缺少必选参数', 400);
        }

        // 获取门诊数量
        $getOutpatientTotalRes = self::GetOutpatientTotal($hospitalId, $doctorId);
        if ($getOutpatientTotalRes->isFail())
        {
            return $getOutpatientTotalRes;
        }

        // 无任何暂停门诊
        $stopTotal = $getOutpatientTotalRes->getData('stopTotal') ?? 0;
        if ($stopTotal <= 0)
        {
            return self::Success(['data' => []]);
        }

        // 获取门诊数据
        $getOutpatientListRes = OutpatientModel::getOutpatientByDoctorId($hospitalId,
                                                                         [$doctorId],
                                                                         [OutpatientStatusEnum::Stop->value]);
        if ($getOutpatientListRes->isEmpty())
        {
            return self::Success(['data' => []]);
        }

        $getOutpatientListRes = $getOutpatientListRes->toArray();

        // 获取挂号数据
        $registrationIds        = array_column($getOutpatientListRes, 'registration_id');
        $getRegistrationListRes = RegistrationsModel::getData(where  : ['hospital_id' => $hospitalId],
                                                              whereIn: ['id' => $registrationIds],
                                                              keyBy  : 'id');
        if (empty($getRegistrationListRes))
        {
            return self::Success(['data' => []]);
        }

        // 获取宠物数据
        $petIds    = array_unique(array_column($getRegistrationListRes, 'pet_id'));
        $getPetRes = PetLogic::GetPetBaseInfoByPetIds($petIds);
        if ($getPetRes->isFail())
        {
            return $getPetRes;
        }

        $getPetRes = $getPetRes->getData();

        // 获取挂号原因
        $reasonIds                = array_unique(array_column($getRegistrationListRes, 'reason_id'));
        $getRegistrationReasonRes = RegistrationReasonModel::getData(whereIn: ['id' => $reasonIds], keyBy: 'id');

        $returnOutpatientList = [];
        foreach ($getOutpatientListRes as $outpatientInfo)
        {
            $curRegistrationInfo = $getRegistrationListRes[$outpatientInfo['registration_id']] ?? [];
            if (empty($curRegistrationInfo))
            {
                continue;
            }

            $curPetInfo    = $getPetRes[$curRegistrationInfo['pet_id']]['petInfo'] ?? [];
            $curReasonInfo = $getRegistrationReasonRes[$curRegistrationInfo['reason_id']] ?? [];

            $tmpOutpatientInfo = [
                'outpatientCode'         => $outpatientInfo['outpatient_code'],
                'outpatientStatus'       => $outpatientInfo['status'],
                'outpatientStatusFormat' => OutpatientStatusEnum::getDescription($outpatientInfo['status']),
                'suspendTreatmentDate'   => $outpatientInfo['suspend_treatment_date'],
                'registrationInfo'       => [
                    'registrationTime' => formatDisplayDateTime($curRegistrationInfo['created_at']),
                    'reason'           => [
                        'id'   => $curReasonInfo['id'],
                        'name' => $curReasonInfo['name'],
                    ],
                ],
                'petInfo'                => $curPetInfo
            ];

            $returnOutpatientList[] = $tmpOutpatientInfo;
        }

        return self::Success(['data' => $returnOutpatientList]);
    }

    /**
     * 门诊-接诊
     *
     * @param       $outpatientId
     * @param array $publicParams
     *
     * @return LogicResult
     * @throws Throwable
     * @noinspection PhpUnused
     */
    public static function StartOutpatient($outpatientId, array $publicParams): LogicResult
    {
        if (empty($outpatientId))
        {
            return self::Fail('outpatientId，缺少必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('publicParams，缺少必选参数', 400);
        }

        // 公共参数
        $hospitalId      = intval(Arr::get($publicParams, '_hospitalId'));
        $hospitalBrandId = intval(Arr::get($publicParams, '_hospitalBrandId'));
        $hospitalOrgId   = intval(Arr::get($publicParams, '_hospitalOrgId'));
        $doctorId        = intval(Arr::get($publicParams, '_userId'));
        if (empty($hospitalId) || empty($hospitalBrandId) || empty($hospitalOrgId))
        {
            return self::Fail('hospitalId、hospitalBrandId、hospitalOrgId，缺少必选参数', 400);
        }
        if (empty($doctorId))
        {
            return self::Fail('doctorId，缺少必选参数', 400);
        }

        // 获取门诊信息
        $getValidOutpatientRes = self::GetValidOutpatientById($outpatientId, $publicParams, true, false);
        if ($getValidOutpatientRes->isFail())
        {
            return $getValidOutpatientRes;
        }

        $getOutpatientRes = $getValidOutpatientRes->getData();
        if ($getOutpatientRes['status'] != OutpatientStatusEnum::Waiting->value)
        {
            return self::Fail('就诊状态发生变化，不可接诊', 36001);
        }
        if (!empty($getOutpatientRes['doctor_id']) && $getOutpatientRes['doctor_id'] != $doctorId)
        {
            return self::Fail('无法接诊其它医生的门诊', 36002);
        }

        $memberId       = $getOutpatientRes['member_id'];
        $petId          = $getOutpatientRes['pet_id'];
        $registrationId = $getOutpatientRes['registration_id'];

        // 获取门诊关联的挂号
        $getRegistrationRes = RegistrationsModel::getOne($registrationId);
        if (empty($getRegistrationRes))
        {
            return self::Fail('门诊关联挂号不存在', 35011);
        }

        // 获取门诊关联挂号时候填写的宠物体重信息
        $registrationPetWeight     = 0;
        $registrationPetBirthday   = '';
        $getRegistrationPetInfoRes = RegistrationPetRecordModel::getData(where: ['registration_id' => $registrationId]);
        if (!empty($getRegistrationPetInfoRes))
        {
            $registrationPetWeight   = current($getRegistrationPetInfoRes)['weight'];
            $registrationPetBirthday = current($getRegistrationPetInfoRes)['birthday'];
        }

        // 获取挂号时宠物体征信息
        $getRegistrationVitalSignRes = PetVitalSignModel::getData(where: [
                                                                             'registration_id' => $registrationId,
                                                                             'case_id'         => 0
                                                                         ]);
        if (!empty($getRegistrationVitalSignRes))
        {
            $getRegistrationVitalSignRes = current($getRegistrationVitalSignRes);
        }

        // 获取医生正在进行的门诊
        $getDoctorInTreatmentOutpatientRes = OutpatientModel::getValidOutpatientByDoctorIds($hospitalId,
                                                                                            $doctorId,
                                                                                            [OutpatientStatusEnum::InTreatment->value]);

        // 获取医生正在诊断的门诊ID
        $doctorInTreatmentOutpatientIds = $getDoctorInTreatmentOutpatientRes->pluck('id')
                                                                            ->toArray();

        try
        {
            DB::beginTransaction();

            // 诊断中的门诊更新状态为暂停，并且记录日志
            if (!empty($doctorInTreatmentOutpatientIds))
            {
                // 更新进行中的门诊为暂停
                OutpatientModel::on()
                               ->whereIn('id', $doctorInTreatmentOutpatientIds)
                               ->update(['status' => OutpatientStatusEnum::Stop->value]);

                $insertOutpatientStatusLogData = [];
                foreach ($doctorInTreatmentOutpatientIds as $inTreatmentOutpatientId)
                {
                    $insertOutpatientStatusLogData = [
                        'outpatient_id' => $inTreatmentOutpatientId,
                        'doctor_id'     => $doctorId,
                        'desc'          => OutpatientStatusEnum::getDescription(OutpatientStatusEnum::Stop->value),
                        'status'        => OutpatientStatusEnum::Stop->value,
                    ];
                }

                // 记录门诊状态变更日志
                OutpatientChangeStatusLogModel::insert($insertOutpatientStatusLogData);
            }

            // 更新挂号为使用状态，并且判断如果挂号没有选择医生，更新为医生主动接诊并记录医生ID
            $upRegistrationData = ['status' => RegistrationStatusEnum::Used->value];
            if (empty($getOutpatientRes['doctor_id']))
            {
                $upRegistrationData['doctor_id']            = $doctorId;
                $upRegistrationData['doctor_selected_type'] = RegistrationDoctorSelectedTypeEnum::DoctorSelected->value;
            }
            RegistrationsModel::updateOne($registrationId, $upRegistrationData);

            // 更新当前门诊为诊断中状态
            OutpatientModel::updateOne($outpatientId,
                                       [
                                           'doctor_id'            => $doctorId,
                                           'status'               => OutpatientStatusEnum::InTreatment->value,
                                           'start_treatment_date' => getCurrentTimeWithMilliseconds()
                                       ]);

            // 记录门诊状态变更日志
            OutpatientChangeStatusLogModel::insertOne([
                                                          'outpatient_id' => $outpatientId,
                                                          'doctor_id'     => $doctorId,
                                                          'desc'          => OutpatientStatusEnum::getDescription(OutpatientStatusEnum::InTreatment->value),
                                                          'status'        => OutpatientStatusEnum::InTreatment->value,
                                                      ]);

            // 生成病历信息
            $insertPetCaseData = [
                'case_code'          => generateBusinessCodeNumber(BusinessCodePrefixEnum::MZBL),
                'source_type'        => CaseSourceTypeEnum::Outpatient->value,
                'source_relation_id' => $outpatientId,
                'hospital_id'        => $hospitalId,
                'brand_id'           => $hospitalBrandId,
                'org_id'             => $hospitalOrgId,
                'doctor_id'          => $doctorId,
                'member_id'          => $memberId,
                'pet_id'             => $petId,
            ];
            $caseId            = CasesModel::insertOne($insertPetCaseData);

            // 更新病历关联挂号时宠物体况信息
            if (!empty($getRegistrationVitalSignRes))
            {
                PetVitalSignModel::on()
                                 ->where(['id' => $getRegistrationVitalSignRes['id']])
                                 ->update(['case_id' => $caseId]);
            }
            else
            {
                $insertPetVitalSignData = [
                    'member_id'       => $memberId,
                    'pet_id'          => $petId,
                    'registration_id' => $registrationId,
                    'case_id'         => $caseId,
                    'weight'          => $registrationPetWeight,
                    'birthday'        => $registrationPetBirthday,
                ];
                PetVitalSignModel::insertOne($insertPetVitalSignData);
            }

            // 如果门诊是院内转诊，更新转诊单为已诊断
            if ($getRegistrationRes['type_id'] == RegistrationTypeEnum::TransferVisit->value)
            {
                // 获取转诊单
                $getTransferRes = TransfersModel::getData(where: [
                                                                     'transfer_registration_id' => $registrationId,
                                                                     'status'                   => TransferStatusEnum::Success->value
                                                                 ]);
                $getTransferRes = $getTransferRes ? current($getTransferRes) : [];
                if (empty($getTransferRes))
                {
                    DB::rollBack();

                    return self::Fail('门诊关联转诊单不存在，接诊异常', 36001);
                }

                // 更新转诊单为已诊断，并且如果院外转诊的话，记录选择的医生
                $upTransferData = [
                    'transfer_case_id' => $caseId,
                ];
                if (empty($getTransferRes['transfer_doctor_id']) && $doctorId > 0)
                {
                    $upTransferData['transfer_doctor_id'] = $doctorId;
                }

                TransfersModel::updateOne($getTransferRes['id'], $upTransferData);
            }

            DB::commit();

            return self::Success();
        } catch (Throwable $throwable)
        {
            DB::rollBack();

            Log::error(__CLASS__ . '::' . __METHOD__ . ' 开始门诊异常', [
                'code'    => $throwable->getCode(),
                'message' => $throwable->getMessage(),
                'file'    => $throwable->getFile(),
                'line'    => $throwable->getLine(),
                'trace'   => $throwable->getTraceAsString(),
            ]);

            return self::Fail('开始门诊异常', 36007);
        }
    }

    /**
     * 门诊-暂停诊断
     *
     * @param       $outpatientId
     * @param array $publicParams
     *
     * @return LogicResult
     * @throws Throwable
     * @noinspection PhpUnused
     */
    public static function SuspendOutpatient($outpatientId, array $publicParams): LogicResult
    {
        if (empty($outpatientId))
        {
            return self::Fail('outpatientId，缺少必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('publicParams，缺少必选参数', 400);
        }

        // 公共参数
        $hospitalId      = intval(Arr::get($publicParams, '_hospitalId'));
        $hospitalBrandId = intval(Arr::get($publicParams, '_hospitalBrandId'));
        $hospitalOrgId   = intval(Arr::get($publicParams, '_hospitalOrgId'));
        $doctorId        = intval(Arr::get($publicParams, '_userId'));
        if (empty($hospitalId) || empty($hospitalBrandId) || empty($hospitalOrgId))
        {
            return self::Fail('hospitalId、hospitalBrandId、hospitalOrgId，缺少必选参数', 400);
        }
        if (empty($doctorId))
        {
            return self::Fail('doctorId，缺少必选参数', 400);
        }

        // 获取正在进行门诊信息
        $getValidOutpatientRes = self::GetUnderWayOutpatientById($outpatientId, $publicParams);
        if ($getValidOutpatientRes->isFail())
        {
            return $getValidOutpatientRes;
        }

        try
        {
            DB::beginTransaction();

            // 更新门诊为暂停状态
            OutpatientModel::updateOne($outpatientId,
                                       [
                                           'status'                 => OutpatientStatusEnum::Stop->value,
                                           'suspend_treatment_date' => getCurrentTimeWithMilliseconds()
                                       ]);

            // 记录门诊状态变更日志
            OutpatientChangeStatusLogModel::insertOne([
                                                          'outpatient_id' => $outpatientId,
                                                          'doctor_id'     => $doctorId,
                                                          'desc'          => OutpatientStatusEnum::getDescription(OutpatientStatusEnum::Stop->value),
                                                          'status'        => OutpatientStatusEnum::Stop->value,
                                                      ]);

            DB::commit();

            return self::Success();
        } catch (Throwable $throwable)
        {
            DB::rollBack();

            Log::error(__CLASS__ . '::' . __METHOD__ . ' 暂停门诊异常', [
                'code'    => $throwable->getCode(),
                'message' => $throwable->getMessage(),
                'file'    => $throwable->getFile(),
                'line'    => $throwable->getLine(),
                'trace'   => $throwable->getTraceAsString(),
            ]);

            return self::Fail('暂停门诊异常', 36007);
        }
    }

    /**
     * 门诊-重启就诊中
     *
     * @param int   $outpatientId
     * @param array $publicParams
     *
     * @return LogicResult
     * @throws Throwable
     *
     * @noinspection PhpUnused
     */
    public static function RestartOutpatient(int $outpatientId, array $publicParams): LogicResult
    {
        if (empty($outpatientId))
        {
            return self::Fail('outpatientId，缺少必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('publicParams，缺少必选参数', 400);
        }

        // 公共参数
        $hospitalId      = intval(Arr::get($publicParams, '_hospitalId'));
        $hospitalBrandId = intval(Arr::get($publicParams, '_hospitalBrandId'));
        $hospitalOrgId   = intval(Arr::get($publicParams, '_hospitalOrgId'));
        $doctorId        = intval(Arr::get($publicParams, '_userId'));
        if (empty($hospitalId) || empty($hospitalBrandId) || empty($hospitalOrgId))
        {
            return self::Fail('hospitalId、hospitalBrandId、hospitalOrgId，缺少必选参数', 400);
        }
        if (empty($doctorId))
        {
            return self::Fail('doctorId，缺少必选参数', 400);
        }

        // 获取门诊信息
        $getValidOutpatientRes = self::GetValidOutpatientById($outpatientId, $publicParams);
        if ($getValidOutpatientRes->isFail())
        {
            return $getValidOutpatientRes;
        }

        // 门诊现在状态是否暂停，只有暂停才可以重启
        $getOutpatientRes = $getValidOutpatientRes->getData();
        if ($getOutpatientRes['status'] != OutpatientStatusEnum::Stop->value)
        {
            return self::Fail('就诊状态发生变化，不可重启诊断', 36003);
        }

        // 获取医生正在进行的诊断
        $getDoctorInTreatmentOutpatientRes = OutpatientModel::getValidOutpatientByDoctorIds($hospitalId,
                                                                                            $doctorId,
                                                                                            [
                                                                                                OutpatientStatusEnum::InTreatment->value,
                                                                                            ]);

        // 获取医生正在诊断的门诊ID
        $doctorInTreatmentOutpatientIds = $getDoctorInTreatmentOutpatientRes->pluck('id')
                                                                            ->toArray();

        try
        {
            DB::beginTransaction();

            // 诊断中的门诊更新状态为暂停，并且记录日志
            if (!empty($doctorInTreatmentOutpatientIds))
            {
                // 更新进行中的门诊为暂停
                OutpatientModel::on()
                               ->whereIn('id', $doctorInTreatmentOutpatientIds)
                               ->update(['status' => OutpatientStatusEnum::Stop->value]);

                $insertOutpatientStatusLogData = [];
                foreach ($doctorInTreatmentOutpatientIds as $inTreatmentOutpatientId)
                {
                    $insertOutpatientStatusLogData = [
                        'outpatient_id' => $inTreatmentOutpatientId,
                        'doctor_id'     => $doctorId,
                        'desc'          => OutpatientStatusEnum::getDescription(OutpatientStatusEnum::Stop->value),
                        'status'        => OutpatientStatusEnum::Stop->value,
                    ];
                }

                // 记录门诊状态变更日志
                OutpatientChangeStatusLogModel::insert($insertOutpatientStatusLogData);
            }

            // 更新暂停的门诊为诊断中
            OutpatientModel::updateOne($outpatientId, ['status' => OutpatientStatusEnum::InTreatment->value]);

            // 记录门诊状态变更日志
            OutpatientChangeStatusLogModel::insertOne([
                                                          'outpatient_id' => $outpatientId,
                                                          'doctor_id'     => $doctorId,
                                                          'desc'          => OutpatientStatusEnum::getDescription(OutpatientStatusEnum::InTreatment->value),
                                                          'status'        => OutpatientStatusEnum::InTreatment->value,
                                                      ]);

            DB::commit();

            return self::Success();
        } catch (Throwable $throwable)
        {
            DB::rollBack();

            Log::error(__CLASS__ . '::' . __METHOD__ . ' 重启门诊异常', [
                'code'    => $throwable->getCode(),
                'message' => $throwable->getMessage(),
                'file'    => $throwable->getFile(),
                'line'    => $throwable->getLine(),
                'trace'   => $throwable->getTraceAsString(),
            ]);

            return self::Fail('重启门诊异常', 36007);
        }
    }

    /**
     * 门诊-结束诊断
     *
     * @param int   $outpatientId
     * @param array $endOutpatientParams
     * @param array $publicParams
     *
     * @return LogicResult
     * @throws Throwable
     * @noinspection PhpUnused
     */
    public static function EndOutpatient(int $outpatientId, array $endOutpatientParams, array $publicParams): LogicResult
    {
        if (empty($outpatientId))
        {
            return self::Fail('outpatientId，缺少必选参数', 400);
        }
        if (empty($endOutpatientParams))
        {
            return self::Fail('endOutpatientParams，缺少必选参数', 400);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        $doctorId   = intval(Arr::get($publicParams, '_userId'));
        if (empty($hospitalId))
        {
            return self::Fail('hospitalId，缺少必选参数', 400);
        }
        if (empty($doctorId))
        {
            return self::Fail('doctorId，缺少必选参数', 400);
        }

        // 结束门诊参数
        $treatmentOutcomeId  = $endOutpatientParams['treatmentOutcome'];
        $transferDoctorUid   = $endOutpatientParams['transferDoctorUid'] ?? '';
        $transferHospitalUid = $endOutpatientParams['transferHospitalUid'] ?? '';
        $transferRemark      = $endOutpatientParams['transferRemark'] ?? '';
        if (empty($treatmentOutcomeId))
        {
            return self::Fail('诊断结果必选', 400);
        }

        // 验证是否可以结束诊断
        $getCheckRes = self::CheckEndOutpatient($outpatientId, $publicParams);
        if ($getCheckRes->isFail())
        {
            return $getCheckRes;
        }

        // 诊断结果是否存在
        $caseId                 = $getCheckRes->getData('caseId', 0);
        $getTreatmentOutcomeRes = CaseLogic::GetEndCaseTreatmentOutcome($caseId, $publicParams);
        if ($getTreatmentOutcomeRes->isFail())
        {
            return $getTreatmentOutcomeRes;
        }

        $getTreatmentOutcomeRes = $getTreatmentOutcomeRes->getData('data', []);
        if (!in_array($treatmentOutcomeId, array_column($getTreatmentOutcomeRes, 'id')))
        {
            return self::Fail('诊断结果不存在', 36006);
        }

        // 结束诊断
        try
        {
            DB::beginTransaction();

            // 更新门诊为结束状态
            OutpatientModel::updateOne($outpatientId,
                                       [
                                           'outcome_id'         => $treatmentOutcomeId,
                                           'status'             => OutpatientStatusEnum::Completed->value,
                                           'end_treatment_date' => getCurrentTimeWithMilliseconds(),
                                       ]);

            // 更新病历为结束状态
            CasesModel::updateOne($caseId,
                                  [
                                      'finished'      => 1,
                                      'finished_time' => getCurrentTimeWithMilliseconds(),
                                      'outcome_id'    => $treatmentOutcomeId
                                  ]);

            // 结束诊断，特殊业务单独处理（院内转诊、内部转院，需生成转诊单）。如果不存在则不会执行
            $callbackMethod = TreatmentOutcomeEnum::TREATMENT_OUTCOME_CALLBACK[$treatmentOutcomeId] ?? [];
            if (!empty($callbackMethod))
            {
                $transferParams = [
                    'caseId'              => $caseId,
                    'sourceType'          => CaseSourceTypeEnum::Outpatient->value,
                    'sourceRelationId'    => $outpatientId,
                    'transferDoctorUid'   => $transferDoctorUid,
                    'transferHospitalUid' => $transferHospitalUid,
                    'transferRemark'      => $transferRemark,
                ];
                $callResult     = call_user_func($callbackMethod, $transferParams, $publicParams);
                if ($callResult->isFail())
                {
                    DB::rollBack();

                    return $callResult;
                }
            }

            // 旧门诊变更记录日志
            $insertOutpatientStatusLogData = [
                'outpatient_id' => $outpatientId,
                'doctor_id'     => $doctorId,
                'desc'          => OutpatientStatusEnum::getDescription(OutpatientStatusEnum::Completed->value),
                'status'        => OutpatientStatusEnum::Completed->value,
            ];
            OutpatientChangeStatusLogModel::insertOne($insertOutpatientStatusLogData);

            DB::commit();

            // 异步生成病历下处方快照
            DataSnapshotLogic::createCaseSnapshot($caseId, $publicParams);

            return self::Success();

        } catch (Throwable $throwable)
        {
            DB::rollBack();

            Log::error(__CLASS__ . '::' . __METHOD__ . ' 结束门诊异常', [
                'code'    => $throwable->getCode(),
                'message' => $throwable->getMessage(),
                'file'    => $throwable->getFile(),
                'line'    => $throwable->getLine(),
                'trace'   => $throwable->getTraceAsString(),
            ]);

            return self::Fail('结束门诊异常', 36007);
        }
    }

    /**
     * 检查是否可以结束诊断
     *
     * @param int   $outpatientId
     * @param array $publicParams
     *
     * @return LogicResult
     */
    public static function CheckEndOutpatient(int $outpatientId, array $publicParams): LogicResult
    {
        if (empty($outpatientId))
        {
            return self::Fail('outpatientId，缺少必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('publicParams，缺少必选参数', 400);
        }

        // 公共参数
        $hospitalId      = intval(Arr::get($publicParams, '_hospitalId'));
        $hospitalBrandId = intval(Arr::get($publicParams, '_hospitalBrandId'));
        $hospitalOrgId   = intval(Arr::get($publicParams, '_hospitalOrgId'));
        $doctorId        = intval(Arr::get($publicParams, '_userId'));
        if (empty($hospitalId) || empty($hospitalBrandId) || empty($hospitalOrgId))
        {
            return self::Fail('hospitalId、hospitalBrandId、hospitalOrgId，缺少必选参数', 400);
        }
        if (empty($doctorId))
        {
            return self::Fail('doctorId，缺少必选参数', 400);
        }

        // 获取门诊信息
        $getValidOutpatientRes = self::GetValidOutpatientById($outpatientId, $publicParams);
        if ($getValidOutpatientRes->isFail())
        {
            return $getValidOutpatientRes;
        }

        $getOutpatientRes = $getValidOutpatientRes->getData();
        if ($getOutpatientRes['status'] != OutpatientStatusEnum::InTreatment->value)
        {
            return self::Fail('就诊状态发生变化，不可结束诊断', 36003);
        }

        // 验证门诊关联的病历是否可以结束
        return CaseLogic::CheckEndAbleBySourceType($hospitalId,
                                                   CaseSourceTypeEnum::Outpatient->value,
                                                   $outpatientId,
                                                   $doctorId);
    }

    /**
     * 获取一个医生正在诊断的门诊，返回门诊对应的病历编码，用户展示工作台病历信息
     *
     * @param int $hospitalId
     * @param int $doctorId
     *
     * @return LogicResult
     */
    public static function GetDoctorInTreatmentOutpatient(int $hospitalId, int $doctorId): LogicResult
    {
        if (empty($hospitalId) || empty($doctorId))
        {
            return self::Fail('hospitalId、doctorId，缺少必选参数', 400);
        }

        // 获取医生正在进行的门诊
        $getDoctorInTreatmentOutpatientRes = OutpatientModel::getValidOutpatientByDoctorIds($hospitalId,
                                                                                            $doctorId,
                                                                                            [OutpatientStatusEnum::InTreatment->value]);
        if ($getDoctorInTreatmentOutpatientRes->isEmpty())
        {
            return self::Success();
        }

        $getDoctorInTreatmentOutpatientRes = $getDoctorInTreatmentOutpatientRes->last();
        $outpatientId                      = $getDoctorInTreatmentOutpatientRes['id'];
        $registrationId                    = $getDoctorInTreatmentOutpatientRes['registration_id'];

        // 获取门诊关联的挂号
        $getRegistrationRes = RegistrationsModel::getOne($registrationId);
        if (empty($getRegistrationRes))
        {
            return self::Fail('正在进行的门诊关联挂号不存在', 35011);
        }

        // 获取门诊关联的病历编码
        $getCaseRes = CasesModel::getCaseByRelationId($hospitalId,
                                                      CaseSourceTypeEnum::Outpatient->value,
                                                      $outpatientId);
        if (empty($getCaseRes))
        {
            return self::Fail('正在进行的门诊关联病例不存在', 38000);
        }

        // 获取病历关联的宠物信息
        $petId     = $getCaseRes['pet_id'];
        $getPetRes = MemberPetsModel::getOne($petId);
        if (empty($getPetRes))
        {
            return self::Fail('正在进行的门诊关联宠物不存在', 32000);
        }

        return self::Success([
                                 'caseCode'         => $getCaseRes['case_code'],
                                 'outpatientCode'   => $getDoctorInTreatmentOutpatientRes['outpatient_code'],
                                 'petUid'           => $getPetRes['uid'],
                                 'registrationCode' => $getRegistrationRes['registration_code']
                             ]);
    }
}
