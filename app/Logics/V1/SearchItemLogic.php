<?php

namespace App\Logics\V1;

use Arr;
use App\Support\Item\ItemHelper;
use App\Support\Stock\StockQuantityConversionHelper;
use App\Enums\ItemSaleTyeEnum;
use App\Enums\ItemUnitTypeEnum;
use App\Logics\Logic;
use App\Logics\LogicResult;
use App\Models\ItemSalePriceModel;
use App\Models\ItemSaleTypeModel;
use App\Models\RecipesStockOccupyModel;
use App\Models\TestsConsumablesTemplatesItemModel;

/**
 * meiLiSearch搜索商品部分业务
 * Class SearchItemLogic
 * @package App\Logics\V1
 */
class SearchItemLogic extends Logic
{
    /**
     * 格式化检索的商品信息，如果包含组合的话，会生成组合嵌套结构体
     *
     * @param array $itemLists
     * @param array $publicParams
     * @param bool  $withItemPrice
     * @param bool  $withItemStock 注：此获取库存只是返回前置是否有库存，对于组合、化验库存只是可开具的库存数字。请勿使用此库存数字认为是商品的实际库存
     *
     * @return LogicResult
     */
    public static function FormatItemInfoStructure(array $itemLists, array $publicParams, bool $withItemPrice = false, bool $withItemStock = false): LogicResult
    {
        if (empty($itemLists))
        {
            return self::Success();
        }
        if (empty($publicParams))
        {
            return self::Fail('格式化商品信息，公共参数不存在', 400);
        }

        // 公共参数
        $orgId      = intval(Arr::get($publicParams, '_hospitalOrgId'));
        $brandId    = intval(Arr::get($publicParams, '_hospitalBrandId'));
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        if (empty($orgId) || empty($brandId) || empty($hospitalId))
        {
            return self::Fail('格式化商品信息，公共参数不存在', 400);
        }

        // 获取所有商品类型
        $getItemSaleTypeRes = ItemSaleTypeModel::getAllSaleType();
        $getItemSaleTypeRes = $getItemSaleTypeRes->isNotEmpty() ? $getItemSaleTypeRes->toArray() : [];

        // 获取商品价格
        if (!empty($withItemPrice))
        {
            // 获取医院信息
            $getHospitalRes = HospitalLogic::GetHospitalBaseInfo('', $hospitalId, true, true);
            if ($getHospitalRes->isFail())
            {
                return $getHospitalRes;
            }

            // 医院所属城市
            $hospitalProvinceId = $getHospitalRes->getData('addressInfo.provinceId', 0);
            $hospitalCityId     = $getHospitalRes->getData('addressInfo.cityId', 0);

            $itemTypeRelationItemIds = [];
            foreach ($itemLists as $item)
            {
                $itemTypeRelationItemIds[$item['item_type']][] = $item['id'];
            }

            // 获取商品价格
            $getItemsPriceRes = [];
            foreach ($itemTypeRelationItemIds as $itemType => $itemIds)
            {
                $getItemsPriceRes[$itemType] = ItemSalePriceModel::getItemSalePrice($itemType, $itemIds, $orgId, $brandId, $hospitalProvinceId, $hospitalCityId, $hospitalId);
            }
        }

        // 获取库存信息
        $getItemStockRes = [];
        if (!empty($withItemStock))
        {
            // 按照商品类型分组：单品、化验、组合
            $groupedItems = self::GroupItemsByType($itemLists);

            // 获取各类型商品库存（化验需要验证耗材库存、组合需要验证组合内单品库存同时如果有化验则需要验证耗材库存）
            $getSingleItemsStockRes = self::GetSingleItemsStock($groupedItems['single'], $publicParams);
            if ($getSingleItemsStockRes->isFail())
            {
                return $getSingleItemsStockRes;
            }

            $getTestItemsStockRes = self::GetTestItemsStock($groupedItems['test'], $publicParams);
            if ($getTestItemsStockRes->isFail())
            {
                return $getTestItemsStockRes;
            }

            $getSuitItemsStockRes = self::GetSuitItemsStock($groupedItems['suit'], $publicParams);
            if ($getSuitItemsStockRes->isFail())
            {
                return $getSuitItemsStockRes;
            }

            // 合并库存结果
            $getItemStockRes = $getSingleItemsStockRes->getData() + $getTestItemsStockRes->getData() + $getSuitItemsStockRes->getData();
        }

        $returnSearchItems = [];
        foreach ($itemLists as $itemInfo)
        {
            // 根据当前商品类型获取价格信息
            $curItemType          = $itemInfo['item_type'] ?? 0;
            $curItemSalePriceRes  = $getItemsPriceRes[$curItemType][$itemInfo['id'] ?? 0] ?? [];
            $curItemSalePriceInfo = $curItemSalePriceRes['sale_price'] ?? [];

            // 商品是否组合
            $curItemIsSuit           = $itemInfo['is_suit'] ?? 0;
            $curItemFirstSaleTypeId  = $itemInfo['first_sale_type']['id'] ?? 0;
            $curItemSecondSaleTypeId = !empty($itemInfo['second_sale_type']) ? ($itemInfo['second_sale_type']['id'] ?? 0) : 0;

            // 商品展示名称
            $curItemDisplayName = ItemHelper::ItemDisplayName($itemInfo);

            // 商品类型，如果是组合不在细分是药品、化验等。
            if ($curItemIsSuit)
            {
                $curItemTypeInfo = ['id' => 0, 'name' => '组合'];
            }
            // 将商品类型单独提取显示
            elseif ($curItemSecondSaleTypeId == ItemSaleTyeEnum::SecondItem->value)
            {
                $curItemTypeInfo = [
                    'id'   => $curItemFirstSaleTypeId,
                    'name' => $getItemSaleTypeRes[$curItemSecondSaleTypeId]['alias_name'] ?? ''
                ];
            }
            else
            {
                $curItemTypeInfo = [
                    'id'   => $curItemFirstSaleTypeId,
                    'name' => $getItemSaleTypeRes[$curItemFirstSaleTypeId]['alias_name'] ?? ''
                ];
            }

            // 商品规格信息
            $curItemPackUnitName = $itemInfo['pack_unit']['name'] ?? '';
            $curItemBulkUnitName = $itemInfo['bulk_unit']['name'] ?? '';
            $curItemUseUnitName  = $itemInfo['use_unit']['name'] ?? '';
            $curItemUseRatio     = $itemInfo['use_ratio'] ?? 0;
            $curItemBulkRatio    = $itemInfo['bulk_ratio'] ?? 0;

            // 如果没有整装单位、散装单位使用计量单位
            if (empty($curItemBulkUnitName))
            {
                $curItemBulkUnitName = $curItemUseUnitName;
            }
            if (empty($curItemPackUnitName))
            {
                $curItemPackUnitName = $curItemUseUnitName;
            }

            // 商品出库单位，1ml/ml、1次、1瓶。（计量比 + 计量单位 + 散装单位 ）
            if ($curItemFirstSaleTypeId == ItemSaleTyeEnum::FirstDrug->value)
            {
                $curItemSpec = formatDisplayNumber($curItemUseRatio) . $curItemUseUnitName . '/' . $curItemBulkUnitName;
            }
            else
            {
                $curItemSpec = 1 . $curItemUseUnitName;
            }

            // 商品是否处方精确计量品、可否整装开具
            $curItemIsPreciseMetering = $itemInfo['is_precise_metering'] ?? false;
            $curItemIsPackSaleAllow   = $itemInfo['is_pack_sale_allow'] ?? false;

            // 商品价格
            $curItemPackSalePrice = $curItemSalePriceInfo['pack_sale_price'] ?? 0;
            $curItemBulkSalePrice = $curItemSalePriceInfo['bulk_sale_price'] ?? 0;

            // 商品库存
            $curItemId        = $itemInfo['id'] ?? 0;
            $curItemStockInfo = $getItemStockRes[$curItemId] ?? [];

            $tmpItemStructure = [
                'uid'               => $itemInfo['uid'],
                'isSuit'            => $curItemIsSuit,
                'type'              => $curItemTypeInfo,
                'name'              => $curItemDisplayName,
                'itemName'          => $itemInfo['name'],
                'itemBarcode'       => $itemInfo['barcode'],
                'useUnit'           => $curItemUseUnitName,
                'useRatio'          => $curItemUseRatio,
                'spec'              => $curItemSpec,
                'packUnit'          => $curItemPackUnitName,
                'packPrice'         => formatDisplayNumber($curItemPackSalePrice),
                'bulkUnit'          => $curItemBulkUnitName,
                'bulkPrice'         => formatDisplayNumber($curItemBulkSalePrice),
                'bulkRatio'         => $curItemBulkRatio,
                'unitType'          => $itemInfo['unit_type'],
                'quantity'          => $itemInfo['quantity'],
                'isPreciseMetering' => $curItemIsPreciseMetering,
                'isPackSaleAllow'   => $curItemIsPackSaleAllow,
                'stock'             => $curItemStockInfo,
                'suitItems'         => [],
            ];

            // 处理组合商品中的子项目
            if ($curItemIsSuit && !empty($itemInfo['suit_items']))
            {
                $tmpItemStructure['suitItems'] = self::FormatItemInfoStructure($itemInfo['suit_items'], $publicParams, $withItemPrice, $withItemStock)
                                                     ->getData();
            }
            $returnSearchItems[] = $tmpItemStructure;
        }

        return self::Success($returnSearchItems);
    }

    /**
     * 按商品类型分组
     *
     * @param array $itemLists 商品列表
     *
     * @return array
     */
    private static function GroupItemsByType(array $itemLists): array
    {
        $grouped = [
            'single' => [],  // 单品
            'test'   => [],  // 化验项
            'suit'   => []   // 组合
        ];

        foreach ($itemLists as $item)
        {
            $isSuit       = $item['is_suit'] ?? 0;
            $itemSaleType = $item['first_sale_type']['id'] ?? 0;
            if ($isSuit)
            {
                $grouped['suit'][] = $item;
            }
            elseif ($itemSaleType == ItemSaleTyeEnum::FirstTest->value)
            {
                $grouped['test'][] = $item;
            }
            else
            {
                $grouped['single'][] = $item;
            }
        }

        return $grouped;
    }

    /**
     * 获取商品、药品库存（用户前置判断是否有库存）
     *
     * @param array $searchSingleItems 单品列表
     * @param array $publicParams      公共参数
     *
     * @return LogicResult
     */
    private static function GetSingleItemsStock(array $searchSingleItems, array $publicParams): LogicResult
    {
        if (empty($searchSingleItems) || empty($publicParams))
        {
            return self::Success();
        }

        $itemIds = array_column($searchSingleItems, 'id');
        if (empty($itemIds))
        {
            return self::Success();
        }

        // 批量查询库存
        $getStockRes = StockItemShelfLogic::GetEffectiveQuantity($itemIds, $publicParams);
        if ($getStockRes->isFail())
        {
            return $getStockRes;
        }

        // 获取占用库存
        $hospitalId          = intval(Arr::get($publicParams, '_hospitalId'));
        $getOccupiedStockRes = RecipesStockOccupyModel::getTotalRecipeOccupyStock($hospitalId, $itemIds);

        // 为每个单品构造库存结果
        $returnStockItem = [];
        foreach ($searchSingleItems as $curItem)
        {
            // 商品基本信息
            $curItemId       = $curItem['id'];
            $curItemSaleType = $curItem['first_sale_type']['id'] ?? 0;

            // 商品类型验证库存，其它类型默认库存（处置、影像）
            if ($curItemSaleType == ItemSaleTyeEnum::FirstDrug->value)
            {
                // 当前商品库存信息，无库存使用默认库存结构
                $curStockInfo = $getStockRes->getData($curItemId) ?? StockQuantityConversionHelper::getStockQuantityStructure();

                // 如果有库存，需要减去占用库存
                if (!empty($curStockInfo['hasStock']))
                {
                    // 如果有实际库存，是否存在占用库存。如果存在的话，需要减去占用库存得到最后可用的实际库存
                    $curOccupiedStockInfo = $getOccupiedStockRes[$curItemId] ?? [];
                    if (!empty($curOccupiedStockInfo))
                    {
                        $getRemainQuantityRes = StockQuantityConversionHelper::getRemainPackAndBulkQuantity($curStockInfo['packQuantity'],
                                                                                                            $curStockInfo['bulkQuantity'],
                                                                                                            $curOccupiedStockInfo['occupy_pack_quantity'],
                                                                                                            $curOccupiedStockInfo['occupy_bulk_quantity'],
                                                                                                            $curStockInfo['bulkRatio']);

                        // 根据返回的剩余可用库存生成新的库存结构
                        $curStockInfo              = StockQuantityConversionHelper::getStockQuantityStructure($getRemainQuantityRes,
                                                                                                              [],
                                                                                                              [
                                                                                                                  'pack' => 'remainPackQuantity',
                                                                                                                  'bulk' => 'remainBulkQuantity'
                                                                                                              ]);
                        $curStockInfo['bulkRatio'] = $getRemainQuantityRes['bulkRatio'];
                    }
                }

                $returnStockItem[$curItemId] = $curStockInfo;
            }
            else
            {
                $returnStockItem[$curItemId] = StockQuantityConversionHelper::getStockQuantityDefaultStructure();
            }
        }

        return self::Success($returnStockItem);
    }

    /**
     * 获取化验项库存（用户前置判断是否有库存）
     * 如果化验项绑定了化验耗材，需要化验耗材同时满足设置的使用量才算有库存
     *
     * @param array $searchTestItems 化验项列表
     * @param array $publicParams    公共参数
     *
     * @return LogicResult
     */
    private static function GetTestItemsStock(array $searchTestItems, array $publicParams): LogicResult
    {
        if (empty($searchTestItems))
        {
            return self::Success();
        }

        // 公共参数
        $hospitalOrgId   = intval(Arr::get($publicParams, '_hospitalOrgId'));
        $hospitalBrandId = intval(Arr::get($publicParams, '_hospitalBrandId'));
        $hospitalId      = intval(Arr::get($publicParams, '_hospitalId'));
        if (empty($hospitalOrgId) || empty($hospitalBrandId) || empty($hospitalId))
        {
            return self::Fail('公共参数，缺少必选参数', 400);
        }

        // 化验项目Ids
        $testItemIds = [];
        foreach ($searchTestItems as $curTestItem)
        {
            if ($curTestItem['first_sale_type']['id'] == ItemSaleTyeEnum::FirstTest->value)
            {
                $testItemIds[] = $curTestItem['id'];
            }
        }
        if (empty($testItemIds))
        {
            return self::Success();
        }

        // 批量获取化验项耗材，可能为空。为空既代表没有绑定化验耗材
        $getConsumablesRes = TestsConsumablesTemplatesItemModel::getConsumablesByTestItemIds($testItemIds, $hospitalId, $hospitalBrandId, $hospitalOrgId);

        // 化验项关联的化验耗材商品Ids
        $testRelationConsumableItemIds = [];
        foreach ($getConsumablesRes as $curConsumablesItems)
        {
            $testRelationConsumableItemIds = array_merge($testRelationConsumableItemIds, array_column($curConsumablesItems, 'item_id'));
        }

        // 获取化验关联的耗材库存、占用库存
        $getTestRelationConsumablesStockRes       = [];
        $getTestRelationConsumablesOccupyStockRes = [];
        if (!empty($testRelationConsumableItemIds))
        {
            $testRelationConsumableItemIds      = array_unique($testRelationConsumableItemIds);
            $getTestRelationConsumablesStockRes = StockItemShelfLogic::GetEffectiveQuantity($testRelationConsumableItemIds, $publicParams);
            if ($getTestRelationConsumablesStockRes->isFail())
            {
                return $getTestRelationConsumablesStockRes;
            }

            $getTestRelationConsumablesStockRes = $getTestRelationConsumablesStockRes->getData();

            // 获取耗材的占用库存
            $getTestRelationConsumablesOccupyStockRes = RecipesStockOccupyModel::getTotalRecipeOccupyStock($hospitalId, $testRelationConsumableItemIds);
        }

        // 为每个化验项计算库存
        $returnStockItem = [];
        foreach ($searchTestItems as $curTestItem)
        {
            $curTestItemId = $curTestItem['id'];

            // 获取当前化验项的耗材信息，如果没有有效耗材，默认认为有库存
            $curTestItemConsumables = $getConsumablesRes[$curTestItemId] ?? [];
            if (empty($curTestItemConsumables))
            {
                $returnStockItem[$curTestItemId] = StockQuantityConversionHelper::getStockQuantityDefaultStructure();
            }
            else
            {
                // 获取当前化验项关联的耗材库存
                $curTestItemConsumablesStock = [];
                foreach ($curTestItemConsumables as $consumableItem)
                {
                    // 当前耗材库存信息，无库存使用默认库存结构
                    $consumableItemId = $consumableItem['item_id'];
                    $curStockInfo     = $getTestRelationConsumablesStockRes[$consumableItemId] ?? StockQuantityConversionHelper::getStockQuantityStructure();

                    // 如果有实际库存，是否存在占用库存。如果存在的话，需要减去占用库存得到最后可用的实际库存
                    if (!empty($curStockInfo['hasStock']))
                    {
                        $curOccupiedStockInfo = $getTestRelationConsumablesOccupyStockRes[$consumableItemId] ?? [];
                        if (!empty($curOccupiedStockInfo))
                        {
                            $getRemainQuantityRes = StockQuantityConversionHelper::getRemainPackAndBulkQuantity($curStockInfo['packQuantity'],
                                                                                                                $curStockInfo['bulkQuantity'],
                                                                                                                $curOccupiedStockInfo['occupy_pack_quantity'],
                                                                                                                $curOccupiedStockInfo['occupy_bulk_quantity'],
                                                                                                                $curStockInfo['bulkRatio']);

                            // 根据返回的剩余可用库存生成新的库存结构
                            $curStockInfo              = StockQuantityConversionHelper::getStockQuantityStructure($getRemainQuantityRes,
                                                                                                                  [],
                                                                                                                  [
                                                                                                                      'pack' => 'remainPackQuantity',
                                                                                                                      'bulk' => 'remainBulkQuantity'
                                                                                                                  ]);
                            $curStockInfo['bulkRatio'] = $getRemainQuantityRes['bulkRatio'];
                        }
                    }

                    $curTestItemConsumablesStock[$consumableItemId] = $curStockInfo;
                }

                // 使用当前化验项的耗材库存计算库存状态
                $getTestCanUseStock              = self::calculateTestStockFromConsumables($curTestItemConsumables, $curTestItemConsumablesStock);
                $returnStockItem[$curTestItemId] = $getTestCanUseStock->getData();
            }
        }

        return self::Success($returnStockItem);
    }

    /**
     * 获取组合商品库存（用户前置判断是否有库存）
     * 组合中单品需要有库存、化验需要满足化验耗材库存
     *
     * @param array $searchSuitItems 组合商品列表
     * @param array $publicParams    公共参数
     *
     * @return LogicResult
     */
    private static function GetSuitItemsStock(array $searchSuitItems, array $publicParams): LogicResult
    {
        if (empty($searchSuitItems))
        {
            return self::Success();
        }

        // 公共参数
        $hospitalOrgId   = intval(Arr::get($publicParams, '_hospitalOrgId'));
        $hospitalBrandId = intval(Arr::get($publicParams, '_hospitalBrandId'));
        $hospitalId      = intval(Arr::get($publicParams, '_hospitalId'));
        if (empty($hospitalOrgId) || empty($hospitalBrandId) || empty($hospitalId))
        {
            return self::Fail('公共参数，缺少必选参数', 400);
        }

        // 为每个组合计算库存
        $returnStockItem = [];
        foreach ($searchSuitItems as $suitItem)
        {
            $suitItemId = $suitItem['id'];
            $suitItems  = $suitItem['suit_items'] ?? [];

            // 标记为有库存，如果任何一个子项无库存即为false
            $hasStock              = true;
            $maxAvailableQuantity  = 0;
            $suitItemMaxQuantities = [];

            foreach ($suitItems as $curItemInfo)
            {
                $curItemId       = $curItemInfo['id'] ?? 0;
                $curItemSaleType = $curItemInfo['first_sale_type']['id'] ?? 0;

                // 无需库存管理
                if (!in_array($curItemSaleType, [ItemSaleTyeEnum::FirstTest->value, ItemSaleTyeEnum::FirstDrug->value]))
                {
                    $getDefaultStockRes      = StockQuantityConversionHelper::getStockQuantityDefaultStructure();
                    $suitItemMaxQuantities[] = $getDefaultStockRes['packQuantity'];
                    continue;
                }

                // 子项是化验项目
                if ($curItemSaleType == ItemSaleTyeEnum::FirstTest->value)
                {
                    $getTestItemStockRes = self::GetTestItemsStock([$curItemInfo], $publicParams);
                    if ($getTestItemStockRes->isFail())
                    {
                        return $getTestItemStockRes;
                    }

                    $curTestItemStock = $getTestItemStockRes->getData($curItemId, []);
                    if (empty($curTestItemStock['hasStock']))
                    {
                        $hasStock = false;
                        break;
                    }

                    // 验证化验项是否满足组合使用的数量
                    $testMaxQuantity     = $curTestItemStock['packQuantity']; // 化验项可开具次数
                    $suitTestUseQuantity = $curItemInfo['quantity'];
                    if ($testMaxQuantity < $suitTestUseQuantity)
                    {
                        $hasStock = false;
                        break;
                    }
                    else
                    {
                        $suitItemMaxQuantities[] = floor($testMaxQuantity / $suitTestUseQuantity);
                        continue;
                    }
                }

                // 子项是商品
                if ($curItemSaleType == ItemSaleTyeEnum::FirstDrug->value)
                {
                    $getSingleItemStockRes = self::GetSingleItemsStock([$curItemInfo], $publicParams);
                    if ($getSingleItemStockRes->isFail())
                    {
                        $hasStock = false;
                        break;
                    }

                    $curItemStock = $getSingleItemStockRes->getData($curItemId, []);
                    if (empty($curItemStock['hasStock']))
                    {
                        $hasStock = false;
                        break;
                    }

                    // 计算当前子商品的可用数量（转换为散装单位）
                    $itemPackQuantity = $curItemStock['packQuantity'];
                    $itemBulkQuantity = $curItemStock['bulkQuantity'];
                    $itemBulkRatio    = $curItemStock['bulkRatio'] ?? 0;

                    // 总散装数量
                    $totalBulkQuantity = StockQuantityConversionHelper::convertToTotalBulkQuantity($itemPackQuantity, $itemBulkQuantity, $itemBulkRatio);

                    // 获取子商品在组合中的使用数量和使用单位
                    $suitItemUseQuantity = $curItemInfo['quantity'];
                    $suitItemUnitType    = $curItemInfo['unit_type'];

                    // 根据使用单位类型转换使用数量为散装单位
                    if ($suitItemUnitType == ItemUnitTypeEnum::UNIT_TYPE_PACK->value && $itemBulkRatio > 0)
                    {
                        $suitItemUseQuantity = numberMul([$suitItemUseQuantity, $itemBulkRatio]);
                    }

                    // 计算基于当前子项可以开具多少个组合
                    if (bccomp($suitItemUseQuantity, '0', 2) > 0 && bccomp($totalBulkQuantity, '0', 2) > 0)
                    {
                        // 计算可开具的组合数量
                        $calculatedQuantity      = numberDiv([$totalBulkQuantity, $suitItemUseQuantity]);
                        $currentItemMaxSuits     = floor($calculatedQuantity);
                        $suitItemMaxQuantities[] = $currentItemMaxSuits;
                    }
                    else
                    {
                        // 如果使用数量为0或库存为0，则无法开具
                        $hasStock = false;
                        break;
                    }
                }
            }

            // 取所有子项中最小的可开具组合数量
            if ($hasStock && !empty($suitItemMaxQuantities))
            {
                $maxAvailableQuantity = min($suitItemMaxQuantities);
            }

            $stockStructure               = StockQuantityConversionHelper::getStockQuantityStructure(['pack_quantity' => $maxAvailableQuantity]);
            $returnStockItem[$suitItemId] = $stockStructure;
        }

        return self::Success($returnStockItem);
    }

    /**
     * 根据耗材库存计算化验项库存状态
     *
     * @param array $consumablesInfoList  化验关联的耗材商品
     * @param array $consumablesItemStock 化验关联的耗材库存
     *
     * @return LogicResult
     */
    private static function calculateTestStockFromConsumables(array $consumablesInfoList, array $consumablesItemStock): LogicResult
    {
        // 标记为有库存，如果任何一个耗材无库存即为false
        $hasStock                = true;
        $maxAvailableQuantity    = 0;
        $consumableMaxQuantities = [];

        foreach ($consumablesInfoList as $curConsumableInfo)
        {
            // 如果耗材无库存，则当前化验项不可使用
            $consumableItemId = $curConsumableInfo['item_id'];
            $consumableStock  = $consumablesItemStock[$consumableItemId] ?? [];
            if (empty($consumableStock['hasStock']))
            {
                $hasStock = false;
                break;
            }

            // 计算耗材的可用数量（转换为散装单位）
            $consumablePackQuantity = $consumableStock['packQuantity'];
            $consumableBulkQuantity = $consumableStock['bulkQuantity'];
            $consumableBulkRatio    = $consumableStock['bulkRatio'] ?? 0;

            // 库存总散装数量
            $totalBulkQuantity = StockQuantityConversionHelper::convertToTotalBulkQuantity($consumablePackQuantity, $consumableBulkQuantity, $consumableBulkRatio);

            // 获取耗材的使用单位和单次用量
            $consumableUnitType = $curConsumableInfo['unit_type'];
            $consumableOnceUse  = $curConsumableInfo['once_use'];

            // 根据使用单位类型转换使用数量为散装单位
            if ($consumableUnitType == ItemUnitTypeEnum::UNIT_TYPE_PACK->value && $consumableBulkRatio > 0)
            {
                $consumableOnceUse = numberMul([$consumableOnceUse, $consumableBulkRatio]);
            }

            // 计算基于当前耗材可以开具多少次化验
            if (bccomp($consumableOnceUse, '0', 2) > 0 && bccomp($totalBulkQuantity, '0', 2) > 0)
            {
                // 向下取整（1.1 => 1）
                $calculatedQuantity        = numberDiv([$totalBulkQuantity, $consumableOnceUse]);
                $currentConsumableMaxTests = floor($calculatedQuantity);
                $consumableMaxQuantities[] = $currentConsumableMaxTests;
            }
            else
            {
                $hasStock = false;
                break;
            }
        }

        // 取所有耗材中最小的可开具化验次数
        if ($hasStock && !empty($consumableMaxQuantities))
        {
            $maxAvailableQuantity = min($consumableMaxQuantities);
        }

        return self::Success(StockQuantityConversionHelper::getStockQuantityStructure(['pack_quantity' => $maxAvailableQuantity]));
    }
}
