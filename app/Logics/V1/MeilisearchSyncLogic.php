<?php

namespace App\Logics\V1;

use App\Logics\Logic;
use App\Logics\LogicResult;
use Illuminate\Support\Arr;
use Carbon\Carbon;
use App\Models\MeiLiSearchItemModel;
use App\Infrastructure\Search\MeiLiSearchClient;
use Exception;
use App\Enums\ItemTypeEnum;
use Log;

/**
 * MeiLiSearch商品同步Logic
 *
 * 使用示例：
 *
 * // 同步单个商品
 * MeiLiSearchSyncLogic::syncItems(ItemTypeEnum::Sku->value, $publicParams, [123]);
 *
 * // 同步多个商品
 * MeiLiSearchSyncLogic::syncItems(ItemTypeEnum::Sku->value, $publicParams, [123, 456, 789]);
 *
 * // 同步指定类型的所有商品（全量同步）
 * MeiLiSearchSyncLogic::syncItems(ItemTypeEnum::Sku->value, $publicParams);
 *
 * // 同步指定类型的所有商品（自定义批次大小）
 * MeiLiSearchSyncLogic::syncItems(ItemTypeEnum::Sku->value, $publicParams, [], 500);
 *
 * // 初始化索引
 * MeiLiSearchSyncLogic::initIndex($publicParams);
 *
 * // 清空索引
 * MeiLiSearchSyncLogic::flushIndex($publicParams);
 *
 * // 增量同步
 * MeiLiSearchSyncLogic::syncIncrementalItems($publicParams, '2024-01-01 00:00:00');
 */
class MeiLiSearchSyncLogic extends Logic
{
    /**
     * 初始化索引
     *
     * @param array $publicParams 公共参数
     *
     * @return LogicResult
     */
    public static function InitializeIndex(array $publicParams): LogicResult
    {
        // 验证参数
        if (empty($publicParams))
        {
            return self::Fail('初始化索引，缺少公共必选参数', 400);
        }

        $hospitalOrgId = intval(Arr::get($publicParams, '_hospitalOrgId'));
        if (empty($hospitalOrgId))
        {
            return self::Fail('初始化索引，缺少机构ID必选参数', 400);
        }

        // 索引主键
        $primaryKey = 'uid';
        $options    = [
            'primaryKey' => $primaryKey,
        ];

        // 索引设置
        $settings = [
            'searchableAttributes' => [
                'alias_name',
                'basis_name',
                'name',
                'english_name',
                'mem_code',
                'suit_items.alias_name',
                'suit_items.basis_name',
                'suit_items.name',
                'suit_items.english_name',
                'suit_items.mem_code',
            ],
            'rankingRules'         => [
                'words',
                'typo',
                'proximity',
                'attribute',
                'sort',
                'exactness',
            ],
            'filterableAttributes' => [
                'id',
                'barcode',
                'item_support_pet_category',
                'is_recipe_allow',
                'is_retail_allow',
                'is_receive_allow',
                'status',
                'is_suit',
                'first_sale_type.id',
                'is_purchasable',
                'brand.id',
                'purchase_hospital_ids',
            ],
            'sortableAttributes'   => [
                'id',
                'created_at',
                'item_type',
                'first_sale_type.id',
                'second_sale_type.id',
                'drug_type.id',
            ],
        ];

        try
        {
            $client    = MeiLiSearchClient::getInstance();
            $indexName = MeiLiSearchClient::generateIndexName($hospitalOrgId);

            // 获取索引信息
            $indexInfo = $client->getIndexInfo($indexName);
            $exists    = $indexInfo['exists'];

            // 检查主键是否正确（如果索引存在）
            if ($exists)
            {
                $primaryKey = $indexInfo['info']['primaryKey'] ?? '';
                if ($primaryKey !== 'uid')
                {
                    // 主键不正确，删除并重新创建索引
                    $client->deleteIndex($indexName);
                    $exists = false;
                }
            }

            // 创建索引（如果不存在或被删除）
            if (!$exists)
            {
                $client->createIndex($indexName, $options);
            }

            // 更新索引设置
            $upSuccess = $client->updateIndexSettings($indexName, $settings);
            if (!$upSuccess)
            {
                return self::Fail('更新索引设置失败');
            }

            return self::Success([
                                     'index_name' => $indexName,
                                     'existed'    => $exists,
                                     'message'    => $exists ? '索引已存在，已更新设置' : '索引创建成功'
                                 ]);

        } catch (Exception $e)
        {
            Log::error(__CLASS__ . '::' . __METHOD__ . ' 初始化索引失败', [
                'code'    => $e->getCode(),
                'message' => $e->getMessage(),
                'file'    => $e->getFile(),
                'line'    => $e->getLine(),
                'trace'   => $e->getTraceAsString(),
            ]);

            return self::Fail('初始化索引失败', 400);
        }
    }

    /**
     * 获取索引状态
     *
     * @param array $publicParams 公共参数
     *
     * @return LogicResult
     */
    public static function GetIndexStatus(array $publicParams): LogicResult
    {

        // 验证参数
        if (empty($publicParams))
        {
            return self::Fail('获取索引状态，缺少公共必选参数', 400);
        }

        // 获取机构ID
        $hospitalOrgId = intval(Arr::get($publicParams, '_hospitalOrgId'));
        if (empty($hospitalOrgId))
        {
            return self::Fail('获取索引状态，缺少机构ID必选参数', 400);
        }

        $client    = MeiLiSearchClient::getInstance();
        $indexName = MeiLiSearchClient::generateIndexName($hospitalOrgId);

        // 获取索引信息
        $indexInfo = $client->getIndexInfo($indexName);

        if ($indexInfo['exists'])
        {
            return self::Success([
                                     'index_name' => $indexName,
                                     'exists'     => true,
                                     'stats'      => $indexInfo['stats'],
                                     'info'       => $indexInfo['info']
                                 ]);
        }
        else
        {
            return self::Success([
                                     'index_name' => $indexName,
                                     'exists'     => false,
                                     'message'    => '索引不存在'
                                 ]);
        }
    }

    /**
     * 同步商品到MeiLiSearch（支持单个、批量或全量同步）
     *
     * @param int   $itemType     商品类型
     * @param array $publicParams 公共参数
     * @param array $itemIds      商品ID数组（为空时同步全部商品）
     * @param int   $batchSize    批次大小，默认1000（仅在全量同步时使用）
     *
     * @return LogicResult
     */
    public static function SyncItems(int $itemType, array $publicParams, array $itemIds = [], int $batchSize = 1000): LogicResult
    {
        try
        {
            if (empty($itemType))
            {
                return self::Fail('同步商品，缺少必选参数', 400);
            }
            if (empty($publicParams))
            {
                return self::Fail('同步商品，缺少公共必选参数', 400);
            }

            // 公共参数
            $hospitalOrgId = intval(Arr::get($publicParams, '_hospitalOrgId'));
            if (empty($hospitalOrgId))
            {
                return self::Fail('同步商品，缺少机构ID必选参数', 400);
            }

            // 获取商品数据
            $getItemRes = self::getItemsByType($itemType, $hospitalOrgId, $itemIds);
            if ($getItemRes->isFail())
            {
                return $getItemRes;
            }

            $items = $getItemRes->getData();
            if (empty($items))
            {
                return self::Success(['synced_count' => 0, 'message' => '没有找到有效商品']);
            }

            // 如果是全量同步（itemIds为空）且数据量大，则分批处理
            if (empty($itemIds) && count($items) > $batchSize)
            {
                $totalSynced = 0;
                $batches     = array_chunk($items, $batchSize);

                foreach ($batches as $batch)
                {
                    $syncRes = self::syncItemsToIndex($batch, $hospitalOrgId);
                    if ($syncRes->isFail())
                    {
                        return self::Fail("批次同步失败: " . $syncRes->getMessage());
                    }
                    $totalSynced += count($batch);
                }

                return self::Success([
                                         'synced_count' => $totalSynced,
                                         'batch_count'  => count($batches),
                                         'item_type'    => $itemType,
                                         'sync_type'    => 'full'
                                     ]);
            }
            else
            {
                // 直接同步（单个、少量批量或小数据量全量）
                $syncRes = self::syncItemsToIndex($items, $hospitalOrgId);
                if ($syncRes->isFail())
                {
                    return $syncRes;
                }

                return self::Success([
                                         'synced_count'    => count($items),
                                         'requested_count' => count($itemIds),
                                         'item_type'       => $itemType,
                                         'sync_type'       => empty($itemIds) ? 'full' : 'partial'
                                     ]);
            }

        } catch (Exception $exception)
        {
            Log::error(__CLASS__ . '::' . __METHOD__ . ' 同步商品失败', [
                'code'    => $exception->getCode(),
                'message' => $exception->getMessage(),
                'file'    => $exception->getFile(),
                'line'    => $exception->getLine(),
                'trace'   => $exception->getTraceAsString(),
            ]);

            return self::Fail('批量同步商品失败', 400);
        }
    }

    /**
     * 从索引中删除商品
     *
     * @param array $itemIds      商品ID数组
     * @param array $publicParams 公共参数
     *
     * @return LogicResult
     * @noinspection PhpUnused
     */
    public static function DeleteItemsFromIndex(array $itemIds, array $publicParams): LogicResult
    {
        try
        {
            if (empty($itemIds))
            {
                return self::Fail('删除商品，缺少商品ID必选参数', 400);
            }

            // 验证参数
            if (empty($publicParams))
            {
                return self::Fail('同步商品，缺少公共必选参数', 400);
            }

            // 获取机构ID
            $hospitalOrgId = intval(Arr::get($publicParams, '_hospitalOrgId'));
            if (empty($hospitalOrgId))
            {
                return self::Fail('同步商品，缺少机构ID必选参数', 400);
            }

            $client    = MeiLiSearchClient::getInstance();
            $indexName = MeiLiSearchClient::generateIndexName($hospitalOrgId);

            // 删除文档
            $success = $client->deleteDocuments($indexName, $itemIds);
            if (!$success)
            {
                return self::Fail('删除文档失败');
            }

            return self::Success([
                                     'deleted_count' => count($itemIds),
                                     'item_ids'      => $itemIds
                                 ]);

        } catch (Exception $e)
        {
            Log::error(__CLASS__ . '::' . __METHOD__ . ' 删除商品失败', [
                'code'    => $e->getCode(),
                'message' => $e->getMessage(),
                'file'    => $e->getFile(),
                'line'    => $e->getLine(),
                'trace'   => $e->getTraceAsString(),
            ]);

            return self::Fail('删除商品失败: ' . $e->getMessage());
        }
    }

    /**
     * 清空索引
     *
     * @param array $publicParams 公共参数
     *
     * @return LogicResult
     */
    public static function FlushIndex(array $publicParams): LogicResult
    {

        // 验证参数
        if (empty($publicParams))
        {
            return self::Fail('清空索引，缺少必选参数', 400);
        }

        // 获取机构ID
        $hospitalOrgId = intval(Arr::get($publicParams, '_hospitalOrgId'));
        if (empty($hospitalOrgId))
        {
            return self::Fail('清空索引，缺少机构ID必选参数', 400);
        }

        $client    = MeiLiSearchClient::getInstance();
        $indexName = MeiLiSearchClient::generateIndexName($hospitalOrgId);

        $success = $client->deleteAllDocuments($indexName);
        if ($success)
        {
            return self::Success([
                                     'index_name' => $indexName,
                                     'message'    => '索引清空成功'
                                 ]);
        }
        else
        {
            return self::Fail('清空索引失败，索引可能不存在');
        }
    }

    /**
     * 获取商品数据（支持按ID获取或获取全部）
     *
     * @param int        $itemType      商品类型
     * @param string|int $hospitalOrgId 机构ID
     * @param array      $itemIds       商品ID数组，为空时获取全部
     *
     * @return LogicResult
     */
    private static function getItemsByType(int $itemType, string|int $hospitalOrgId, array $itemIds = []): LogicResult
    {
        try
        {
            switch ($itemType)
            {
                case ItemTypeEnum::Sku->value:
                    $items = MeiLiSearchItemModel::getItemList($hospitalOrgId, $itemIds);
                    break;
                case ItemTypeEnum::Beauty->value:
                    $items = MeiLiSearchItemModel::getBeautyItemList($hospitalOrgId, $itemIds);
                    break;
                case ItemTypeEnum::Spu->value:
                    $items = MeiLiSearchItemModel::getSuitItemList($hospitalOrgId, $itemIds);
                    break;
                default:
                    return self::Fail('不支持的商品类型: ' . $itemType);
            }

            return self::Success($items);

        } catch (Exception $e)
        {
            return self::Fail('获取商品数据失败: ' . $e->getMessage());
        }
    }

    /**
     * 将商品数据同步到索引
     *
     * @param array      $items         商品数据数组
     * @param string|int $hospitalOrgId 机构ID
     *
     * @return LogicResult
     */
    private static function syncItemsToIndex(array $items, string|int $hospitalOrgId): LogicResult
    {
        try
        {
            if (empty($items))
            {
                return self::Success(['message' => '没有商品需要同步']);
            }

            $client    = MeiLiSearchClient::getInstance();
            $indexName = MeiLiSearchClient::generateIndexName($hospitalOrgId);

            // 构建索引文档
            $documents = self::BuildIndexDoc($items);

            // 添加或更新文档
            $success = $client->addDocuments($indexName, $documents);
            if (!$success)
            {
                return self::Fail('添加文档到索引失败');
            }

            return self::Success([
                                     'synced_count' => count($documents),
                                     'index_name'   => $indexName
                                 ]);

        } catch (Exception $e)
        {
            return self::Fail('同步到索引失败: ' . $e->getMessage());
        }
    }


    /**
     * 构建索引文档
     *
     * @param array $items 商品数据数组
     *
     * @return array
     */
    private static function BuildIndexDoc(array $items): array
    {
        $result = [];
        foreach ($items as $value)
        {
            // 一级项目类型
            $curFirstSaleType = [];
            if ($value?->first_sale_type_id)
            {
                $curFirstSaleType = [
                    'id'         => intval($value?->first_sale_type_id),
                    'name'       => trim($value?->first_sale_type_name ?? ''),
                    'alias_name' => trim($value?->first_sale_type_alias_name ?? ''),
                ];
            }

            // 二级项目类型
            $curSecondSaleType = [];
            if (!empty($value->second_sale_type_id))
            {
                $curSecondSaleType = [
                    'id'         => intval($value?->second_sale_type_id),
                    'name'       => trim($value?->second_sale_type_name ?? ''),
                    'alias_name' => trim($value?->second_sale_type_alias_name ?? ''),
                ];
            }

            // 药品类型
            $curDrugType = [];
            if (!empty($value->drug_type_id))
            {
                $curDrugType = [
                    'id'         => intval($value?->drug_type_id),
                    'name'       => trim($value?->drug_type_name ?? ''),
                    'alias_name' => trim($value?->drug_type_alias_name ?? ''),
                ];
            }

            // 一级分类
            $curFirstCategory = [];
            if (!empty($value->first_category_id))
            {
                $curFirstCategory = [
                    'id'         => intval($value?->first_category_id),
                    'name'       => trim($value?->first_category_name ?? ''),
                    'alias_name' => trim($value?->first_category_alias_name ?? ''),
                ];
            }

            // 二级分类
            $curSecondCategory = [];
            if (!empty($value->second_category_id))
            {
                $curSecondCategory = [
                    'id'         => intval($value?->second_category_id),
                    'name'       => trim($value?->second_category_name ?? ''),
                    'alias_name' => trim($value?->second_category_alias_name ?? ''),
                ];
            }

            // 品牌
            $curBrand = [];
            if (!empty($value->brand_id))
            {
                $curBrand = [
                    'id'           => intval($value?->brand_id),
                    'name'         => trim($value?->brand_name ?? ''),
                    'chinese_name' => trim($value?->brand_chinese_name ?? ''),
                    'english_name' => trim($value?->brand_english_name ?? ''),
                ];
            }

            // 整装单位
            $curPackUnit = [];
            if (!empty($value->pack_unit_id))
            {
                $curPackUnit = [
                    'id'   => intval($value?->pack_unit_id),
                    'name' => trim($value?->pack_unit ?? ''),
                ];
            }

            // 散装单位
            $curBulkUnit = [];
            if (!empty($value->bulk_unit_id))
            {
                $curBulkUnit = [
                    'id'   => intval($value?->bulk_unit_id),
                    'name' => trim($value?->bulk_unit ?? ''),
                ];
            }

            // 计量单位
            $curUseUnit = [];
            if (!empty($value->use_unit_id))
            {
                $curUseUnit = [
                    'id'   => intval($value?->use_unit_id),
                    'name' => trim($value?->use_unit ?? ''),
                ];
            }

            // 组合明细
            $suitItemDetail = [];

            // 单品直接使用原有的采购医院ID
            if (empty($value->suit_items))
            {
                $purchaseHospitalIds = array_values(array_filter(array_map('intval', explode(',', $value?->purchase_hospital_ids ?? ''))));
            }
            else
            {
                // 组合商品：构建子商品详情
                $suitItemDetail      = self::BuildIndexDoc($value->suit_items);
                $purchaseHospitalIds = [];

                /*
                // 计算所有子商品都支持的医院ID（交集）
                $allHospitalIds = null;
                foreach ($value->suit_items as $suitItem)
                {
                    $itemHospitalIds = array_values(array_filter(array_map('intval', explode(',', $suitItem->purchase_hospital_ids ?? ''))));
                    if ($allHospitalIds === null)
                    {
                        // 第一个子商品，初始化
                        $allHospitalIds = $itemHospitalIds;
                    }
                    else
                    {
                        // 取交集：只保留所有子商品都支持的医院
                        $allHospitalIds = array_intersect($allHospitalIds, $itemHospitalIds);
                    }
                }

                $purchaseHospitalIds = array_values($allHospitalIds ?? []);
                */
            }

            $result[] = [
                'id'                        => intval($value?->id),
                'uid'                       => trim($value?->uid),
                'org_id'                    => intval($value?->org_id),
                'is_suit'                   => $value->is_suit ?? 0,
                'barcode'                   => trim($value?->barcode ?? ''),
                'name'                      => trim($value?->name),
                'english_name'              => trim($value?->english_name),
                'basis_name'                => trim($value?->basis_name ?? ''),
                'alias_name'                => trim($value?->alias_name ?? ''),
                'mem_code'                  => trim($value?->mem_code ?? ''),
                'item_type'                 => intval($value?->item_type),
                'item_support_pet_category' => array_values(array_filter(array_map('intval', explode(',', $value?->item_support_pet_category ?? '')))),
                'first_sale_type'           => $curFirstSaleType,
                'second_sale_type'          => $curSecondSaleType,
                'drug_type'                 => $curDrugType,
                'first_category'            => $curFirstCategory,
                'second_category'           => $curSecondCategory,
                'brand'                     => $curBrand,
                'pack_unit'                 => $curPackUnit,
                'bulk_unit'                 => $curBulkUnit,
                'use_unit'                  => $curUseUnit,
                'bulk_ratio'                => $value?->bulk_ratio ?? 0,
                'use_ratio'                 => number_format((float) ($value?->use_ratio ?? 0), 2, '.', ''),
                'pack_spec'                 => trim($value?->pack_spec ?? ''),
                'bulk_spec'                 => trim($value?->bulk_spec ?? ''),
                'is_licensed'               => boolval($value?->is_licensed ?? 0),
                'is_precise_metering'       => boolval($value?->is_precise_metering ?? 0),
                'is_recipe_allow'           => boolval($value?->is_recipe_allow ?? 0),
                'is_retail_allow'           => boolval($value?->is_retail_allow ?? 0),
                'is_pack_sale_allow'        => boolval($value?->is_pack_sale_allow ?? 0),
                'is_shelf_life'             => boolval($value?->is_shelf_life ?? 0),
                'shelf_life'                => trim($value?->shelf_life ?? ''),
                'shelf_life_day'            => $value?->shelf_life_day ?? 0,
                'over_life_day'             => $value?->over_life_day ?? 0,
                'is_receive_allow'          => boolval($value?->is_receive_allow ?? 0),
                'desc'                      => trim($value?->desc ?? ''),
                'remark'                    => trim($value?->remark ?? ''),
                'is_purchasable'            => $value?->is_purchasable ?? 0,
                'purchase_hospital_ids'     => $purchaseHospitalIds, // 单品：医院ID，组合：所有子商品都支持的医院ID交集
                'status'                    => intval($value?->status),
                'created_at'                => optional(Carbon::createFromFormat('Y-m-d H:i:s.u', $value->created_at))->format('Y-m-d\TH:i:s.vP') ?? null,
                'updated_at'                => optional(Carbon::createFromFormat('Y-m-d H:i:s.u', $value->updated_at))->format('Y-m-d\TH:i:s.vP') ?? null,
                'quantity'                  => formatDisplayNumber($value->quantity ?? 0),
                'unit_type'                 => $value->unit_type ?? '',
                'suit_items'                => $suitItemDetail,
            ];
        }

        return $result;
    }
}
