<?php

namespace App\Logics\V1;

use Arr;
use App\Support\Item\ItemHelper;
use App\Support\Stock\StockQuantityConversionHelper;
use App\Logics\Logic;
use App\Logics\LogicResult;
use App\Enums\PageEnum;
use App\Enums\StockItemExpireTypeEnum;
use App\Models\ItemModel;
use App\Models\StockItemShelfModel;
use App\Enums\StockGroupTypeEnum;

class StockItemShelfLogic extends Logic
{
    /**
     * 获取库存商品列表
     *
     * @param array $searchParams 搜索参数
     * @param array $publicParams 公共参数
     *
     * @return LogicResult
     */
    public static function GetStockItemLists(array $searchParams, array $publicParams): LogicResult
    {
        if (empty($searchParams))
        {
            return self::Fail('获取库存列表，缺少必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('获取库存列表，缺少公共必选参数', 400);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        if (empty($hospitalId))
        {
            return self::Fail('获取库存列表，缺少医院ID必选参数', 400);
        }

        // 搜索参数处理
        $keywords = trimWhitespace(Arr::get($searchParams, 'keywords', ''));
        $page     = intval(Arr::get($searchParams, 'page', 1)) ?? PageEnum::DefaultPageIndex->value;
        $count    = intval(Arr::get($searchParams, 'count', 10)) ?? PageEnum::DefaultPageSize->value;

        // 构建查询参数
        $queryParams = [
            'hospitalId' => $hospitalId,
            'keywords'   => $keywords,
        ];

        // 获取库存列表数据
        $getStockItemListRes = StockItemShelfModel::getEffectiveStockItemListData($queryParams, $page, $count);
        $totalCount          = $getStockItemListRes['total'] ?? 0;
        $stockItemList       = $getStockItemListRes['data'] ?? [];
        if ($totalCount <= 0 || empty($stockItemList))
        {
            return self::Success(['data' => [], 'total' => 0]);
        }

        $stockItemIds        = array_column($stockItemList, 'item_id');
        $getStockItemInfoRes = ItemLogic::GetItemFullInfo(itemIds: $stockItemIds, publicParams: $publicParams, withItemStock: true);
        if ($getStockItemInfoRes->isFail())
        {
            return $getStockItemInfoRes;
        }

        // 格式化商品信息
        $returnStockItemList = [];
        $getStockItemInfoRes = $getStockItemInfoRes->getData();
        foreach ($getStockItemInfoRes as $curStockItemInfo)
        {
            $curFormatItemInfoRes = ItemHelper::FormatItemInfoStructure($curStockItemInfo);
            if (empty($curFormatItemInfoRes))
            {
                continue;
            }

            $returnStockItemList[] = $curFormatItemInfoRes;
        }

        return self::Success(['total' => $totalCount, 'data' => $returnStockItemList]);
    }

    /**
     * 获取库存预警列表
     *
     * @param array $searchParams
     * @param array $publicParams
     *
     * @return LogicResult
     */
    public static function GetWarningStockItemLists(array $searchParams, array $publicParams): LogicResult
    {
        if (empty($searchParams))
        {
            return self::Fail('获取库存预警列表，缺少必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('获取库存预警列表，缺少公共必选参数', 400);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        if (empty($hospitalId))
        {
            return self::Fail('获取库存预警列表，缺少医院ID必选参数', 400);
        }

        // 搜索参数处理
        $keywords = trimWhitespace(Arr::get($searchParams, 'keywords', ''));
        $page     = intval(Arr::get($searchParams, 'page', 1)) ?? PageEnum::DefaultPageIndex->value;
        $count    = intval(Arr::get($searchParams, 'count', 10)) ?? PageEnum::DefaultPageSize->value;

        // 构建查询参数
        $queryParams = [
            'hospitalId' => $hospitalId,
            'keywords'   => $keywords,
        ];

        // 获取库存预警列表数据
        $getWarningStockItemListRes = StockItemShelfModel::getEffectiveStockWarningItemListData($queryParams, $page, $count);
        $totalCount                 = $getWarningStockItemListRes['total'] ?? 0;
        $stockItemList              = $getWarningStockItemListRes['data'] ?? [];
        if ($totalCount <= 0 || empty($stockItemList))
        {
            return self::Success(['data' => [], 'total' => 0]);
        }

        $stockItemIds        = array_column($stockItemList, 'item_id');
        $getStockItemInfoRes = ItemLogic::GetItemFullInfo(itemIds: $stockItemIds, publicParams: $publicParams, withItemStock: true);
        if ($getStockItemInfoRes->isFail())
        {
            return $getStockItemInfoRes;
        }

        // 格式化商品信息
        $returnStockItemList = [];
        $getStockItemInfoRes = $getStockItemInfoRes->getData();
        foreach ($getStockItemInfoRes as $curStockItemInfo)
        {
            $curFormatItemInfoRes = ItemHelper::FormatItemInfoStructure($curStockItemInfo);
            if (empty($curFormatItemInfoRes))
            {
                continue;
            }

            $returnStockItemList[] = $curFormatItemInfoRes;
        }

        return self::Success(['total' => $totalCount, 'data' => $returnStockItemList]);
    }

    /**
     * 获取临/过期预警商品列表
     *
     * @param array $searchParams 搜索参数
     * @param array $publicParams 公共参数
     *
     * @return LogicResult
     */
    public static function GetExpireStockItemLists(array $searchParams, array $publicParams): LogicResult
    {
        if (empty($searchParams))
        {
            return self::Fail('获取临过期预警列表，缺少必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('获取临过期预警列表，缺少公共必选参数', 400);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        if (empty($hospitalId))
        {
            return self::Fail('获取临过期预警列表，缺少医院ID必选参数', 400);
        }

        // 搜索参数处理
        $keywords        = trimWhitespace(Arr::get($searchParams, 'keywords', ''));
        $expireType      = intval(Arr::get($searchParams, 'expireType', 0));
        $expireStartDate = trimWhitespace(Arr::get($searchParams, 'expireStartDate', ''));
        $expireEndDate   = trimWhitespace(Arr::get($searchParams, 'expireEndDate', ''));
        $page            = intval(Arr::get($searchParams, 'page', PageEnum::DefaultPageIndex->value)) ?: PageEnum::DefaultPageIndex->value;
        $count           = intval(Arr::get($searchParams, 'count', PageEnum::DefaultPageSize->value)) ?: PageEnum::DefaultPageSize->value;

        // 验证过期类型
        if (!empty($expireType) && StockItemExpireTypeEnum::notExists($expireType))
        {
            return self::Fail('获取临过期预警列表，过期类型参数错误', 400);
        }

        // 验证日期格式
        if (!empty($expireStartDate) && !checkDateIsValid($expireStartDate))
        {
            return self::Fail('获取临过期预警列表，开始日期格式错误', 400);
        }
        if (!empty($expireEndDate) && !checkDateIsValid($expireEndDate))
        {
            return self::Fail('获取临过期预警列表，结束日期格式错误', 400);
        }

        // 构建查询参数
        $queryParams = [
            'hospitalId'      => $hospitalId,
            'keywords'        => $keywords,
            'expireType'      => $expireType,
            'expireStartDate' => $expireStartDate,
            'expireEndDate'   => $expireEndDate,
        ];

        // 获取临/过期预警列表数据
        $getExpireStockItemListRes = StockItemShelfModel::getExpireWarningItemListData($queryParams, $page, $count);
        $totalCount                = $getExpireStockItemListRes['total'] ?? 0;
        $expireItemList            = $getExpireStockItemListRes['data'] ?? [];
        if ($totalCount <= 0 || empty($expireItemList))
        {
            return self::Success(['data' => [], 'total' => 0]);
        }

        // 获取商品基础信息
        $itemIds        = array_unique(array_column($expireItemList, 'item_id'));
        $getItemInfoRes = ItemLogic::GetItemFullInfo(itemIds: $itemIds, publicParams: $publicParams);
        if ($getItemInfoRes->isFail())
        {
            return $getItemInfoRes;
        }

        $getItemInfoRes = $getItemInfoRes->getData();
        $getItemInfoRes = array_column($getItemInfoRes, null, 'id');

        // 格式化返回数据
        $returnExpireItemList = [];
        foreach ($expireItemList as $curExpireStockItem)
        {
            $curItemInfo = $getItemInfoRes[$curExpireStockItem['item_id']] ?? [];
            if (empty($curItemInfo))
            {
                continue;
            }

            // 计算临期、过期状态
            $curExpireStatus = $curExpireStockItem['expire_days'] > 0 ? StockItemExpireTypeEnum::NearExpire->value : StockItemExpireTypeEnum::Expired->value;

            $returnExpireItemList[] = [
                'type'        => [
                    'id'   => $curExpireStatus,
                    'name' => StockItemExpireTypeEnum::getDescription($curExpireStatus),
                ],
                'stock'       => StockQuantityConversionHelper::getStockQuantityStructure($curExpireStockItem,
                                                                                          $curItemInfo,
                                                                                          ['pack' => 'total_pack_quantity', 'bulk' => 'total_bulk_quantity']),
                'expiredDate' => $curExpireStockItem['expired_date'],
                'expiredDays' => abs($curExpireStockItem['expire_days']),
                'shelfCode'   => $curExpireStockItem['shelf_code'],
                'item'        => ItemHelper::FormatItemInfoStructure($curItemInfo),
            ];
        }

        return self::Success(['total' => $totalCount, 'data' => $returnExpireItemList]);
    }

    /**
     * 获取商品库存详情，按照货位+过期时间分组
     *
     * @param int   $itemId
     * @param array $publicParams
     * @param bool  $stockIsValid   有效库存：true:未过期 false:包含过期
     * @param bool  $isExpiryDetail true:根据效期分组 false:根据效期+货位码分组
     *
     * @return LogicResult
     */
    public static function GetStockItemDetail(int $itemId, array $publicParams, bool $stockIsValid = true, bool $isExpiryDetail = false): LogicResult
    {
        if (empty($itemId))
        {
            return self::Fail('获取商品库存详情，缺少商品ID必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('获取商品库存详情，缺少公共必选参数', 400);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        if (empty($hospitalId))
        {
            return self::Fail('获取商品库存详情，缺少医院ID必选参数', 400);
        }

        $getItemInfoRes = ItemLogic::GetItemFullInfo(itemIds: [$itemId], publicParams: $publicParams, withItemPrice: true, withItemStock: true);
        if ($getItemInfoRes->isFail())
        {
            return $getItemInfoRes;
        }

        // 格式化商品信息
        $getItemInfoRes       = !empty($getItemInfoRes->getData()) ? current($getItemInfoRes->getData()) : [];
        $getFormatItemInfoRes = ItemHelper::FormatItemInfoStructure($getItemInfoRes);
        if (empty($getFormatItemInfoRes))
        {
            return self::Fail('获取商品库存详情，商品信息错误', 30000);
        }

        // 获取商品库存明细，$isExpiryDetail=>true:效期分组；否则:货位 + 效期分组
        if (!empty($isExpiryDetail))
        {
            $getStockItemQuantityDetailRes = self::GetEffectiveStockGroupedByExpire([$itemId], $publicParams, $stockIsValid);
        }
        else
        {
            $getStockItemQuantityDetailRes = self::GetEffectiveStockGroupedByShelfAndExpire([$itemId], $publicParams, $stockIsValid);
        }

        if ($getStockItemQuantityDetailRes->isFail())
        {
            return $getStockItemQuantityDetailRes;
        }

        $getStockItemQuantityDetailRes = $getStockItemQuantityDetailRes->getData($itemId, []);

        return self::Success(['item' => $getFormatItemInfoRes, 'stock' => $getStockItemQuantityDetailRes]);
    }

    /**
     * 获取商品库存汇总
     *
     * @param array $itemIds
     * @param array $publicParams
     * @param bool  $isValid
     * @param array $filterCondition 过滤条件，指定效期；指定货位
     *
     * @return LogicResult
     */
    public static function GetEffectiveQuantity(array $itemIds, array $publicParams, bool $isValid = true, array $filterCondition = []): LogicResult
    {
        if (empty($itemIds))
        {
            return self::Fail('获取商品库存汇总，缺少商品ID必选参数', 400);
        }

        if (empty($publicParams))
        {
            return self::Fail('获取商品库存汇总，缺少公共必选参数', 400);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        if (empty($hospitalId))
        {
            return self::Fail('获取商品库存汇总，缺少医院ID必选参数', 400);
        }

        // 获取商品单位
        $getItemUnitRes = ItemModel::getData(fields: ['id', 'pack_unit', 'bulk_unit'], whereIn: ['id' => $itemIds], keyBy: 'id');
        if (empty($getItemUnitRes))
        {
            return self::Fail('获取商品库存汇总，商品信息不存在', 33000);
        }

        // 获取商品库存
        $getStockItemTotalQuantityRes = StockItemShelfModel::getStockItemTotalEffectiveStock(hospitalId     : $hospitalId,
                                                                                             itemIds        : $itemIds,
                                                                                             filterCondition: $filterCondition,
                                                                                             isValid        : $isValid);

        $returnItemTotalQuantity = [];
        foreach ($itemIds as $curItemId)
        {
            foreach ($getStockItemTotalQuantityRes as $curItemQuantity)
            {
                if ($curItemQuantity['item_id'] != $curItemId)
                {
                    continue;
                }

                $returnItemTotalQuantity[$curItemId] = array_merge([
                                                                       'itemBarcode' => $curItemQuantity['item_barcode'],
                                                                       'bulkRatio'   => (int) $curItemQuantity['bulk_ratio'],
                                                                   ],
                                                                   StockQuantityConversionHelper::getStockQuantityStructure($curItemQuantity,
                                                                                                                            $getItemUnitRes[$curItemId] ?? []));
            }
        }

        return self::Success($returnItemTotalQuantity);
    }

    /**
     * 获取商品库存汇总，根据效期分组
     *
     * @param array $itemIds
     * @param array $publicParams
     * @param bool  $isValid 有效库存：true:未过期 false:包含过期
     *
     * @return LogicResult
     */
    public static function GetEffectiveStockGroupedByExpire(array $itemIds, array $publicParams, bool $isValid = true): LogicResult
    {
        if (empty($itemIds))
        {
            return self::Fail('获取商品库存效期分组汇总，缺少商品ID必选参数', 400);
        }

        if (empty($publicParams))
        {
            return self::Fail('获取商品库存效期分组汇总，缺少公共必选参数', 400);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        if (empty($hospitalId))
        {
            return self::Fail('获取商品库存效期分组汇总，缺少医院ID必选参数', 400);
        }

        // 获取商品单位
        $getItemUnitRes = ItemModel::getData(fields: ['id', 'uid', 'pack_unit', 'bulk_unit'], whereIn: ['id' => $itemIds], keyBy: 'id');
        if (empty($getItemUnitRes))
        {
            return self::Fail('获取商品库存效期分组汇总，商品信息不存在', 33000);
        }

        // 获取商品库存
        $getStockItemQuantityDetailRes = StockItemShelfModel::getStockItemEffectiveStockGrouped(hospitalId: $hospitalId,
                                                                                                itemIds   : $itemIds,
                                                                                                groupType : StockGroupTypeEnum::ByShelfAndExpire,
                                                                                                isValid   : $isValid);
        if (empty($getStockItemQuantityDetailRes))
        {
            return self::Success();
        }

        $returnItemQuantityDetail = [];
        foreach ($getStockItemQuantityDetailRes as $curQuantityInfo)
        {
            $returnItemQuantityDetail[$curQuantityInfo['item_id']][] = [
                'itemUid'     => $getItemUnitRes[$curQuantityInfo['item_id']]['uid'] ?? '',
                'itemBarcode' => $curQuantityInfo['item_barcode'],
                'expiredDate' => $curQuantityInfo['expired_date'],
                'stock'       => StockQuantityConversionHelper::getStockQuantityStructure($curQuantityInfo, $getItemUnitRes[$curQuantityInfo['item_id']] ?? []),
            ];
        }

        return self::Success($returnItemQuantityDetail);
    }

    /**
     * 获取商品库存汇总，根据货位+效期分组
     *
     * @param array $itemIds
     * @param array $publicParams
     * @param bool  $isValid 有效库存：true:未过期 false:包含过期
     *
     * @return LogicResult
     */
    public static function GetEffectiveStockGroupedByShelfAndExpire(array $itemIds, array $publicParams, bool $isValid = true): LogicResult
    {
        if (empty($itemIds))
        {
            return self::Fail('获取商品库存分组汇总，缺少商品ID必选参数', 400);
        }

        if (empty($publicParams))
        {
            return self::Fail('获取商品库存分组汇总，缺少公共必选参数', 400);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        if (empty($hospitalId))
        {
            return self::Fail('获取商品库存分组汇总，缺少医院ID必选参数', 400);
        }

        // 获取商品单位
        $getItemUnitRes = ItemModel::getData(fields: ['id', 'uid', 'pack_unit', 'bulk_unit'], whereIn: ['id' => $itemIds], keyBy: 'id');
        if (empty($getItemUnitRes))
        {
            return self::Fail('获取商品库存分组汇总，商品信息不存在', 33000);
        }

        // 获取商品库存
        $getStockItemQuantityDetailRes = StockItemShelfModel::getStockItemEffectiveStockGrouped(hospitalId: $hospitalId,
                                                                                                itemIds   : [$itemIds],
                                                                                                groupType : StockGroupTypeEnum::ByShelfAndExpire,
                                                                                                isValid   : $isValid);
        if (empty($getStockItemQuantityDetailRes))
        {
            return self::Success();
        }

        $returnItemQuantityDetail = [];
        foreach ($getStockItemQuantityDetailRes as $curQuantityInfo)
        {
            $returnItemQuantityDetail[$curQuantityInfo['item_id']][] = [
                'itemUid'     => $getItemUnitRes[$curQuantityInfo['item_id']]['uid'] ?? '',
                'itemBarcode' => $curQuantityInfo['item_barcode'],
                'expiredDate' => $curQuantityInfo['expired_date'],
                'shelfCode'   => $curQuantityInfo['shelf_code'],
                'stock'       => StockQuantityConversionHelper::getStockQuantityStructure($curQuantityInfo, $getItemUnitRes[$curQuantityInfo['item_id']] ?? []),
            ];
        }

        return self::Success($returnItemQuantityDetail);
    }
}
