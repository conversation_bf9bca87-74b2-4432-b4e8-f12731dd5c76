<?php

namespace App\Logics\V1;

use DB;
use Log;
use Throwable;
use App\Logics\Logic;
use App\Logics\LogicResult;
use App\Models\CaseSnapshotModel;
use App\Models\RecipeModel;
use App\Models\RecipeSnapshotModel;
use App\Support\Recipe\RecipeHelper;

/**
 * 数据快照
 * Class DataSnapshotLogic
 * @package      App\Logics\V1
 * @noinspection PhpUnused
 */
class DataSnapshotLogic extends Logic
{
    /**
     * 创建处方快照
     *
     * @param int   $caseId
     * @param array $publicParams
     *
     * @return LogicResult
     * @noinspection PhpUnused
     * @throws Throwable
     */
    public static function createCaseSnapshot(int $caseId, array $publicParams): LogicResult
    {
        if (empty($caseId))
        {
            return self::Fail('caseId，缺少必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('publicParams，缺少必选参数', 400);
        }

        // 获取病历信息
        $getCaseRes = CaseLogic::GetValidCaseById($caseId, $publicParams);
        if ($getCaseRes->isFail())
        {
            return $getCaseRes;
        }

        $getCaseRes = $getCaseRes->getData();
        if ($getCaseRes['finished'] != 1)
        {
            return self::Fail('病历未完结，无法生成快照', 38000);
        }

        // 获取病历详情
        $getCaseDetailRes = CaseLogic::GetCaseDetail($caseId, $publicParams, true);
        if ($getCaseDetailRes->isFail())
        {
            return $getCaseDetailRes;
        }

        // 病历中各个维度详情
        $getCaseDetailRes = $getCaseDetailRes->getData();
        if (empty($getCaseDetailRes['caseInfo']) || empty($getCaseDetailRes['memberInfo']) || empty($getCaseDetailRes['petInfo']) || empty($getCaseDetailRes['petVitalSignInfo']) || empty($getCaseDetailRes['diagnosisInfo']))
        {
            return self::Fail('病历详情信息不完整，无法生成快照', 38000);
        }

        // 获取处方信息
        $getRecipeRes = RecipeModel::getData(where: ['case_id' => $caseId, 'status' => 1]);
        if (!empty($getRecipeRes))
        {
            // 格式化处方信息
            $getFormatRecipeRes = RecipeHelper::FormatRecipeStructure($getCaseRes['case_code'],
                                                                      $getRecipeRes,
                                                                      $publicParams);
            if ($getFormatRecipeRes->isFail())
            {
                return $getFormatRecipeRes;
            }

            $getFormatRecipeRes = $getFormatRecipeRes->getData('data', []);
            $getFormatRecipeRes = array_column($getFormatRecipeRes, null, 'recipeCode');
        }

        // 写入快照数据
        try
        {
            DB::beginTransaction();

            // 病历快照
            $caseSnapshotData = [
                'case_id'            => $caseId,
                'case_code'          => $getCaseRes['case_code'],
                'source_type'        => $getCaseRes['source_type'],
                'source_relation_id' => $getCaseRes['source_relation_id'],
                'org_id'             => $getCaseRes['org_id'],
                'brand_id'           => $getCaseRes['brand_id'],
                'hospital_id'        => $getCaseRes['hospital_id'],
                'doctor_id'          => $getCaseRes['doctor_id'],
                'member_id'          => $getCaseRes['member_id'],
                'pet_id'             => $getCaseRes['pet_id'],
                'finished'           => $getCaseRes['finished'],
                'finished_time'      => $getCaseRes['finished_time'],
                'outcome_id'         => $getCaseRes['outcome_id'],
                'case_base_info'     => json_encode($getCaseDetailRes['caseInfo'], JSON_UNESCAPED_UNICODE),
                'case_full_info'     => json_encode($getCaseDetailRes, JSON_UNESCAPED_UNICODE),
            ];
            $isExist          = CaseSnapshotModel::getTotalNumber(['case_id' => $caseId]);
            if (!empty($isExist))
            {
                CaseSnapshotModel::on()
                                 ->where(['case_id' => $caseId])
                                 ->update($caseSnapshotData);
            }
            else
            {
                CaseSnapshotModel::insertOne($caseSnapshotData);
            }

            foreach ($getRecipeRes as $recipeInfo)
            {
                // 格式化的处方信息
                $curFormatRecipeInfo = $getFormatRecipeRes[$recipeInfo['recipe_code']] ?? [];
                if (empty($curFormatRecipeInfo))
                {
                    continue;
                }

                // 格式化处方项目结构
                $curRecipeDetailRes = RecipeLogic::GetRecipeDetail($recipeInfo['id'], $publicParams, false, false);
                if ($curRecipeDetailRes->isFail())
                {
                    DB::rollBack();

                    return $curRecipeDetailRes;
                }

                $curRecipeItemsList      = $curRecipeDetailRes->getData('items', []);
                $curRecipeItemsGroupList = $curRecipeDetailRes->getData('itemGroupList', []);
                if (empty($curRecipeItemsList) || empty($curRecipeItemsGroupList))
                {
                    continue;
                }

                $tmpRecipeSnapshotData = [
                    'recipe_id'          => $recipeInfo['id'],
                    'recipe_code'        => $recipeInfo['recipe_code'],
                    'case_id'            => $recipeInfo['case_id'],
                    'member_id'          => $recipeInfo['member_id'],
                    'pet_id'             => $recipeInfo['pet_id'],
                    'org_id'             => $recipeInfo['org_id'],
                    'brand_id'           => $recipeInfo['brand_id'],
                    'hospital_id'        => $recipeInfo['hospital_id'],
                    'doctor_id'          => $recipeInfo['doctor_id'],
                    'source_type'        => $recipeInfo['source_type'],
                    'source_relation_id' => $recipeInfo['source_relation_id'],
                    'price'              => $recipeInfo['price'],
                    'is_paid'            => $recipeInfo['is_paid'],
                    'status'             => $recipeInfo['status'],
                    'recipe_info'        => json_encode($curFormatRecipeInfo, JSON_UNESCAPED_UNICODE),
                    'recipe_items'       => json_encode($curRecipeItemsList, JSON_UNESCAPED_UNICODE),
                    'recipe_items_group' => json_encode($curRecipeItemsGroupList, JSON_UNESCAPED_UNICODE),
                ];

                // 存在则更新，不存在则新增
                $isExist = RecipeSnapshotModel::getTotalNumber(['recipe_code' => $recipeInfo['recipe_code']]);
                if ($isExist > 0)
                {
                    RecipeSnapshotModel::on()
                                       ->where(['recipe_code' => $recipeInfo['recipe_code']])
                                       ->update($tmpRecipeSnapshotData);
                }
                else
                {
                    RecipeSnapshotModel::insertOne($tmpRecipeSnapshotData);
                }
            }

            DB::commit();

            return self::Success();
        } catch (Throwable $throwable)
        {
            DB::rollBack();

            Log::error(__CLASS__ . '::' . __METHOD__ . ' 生成病历快照异常', [
                'code'    => $throwable->getCode(),
                'message' => $throwable->getMessage(),
                'file'    => $throwable->getFile(),
                'line'    => $throwable->getLine(),
                'trace'   => $throwable->getTraceAsString(),
            ]);

            return self::Fail('生成病历快照异常', 38000);
        }
    }
}
