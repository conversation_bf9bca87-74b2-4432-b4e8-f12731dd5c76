<?php

namespace App\Logics\V1;

use DB;
use Log;
use Exception;
use Throwable;
use Illuminate\Support\Arr;
use App\Enums\PageEnum;
use App\Enums\PetGenderEnum;
use App\Enums\PetSterileStatusEnum;
use App\Enums\PetPreventStatusEnum;
use App\Enums\PetDewormingStatusEnum;
use App\Enums\PetWeightSourceTypeEnum;
use App\Enums\PetPreventDewormingSourceTypeEnum;
use App\Logics\Logic;
use App\Logics\LogicResult;
use App\Models\MemberModel;
use App\Models\HospitalModel;
use App\Models\MemberPetsModel;
use App\Models\PetBreedDictModel;
use App\Models\PetColorDictModel;
use App\Models\PetPreventRecordModel;
use App\Models\PetDewormingRecordModel;
use App\Models\PetWeightRecordModel;
use App\Models\PetCategoryDictModel;
use App\Models\PetLiveStatusDictModel;

class PetLogic extends Logic
{
    /**
     * 宠物最大生日年龄
     */
    const int MAX_BIRTHDAY_AGE = 30;

    /**
     * 宠物最大体重
     */
    const int MAX_WEIGHT = 100;

    /**
     * 获取有效宠物信息
     *
     * @param int    $petId
     * @param string $petUid
     * @param int    $memberId
     *
     * @return LogicResult
     */
    public static function GetValidPetByIdOrUid(int $petId = 0, string $petUid = '', int $memberId = 0): LogicResult
    {
        if (empty($petId) && empty($petUid))
        {
            return self::Fail('查找有效宠物，缺少必选参数', 400);
        }

        $where = ['status' => 1];
        if (!empty($petId))
        {
            $where['id'] = $petId;
        }
        if (!empty($petUid))
        {
            $where['uid'] = $petUid;
        }
        if (!empty($memberId))
        {
            $where['member_id'] = $memberId;
        }

        $getPetRes = MemberPetsModel::getData(where: $where);
        if (empty($getPetRes))
        {
            return self::Fail('宠物无效、不存在', 32000);
        }

        return self::Success(current($getPetRes));
    }

    /**
     * 查找会员宠物列表
     *
     * @param int   $memberId
     * @param array $searchParams
     *
     * @return LogicResult
     * @throws Exception
     */
    public static function SearchMemberPetList(int $memberId, array $searchParams = []): LogicResult
    {
        if (empty($memberId))
        {
            return self::Fail('查找宠物列表，缺少用户ID必选参数', 400);
        }

        $where = [['member_id', '=', $memberId], ['status', '=', 1]];
        if (!empty($searchParams['name']))
        {
            $where[] = ['name', 'like', '%' . $searchParams['name'] . '%'];
        }
        if (!empty($searchParams['uid']))
        {
            $where[] = ['uid', '=', $searchParams['uid']];
        }
        if (!empty($searchParams['startDate']) && strtotime($searchParams['startDate']) !== false)
        {
            $where[] = ['created_at', '>=', $searchParams['startDate'] . ' 00:00:00'];
        }
        if (!empty($searchParams['endDate']) && strtotime($searchParams['endDate']) !== false)
        {
            $where[] = ['created_at', '<=', $searchParams['endDate'] . ' 23:59:59'];
        }

        // 获取条目总数
        $getMemberPetTotalRes = MemberPetsModel::getTotalNumber($where);
        if (empty($getMemberPetTotalRes) || $getMemberPetTotalRes <= 0)
        {
            return self::Success(['total' => 0, 'data' => []]);
        }

        // 获取条目数据
        $getMemberPetListRes = MemberPetsModel::getData(where    : $where,
                                                        orderBys : ['created_at' => 'desc'],
                                                        pageIndex: $searchParams['page'] ?? PageEnum::DefaultPageIndex->value,
                                                        pageSize : $searchParams['count'] ?? PageEnum::DefaultPageSize->value);
        if (empty($getMemberPetListRes))
        {
            return self::Success(['total' => 0, 'data' => []]);
        }

        // 宠物种类
        $petCategoryIds  = array_column($getMemberPetListRes, 'category_id');
        $petCategoryList = PetCategoryDictModel::getData(whereIn: ['id' => $petCategoryIds], keyBy: 'id');

        // 宠物品种
        $petBreedIds  = array_column($getMemberPetListRes, 'breed_id');
        $petBreedList = PetBreedDictModel::getData(whereIn: ['id' => $petBreedIds], keyBy: 'id');

        $returnPetList = [];
        foreach ($getMemberPetListRes as $curPetInfo)
        {
            // 宠物种类
            $tmpPetCategory = null;
            if (!empty($petCategoryList[$curPetInfo['category_id']]))
            {
                $tmpPetCategory = [
                    'id'   => $petCategoryList[$curPetInfo['category_id']]['id'] ?? null,
                    'name' => $petCategoryList[$curPetInfo['category_id']]['name'] ?? '',
                ];
            }

            // 宠物品种
            $tmpPetBreed = null;
            if (empty($curPetInfo['breed_id']))
            {
                $tmpPetBreed = [
                    'id'   => 0,
                    'name' => $curPetInfo['breed_desc'],
                ];
            }
            else
            {
                $tmpPetBreed = [
                    'id'   => $petBreedList[$curPetInfo['breed_id']]['id'] ?? null,
                    'name' => $petBreedList[$curPetInfo['breed_id']]['name'] ?? '',
                ];
            }

            // 宠物性别
            $tmpPetGender = [
                'id'   => $curPetInfo['gender'],
                'name' => PetGenderEnum::getDescription($curPetInfo['gender']),
            ];

            $tmpPetInfo = [
                'id'           => $curPetInfo['id'],
                'uid'          => $curPetInfo['uid'],
                'name'         => $curPetInfo['name'],
                'category'     => $tmpPetCategory,
                'breed'        => $tmpPetBreed,
                'gender'       => $tmpPetGender,
                'recordNumber' => $curPetInfo['record_number'],
                'birthday'     => $curPetInfo['birthday'],
                'formatAge'    => calculatePetAge($curPetInfo['birthday']),
                'remark'       => $curPetInfo['remark'],
                'createdAt'    => $curPetInfo['created_at'],
            ];

            $returnPetList[] = $tmpPetInfo;
        }

        return self::Success(['total' => $getMemberPetTotalRes, 'data' => $returnPetList]);
    }

    /**
     * 添加宠物
     *
     * @param int   $memberId
     * @param array $addPetParams
     * @param array $publicParams
     *
     * @return LogicResult
     */
    public static function AddPet(int $memberId, array $addPetParams, array $publicParams): LogicResult
    {
        // 验证会员是否存在
        if (empty($memberId))
        {
            return self::Fail('memberId，缺少必选参数', 400);
        }
        if (empty($addPetParams) || empty($publicParams))
        {
            return self::Fail('添加宠物，缺少必选参数', 400);
        }

        // 公共参数
        $hospitalOrgId   = intval(Arr::get($publicParams, '_hospitalOrgId'));
        $hospitalBrandId = intval(Arr::get($publicParams, '_hospitalBrandId'));
        $hospitalId      = intval(Arr::get($publicParams, '_hospitalId'));

        // 获取有效会员信息
        $getMemberRes = MemberLogic::GetValidMemberByIdOrUid($memberId);
        if ($getMemberRes->isFail())
        {
            return $getMemberRes;
        }

        $getMemberRes = $getMemberRes->getData();

        // 接受统一验证后的参数
        $getValidateRes = self::_validateAddPetParams($addPetParams);
        if ($getValidateRes->isFail())
        {
            return $getValidateRes;
        }

        try
        {
            DB::beginTransaction();

            $insertPetData = array_merge([
                                             'member_id'   => $getMemberRes['id'],
                                             'uid'         => generateUUID(),
                                             'hospital_id' => $hospitalId,
                                             'brand_id'    => $hospitalBrandId,
                                             'org_id'      => $hospitalOrgId
                                         ],
                                         $getValidateRes->getData());
            $petId         = MemberPetsModel::insertOne($insertPetData);
            if (empty($petId))
            {
                DB::rollBack();

                return self::Fail('添加宠物失败', 32001);
            }

            // 如果宠物病历号为空，则自动生成一个
            if (empty($petRecordNumber))
            {
                $petRecordNumber = generatePetRecordNumber();
                MemberPetsModel::updateOne($petId, ['record_number' => $petRecordNumber]);
            }

            // 写入宠物体重记录
            $insertPetWeightData = [
                'weight'       => sprintf("%.2f", $getValidateRes->getData('weight')),
                'relationType' => PetWeightSourceTypeEnum::Add->value,
                'relationId'   => $petId,
            ];
            $getEditPetWeightRes = self::EditPetWeight($petId, $insertPetWeightData, $publicParams);
            if ($getEditPetWeightRes->isFail())
            {
                DB::rollBack();

                return $getEditPetWeightRes;
            }

            DB::commit();

            // 宠物新增完成
            return self::Success();
        } catch (Throwable $throwable)
        {

            Log::error(__CLASS__ . '::' . __METHOD__ . ' 添加宠物异常', [
                'code'    => $throwable->getCode(),
                'message' => $throwable->getMessage(),
                'file'    => $throwable->getFile(),
                'line'    => $throwable->getLine(),
                'trace'   => $throwable->getTraceAsString(),
            ]);

            return self::Fail('添加宠物异常', 32001);
        }
    }

    /**
     * 编辑宠物
     *
     * @param int   $petId
     * @param array $editPetParams
     * @param array $publicParams
     *
     * @return LogicResult
     * @throws Throwable
     */
    public static function EditPet(int $petId, array $editPetParams, array $publicParams): LogicResult
    {
        if (empty($petId))
        {
            return self::Fail('更新宠物信息，缺少宠物ID必选参数', 32002);
        }
        if (empty($editPetParams))
        {
            return self::Fail('更新宠物信息，缺少宠物信息必选参数', 32002);
        }
        if (empty($publicParams))
        {
            return self::Fail('更新宠物信息，缺少公共必选参数', 32002);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        $orgId      = intval(Arr::get($publicParams, '_hospitalOrgId'));
        if (empty($hospitalId) || empty($orgId))
        {
            return self::Fail('更新宠物信息，缺少医院ID、机构ID必选参数', 32002);
        }

        // 获取宠物信息
        $getPetInfoRes = self::GetValidPetByIdOrUid($petId);
        if ($getPetInfoRes->isFail())
        {
            return $getPetInfoRes;
        }

        // 接受统一验证后的参数
        $getValidateRes = self::_validateAddPetParams($editPetParams);
        if ($getValidateRes->isFail())
        {
            return $getValidateRes;
        }

        // 转化后的参数对应POST过来修改的参数
        $editPetParamsRelationMap = [
            'name'            => 'petName',
            'gender'          => 'petGender',
            'category_id'     => 'petCategoryId',
            'icon'            => 'petIcon',
            'birthday'        => 'petBirthday',
            'breed_id'        => 'petBreedId',
            'breed_desc'      => 'petBreedDesc',
            'weight'          => 'petWeight',
            'sterile_status'  => 'petSterileStatus',
            'sterile_date'    => 'petSterileDate',
            'remark'          => 'remark',
            'color_id'        => 'petColorId',
            'color_desc'      => 'petColorDesc',
            'live_status'     => 'petLiveStatus',
            'prevent_status'  => 'petPreventStatus',
            'irritate_remark' => 'petIrritateRemark',
        ];

        // 比较新旧数据，只更新变化的字段
        $updateData = [];
        foreach ($getValidateRes->getData() as $fieldKey => $newValue)
        {
            $tmpEditFieldName = $editPetParamsRelationMap[$fieldKey] ?? '';
            if (empty($tmpEditFieldName))
            {
                continue;
            }

            // 如果传递的参数中不存在这项默认不修改，过滤
            if (!array_key_exists($tmpEditFieldName, $editPetParams))
            {
                continue;
            }

            // 验证之后的新值与旧值不一致
            if ($newValue != $getPetInfoRes->getData($fieldKey))
            {
                $updateData[$fieldKey] = $newValue;
            }
        }

        // 如果没有需要更新的数据，直接返回成功
        if (empty($updateData))
        {
            return self::Success();
        }

        try
        {
            DB::beginTransaction();

            // 宠物体重变动
            if (array_key_exists('weight', $updateData))
            {
                $editPetWeightParams = [
                    'weight'       => sprintf("%.2f", $updateData['weight']),
                    'relationType' => PetWeightSourceTypeEnum::Edit->value,
                    'relationId'   => $petId,
                ];
                $getEditPetWeightRes = self::EditPetWeight($petId, $editPetWeightParams, $publicParams);
                if ($getEditPetWeightRes->isFail())
                {
                    DB::rollBack();

                    return $getEditPetWeightRes;
                }
            }

            // 更新宠物信息
            MemberPetsModel::updateOne($petId, $updateData);

            DB::commit();

            return self::Success();

        } catch (Throwable $throwable)
        {
            DB::rollBack();

            Log::error(__CLASS__ . '::' . __METHOD__ . ' 编辑宠物异常', [
                'code'    => $throwable->getCode(),
                'message' => $throwable->getMessage(),
                'file'    => $throwable->getFile(),
                'line'    => $throwable->getLine(),
                'trace'   => $throwable->getTraceAsString(),
            ]);
        }

        return self::Fail('编辑宠物异常', 32002);
    }

    /**
     * 更新宠物体重
     *
     * @param int   $petId
     * @param array $editPetWeightParams
     * @param array $publicParams
     *
     * @return LogicResult
     * @throws Throwable
     */
    public static function EditPetWeight(int $petId, array $editPetWeightParams, array $publicParams): LogicResult
    {
        if (empty($petId))
        {
            return self::Fail('更新宠物体重，缺少宠物ID必选参数', 32002);
        }
        if (empty($editPetWeightParams))
        {
            return self::Fail('更新宠物体重，缺少体重必选参数', 32002);
        }
        if (empty($publicParams))
        {
            return self::Fail('更新宠物体重，缺少公共必选参数', 32002);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        $orgId      = intval(Arr::get($publicParams, '_hospitalOrgId'));
        if (empty($hospitalId) || empty($orgId))
        {
            return self::Fail('更新宠物体重，缺少医院ID、机构ID必选参数', 32002);
        }

        // 业务参数
        $weight       = (string) Arr::get($editPetWeightParams, 'weight', 0);
        $relationType = intval(Arr::get($editPetWeightParams, 'relationType', 0));
        $relationId   = intval(Arr::get($editPetWeightParams, 'relationId', 0));
        if ($weight < 0 || $weight > self::MAX_WEIGHT)
        {
            return self::Fail('宠物体重参数错误。0-100kg区间', 32002);
        }
        if (empty($relationType) || empty($relationId))
        {
            return self::Fail('更新宠物体重，缺少关联类型、关联ID必选参数', 32002);
        }
        if (!in_array($relationType, PetWeightSourceTypeEnum::values()))
        {
            return self::Fail('更新宠物体重，关联类型参数错误', 32002);
        }

        // 获取宠物信息
        $getPetRes = self::GetValidPetByIdOrUid($petId);
        if ($getPetRes->isFail())
        {
            return $getPetRes;
        }

        try
        {
            DB::beginTransaction();

            // 写入宠物体重记录，如果是新增的话，初始化旧体重为0。新增不需要更新宠物体重
            if ($relationType == PetWeightSourceTypeEnum::Add->value)
            {
                $oldWeight = 0;
            }
            else
            {
                // 更新宠物体重
                MemberPetsModel::updateOne($petId, ['weight' => $weight]);
                $oldWeight = sprintf("%.2f", $getPetRes->getData('weight', 0));
            }
            $insertPetWeightData = [
                'pet_id'        => $petId,
                'old_weight'    => $oldWeight,
                'weight'        => sprintf("%.2f", $weight),
                'hospital_id'   => $hospitalId,
                'org_id'        => $orgId,
                'relation_type' => $relationType,
                'relation_id'   => $relationId,
            ];
            PetWeightRecordModel::insertOne($insertPetWeightData);

            DB::commit();

            return self::Success();
        } catch (Throwable $throwable)
        {
            DB::rollBack();

            Log::error(__CLASS__ . '::' . __METHOD__ . ' 更新宠物体重异常', [
                'code'    => $throwable->getCode(),
                'message' => $throwable->getMessage(),
                'file'    => $throwable->getFile(),
                'line'    => $throwable->getLine(),
                'trace'   => $throwable->getTraceAsString(),
            ]);
        }

        return self::Fail('更新宠物体重异常', 32002);
    }

    /**
     * 批量获取宠物基础信息
     *
     * @param array $petIds
     *
     * @return LogicResult
     * @noinspection PhpUnused
     */
    public static function GetPetBaseInfoByPetIds(array $petIds): LogicResult
    {
        if (empty($petIds))
        {
            return self::Fail('获取宠物基础信息，缺少宠物ID必选参数', 400);
        }

        // 获取宠物信息
        $getPetRes = MemberPetsModel::getManyByIds($petIds)
                                    ->keyBy('id');
        if (empty($getPetRes))
        {
            return self::Success(['data' => []]);
        }

        // 获取用户信息
        $memberIds    = $getPetRes->pluck('member_id')
                                  ->unique()
                                  ->toArray();
        $getMemberRes = MemberModel::getManyByIds($memberIds)
                                   ->keyBy('id');

        // 宠物种类
        $petCategoryIds  = $getPetRes->pluck('category_id')
                                     ->unique()
                                     ->toArray();
        $petCategoryList = PetCategoryDictModel::getManyByIds($petCategoryIds)
                                               ->keyBy('id');

        $returnPetList = [];
        foreach ($getPetRes as $petId => $petInfo)
        {
            // 宠物种类
            $tmpPetCategory = null;
            if (!empty($petCategoryList[$petInfo['category_id']]))
            {
                $tmpPetCategory = [
                    'id'   => $petCategoryList[$petInfo['category_id']]['id'] ?? null,
                    'name' => $petCategoryList[$petInfo['category_id']]['name'] ?? '',
                ];
            }

            // 宠物品种
            $tmpPetBreed = [
                'id'   => $petInfo['breed_id'],
                'name' => $petInfo['breed_desc'],
            ];

            // 宠物性别
            $tmpPetGender = [
                'id'   => $petInfo['gender'],
                'name' => PetGenderEnum::getDescription($petInfo['gender']),
            ];

            // 宠物毛色
            $tmpPetColor = [
                'id'   => $petInfo['color_id'],
                'name' => $petInfo['color_desc'],
            ];

            // 宠物绝育状态
            $tmpSterileStatus = [
                'id'   => $petInfo['sterile_status'],
                'name' => PetsterileStatusEnum::getDescription($petInfo['sterile_status']),
                'date' => normalizeDatetime($petInfo['sterile_date']),
            ];

            // 宠物免疫状态
            $tmpPreventStatus = [
                'id'   => $petInfo['prevent_status'],
                'name' => PetPreventStatusEnum::getDescription($petInfo['prevent_status']),
                'date' => normalizeDatetime($petInfo['prevent_date']),
            ];

            // 宠物驱虫状态
            $tmpDewormingStatus = [
                'id'   => $petInfo['deworming_status'],
                'name' => PetDewormingStatusEnum::getDescription($petInfo['deworming_status']),
                'date' => normalizeDatetime($petInfo['deworming_date']),
            ];

            $tmpMemberInfo = [
                'uid'   => $getMemberRes[$petInfo['member_id']]['uid'],
                'name'  => $getMemberRes[$petInfo['member_id']]['name'],
                'phone' => secretCellphone($getMemberRes[$petInfo['member_id']]['phone']),
            ];

            $tmpPetInfo            = [
                'uid'             => $petInfo['uid'],
                'name'            => $petInfo['name'],
                'category'        => $tmpPetCategory,
                'breed'           => $tmpPetBreed,
                'gender'          => $tmpPetGender,
                'color'           => $tmpPetColor,
                'weight'          => $petInfo['weight'],
                'birthday'        => $petInfo['birthday'],
                'formatAge'       => calculatePetAge($petInfo['birthday']),
                'recordNumber'    => $petInfo['record_number'],
                'sterileStatus'   => $tmpSterileStatus,
                'preventStatus'   => $tmpPreventStatus,
                'dewormingStatus' => $tmpDewormingStatus,
                'createdAt'       => $petInfo['created_at'],
            ];
            $returnPetList[$petId] = [
                'petInfo'    => $tmpPetInfo,
                'memberInfo' => $tmpMemberInfo,
            ];
        }

        return self::Success($returnPetList);
    }

    /**
     * 获取宠物详情
     *
     * @param int $petId
     *
     * @return LogicResult
     */
    public static function GetPetDetailFullInfo(int $petId): LogicResult
    {
        if (empty($petId))
        {
            return self::Fail('petId，缺少必选参数', 400);
        }

        // 获取宠物信息
        $getPetRes = self::GetValidPetByIdOrUid($petId);
        if ($getPetRes->isFail())
        {
            return $getPetRes;
        }

        $getPetRes = $getPetRes->getData();

        // 获取用户信息
        $getMemberRes = MemberLogic::GetValidMemberByIdOrUid($getPetRes['member_id']);
        if ($getMemberRes->isFail() || empty($getMemberRes->getData()))
        {
            return $getMemberRes;
        }
        $memberInfo = $getMemberRes->getData();

        if ($getPetRes['member_id'] != $memberInfo['id'])
        {
            return self::Fail('宠物不属于当前用户', 32000);
        }

        // 宠物种类
        $petCategoryInfo   = [];
        $getPetCategoryRes = PetCategoryDictModel::getOne($getPetRes['category_id']);
        if (!empty($getPetCategoryRes))
        {
            $petCategoryInfo = [
                'id'      => $getPetCategoryRes['id'],
                'name'    => $getPetCategoryRes['name'],
                'isOther' => $getPetCategoryRes['is_other'],
            ];
        }

        // 宠物品种
        $petBreedInfo = [];
        if (empty($getPetRes['breed_id']))
        {
            $petBreedInfo = [
                'id'   => 0,
                'name' => $getPetRes['breed_desc'],
            ];
        }
        else
        {
            $getPetBreedRes = PetBreedDictModel::getOne($getPetRes['breed_id']);
            if (!empty($getPetBreedRes))
            {
                $petBreedInfo = [
                    'id'   => $getPetRes['breed_id'],
                    'name' => $getPetRes['breed_desc'],
                ];
            }
        }

        // 宠物毛色
        $petColorInfo = [];
        if (!empty($getPetRes['color_id']))
        {
            $getPetColorRes = PetColorDictModel::getOne($getPetRes['color_id']);
            $petColorInfo   = [
                'id'      => $getPetRes['color_id'],
                'name'    => $getPetRes['color_desc'],
                'isOther' => $getPetColorRes['is_other'],
            ];
        }

        //宠物存活状态
        $petLiveStatusInfo = [];
        if (!empty($getPetRes['live_status']))
        {
            $getPetLiveStatusRes = PetLiveStatusDictModel::getOne($getPetRes['live_status']);
            $petLiveStatusInfo   = [
                'id'   => $getPetLiveStatusRes['id'],
                'name' => $getPetLiveStatusRes['name'],
            ];
        }

        $returnMemberInfo = [
            'uid'   => $memberInfo['uid'],
            'name'  => $memberInfo['name'],
            'phone' => secretCellphone($memberInfo['phone']),
        ];

        $returnPetFullInfo = [
            'uid'             => $getPetRes['uid'],
            'name'            => $getPetRes['name'],
            'gender'          => [
                'id'   => $getPetRes['gender'],
                'name' => PetGenderEnum::getDescription($getPetRes['gender']),
            ],
            'category'        => $petCategoryInfo,
            'breed'           => $petBreedInfo,
            'color'           => $petColorInfo,
            'birthday'        => $getPetRes['birthday'],
            'weight'          => $getPetRes['weight'],
            'sterileStatus'   => [
                'id'   => $getPetRes['sterile_status'],
                'name' => PetsterileStatusEnum::getDescription($getPetRes['sterile_status']),
                'date' => normalizeDatetime($getPetRes['sterile_date']),
            ],
            'preventStatus'   => [
                'id'   => $getPetRes['prevent_status'],
                'name' => PetPreventStatusEnum::getDescription($getPetRes['prevent_status']),
                'date' => normalizeDatetime($getPetRes['prevent_date']),
            ],
            'dewormingStatus' => [
                'id'   => $getPetRes['deworming_status'],
                'name' => PetDewormingStatusEnum::getDescription($getPetRes['deworming_status']),
                'date' => normalizeDatetime($getPetRes['deworming_date']),
            ],
            'recordNumber'    => $getPetRes['record_number'],
            'liveStatus'      => $petLiveStatusInfo,
            'irritateRemark'  => $getPetRes['irritate_remark'],
            'remark'          => $getPetRes['remark'],
        ];

        return self::Success(['petInfo' => $returnPetFullInfo, 'memberInfo' => $returnMemberInfo]);
    }

    /**
     * 添加宠物免疫记录
     *
     * @param int   $petId
     * @param array $preventParams
     * @param array $publicParams
     *
     * @return LogicResult
     * @throws Throwable
     */
    public static function AddPetPrevent(int $petId, array $preventParams, array $publicParams): LogicResult
    {
        if (empty($petId))
        {
            return self::Fail('petId，缺少必选参数', 400);
        }

        // 公共参数
        $hospitalOrgId   = intval(Arr::get($publicParams, '_hospitalOrgId'));
        $hospitalBrandId = intval(Arr::get($publicParams, '_hospitalBrandId'));
        $hospitalId      = intval(Arr::get($publicParams, '_hospitalId'));
        $userId          = intval(Arr::get($publicParams, '_userId'));
        if (empty($userId) || empty($hospitalOrgId) || empty($hospitalBrandId) || empty($hospitalId))
        {
            return self::Fail('公共参数，缺少必选参数', 400);
        }

        // 验证业务参数
        $sourceType    = intval(Arr::get($publicParams, 'sourceType', 1));
        $preventDate   = trim(Arr::get($preventParams, 'preventDate', '')) ?: null;
        $preventRemark = trim(Arr::get($preventParams, 'preventRemark', ''));
        if (empty($sourceType) || empty(PetPreventDewormingSourceTypeEnum::exists($sourceType)))
        {
            return self::Fail('sourceType，宠物免疫来源参数错误', 400);
        }
        if (!empty($preventDate) && strtotime($preventDate) === false)
        {
            return self::Fail('preventTime，宠物免疫时间参数错误', 400);
        }
        if (!empty($preventRemark) && mb_strlen($preventRemark) > 200)
        {
            return self::Fail('preventRemark，宠物免疫备注参数错误', 400);
        }

        // 获取宠物信息
        $getPetRes = self::GetValidPetByIdOrUid($petId);
        if ($getPetRes->isFail())
        {
            return $getPetRes;
        }

        // 获取用户信息
        $memberId     = $getPetRes->getData('member_id', 0);
        $getMemberRes = MemberLogic::GetValidMemberByIdOrUid($memberId);
        if ($getMemberRes->isFail())
        {
            return $getMemberRes;
        }

        $memberUniqueId = $getMemberRes->getData('unique_id', '');

        try
        {
            // 开始更新宠物免疫状态
            DB::beginTransaction();

            $insertPetPreventRecordData = [
                'org_id'      => $hospitalOrgId,
                'brand_id'    => $hospitalBrandId,
                'hospital_id' => $hospitalId,
                'unique_id'   => $memberUniqueId,
                'member_id'   => $memberId,
                'pet_id'      => $petId,
                'date'        => $preventDate,
                'remark'      => $preventRemark,
                'source_type' => $sourceType,
                'created_by'  => $userId,
            ];
            PetPreventRecordModel::insertOne($insertPetPreventRecordData);

            // 更新宠物最新免疫状态
            MemberPetsModel::updateOne($petId,
                                       [
                                           'prevent_status' => PetPreventStatusEnum::Prevent->value,
                                           'prevent_date'   => $preventDate
                                       ]);

            DB::commit();

            return self::Success();
        } catch (Throwable $throwable)
        {
            DB::rollBack();

            Log::error(__CLASS__ . '::' . __METHOD__ . ' 添加宠物免疫记录异常', [
                'code'    => $throwable->getCode(),
                'message' => $throwable->getMessage(),
                'file'    => $throwable->getFile(),
                'line'    => $throwable->getLine(),
                'trace'   => $throwable->getTraceAsString(),
            ]);
        }

        return self::Fail('添加宠物免疫记录异常', 32003);
    }

    /**
     * 添加宠物驱虫记录
     *
     * @param int   $petId
     * @param array $dewormingParams
     * @param array $publicParams
     *
     * @return LogicResult
     * @throws Throwable
     * @noinspection PhpUnused
     */
    public static function AddPetDeworming(int $petId, array $dewormingParams, array $publicParams): LogicResult
    {
        if (empty($petId))
        {
            return self::Fail('petId，缺少必选参数', 400);
        }

        // 公共参数
        $hospitalOrgId   = intval(Arr::get($publicParams, '_hospitalOrgId'));
        $hospitalBrandId = intval(Arr::get($publicParams, '_hospitalBrandId'));
        $hospitalId      = intval(Arr::get($publicParams, '_hospitalId'));
        $userId          = intval(Arr::get($publicParams, '_userId'));
        if (empty($userId) || empty($hospitalOrgId) || empty($hospitalBrandId) || empty($hospitalId))
        {
            return self::Fail('公共参数，缺少必选参数', 400);
        }

        // 业务参数
        $sourceType      = intval(Arr::get($dewormingParams, 'sourceType', 1));
        $dewormingDate   = trim(Arr::get($dewormingParams, 'dewormingDate', '')) ?: null;
        $dewormingRemark = trim(Arr::get($dewormingParams, 'dewormingRemark', ''));
        if (empty($sourceType) || empty(PetPreventDewormingSourceTypeEnum::exists($sourceType)))
        {
            return self::Fail('sourceType，宠物驱虫来源参数错误', 400);
        }
        if (!empty($dewormingDate) && strtotime($dewormingDate) === false)
        {
            return self::Fail('preventTime，宠物驱虫时间参数错误', 400);
        }
        if (!empty($dewormingRemark) && mb_strlen($dewormingRemark) > 200)
        {
            return self::Fail('preventRemark，宠物驱虫备注参数错误', 400);
        }

        // 获取宠物信息
        $getPetRes = self::GetValidPetByIdOrUid($petId);
        if ($getPetRes->isFail())
        {
            return $getPetRes;
        }

        // 获取用户信息
        $memberId     = $getPetRes->getData('member_id', 0);
        $getMemberRes = MemberLogic::GetValidMemberByIdOrUid($memberId);
        if ($getMemberRes->isFail())
        {
            return $getMemberRes;
        }

        $memberUniqueId = $getMemberRes->getData('unique_id', '');

        try
        {
            // 开始更新宠物驱虫状态
            DB::beginTransaction();

            $insertPetDewormingRecordData = [
                'org_id'      => $hospitalOrgId,
                'brand_id'    => $hospitalBrandId,
                'hospital_id' => $hospitalId,
                'unique_id'   => $memberUniqueId,
                'member_id'   => $memberId,
                'pet_id'      => $petId,
                'date'        => $dewormingDate,
                'remark'      => $dewormingRemark,
                'source_type' => $sourceType,
                'created_by'  => $userId,
            ];
            PetDewormingRecordModel::insertOne($insertPetDewormingRecordData);

            // 更新宠物最新驱虫状态
            MemberPetsModel::updateOne($petId,
                                       [
                                           'deworming_status' => PetDewormingStatusEnum::Deworming->value,
                                           'deworming_date'   => $dewormingDate
                                       ]);

            DB::commit();

            return self::Success();
        } catch (Throwable $throwable)
        {
            DB::rollBack();

            Log::error(__CLASS__ . '::' . __METHOD__ . ' 添加宠物驱虫记录异常', [
                'code'    => $throwable->getCode(),
                'message' => $throwable->getMessage(),
                'file'    => $throwable->getFile(),
                'line'    => $throwable->getLine(),
                'trace'   => $throwable->getTraceAsString(),
            ]);
        }

        return self::Fail('添加宠物驱虫记录异常', 32004);
    }

    /**
     * 获取宠物免疫驱虫记录
     *
     * @param int $petId
     *
     * @return LogicResult
     */
    public static function GetPetPreventList(int $petId): LogicResult
    {
        if (empty($petId))
        {
            return self::Fail('petId，缺少必选参数', 400);
        }

        // 获取免疫信息
        $getPetPreventListRes = PetPreventRecordModel::getData(where   : ['pet_id' => $petId, 'status' => 1],
                                                               orderBys: ['date' => 'desc']);
        if (empty($getPetPreventListRes))
        {
            return self::Success(['data' => []]);
        }

        // 免疫医院信息
        $getHospitalRes = [];
        $hospitalIds    = array_unique(array_filter(array_column($getPetPreventListRes, 'hospital_id')));
        if (!empty($hospitalIds))
        {
            $getHospitalRes = HospitalModel::getData(whereIn: ['id' => $hospitalIds], keyBy: 'id');
        }

        // 获取宠物信息
        $getPetRes = MemberPetsModel::getOne($petId);
        if (empty($getPetRes))
        {
            return self::Success(['data' => []]);
        }

        $returnPetPreventList = [];
        foreach ($getPetPreventListRes as $curInfo)
        {
            $returnPetPreventList[] = [
                'date'         => $curInfo['date'],
                'remark'       => $curInfo['remark'],
                'itemName'     => '', // TODO 免疫商品名称
                'sourceName'   => PetPreventDewormingSourceTypeEnum::getDescription($curInfo['source_type']),
                'petInfo'      => [
                    'uid' => $getPetRes['uid'],
                ],
                'hospitalInfo' => [
                    'name' => $getHospitalRes[$curInfo['hospital_id']]['alias_name'] ?? '',
                ],
            ];
        }

        return self::Success(['data' => $returnPetPreventList]);
    }

    /**
     * 获取宠物免疫驱虫记录
     *
     * @param int $petId
     *
     * @return LogicResult
     */
    public static function GetPetDewormingList(int $petId): LogicResult
    {
        if (empty($petId))
        {
            return self::Fail('petId，缺少必选参数', 400);
        }

        // 获取驱虫信息
        $getPetDewormingListRes = PetDewormingRecordModel::getData(where   : ['pet_id' => $petId, 'status' => 1],
                                                                   orderBys: ['date' => 'desc']);
        if (empty($getPetDewormingListRes))
        {
            return self::Success(['data' => []]);
        }

        // 免疫医院信息
        $getHospitalRes = [];
        $hospitalIds    = array_unique(array_filter(array_column($getPetDewormingListRes, 'hospital_id')));
        if (!empty($hospitalIds))
        {
            $getHospitalRes = HospitalModel::getData(whereIn: ['id' => $hospitalIds], keyBy: 'id');
        }

        // 获取宠物信息
        $getPetRes = MemberPetsModel::getOne($petId);
        if (empty($getPetRes))
        {
            return self::Success(['data' => []]);
        }

        $returnPetDewormingList = [];
        foreach ($getPetDewormingListRes as $curInfo)
        {
            $returnPetDewormingList[] = [
                'date'         => $curInfo['date'],
                'remark'       => $curInfo['remark'],
                'itemName'     => '', // TODO 免疫商品名称
                'sourceName'   => PetPreventDewormingSourceTypeEnum::getDescription($curInfo['source_type']),
                'petInfo'      => [
                    'uid' => $getPetRes['uid'],
                ],
                'hospitalInfo' => [
                    'name' => $getHospitalRes[$curInfo['hospital_id']]['alias_name'] ?? '',
                ],
            ];
        }

        return self::Success(['data' => $returnPetDewormingList]);
    }

    /**
     * 获取宠物体重记录
     *
     * @param int   $petId
     * @param array $publicParams
     *
     * @return LogicResult
     */
    public static function GetPetWeightList(int $petId, array $publicParams): LogicResult
    {
        if (empty($petId))
        {
            return self::Fail('获取宠物体重记录，缺少宠物ID必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('获取宠物体重记录，缺少公共必选参数', 400);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        if (empty($hospitalId))
        {
            return self::Fail('获取宠物体重记录，缺少医院ID必选参数', 400);
        }

        // 获取宠物信息
        $getPetRes = self::GetValidPetByIdOrUid($petId);
        if ($getPetRes->isFail())
        {
            return $getPetRes;
        }

        // 获取宠物体重记录。最多获取15条
        $getPetWeightListRes = PetWeightRecordModel::getData(where    : ['pet_id' => $petId],
                                                             orderBys : ['created_at' => 'desc'],
                                                             pageIndex: PageEnum::DefaultPageIndex->value,
                                                             pageSize : PageEnum::DefaultPageSize->value);
        if (empty($getPetWeightListRes))
        {
            return self::Success(['data' => []]);
        }

        $returnPetWeightList = [];
        foreach ($getPetWeightListRes as $curInfo)
        {
            $tmpPetWeightInfo = [
                'weight'     => $curInfo['weight'],
                'oldWeight'  => $curInfo['old_weight'],
                'sourceInfo' => [
                    'id'   => $curInfo['relation_type'],
                    'name' => PetWeightSourceTypeEnum::getDescription($curInfo['relation_type']),
                ],
                'createdAt'  => formatDisplayDateTime($curInfo['created_at']),
            ];

            // 体重升序排序，前端生成曲线
            array_unshift($returnPetWeightList, $tmpPetWeightInfo);
        }

        return self::Success(['data' => $returnPetWeightList]);
    }

    /**
     * 新增、编辑宠物获取统一参数
     *
     * @param array $petParams
     *
     * @return LogicResult
     */
    private static function _validateAddPetParams(array $petParams): LogicResult
    {
        // @formatter:off
        $petName           = Arr::has($petParams, 'petName') ? trim(Arr::get($petParams, 'petName', '')) : null;
        $petGender         = Arr::has($petParams, 'petGender') ? intval(Arr::get($petParams, 'petGender', 0)) : null;
        $petCategoryId     = Arr::has($petParams, 'petCategoryId') ?  intval(Arr::get($petParams, 'petCategoryId', 0)) : null;
        $petBirthday       = Arr::has($petParams, 'petBirthday') ?  trim(Arr::get($petParams, 'petBirthday', '')) : null;
        $petBreedId        = Arr::has($petParams, 'petBreedId') ?  intval(Arr::get($petParams, 'petBreedId', 0)) : null;
        $petBreedDesc      = Arr::has($petParams, 'petBreedDesc') ?  trim(Arr::get($petParams, 'petBreedDesc', '')) : null;
        $petWeight         = Arr::has($petParams, 'petWeight') ?  (string)Arr::get($petParams, 'petWeight', 0) : null;
        $petColorId        = Arr::has($petParams, 'petColorId') ?  intval(Arr::get($petParams, 'petColorId', 0)) : null;
        $petColorDesc      = Arr::has($petParams, 'petColorDesc') ?  trim(Arr::get($petParams, 'petColorDesc', '')) : null;
        $petSterileStatus  = Arr::has($petParams, 'petSterileStatus') ?  intval(Arr::get($petParams, 'petSterileStatus', 0)) : null;
        $petSterileDate    = Arr::has($petParams, 'petSterileDate') ?  trim(Arr::get($petParams, 'petSterileDate')) : null;
        $petPreventStatus  = Arr::has($petParams, 'petPreventStatus') ?  intval(Arr::get($petParams, 'petPreventStatus', 0)) : null;
        $petLiveStatus     = Arr::has($petParams, 'petLiveStatus') ?  intval(Arr::get($petParams, 'petLiveStatus', 0)) : null;
        $petRecordNumber   = Arr::has($petParams, 'petRecordNumber') ?  trim(Arr::get($petParams, 'petRecordNumber', '')) : null;
        $petIrritateRemark = Arr::has($petParams, 'petIrritateRemark') ?  trim(Arr::get($petParams, 'petIrritateRemark', '')) : null;
        $remark            = Arr::has($petParams, 'remark') ?  trim(Arr::get($petParams, 'remark', '')) : null;
        // @formatter:on

        // 宠物名称
        if ($petName !== null && empty($petName))
        {
            return self::Fail('宠物昵称填写错误', 32001);
        }

        // 宠物性别
        if ($petGender !== null && (empty($petGender) || empty(PetGenderEnum::exists($petGender))))
        {
            return self::Fail('宠物性别选择错误', 32001);
        }

        // 验证宠物种类
        if ($petCategoryId !== null)
        {
            $getPetCategoryRes = PetCategoryDictModel::getOne($petCategoryId);
            if (empty($petCategoryId) || empty($getPetCategoryRes))
            {
                return self::Fail('宠物分类选择错误', 32001);
            }

            // 如果宠物种类不是其它，那么必须选择品种。如果为其它，品种可不选。但是需要填写
            if (empty($getPetCategoryRes->is_other))
            {
                $getPetBreedRes = PetBreedDictModel::getOne($petBreedId);
                if (empty($petBreedId) || empty($getPetBreedRes))
                {
                    return self::Fail('宠物品种选择错误', 32001);
                }
                if ($getPetBreedRes['pet_category_id'] != $getPetCategoryRes['id'])
                {
                    return self::Fail('选择的宠物品种与种类不一致', 32001);
                }

                $petBreedDesc = $getPetBreedRes['name'];
            }
            else
            {
                if ($petBreedId > 0)
                {
                    return self::Fail('宠物种类为其它则品种ID不可选', 32001);
                }
                if (empty($petBreedDesc))
                {
                    return self::Fail('宠物种类为其它则品种必须填写', 32001);
                }
            }
        }

        // 宠物毛色
        if ($petColorId !== null)
        {
            $getPetColorRes = PetColorDictModel::getOne($petColorId);
            if (empty($getPetColorRes))
            {
                return self::Fail('宠物毛色选择错误', 32001);
            }

            // 毛色为选择的情况下自动使用毛色名称，如果为其它，则必须填写毛色
            if (empty($getPetColorRes->is_other))
            {
                $petColorDesc = $getPetColorRes['name'];
            }
            else
            {
                if (empty($petColorDesc))
                {
                    return self::Fail('宠物毛色参数填写错误', 32001);
                }
            }
        }

        if ($petBirthday !== null && ($petBirthday != '' && strtotime($petBirthday) === false))
        {
            return self::Fail('宠物生日选择错误', 32001);
        }
        if ($petWeight !== null && ($petWeight < 0 || $petWeight > self::MAX_WEIGHT))
        {
            return self::Fail('宠物体重填写错误。0-100/kg区间', 32001);
        }
        if ($petSterileStatus !== null && (empty($petSterileStatus) || empty(PetSterileStatusEnum::exists($petSterileStatus))))
        {
            return self::Fail('宠物绝育状态选择错误', 32001);
        }
        if ($petLiveStatus !== null && (empty($petLiveStatus) || empty(PetLiveStatusDictModel::getOne($petLiveStatus))))
        {
            return self::Fail('宠物生存状态选择错误', 32001);
        }
        if (!empty($petIrritateRemark) && mb_strlen($petIrritateRemark) > 200)
        {
            return self::Fail('宠物过敏备注参数错误', 32001);
        }
        if (!empty($remark) && mb_strlen($remark) > 200)
        {
            return self::Fail('宠物备注参数错误', 32001);
        }

        // 宠物最大年龄是否超限
        if ($petBirthday != '0000-00-00' && strtotime($petBirthday) !== false)
        {
            $petBirthdayLimit = date('Y') - date('Y', strtotime($petBirthday));
            if ($petBirthdayLimit > self::MAX_BIRTHDAY_AGE)
            {
                return self::Fail('宠物年龄有误，请检查宠物年龄。0-30区间', 32001);
            }
        }

        // 返回验证通过的参数
        return self::Success([
                                 'name'            => $petName !== null ? $petName : '',
                                 'gender'          => $petGender !== null ? $petGender : 0,
                                 'birthday'        => ($petBirthday !== null && $petBirthday != '') ? $petBirthday : '0000-00-00',
                                 'weight'          => $petBirthday !== null ? sprintf("%.2f", $petWeight) : 0,
                                 'category_id'     => $petCategoryId !== null ? $petCategoryId : 0,
                                 'breed_id'        => $petBreedId !== null ? $petBreedId : 0,
                                 'breed_desc'      => $petBreedDesc !== null ? $petBreedDesc : '',
                                 'color_id'        => $petColorId !== null ? $petColorId : 0,
                                 'color_desc'      => $petColorDesc !== null ? $petColorDesc : '',
                                 'sterile_status'  => $petSterileStatus !== null ? $petSterileStatus : 0,
                                 'sterile_date'    => ($petSterileDate !== null && $petSterileDate != '') ? $petSterileDate : '0000-00-00',
                                 'prevent_status'  => $petPreventStatus !== null ? $petPreventStatus : 0,
                                 'live_status'     => $petLiveStatus !== null ? $petLiveStatus : 0,
                                 'record_number'   => $petRecordNumber !== null ? $petRecordNumber : '',
                                 'irritate_remark' => $petIrritateRemark !== null ? $petIrritateRemark : '',
                                 'remark'          => $remark !== null ? $remark : '',
                             ]);
    }
}
