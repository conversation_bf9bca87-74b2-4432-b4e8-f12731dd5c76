<?php

namespace App\Logics\V1;

use DB;
use Log;
use Arr;
use DateTime;
use Throwable;
use DateMalformedStringException;
use App\Support\Stock\AppointmentHelper;
use App\Enums\PageEnum;
use App\Enums\AppointmentEnum;
use App\Enums\BusinessCodePrefixEnum;
use App\Logics\Logic;
use App\Logics\LogicResult;
use App\Models\UsersModel;
use App\Models\AppointmentModel;
use App\Models\AppointmentPetModel;
use App\Models\AppointmentTypeModel;
use App\Models\AppointmentReasonModel;

class AppointmentLogic extends Logic
{
    /**
     * 获取预约基础数据
     *
     * @param array $publicParams 公共参数
     *
     * @return LogicResult
     */
    public static function GetAddAppointmentOptions(array $publicParams): LogicResult
    {
        // 参数验证
        if (empty($publicParams))
        {
            return self::Fail('缺少必选参数', 400);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        if (empty($hospitalId))
        {
            return self::Fail('缺少医院ID', 400);
        }

        // 获取预约类型
        $getTypeRes = AppointmentTypeModel::getData(where: ['status' => 1]);
        if (empty($getTypeRes))
        {
            return self::Success();
        }

        // 获取预约原因
        $getReasonListRes = AppointmentReasonModel::getData(where: ['status' => 1]);
        if (empty($getReasonListRes))
        {
            return self::Success();
        }

        $returnTypeReasons = [];
        foreach ($getTypeRes as $curType)
        {
            $curChildren = [];
            foreach ($getReasonListRes as $curReason)
            {
                if ($curType['id'] != $curReason['appointment_type_id'])
                {
                    continue;
                }
                $curChildren[] = [
                    'id'   => $curReason['id'],
                    'name' => $curReason['name'],
                ];
            }

            $returnTypeReasons[] = [
                'id'       => $curType['id'],
                'name'     => $curType['name'],
                'children' => $curChildren,
            ];
        }

        $returnData = [
            'appointmentTypeCategoryOptions' => $returnTypeReasons,
            'startTime'                      => AppointmentEnum::StartHour->value,
            'endTime'                        => AppointmentEnum::EndHour->value,
            'timeStep'                       => AppointmentEnum::IntervalMinutes->value,
        ];

        return self::Success($returnData);
    }

    /**
     * 获取预约列表筛选项
     *
     * @param array $publicParams
     *
     * @return LogicResult
     */
    public static function GetFilterOptions(array $publicParams): LogicResult
    {
        if (empty($publicParams))
        {
            return self::Fail('获取筛选项，缺少必选参数', 400);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        if (empty($hospitalId))
        {
            return self::Fail('获取筛选项，缺少医院ID必选参数', 400);
        }

        // 获取预约类型
        $getAppointmentTypeRes = AppointmentTypeModel::getData(where: ['status' => 1]);

        // 获取存在的预约医生/美容师
        $getExecRes        = [];
        $getAppointmentRes = AppointmentModel::getData(where: [['exec_id', '!=', 0], ['hospital_id', '=', $hospitalId], ['status', '=', 1]]);
        if (!empty($getAppointmentRes))
        {
            $execIds    = array_unique(array_column($getAppointmentRes, 'exec_id'));
            $getExecRes = UsersModel::getManyByIds($execIds);
        }

        // 格式化返回数据
        $returnTypeOptions = [];
        $returnExecOptions = [];
        foreach ($getAppointmentTypeRes as $curType)
        {
            $returnTypeOptions[] = [
                'id'   => $curType['id'],
                'name' => $curType['name'],
            ];
        }
        foreach ($getExecRes as $curExec)
        {
            $returnExecOptions[] = [
                'uid'  => $curExec['uid'],
                'name' => $curExec['name'],
            ];
        }

        return self::Success([
                                 'typeOptions' => $returnTypeOptions,
                                 'execOptions' => $returnExecOptions,
                             ]);
    }

    /**
     * 获取预约列表
     *
     * @param array $searchParams
     * @param array $publicParams
     *
     * @return LogicResult
     */
    public static function GetAppointmentList(array $searchParams, array $publicParams): LogicResult
    {
        if (empty($publicParams))
        {
            return self::Fail('获取预约列表，缺少公共必选参数', 400);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        if (empty($hospitalId))
        {
            return self::Fail('获取预约列表，缺少医院ID必选参数', 400);
        }

        // 业务参数
        $startDate = trimWhitespace(Arr::get($searchParams, 'startDate', ''));
        $endDate   = trimWhitespace(Arr::get($searchParams, 'endDate', ''));
        $iPage     = intval(Arr::get($searchParams, 'page', 0)) ?? PageEnum::DefaultPageIndex->value;
        $iPageSize = intval(Arr::get($searchParams, 'count', 0)) ?? PageEnum::DefaultPageSize->value;
        if (!empty($startDate) && !checkDateIsValid($startDate))
        {
            return self::Fail('获取预约列表，开始日期格式错误', 34001);
        }
        if (!empty($endDate) && !checkDateIsValid($endDate))
        {
            return self::Fail('获取预约列表，结束日期格式错误', 34001);
        }
        if (!empty($startDate) && !empty($endDate) && strtotime($startDate) > strtotime($endDate))
        {
            return self::Fail('获取预约列表，开始日期不能大于结束日期', 34001);
        }

        // 获取预约列表
        $searchParams     = array_merge($searchParams, $publicParams);
        $getRefundListRes = AppointmentModel::getAppointmentListData($searchParams, $iPage, $iPageSize);
        if (empty($getRefundListRes))
        {
            return self::Success(['total' => 0, 'data' => []]);
        }

        $totalCount         = $getRefundListRes['total'] ?? 0;
        $appointmentListRes = $getRefundListRes['data'] ?? [];
        if (empty($totalCount) || empty($appointmentListRes))
        {
            return self::Success(['total' => 0, 'data' => []]);
        }

        $getFormatAppointmentRes = self::FormatAppointmentStructure($appointmentListRes);

        return self::Success(['total' => $totalCount, 'data' => $getFormatAppointmentRes->getData()]);
    }

    /**
     * 获取预约统计数据
     *
     * @param string $date         前端传递的日期 (Y-m-d 格式)
     * @param array  $publicParams 公共参数
     *
     * @return LogicResult
     * @throws DateMalformedStringException
     */
    public static function GetAppointmentStatistics(string $date, array $publicParams): LogicResult
    {
        if (empty($date))
        {
            return self::Fail('获取预约统计数据，缺少日期必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('获取预约统计数据，缺少公共参数', 400);
        }

        // 验证日期格式
        if (!checkDateIsValid($date))
        {
            return self::Fail('统计日期格式错误，请使用Y-m-d格式', 34001);
        }

        // 公共参数验证
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        if (empty($hospitalId))
        {
            return self::Fail('获取预约统计数据，缺少医院ID必选参数', 34001);
        }


        // 计算统计时间范围
        $inputDate = new DateTime($date);

        // 获取当月第一天
        $monthStart = new DateTime($inputDate->format('Y-m-01'));

        // 获取当月最后一天
        $monthEnd = clone $monthStart;
        $monthEnd->modify('last day of this month');

        // 前后各扩展7天
        $startDate = clone $monthStart;
        $startDate->modify('-7 days');

        $endDate = clone $monthEnd;
        $endDate->modify('+7 days');

        // 格式化为字符串
        $appointmentStartDate = $startDate->format('Y-m-d');
        $appointmentEndDate   = $endDate->format('Y-m-d');
        $statisticsData       = AppointmentModel::getAppointmentStatistics($hospitalId, $appointmentStartDate, $appointmentEndDate);

        // 生成完整的日期范围（包含没有预约的日期）
        $fullDateRange = [];
        $currentDate   = clone $startDate;
        while ($currentDate <= $endDate)
        {
            $dateKey                 = $currentDate->format('Y-m-d');
            $fullDateRange[$dateKey] = [
                'date'  => $dateKey,
                'count' => 0,
            ];
            $currentDate->modify('+1 day');
        }

        // 合并实际统计数据
        foreach ($statisticsData as $item)
        {
            $dateKey = $item['appointment_date'];
            if (isset($fullDateRange[$dateKey]))
            {
                $fullDateRange[$dateKey]['count'] = intval($item['appointment_count']);
            }
        }

        // 转换为数组并按日期排序
        $result = array_values($fullDateRange);

        return self::Success([
                                 'month'      => $inputDate->format('Y-m'),
                                 'statistics' => $result
                             ]);
    }

    /**
     * 获取有效预约
     *
     * @param int   $appointmentId
     * @param array $publicParams
     *
     * @return LogicResult
     */
    public static function GetValidAppointment(int $appointmentId, array $publicParams): LogicResult
    {
        if (empty($appointmentId))
        {
            return self::Fail('获取有效预约，缺少预约ID必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('获取有效预约，缺少公共必选参数', 400);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        if (empty($hospitalId))
        {
            return self::Fail('获取有效预约，缺少医院ID必选参数', 400);
        }

        // 获取预约信息
        $getAppointmentRes = AppointmentModel::getData(where: ['id' => $appointmentId, 'hospital_id' => $hospitalId]);
        $getAppointmentRes = Arr::first($getAppointmentRes);
        if (empty($getAppointmentRes))
        {
            return self::Fail('预约不存在或已失效', 34000);
        }

        return self::Success($getAppointmentRes);
    }

    /**
     * 获取预约详情
     *
     * @param int   $appointmentId
     * @param array $publicParams
     *
     * @return LogicResult
     */
    public static function GetAppointmentDetail(int $appointmentId, array $publicParams): LogicResult
    {
        if (empty($appointmentId))
        {
            return self::Fail('获取预约详情，缺少预约ID必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('获取预约详情，缺少公共必选参数', 400);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        if (empty($hospitalId))
        {
            return self::Fail('获取预约详情，缺少医院ID必选参数', 400);
        }

        // 获取预约信息
        $getAppointmentRes = self::GetValidAppointment($appointmentId, $publicParams);
        if ($getAppointmentRes->isFail())
        {
            return $getAppointmentRes;
        }
        $getAppointmentRes = $getAppointmentRes->getData();

        $getFormattedAppointmentRes = self::FormatAppointmentStructure([$getAppointmentRes]);
        if ($getFormattedAppointmentRes->isFail())
        {
            return $getFormattedAppointmentRes;
        }

        $getFormattedAppointmentRes = Arr::first($getFormattedAppointmentRes->getData());

        return self::Success($getFormattedAppointmentRes);
    }

    /**
     * 添加预约
     *
     * @param array $addAppointmentParams
     * @param array $publicParams
     *
     * @return LogicResult
     * @throws Throwable
     */
    public static function AddAppointment(array $addAppointmentParams, array $publicParams): LogicResult
    {
        if (empty($addAppointmentParams))
        {
            return self::Fail('添加预约，缺少必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('添加预约，缺少公共必选参数', 400);
        }

        // 公共参数
        $hospitalOrgId   = intval(Arr::get($publicParams, '_hospitalOrgId'));
        $hospitalBrandId = intval(Arr::get($publicParams, '_hospitalBrandId'));
        $hospitalId      = intval(Arr::get($publicParams, '_hospitalId'));
        $userId          = intval(Arr::get($publicParams, '_userId'));
        if (empty($hospitalOrgId) || empty($hospitalBrandId) || empty($hospitalId) || empty($userId))
        {
            return self::Fail('添加预约，公共必选参数错误', 400);
        }

        // 验证参数
        $getValidatedParamsRes = AppointmentHelper::GetValidatedAppointmentParams($addAppointmentParams, $publicParams);
        if ($getValidatedParamsRes->isFail())
        {
            return $getValidatedParamsRes;
        }

        // 验证后的业务参数
        $appointmentData = $getValidatedParamsRes->getData();

        // 预约宠物
        $appointmentPetIds = $appointmentData['petIds'];
        unset($appointmentData['petIds']);

        try
        {
            DB::beginTransaction();

            // 添加预约
            $appointmentCode = generateBusinessCodeNumber(BusinessCodePrefixEnum::YYDH);
            $appointmentData = array_merge([
                                               'hospital_id'      => $hospitalId,
                                               'brand_id'         => $hospitalBrandId,
                                               'org_id'           => $hospitalOrgId,
                                               'appointment_code' => $appointmentCode,
                                               'created_by'       => $userId
                                           ],
                                           $appointmentData);
            $appointmentId   = AppointmentModel::insertOne($appointmentData);

            // 预约宠物
            if (!empty($appointmentPetIds))
            {
                $insertAppointmentPetData = [];
                foreach ($appointmentPetIds as $curPetId)
                {
                    if (empty($curPetId))
                    {
                        continue;
                    }

                    $insertAppointmentPetData[] = [
                        'appointment_id'   => $appointmentId,
                        'appointment_code' => $appointmentCode,
                        'member_id'        => $appointmentData['member_id'],
                        'pet_id'           => $curPetId,
                        'created_by'       => $userId,
                    ];
                }
                AppointmentPetModel::insert($insertAppointmentPetData);
            }

            DB::commit();

            return self::Success();
        } catch (Throwable $throwable)
        {
            DB::rollBack();

            Log::error(__CLASS__ . '::' . __METHOD__ . ' 添加预约异常', [
                'code'    => $throwable->getCode(),
                'message' => $throwable->getMessage(),
                'file'    => $throwable->getFile(),
                'line'    => $throwable->getLine(),
                'trace'   => $throwable->getTraceAsString(),
            ]);

            return self::Fail('添加预约异常', 34002);
        }
    }

    /**
     * 编辑预约
     *
     * @param int   $appointmentId
     * @param array $editAppointmentParams
     * @param array $publicParams
     *
     * @return LogicResult
     * @throws Throwable
     */
    public static function EditAppointment(int $appointmentId, array $editAppointmentParams, array $publicParams): LogicResult
    {
        if (empty($appointmentId))
        {
            return self::Fail('编辑预约，缺少预约ID必选参数', 400);
        }
        if (empty($editAppointmentParams))
        {
            return self::Fail('编辑预约，缺少预约必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('编辑预约，缺少公共必选参数', 400);
        }

        // 公共参数
        $userId = intval(Arr::get($publicParams, '_userId'));
        if (empty($userId))
        {
            return self::Fail('编辑预约，缺少公共必选参数', 400);
        }

        // 获取预约信息
        $getAppointmentRes = self::GetValidAppointment($appointmentId, $publicParams);
        if ($getAppointmentRes->isFail())
        {
            return $getAppointmentRes;
        }
        $getAppointmentRes = $getAppointmentRes->getData();

        // 获取预约宠物信息
        $getAppointmentPetRes = AppointmentPetModel::getData(where: ['appointment_id' => $appointmentId, 'status' => 1]);
        $appointmentOldPetIds = array_column($getAppointmentPetRes, 'pet_id');

        // 验证预约是否可以编辑
        $checkEditableRes = self::CheckEditOrDeleteAbleAppointment($getAppointmentRes);
        if ($checkEditableRes->isFail())
        {
            return $checkEditableRes;
        }

        // 验证参数
        $getValidatedParamsRes = AppointmentHelper::GetValidatedAppointmentParams($editAppointmentParams, $publicParams);
        if ($getValidatedParamsRes->isFail())
        {
            return $getValidatedParamsRes;
        }

        // 验证后的业务参数
        $updateAppointmentData = $getValidatedParamsRes->getData();

        // 预约宠物
        $updateAppointmentPetIds = $updateAppointmentData['petIds'];
        $deletePetIds            = array_diff($appointmentOldPetIds, $updateAppointmentPetIds);
        $insertPetIds            = array_diff($updateAppointmentPetIds, $appointmentOldPetIds);
        unset($updateAppointmentData['petIds']);

        try
        {
            DB::beginTransaction();

            $updateAppointmentData['updated_by'] = $userId;
            AppointmentModel::updateOne($appointmentId, $updateAppointmentData);

            // 删除宠物
            if (!empty($deletePetIds))
            {
                AppointmentPetModel::on()
                                   ->where(['appointment_id' => $appointmentId])
                                   ->whereIn('pet_id', $deletePetIds)
                                   ->update(['status' => 0, 'updated_by' => $userId]);
            }

            // 添加宠物
            if (!empty($insertPetIds))
            {
                $insertAppointmentPetData = [];
                foreach ($insertPetIds as $curPetId)
                {
                    if (empty($curPetId))
                    {
                        continue;
                    }

                    $insertAppointmentPetData[] = [
                        'appointment_id'   => $appointmentId,
                        'appointment_code' => $getAppointmentRes['appointment_code'],
                        'member_id'        => $updateAppointmentData['member_id'],
                        'pet_id'           => $curPetId,
                        'created_by'       => $userId,
                    ];
                }
                AppointmentPetModel::insert($insertAppointmentPetData);
            }

            DB::commit();

            return self::Success();
        } catch (Throwable $throwable)
        {
            DB::rollBack();

            Log::error(__CLASS__ . '::' . __METHOD__ . ' 编辑预约异常', [
                'appointmentId' => $appointmentId,
                'code'          => $throwable->getCode(),
                'message'       => $throwable->getMessage(),
                'file'          => $throwable->getFile(),
                'line'          => $throwable->getLine(),
                'trace'         => $throwable->getTraceAsString(),
            ]);

            return self::Fail('编辑预约异常', 34002);
        }
    }

    /**
     * 取消预约
     *
     * @param int    $appointmentId
     * @param string $cancelReason
     * @param array  $publicParams
     *
     * @return LogicResult
     */
    public static function CancelAppointment(int $appointmentId, string $cancelReason, array $publicParams): LogicResult
    {
        if (empty($appointmentId))
        {
            return self::Fail('取消预约，缺少预约ID必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('取消预约，缺少公共必选参数', 400);
        }

        // 公共参数
        $userId = intval(Arr::get($publicParams, '_userId'));
        if (empty($userId))
        {
            return self::Fail('取消预约，缺少用户ID必选参数', 400);
        }

        // 业务参数
        $cancelReason = trimWhitespace($cancelReason);
        if (empty($cancelReason))
        {
            return self::Fail('取消预约，缺少取消原因必选参数', 34002);
        }

        // 获取预约信息
        $getAppointmentRes = self::GetValidAppointment($appointmentId, $publicParams);
        if ($getAppointmentRes->isFail())
        {
            return $getAppointmentRes;
        }

        // 验证预约是否可以取消
        $checkEditableRes = self::CheckEditOrDeleteAbleAppointment($getAppointmentRes->getData());
        if ($checkEditableRes->isFail())
        {
            return $checkEditableRes;
        }

        AppointmentModel::updateOne($appointmentId,
                                    ['status' => 0, 'cancel_reason' => $cancelReason, 'cancel_by' => $userId, 'cancel_at' => getCurrentTimeWithMilliseconds()]);

        return self::Success();
    }

    /**
     * 检查预约是否可以编辑或删除
     *
     * @param array $appointmentInfo
     *
     * @return LogicResult
     */
    private static function CheckEditOrDeleteAbleAppointment(array $appointmentInfo): LogicResult
    {
        if (empty($appointmentInfo))
        {
            return self::Fail('验证预约是否可操作，缺少预约信息必选参数', 400);
        }
        if ($appointmentInfo['status'] != 1)
        {
            return self::Fail('预约已取消或无效，不可操作', 34003);
        }

        return self::Success();
    }

    /**
     * 格式化预约结构
     *
     * @param array $appointmentList
     *
     * @return LogicResult
     */
    private static function FormatAppointmentStructure(array $appointmentList): LogicResult
    {
        if (empty($appointmentList))
        {
            return self::Success();
        }

        // 预约类型
        $typeIds               = array_unique(array_filter(array_column($appointmentList, 'type_id')));
        $getAppointmentTypeRes = AppointmentTypeModel::getManyByIds($typeIds)
                                                     ->keyBy('id')
                                                     ->toArray();

        // 预约原因
        $reasonIds               = array_unique(array_filter(array_column($appointmentList, 'reason_id')));
        $getAppointmentReasonRes = AppointmentReasonModel::getManyByIds($reasonIds)
                                                         ->keyBy('id')
                                                         ->toArray();

        // 预约医生
        $doctorIds               = array_unique(array_filter(array_column($appointmentList, 'exec_id')));
        $getAppointmentDoctorRes = UsersModel::getManyByIds($doctorIds)
                                             ->keyBy('id')
                                             ->toArray();

        // 预约宠物信息
        $getPetInfoRes        = [];
        $appointmentIds       = array_column($appointmentList, 'id');
        $getAppointmentPetRes = AppointmentPetModel::getData(where: ['status' => 1], whereIn: ['appointment_id' => $appointmentIds]);
        if (!empty($getAppointmentPetRes))
        {
            $getPetInfoRes = PetLogic::GetPetBaseInfoByPetIds(array_column($getAppointmentPetRes, 'pet_id'));
            $getPetInfoRes = $getPetInfoRes->getData();
        }

        $returnAppointmentList = [];
        foreach ($appointmentList as $curAppointment)
        {
            // 预约单基本信息
            $curTypeInfo   = $getAppointmentTypeRes[$curAppointment['type_id']] ?? [];
            $curReasonInfo = $getAppointmentReasonRes[$curAppointment['reason_id']] ?? [];
            $curDoctorInfo = $getAppointmentDoctorRes[$curAppointment['exec_id']] ?? [];

            // 预约单宠物信息
            $curPetList = [];
            foreach ($getAppointmentPetRes as $curAppointmentPet)
            {
                if ($curAppointmentPet['appointment_id'] != $curAppointment['id'])
                {
                    continue;
                }

                $curPetInfo = $getPetInfoRes[$curAppointmentPet['pet_id']]['petInfo'] ?? [];
                if (!empty($curPetInfo))
                {
                    $curPetList[] = $curPetInfo;
                }
            }

            $tmpAppointmentInfo = [
                'appointmentCode' => $curAppointment['appointment_code'],
                'type'            => [
                    'id'   => $curTypeInfo['id'] ?? '',
                    'name' => $curTypeInfo['name'] ?? '',
                ],
                'reason'          => [
                    'id'   => $curReasonInfo['id'] ?? '',
                    'name' => $curReasonInfo['name'] ?? '',
                ],
                'doctor'          => [
                    'uid'  => $curDoctorInfo['uid'] ?? '',
                    'name' => $curDoctorInfo['name'] ?? '',
                ],
                'memberInfo'      => [
                    'name'  => $curAppointment['salutation'],
                    'phone' => $curAppointment['phone'],
                ],
                'petInfos'        => $curPetList,
                'date'            => formatDisplayDateTime($curAppointment['start_time'], 'Y-m-d'),
                'time'            => [
                    'start' => formatDisplayDateTime($curAppointment['start_time'], 'H:i'),
                    'end'   => formatDisplayDateTime($curAppointment['end_time'], 'H:i'),
                ],
                'remark'          => $curAppointment['remark'],
                'createTime'      => $curAppointment['created_at'],
            ];

            $returnAppointmentList[] = $tmpAppointmentInfo;
        }

        return self::Success($returnAppointmentList);
    }
}
