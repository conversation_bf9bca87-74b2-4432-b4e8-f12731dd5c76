<?php

namespace App\Logics\V1;

use Throwable;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Arr;
use App\Support\Item\ItemHelper;
use App\Support\Stock\StockRefundHelper;
use App\Support\Stock\StockQuantityConversionHelper;
use App\Logics\Logic;
use App\Logics\LogicResult;
use App\Enums\PageEnum;
use App\Enums\StockReduceTypeEnum;
use App\Enums\StockRefundTypeEnum;
use App\Enums\StockRefundStatusEnum;
use App\Enums\BusinessCodePrefixEnum;
use App\Enums\StockOutboundStatusEnum;
use App\Models\UsersModel;
use App\Models\StockRefundModel;
use App\Models\StockRefundItemModel;
use App\Models\StockRefundOperationLogModel;

class StockRefundLogic extends Logic
{
    /**
     * 获取退货单创建人选项
     *
     * @param array $publicParams
     *
     * @return LogicResult
     */
    public static function GetCreateUsersOptions(array $publicParams): LogicResult
    {
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        if (empty($hospitalId))
        {
            return self::Fail('获取退货单创建人选项，缺少医院ID必选参数', 400);
        }

        $userOptions = StockRefundModel::GetCreateUsersOptions($hospitalId);

        return self::Success($userOptions);
    }

    /**
     * 退货单列表
     *
     * @param array $searchParams
     * @param array $publicParams
     *
     * @return LogicResult
     */
    public static function GetRefundList(array $searchParams, array $publicParams): LogicResult
    {
        if (empty($publicParams))
        {
            return self::Fail('获取退货单列表，缺少公共必选参数', 400);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        if (empty($hospitalId))
        {
            return self::Fail('获取退货单列表，缺少公共必选参数', 400);
        }

        // 业务参数
        $iPage     = intval(Arr::get($searchParams, 'page', 0)) ?? PageEnum::DefaultPageIndex->value;
        $iPageSize = intval(Arr::get($searchParams, 'count', 0)) ?? PageEnum::DefaultPageSize->value;

        // 获取退货单列表
        $searchParams['hospitalId'] = $hospitalId;
        $getRefundListRes           = StockRefundModel::getStockRefundListData($searchParams, $iPage, $iPageSize);
        if (empty($getRefundListRes))
        {
            return self::Success(['total' => 0, 'data' => []]);
        }

        $totalCount    = $getRefundListRes['total'] ?? 0;
        $refundListRes = $getRefundListRes['data'] ?? [];
        if (empty($totalCount) || empty($refundListRes))
        {
            return self::Success(['total' => 0, 'data' => []]);
        }

        // 格式化退货单信息
        $getFormatRefundListRes = self::FormatRefundStructure($refundListRes);
        if ($getFormatRefundListRes->isFail())
        {
            return $getFormatRefundListRes;
        }

        return self::Success(['total' => $totalCount, 'data' => $getFormatRefundListRes->getData()]);
    }

    /**
     * 获取有效退货单
     *
     * @param int   $refundId
     * @param array $publicParams
     *
     * @return LogicResult
     */
    public static function GetValidStockRefund(int $refundId, array $publicParams): LogicResult
    {
        if (empty($refundId))
        {
            return self::Fail('获取有效退货单，缺少退货单ID必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('获取有效退货单，缺少公共必选参数', 400);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        if (empty($hospitalId))
        {
            return self::Fail('获取有效退货单，缺少医院ID必选参数', 400);
        }

        // 获取退货单信息
        $getRefundRes = StockRefundModel::getData(where: ['id' => $refundId, 'hospital_id' => $hospitalId]);
        $getRefundRes = $getRefundRes ? current($getRefundRes) : [];
        if (empty($getRefundRes))
        {
            return self::Fail('退货单不存在或已失效', 44000);
        }

        return self::Success($getRefundRes);
    }

    /**
     * 获取退货单详情
     *
     * @param int   $refundId
     * @param array $publicParams
     *
     * @return LogicResult
     */
    public static function GetRefundDetail(int $refundId, array $publicParams): LogicResult
    {
        if (empty($refundId))
        {
            return self::Fail('获取退货单详情，缺少退货单ID必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('获取退货单详情，缺少公共必选参数', 400);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        if (empty($hospitalId))
        {
            return self::Fail('获取退货单详情，缺少公共医院ID必选参数', 400);
        }

        // 获取退货单信息
        $getRefundRes = StockRefundModel::getStockRefundListData(['stockRefundId' => $refundId, 'hospitalId' => $hospitalId]);
        $getRefundRes = !empty($getRefundRes['data']) ? current($getRefundRes['data']) : [];
        if (empty($getRefundRes))
        {
            return self::Fail('退货单不存在', 44000);
        }

        // 获取退货单商品明细
        $getRefundItemRes = StockRefundItemModel::getData(where: ['stock_refund_id' => $refundId, 'status' => 1]);
        if (empty($getRefundItemRes))
        {
            return self::Fail('退货单商品不存在', 44001);
        }

        // 获取退货单商品信息
        $itemIds              = array_column($getRefundItemRes, 'item_id');
        $getRefundItemInfoRes = ItemLogic::GetItemFullInfo(itemIds: $itemIds, itemStatus: [], publicParams: $publicParams, withItemPrice: true, withItemStock: true);
        if ($getRefundItemInfoRes->isFail())
        {
            return $getRefundItemInfoRes;
        }

        // 格式化退货单信息
        $getFormatRefundRes = self::FormatRefundStructure([$getRefundRes]);
        if ($getFormatRefundRes->isFail())
        {
            return $getFormatRefundRes;
        }

        // 格式化退货单商品信息
        $getFormatRefundItemRes = self::FormatRefundItemStructure($getRefundItemRes, $getRefundItemInfoRes->getData());
        if ($getFormatRefundItemRes->isFail())
        {
            return $getFormatRefundItemRes;
        }

        $getFormatRefundRes          = current($getFormatRefundRes->getData());
        $getFormatRefundRes['items'] = $getFormatRefundItemRes->getData();

        return self::Success($getFormatRefundRes);
    }

    /**
     * 添加退货单
     *
     * @param       $addStockRefundParams
     * @param array $publicParams
     *
     * @return LogicResult
     * @throws Throwable
     */
    public static function AddStockRefund($addStockRefundParams, array $publicParams): LogicResult
    {
        if (empty($addStockRefundParams))
        {
            return self::Fail('添加退货单，缺少必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('添加退货单，缺少公共必选参数', 400);
        }

        // 公共参数
        $hospitalOrgId   = intval(Arr::get($publicParams, '_hospitalOrgId'));
        $hospitalBrandId = intval(Arr::get($publicParams, '_hospitalBrandId'));
        $hospitalId      = intval(Arr::get($publicParams, '_hospitalId'));
        $userId          = intval(Arr::get($publicParams, '_userId'));
        if (empty($hospitalOrgId) || empty($hospitalBrandId) || empty($hospitalId) || empty($userId))
        {
            return self::Fail('添加退货单，缺少医院相关必选参数', 400);
        }

        // 验证退货基本参数是否正确
        $getValidRefundParams = StockRefundHelper::GetValidRefundParams($addStockRefundParams, $publicParams);
        if ($getValidRefundParams->isFail())
        {
            return $getValidRefundParams;
        }

        // 获取验证后的退货参数
        $getValidRefundParams = $getValidRefundParams->getData();
        $refundItems          = $getValidRefundParams['items'];
        $refundRemark         = $getValidRefundParams['remark'];
        $refundTotalPrice     = $getValidRefundParams['refundTotalPrice'];
        $submitType           = $getValidRefundParams['submitType'];
        if (empty($refundItems) || empty($refundRemark))
        {
            return self::Fail('添加退货单，验证退货单信息异常', 44042);
        }

        try
        {
            DB::beginTransaction();

            // 创建退货单
            $refundCode       = generateBusinessCodeNumber(BusinessCodePrefixEnum::THCK);
            $insertRefundData = [
                'refund_code'  => $refundCode,
                'refund_type'  => StockRefundTypeEnum::Common->value,
                'org_id'       => $hospitalOrgId,
                'brand_id'     => $hospitalBrandId,
                'hospital_id'  => $hospitalId,
                'refund_price' => $refundTotalPrice,
                'status'       => $submitType,
                'remark'       => $refundRemark,
                'created_by'   => $userId,
            ];

            // 如果创建既提交审核记录提交审核人、提交审核时间
            if ($submitType == StockRefundStatusEnum::Pending->value)
            {
                $insertRefundData['submitted_by'] = $userId;
                $insertRefundData['submitted_at'] = getCurrentTimeWithMilliseconds();
            }

            $stockRefundId = StockRefundModel::insertOne($insertRefundData);

            // 添加退货单商品
            $getInsertRefundItemRes = self::InsertRefundItem(['refundId' => $stockRefundId, 'refundCode' => $refundCode], $refundItems, $publicParams);
            if ($getInsertRefundItemRes->isFail())
            {
                DB::rollBack();

                return $getInsertRefundItemRes;
            }

            DB::commit();

            return self::Success();
        } catch (Throwable $throwable)
        {
            DB::rollBack();

            Log::error(__CLASS__ . '::' . __METHOD__ . ' 添加退货单异常', [
                'code'    => $throwable->getCode(),
                'message' => $throwable->getMessage(),
                'file'    => $throwable->getFile(),
                'line'    => $throwable->getLine(),
                'trace'   => $throwable->getTraceAsString(),
            ]);

            return self::Fail('添加退货单异常', 44043);
        }
    }

    /**
     * 编辑退货单
     *
     * @param int   $refundId
     * @param array $editStockRefundParams
     * @param array $publicParams
     *
     * @return LogicResult
     * @throws Throwable
     */
    public static function EditStockRefund(int $refundId, array $editStockRefundParams, array $publicParams): LogicResult
    {
        if (empty($refundId))
        {
            return self::Fail('编辑退货单，缺少退货单ID必选参数', 400);
        }
        if (empty($editStockRefundParams))
        {
            return self::Fail('编辑退货单，缺少退货单必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('编辑退货单，缺少公共必选参数', 400);
        }

        // 公共参数
        $userId = intval(Arr::get($publicParams, '_userId'));
        if (empty($userId))
        {
            return self::Fail('编辑退货单，缺少用户ID必选参数', 400);
        }

        // 获取退货单信息
        $getRefundRes = self::GetValidStockRefund($refundId, $publicParams);
        if ($getRefundRes->isFail())
        {
            return $getRefundRes;
        }
        $getRefundRes = $getRefundRes->getData();

        // 获取退货单商品明细
        $getRefundItemRes = StockRefundItemModel::getData(where: ['stock_refund_id' => $refundId, 'status' => 1]);
        if (empty($getRefundItemRes))
        {
            return self::Fail('退货单商品不存在', 44001);
        }

        // 获取退货单商品信息，用于如果修改的话记录名称等
        $oldItemIds              = array_column($getRefundItemRes, 'item_id');
        $getOldRefundItemInfoRes = ItemLogic::GetItemFullInfo(itemIds: $oldItemIds, itemStatus: [], publicParams: $publicParams);
        if ($getOldRefundItemInfoRes->isFail())
        {
            return $getOldRefundItemInfoRes;
        }
        $getOldRefundItemInfoRes = array_column($getOldRefundItemInfoRes->getData(), null, 'id');

        // 验证退货单是否可以编辑
        $getCheckEditAbleRes = self::CheckEditOrDeleteAbleRefund($getRefundRes, $publicParams);
        if ($getCheckEditAbleRes->isFail())
        {
            return $getCheckEditAbleRes;
        }

        // 验证退货基本参数是否正确
        $getValidRefundParams = StockRefundHelper::GetValidRefundParams($editStockRefundParams, $publicParams, $getRefundItemRes);
        if ($getValidRefundParams->isFail())
        {
            return $getValidRefundParams;
        }

        // 获取验证后的退货参数
        $getValidRefundParams = $getValidRefundParams->getData();
        $refundItems          = $getValidRefundParams['items'];
        $refundRemark         = $getValidRefundParams['remark'];
        $refundTotalPrice     = $getValidRefundParams['refundTotalPrice'];
        $submitType           = $getValidRefundParams['submitType'];
        if (empty($refundItems) || empty($refundRemark))
        {
            return self::Fail('添加退货单，验证退货单信息异常', 44042);
        }

        // 退货单相关操作的日志说明
        $operationDesc = [];

        // 更新退货单
        $updateRefundData = [];
        if ($refundRemark != $getRefundRes['remark'])
        {
            $updateRefundData['remark'] = $refundRemark;
            $operationDesc[]            = "退货说明由：{$getRefundRes['remark']} 修改为：$refundRemark";
        }
        if (bccomp($refundTotalPrice, $getRefundRes['refund_price'], 4) !== 0)
        {
            $updateRefundData['refund_price'] = $refundTotalPrice;
            $operationDesc[]                  = "退货单金额由：{$getRefundRes['refund_price']} 修改为：$refundTotalPrice";
        }
        if ($submitType != $getRefundRes['status'])
        {
            $updateRefundData['status'] = $submitType;
            $operationDesc[]            = "退货单状态由：{$getRefundRes['status']} 修改为：$submitType";

            // 如果直接提交审核，修改提审状态
            if ($submitType == StockRefundStatusEnum::Pending->value)
            {
                $updateRefundData['submitted_by'] = $userId;
                $updateRefundData['submitted_at'] = getCurrentTimeWithMilliseconds();

                $operationDesc[] = "退货单提审核人由：{$getRefundRes['submitted_by']} 修改为：$userId";
            }
            else
            {
                $updateRefundData['submitted_by'] = 0;
                $updateRefundData['submitted_at'] = null;
            }
        }

        // 更新退货单商品
        $insertRefundItemData = [];
        $updateRefundItemData = [];
        $deleteRefundItemData = [];
        foreach ($getRefundItemRes as $oldKey => $oldRefundItem)
        {
            foreach ($refundItems as $newKey => $newRefundItem)
            {
                // 退货单商品在表中的UID
                $curRefundItemUid = $newRefundItem['uid'] ?? '';

                // 如果uid为空，则代表是新增的记录
                if (empty($curRefundItemUid))
                {
                    $insertRefundItemData[] = $newRefundItem;
                    unset($refundItems[$newKey]);
                    continue;
                }

                // 如果uid不一致，则代表不是同一个商品
                if ($oldRefundItem['uid'] != $newRefundItem['uid'])
                {
                    continue;
                }

                // 退货单商品的主信息
                $curRefundItemInfo = $newRefundItem['itemInfo'] ?? [];
                if (empty($curRefundItemInfo))
                {
                    return self::Fail('退货商品信息不存在', 44042);
                }

                // 如果是同一个商品，商品ID是否同一个，防止错误
                if ($oldRefundItem['item_id'] != $curRefundItemInfo['id'])
                {
                    return self::Fail('提交退货单商品与存在的商品ID不一致', 44042);
                }

                // 对比是否存在修改退货的信息：散装数量、散装价格、整装数量、整装价格
                $curDiffInfo = StockRefundHelper::CompareRefundItemChange($oldRefundItem, $newRefundItem);
                if (!empty($curDiffInfo))
                {
                    // 如果更新数据、更新数据日志不完整则说明错误
                    [$curUpData, $curUpLog] = $curDiffInfo;
                    if (!empty($curUpData) && !empty($curUpLog))
                    {
                        $updateRefundItemData[] = ['where' => ['id' => $oldRefundItem['id']], 'update' => $curUpData, 'log' => $curUpLog];
                    }
                }

                unset($refundItems[$newKey]);
                unset($getRefundItemRes[$oldKey]);
            }
        }

        // 新提交的退货单商品如果存在，则代表说明存在找不到的商品。1、提交新商品uid为空，属于新增，已经删除掉；2、如果是修改旧的商品，记录修改信息后会删除掉；3、剩余的不属于新增、也不属于修改的
        if (!empty($refundItems))
        {
            return self::Fail('退货单商品信息异常，请重试', 44042);
        }

        // 如果旧的退货单还存在商品，则需要删除掉
        if (!empty($getRefundItemRes))
        {
            $deleteRefundItemData = array_column($getRefundItemRes, null, 'id');
        }

        // 无任何更改
        if (empty($updateRefundData) && empty($insertRefundItemData) && empty($updateRefundItemData) && empty($deleteRefundItemData))
        {
            return self::Success();
        }

        try
        {
            DB::beginTransaction();

            // 更新退货单
            if (!empty($updateRefundData))
            {
                StockRefundModel::updateOne($refundId, $updateRefundData);
            }

            // 添加退货单商品
            if (!empty($insertRefundItemData))
            {
                $getInsertRefundItemRes = self::InsertRefundItem(['refundId' => $refundId, 'refundCode' => $getRefundRes['refund_code']],
                                                                 $insertRefundItemData,
                                                                 $publicParams);
                if ($getInsertRefundItemRes->isFail())
                {
                    DB::rollBack();

                    return $getInsertRefundItemRes;
                }
            }

            // 更新退货单商品
            foreach ($updateRefundItemData as $curUpdateData)
            {
                StockRefundItemModel::updateOne($curUpdateData['where']['id'], $curUpdateData['update']);

                // 添加日志
                $operationDesc = array_merge($operationDesc, [$curUpdateData['log']]);
            }

            // 删除退货单商品
            if (!empty($deleteRefundItemData))
            {
                StockRefundItemModel::on()
                                    ->whereIn('id', array_keys($deleteRefundItemData))
                                    ->update(['status' => 0]);

                // 添加日志
                array_walk($deleteRefundItemData, function ($item) use (&$operationDesc, $getOldRefundItemInfoRes) {
                    $delItemId     = $item['item_id'];
                    $delItemName   = $getOldRefundItemInfoRes[$delItemId]['item_display_name'] ?? '';
                    $operationDesc = array_merge($operationDesc, ["删除商品：【{$delItemId}】$delItemName"]);
                });
            }

            // 记录日志
            $insertOperationData = [
                'stock_refund_id'     => $refundId,
                'stock_refund_code'   => $getRefundRes['refund_code'],
                'operation_desc'      => json_encode($operationDesc, JSON_UNESCAPED_UNICODE),
                'operation_user_id'   => $userId,
                'operation_user_name' => UsersModel::getOneUserNameById($userId),
                'operation_channel'   => 1,
            ];
            StockRefundOperationLogModel::insert($insertOperationData);

            DB::commit();

            return self::Success();
        } catch (Throwable $throwable)
        {
            DB::rollBack();
            Log::error(__CLASS__ . '::' . __METHOD__ . ' 编辑退货单异常',
                       [
                           'code'    => $throwable->getCode(),
                           'message' => $throwable->getMessage(),
                           'file'    => $throwable->getFile(),
                           'line'    => $throwable->getLine(),
                           'trace'   => $throwable->getTraceAsString(),
                       ]);

            return self::Fail('编辑退货单异常', 44043);
        }
    }

    /**
     * 退货出库
     *
     * @param int   $refundId
     * @param array $publicParams
     *
     * @return LogicResult
     * @throws Throwable
     */
    public static function StockRefundOutbound(int $refundId, array $publicParams): LogicResult
    {
        if (empty($refundId))
        {
            return self::Fail('退货出库，缺少退货单ID必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('退货出库，缺少公共必选参数', 400);
        }

        // 公共参数
        $hospitalId      = intval(Arr::get($publicParams, '_hospitalId'));
        $hospitalBrandId = intval(Arr::get($publicParams, '_hospitalBrandId'));
        $hospitalOrgId   = intval(Arr::get($publicParams, '_hospitalOrgId'));
        $userId          = intval(Arr::get($publicParams, '_userId'));
        if (empty($hospitalId) || empty($hospitalBrandId) || empty($hospitalOrgId) || empty($userId))
        {
            return self::Fail('退货出库，缺少公共必选参数', 400);
        }

        // 获取退货单信息
        $getRefundRes = self::GetValidStockRefund($refundId, $publicParams);
        if ($getRefundRes->isFail())
        {
            return $getRefundRes;
        }
        $getRefundRes = $getRefundRes->getData();

        // 获取退货单商品明细
        $getRefundItemRes = StockRefundItemModel::getData(where: ['stock_refund_id' => $refundId, 'status' => 1]);
        if (empty($getRefundItemRes))
        {
            return self::Fail('退货单商品不存在', 44001);
        }


        // 出库信息
        $outboundItemData = [];
        foreach ($getRefundItemRes as $curRefundItem)
        {
            if (empty($curRefundItem['item_id']) || empty($curRefundItem['item_barcode']))
            {
                return self::Fail('退货出库，商品信息异常', 44001);
            }
            if ($curRefundItem['pack_quantity'] <= 0 && $curRefundItem['bulk_quantity'] <= 0)
            {
                return self::Fail("退货出库【{$curRefundItem['item_barcode']}】，商品数量异常", 44001);
            }

            // 是否已经全部出库完成
            $getRemainQuantityRes  = StockQuantityConversionHelper::getRemainPackAndBulkQuantity($curRefundItem['pack_quantity'],
                                                                                                 $curRefundItem['bulk_quantity'],
                                                                                                 $curRefundItem['outbound_pack_quantity'],
                                                                                                 $curRefundItem['outbound_bulk_quantity'],
                                                                                                 $curRefundItem['item_bulk_ratio']);
            $curRemainPackQuantity = $getRemainQuantityRes['remainPackQuantity'];
            $curRemainBulkQuantity = $getRemainQuantityRes['remainBulkQuantity'];
            if ($curRemainPackQuantity <= 0 && $curRemainBulkQuantity <= 0)
            {
                return self::Fail("退货出库【{$curRefundItem['item_barcode']}】，商品出库数量异常", 44001);
            }

            $outboundItemData[] = [
                'itemId'           => $curRefundItem['item_id'],
                'itemBarcode'      => $curRefundItem['item_barcode'],
                'packQuantity'     => $curRemainPackQuantity,
                'bulkQuantity'     => $curRemainBulkQuantity,
                'expiredDate'      => $curRefundItem['expired_date'],
                'relationCode'     => $getRefundRes['refund_code'],
                'relationId'       => $refundId,
                'relationDetailId' => $curRefundItem['id'],
                'reduceType'       => StockReduceTypeEnum::Return->value,
                'remark'           => StockReduceTypeEnum::getDescription(StockReduceTypeEnum::Return->value),
            ];
        }

        try
        {
            DB::beginTransaction();

            // 退货出库
            $getReduceStockRes = StockItemShelfReduceLogic::ReduceStockShelfQuantity($outboundItemData, $publicParams);
            if ($getReduceStockRes->isFail())
            {
                DB::rollBack();

                return $getReduceStockRes;
            }

            // 更新退货单商品明细
            $totalOutboundPrice = 0;
            foreach ($getRefundItemRes as $curRefundItem)
            {
                $curItemId             = $curRefundItem['item_id'];
                $curItemReduceStockRes = $getReduceStockRes->getData($curItemId, []);
                if (empty($curItemReduceStockRes))
                {
                    DB::rollBack();

                    return self::Fail("退货出库【{$curRefundItem['item_barcode']}】，商品出库异常", 45004);
                }

                // 更新出库数量。并且根据本次出库和历史处理数量计算是否出库完成
                $curOutboundTotalPackQuantity = numberAdd([$curRefundItem['outbound_pack_quantity'], $curItemReduceStockRes['reducePackQuantity']]);
                $curOutboundTotalBulkQuantity = numberAdd([$curRefundItem['outbound_bulk_quantity'], $curItemReduceStockRes['reduceBulkQuantity']]);
                $curOutboundBulkQuantity      = StockQuantityConversionHelper::convertToTotalBulkQuantity($curOutboundTotalPackQuantity,
                                                                                                          $curOutboundTotalBulkQuantity,
                                                                                                          $curRefundItem['item_bulk_ratio']);

                $curNeedOutboundQuantity = StockQuantityConversionHelper::convertToTotalBulkQuantity($curRefundItem['pack_quantity'],
                                                                                                     $curRefundItem['bulk_quantity'],
                                                                                                     $curRefundItem['item_bulk_ratio']);
                $curOutboundComplete     = 0;
                if ($curOutboundBulkQuantity >= $curNeedOutboundQuantity)
                {
                    $curOutboundComplete = 1;
                }

                StockRefundItemModel::updateOne($curRefundItem['id'],
                                                [
                                                    'outbound_pack_quantity' => DB::raw('outbound_pack_quantity + ' . $curItemReduceStockRes['reducePackQuantity']),
                                                    'outbound_bulk_quantity' => DB::raw('outbound_bulk_quantity + ' . $curItemReduceStockRes['reduceBulkQuantity']),
                                                    'is_outbound_complete'   => $curOutboundComplete,
                                                ]);

                // 汇总出库总金额
                $curTotalPackPrice  = numberMul([$curItemReduceStockRes['reducePackQuantity'], $curItemReduceStockRes['packDailyPrice']], 4);
                $curTotalBulkPrice  = numberMul([$curItemReduceStockRes['reduceBulkQuantity'], $curItemReduceStockRes['bulkDailyPrice']], 4);
                $totalOutboundPrice = numberAdd([$totalOutboundPrice, $curTotalPackPrice, $curTotalBulkPrice], 4);
            }

            $notOutboundCount = StockRefundItemModel::getTotalNumber(where: ['stock_refund_id' => $refundId, 'status' => 1, 'is_outbound_complete' => 0]);
            if ($notOutboundCount > 0)
            {
                $outboundStatus     = StockOutboundStatusEnum::PartiallyOutbound->value;
                $outboundCompleteAt = null;
            }
            else
            {
                $outboundStatus     = StockOutboundStatusEnum::FullyOutbound->value;
                $outboundCompleteAt = getCurrentTimeWithMilliseconds();
            }

            // 回写退货单出库金额
            StockRefundModel::updateOne($refundId,
                                        ['outbound_price' => $totalOutboundPrice, 'outbound_status' => $outboundStatus, 'outbound_complete_at' => $outboundCompleteAt]);

            DB::commit();

            return self::Success();
        } catch (Throwable $throwable)
        {
            DB::rollBack();
            Log::error(__CLASS__ . '::' . __METHOD__ . ' 退货出库异常', [
                'code'    => $throwable->getCode(),
                'message' => $throwable->getMessage(),
                'file'    => $throwable->getFile(),
                'line'    => $throwable->getLine(),
                'trace'   => $throwable->getTraceAsString(),
            ]);

            return self::Fail('退货出库异常', 45004);
        }
    }

    /**
     * 删除退货单
     *
     * @param int   $refundId
     * @param array $publicParams
     *
     * @return LogicResult
     * @throws Throwable
     */
    public static function DeleteStockRefund(int $refundId, array $publicParams): LogicResult
    {
        if (empty($refundId))
        {
            return self::Fail('删除退货单，缺少退货单ID必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('删除退货单，缺少公共必选参数', 400);
        }

        // 公共参数
        $userId = intval(Arr::get($publicParams, '_userId'));
        if (empty($userId))
        {
            return self::Fail('删除退货单，缺少用户ID必选参数', 400);
        }

        // 获取退货单信息
        $getRefundRes = self::GetValidStockRefund($refundId, $publicParams);
        if ($getRefundRes->isFail())
        {
            return $getRefundRes;
        }

        $getRefundRes = $getRefundRes->getData();
        $refundCode   = $getRefundRes['refund_code'];

        // 验证退货单是否可以编辑
        $getCheckEditAbleRes = self::CheckEditOrDeleteAbleRefund($getRefundRes, $publicParams);
        if ($getCheckEditAbleRes->isFail())
        {
            return $getCheckEditAbleRes;
        }

        try
        {
            DB::beginTransaction();

            // 删除退货单
            StockRefundModel::updateOne($refundId, ['status' => StockRefundStatusEnum::Cancelled->value, 'deleted_at' => getCurrentTimeWithMilliseconds()]);

            // 记录日志
            $operationDesc       = ["删除退货单：$refundCode"];
            $insertOperationData = [
                'stock_refund_id'     => $refundId,
                'stock_refund_code'   => $refundCode,
                'operation_desc'      => json_encode($operationDesc, JSON_UNESCAPED_UNICODE),
                'operation_user_id'   => $userId,
                'operation_user_name' => UsersModel::getOneUserNameById($userId),
                'operation_channel'   => 1,
            ];
            StockRefundOperationLogModel::insert($insertOperationData);

            DB::commit();

            return self::Success();
        } catch (Throwable $throwable)
        {
            DB::rollBack();

            Log::error(__CLASS__ . '::' . __METHOD__ . ' 删除退货单异常', [
                'code'    => $throwable->getCode(),
                'message' => $throwable->getMessage(),
                'file'    => $throwable->getFile(),
                'line'    => $throwable->getLine(),
                'trace'   => $throwable->getTraceAsString(),
            ]);

            return self::Fail('删除退货单异常', 44044);
        }
    }

    /**
     * 添加退货单商品
     *
     * @param array $refundInfo
     * @param array $refundItems
     * @param array $publicParams
     *
     * @return LogicResult
     * @throws Throwable
     */
    private static function InsertRefundItem(array $refundInfo, array $refundItems, array $publicParams): LogicResult
    {
        if (empty($refundInfo))
        {
            return self::Fail('添加退货单商品，缺少退货单主信息参数', 400);
        }
        if (empty($refundItems))
        {
            return self::Fail('添加退货单商品，缺少退货单商品必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('添加退货单商品，缺少公共必选参数', 400);
        }

        // 公共参数
        $userId = intval(Arr::get($publicParams, '_userId'));
        if (empty($userId))
        {
            return self::Fail('添加退货单商品，缺少用户ID必选参数', 400);
        }

        // 业务参数
        $stockRefundId   = intval(Arr::get($refundInfo, 'refundId', 0));
        $stockRefundCode = Arr::get($refundInfo, 'refundCode', '');
        if (empty($stockRefundId))
        {
            return self::Fail('添加退货单商品，缺少退货单ID、退货单号必选参数', 400);
        }

        $insertRefundItemData = [];
        foreach ($refundItems as $curRefundItem)
        {
            $insertRefundItemData[] = [
                'uid'               => generateUUID(),
                'stock_refund_id'   => $stockRefundId,
                'item_id'           => $curRefundItem['itemId'],
                'item_barcode'      => $curRefundItem['itemBarcode'],
                'item_bulk_ratio'   => $curRefundItem['itemInfo']['bulk_ratio'],
                'pack_refund_price' => $curRefundItem['packPrice'],
                'bulk_refund_price' => $curRefundItem['bulkPrice'],
                'pack_quantity'     => $curRefundItem['packQuantity'],
                'bulk_quantity'     => $curRefundItem['bulkQuantity'],
                'expired_date'      => $curRefundItem['expiredDate'],
            ];
        }

        try
        {
            DB::beginTransaction();

            // 添加商品
            StockRefundItemModel::insert($insertRefundItemData);

            // 记录日志
            $operationDesc = [];
            array_walk($refundItems, function ($item) use (&$operationDesc) {
                $operationDesc[] = "添加商品：【{$item['itemInfo']['id']}】{$item['itemName']}";
            });

            $insertOperationData = [
                'stock_refund_id'     => $stockRefundId,
                'stock_refund_code'   => $stockRefundCode,
                'operation_desc'      => json_encode($operationDesc, JSON_UNESCAPED_UNICODE),
                'operation_user_id'   => $userId,
                'operation_user_name' => UsersModel::getOneUserNameById($userId),
                'operation_channel'   => 1,
            ];
            StockRefundOperationLogModel::insert($insertOperationData);

            DB::commit();

            return self::Success();
        } catch (Throwable $throwable)
        {
            DB::rollBack();
            Log::error(__CLASS__ . '::' . __METHOD__ . ' 添加退货单商品异常', [
                'code'    => $throwable->getCode(),
                'message' => $throwable->getMessage(),
                'file'    => $throwable->getFile(),
                'line'    => $throwable->getLine(),
                'trace'   => $throwable->getTraceAsString(),
            ]);

            return self::Fail('添加退货单商品异常', 44043);
        }
    }

    /**
     * 验证退货单是否可编辑
     *
     * @param array $refundInfo
     * @param array $publicParams
     *
     * @return LogicResult
     */
    private static function CheckEditOrDeleteAbleRefund(array $refundInfo, array $publicParams): LogicResult
    {
        if (empty($refundInfo))
        {
            return self::Fail('验证退货单是否可操作，缺少退货单信息必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('验证退货单是否可操作，缺少公共必选参数', 400);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        if (empty($hospitalId))
        {
            return self::Fail('验证退货单是否可操作，缺少医院ID必选参数', 400);
        }

        if (!in_array($refundInfo['status'], [StockRefundStatusEnum::Draft->value, StockRefundStatusEnum::Rejected->value]))
        {
            return self::Fail('退货单状态非草稿、驳回，不可操作', 45005);
        }
        if ($refundInfo['hospital_id'] != $hospitalId)
        {
            return self::Fail('退货单医院不一致，不可操作', 45005);
        }

        return self::Success();
    }

    /**
     * 格式化退货单列表
     *
     * @param array $refundListRes
     *
     * @return LogicResult
     */
    private static function FormatRefundStructure(array $refundListRes): LogicResult
    {
        if (empty($refundListRes))
        {
            return self::Success();
        }

        $returnRefundList = [];
        foreach ($refundListRes as $curRefundInfo)
        {
            $returnRefundList[] = [
                'refundCode'     => $curRefundInfo['refund_code'],
                'refundType'     => [
                    'id'   => $curRefundInfo['refund_type'],
                    'name' => StockRefundTypeEnum::getDescription($curRefundInfo['refund_type']),
                ],
                'purchaseCode'   => $curRefundInfo['purchase_code'],
                'refundPrice'    => formatDisplayNumber($curRefundInfo['refund_price'], 4),
                'outboundPrice'  => formatDisplayNumber($curRefundInfo['outbound_price'], 4),
                'refundStatus'   => [
                    'id'   => $curRefundInfo['status'],
                    'name' => StockRefundStatusEnum::getDescription($curRefundInfo['status']),
                ],
                'outboundStatus' => [
                    'id'   => $curRefundInfo['outbound_status'],
                    'name' => StockOutboundStatusEnum::getDescription($curRefundInfo['outbound_status']),
                ],
                'createUser'     => [
                    'uid'  => $curRefundInfo['user_uid'],
                    'name' => $curRefundInfo['user_name'],
                ],
                'createTime'     => formatDisplayDateTime($curRefundInfo['created_at']),
                'remark'         => $curRefundInfo['remark'],
            ];
        }

        return self::Success($returnRefundList);
    }

    /**
     * 格式化退货单商品信息
     *
     * @param array $refundItemRes
     * @param array $itemInfoList
     *
     * @return LogicResult
     */
    private static function FormatRefundItemStructure(array $refundItemRes, array $itemInfoList): LogicResult
    {
        if (empty($refundItemRes) || empty($itemInfoList))
        {
            return self::Success();
        }

        // 按照商品ID数组
        $itemInfoList = array_column($itemInfoList, null, 'id');

        $returnRefundItemList = [];
        foreach ($refundItemRes as $curRefundItem)
        {
            // 商品信息
            $curItemInfo = $itemInfoList[$curRefundItem['item_id']] ?? [];
            if (empty($curItemInfo))
            {
                continue;
            }

            $tmpTransferItem = [
                'uid'                  => $curRefundItem['uid'],
                'itemUid'              => $curItemInfo['uid'],
                'itemBarcode'          => $curRefundItem['item_barcode'],
                'itemName'             => ItemHelper::ItemDisplayName($curItemInfo),
                'packQuantity'         => formatDisplayNumber($curRefundItem['pack_quantity']),
                'bulkQuantity'         => formatDisplayNumber($curRefundItem['bulk_quantity']),
                'expiredDate'          => $curRefundItem['expired_date'],
                'packRefundPrice'      => $curRefundItem['pack_refund_price'],
                'bulkRefundPrice'      => $curRefundItem['bulk_refund_price'],
                'outboundPackQuantity' => $curRefundItem['outbound_pack_quantity'],
                'outboundBulkQuantity' => $curRefundItem['outbound_bulk_quantity'],
                'isOutboundComplete'   => $curRefundItem['is_outbound_complete'],
                'itemInfo'             => ItemHelper::FormatItemInfoStructure($curItemInfo),
            ];

            $returnRefundItemList[] = $tmpTransferItem;
        }

        return self::Success($returnRefundItemList);
    }

}
