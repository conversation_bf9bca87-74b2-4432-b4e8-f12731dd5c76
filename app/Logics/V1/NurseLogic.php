<?php

namespace App\Logics\V1;

use Arr;
use DB;
use Log;
use Throwable;
use App\Logics\Logic;
use App\Logics\LogicResult;
use App\Enums\NurseExecutionStatusEnum;
use App\Enums\TestOrImagePaidStatusEnum;
use App\Models\NurseModel;
use App\Models\RecipeItemModel;
use App\Models\HospitalUserModel;
use App\Models\NurseExecutorModel;

class NurseLogic extends Logic
{
    /**
     * 获取处置列表筛选项
     *
     * @param array $publicParams
     *
     * @return LogicResult
     */
    public static function GetNurseFilterOptions(array $publicParams): LogicResult
    {
        if (empty($publicParams))
        {
            return self::Fail('获取影像列表筛选项，缺少必选参数', 400);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        if (empty($hospitalId))
        {
            return self::Fail('获取影像列表筛选项，缺少医院ID必选参数', 400);
        }

        // 获取开具医院列表
        $getDoctorOptions = NurseModel::GetCreateUsersOptions($hospitalId, 'doctor_id');

        return self::Success([
                                 'doctorOptions' => $getDoctorOptions,
                             ]);
    }

    /**
     * 获取处置列表
     *
     * @param array $searchParams
     * @param array $publicParams
     * @param int   $iPage
     * @param int   $iPageSize
     *
     * @return LogicResult
     */
    public static function GetNurseList(array $searchParams, array $publicParams, int $iPage, int $iPageSize): LogicResult
    {
        if (empty($publicParams))
        {
            return self::Fail('处置列表，缺少必选参数', 400);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        if (empty($hospitalId))
        {
            return self::Fail('处置列表，缺少医院ID必选参数', 400);
        }

        // 获取当前医院下处置列表
        $searchParams['hospitalId'] = $hospitalId;
        $getNurseListRes            = NurseModel::getNurseListData($searchParams, $iPage, $iPageSize);
        if (empty($getNurseListRes))
        {
            return self::Success(['total' => 0, 'data' => []]);
        }

        $totalCount   = $getNurseListRes['total'] ?? 0;
        $nurseListRes = $getNurseListRes['data'] ?? [];
        if (empty($totalCount) || empty($nurseListRes))
        {
            return self::Success(['total' => 0, 'data' => []]);
        }

        // 格式化处置信息
        $getFormatNurseRes = self::FormatNurseInfo($nurseListRes);
        if ($getFormatNurseRes->isFail())
        {
            return $getFormatNurseRes;
        }

        return self::Success(['total' => $totalCount, 'data' => $getFormatNurseRes->getData()]);
    }

    /**
     * 获取有效处置信息
     *
     * @param int      $nurseId
     * @param array    $publicParams
     * @param bool     $withHospitalId
     * @param int|null $nurseStatus
     *
     * @return LogicResult
     */
    public static function GetValidNurse(int $nurseId, array $publicParams, bool $withHospitalId = true, ?int $nurseStatus = 1): LogicResult
    {
        if (empty($nurseId))
        {
            return self::Fail('获取处置，缺少处置ID必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('获取处置，缺少公共必选参数', 400);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        if (empty($hospitalId))
        {
            return self::Fail('获取处置，缺少医院ID必选参数', 400);
        }

        // 根据处置编码查询处置记录
        $getWhere = ['id' => $nurseId];
        if (!empty($withHospitalId))
        {
            $getWhere['hospital_id'] = $hospitalId;
        }

        $getNurseRes = NurseModel::getData(where: $getWhere);
        $getNurseRes = $getNurseRes ? current($getNurseRes) : [];
        if (empty($getNurseRes))
        {
            return self::Fail('处置不存在', 40201);
        }
        if (!empty($nurseStatus) && $getNurseRes['status'] != $nurseStatus)
        {
            return self::Fail('处置状态已发生变化，不可操作', 40201);
        }

        return self::Success($getNurseRes);
    }

    /**
     * 验证处置是否可操作：开始执行、录入执行人、填写报告结果
     * 1、化验项有效
     * 2、病历未结束（操作人可以是非诊断医生）
     *
     * @param int   $nurseId
     * @param array $publicParams
     *
     * @return LogicResult
     */
    public static function GetCheckNurseOperable(int $nurseId, array $publicParams): LogicResult
    {
        if (empty($nurseId))
        {
            return self::Fail('验证处置操作，缺少处置ID必选参数', 400);
        }

        $getNurseRes = self::GetValidNurse($nurseId, $publicParams);
        if ($getNurseRes->isFail())
        {
            return $getNurseRes;
        }

        $getNurseRes = $getNurseRes->getData();

        // 获取关联的病历信息
        $getCaseRes = CaseLogic::GetValidCaseById($getNurseRes['case_id'], $publicParams, withDoctorId: false);
        if ($getCaseRes->isFail())
        {
            return $getCaseRes;
        }

        // 病历是否已经结束
        $getCaseRes = $getCaseRes->getData();
        if (!empty($getCaseRes['finished']))
        {
            return self::Fail('关联病历已结束诊断，不可操作', 40006);
        }

        return self::Success($getNurseRes);
    }

    /**
     * 开始执行
     *
     * @param int   $nurseId
     * @param array $executorIds
     * @param array $publicParams
     *
     * @return LogicResult
     * @throws Throwable
     */
    public static function StartExecution(int $nurseId, array $executorIds, array $publicParams): LogicResult
    {
        if (empty($nurseId))
        {
            return self::Fail('开始执行处置，缺少处置ID参数', 400);
        }
        if (empty($executorIds))
        {
            return self::Fail('开始执行处置，至少需要一个执行人', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('开始执行处置，缺少必选参数', 400);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        $userId     = Arr::get($publicParams, '_userId');
        if (empty($hospitalId))
        {
            return self::Fail('开始执行处置，缺少医院ID必选参数', 400);
        }
        if (empty($userId))
        {
            return self::Fail('开始执行处置，缺少用户ID必选参数', 400);
        }

        // 根据处置编码查询处置记录
        $getNurseRes = self::GetCheckNurseOperable($nurseId, $publicParams);
        if ($getNurseRes->isFail())
        {
            return $getNurseRes;
        }

        // 已经执行处置
        $getNurseRes = $getNurseRes->getData();
        if ($getNurseRes['execution_status'] == 1)
        {
            return self::Fail('处置已开始执行，不可重复开始', 40203);
        }

        // 开始执行
        try
        {
            DB::beginTransaction();

            // 录入执行人
            $verifyTesterIdsRes = self::NurseFollowExecutor($nurseId, $executorIds, $publicParams);
            if ($verifyTesterIdsRes->isFail())
            {
                DB::rollBack();

                return $verifyTesterIdsRes;
            }

            // 更新处置为开始执行状态
            NurseModel::updateOne($nurseId, ['execution_status' => 1, 'execution_date' => getCurrentTimeWithMilliseconds()]);

            // 增加处方商品化验使用数量
            RecipeItemModel::on()
                           ->where(['uid' => $getNurseRes['recipe_item_uid']])
                           ->whereColumn('used_quantity', '<', 'quantity')
                           ->increment('used_quantity');

            DB::commit();

            return self::Success();
        } catch (Throwable $throwable)
        {
            DB::rollBack();

            Log::error(__CLASS__ . '::' . __METHOD__ . ' 开始执行处置异常', [
                'code'    => $throwable->getCode(),
                'message' => $throwable->getMessage(),
                'file'    => $throwable->getFile(),
                'line'    => $throwable->getLine(),
                'trace'   => $throwable->getTraceAsString(),
            ]);

            return self::Fail('开始执行处置异常', 40204);
        }
    }

    /**
     * 录入处置执行人
     *
     * @param int   $nurseId
     * @param array $executorIds
     * @param array $publicParams
     *
     * @return LogicResult
     * @throws Throwable
     */
    public static function NurseFollowExecutor(int $nurseId, array $executorIds, array $publicParams): LogicResult
    {
        if (empty($nurseId))
        {
            return self::Fail('录入处置执行人，缺少处置ID必选参数', 400);
        }
        if (empty($executorIds))
        {
            return self::Fail('录入处置执行人，缺少执行人必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('录入处置执行人，缺少公共参数', 400);
        }

        $executorIds = array_filter($executorIds);
        if (empty($executorIds))
        {
            return self::Fail('处置执行人必选', 400);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        if (empty($hospitalId))
        {
            return self::Fail('录入处置执行人，缺少医院ID必选参数', 400);
        }

        // 获取处置信息
        $getNurseRes = self::GetValidNurse($nurseId, $publicParams);
        if ($getNurseRes->isFail())
        {
            return $getNurseRes;
        }

        // 处置关联处方ID、商品ID
        $getNurseRes   = $getNurseRes->getData();
        $nurseRecipeId = $getNurseRes['recipe_id'];
        $nurseItemId   = $getNurseRes['item_id'];

        // 验证执行人是否存在
        $getExecutorRes = HospitalUserModel::getHospitalUsers($hospitalId, $executorIds);
        if ($getExecutorRes->isEmpty())
        {
            return self::Fail('处置执行人不存在', 40202);
        }

        // 部分执行人不存在
        $getExecutorIds  = $getExecutorRes->pluck('user_id')
                                          ->unique()
                                          ->toArray();
        $diffExecutorIds = array_diff($executorIds, $getExecutorIds);
        if (!empty($diffExecutorIds))
        {
            return self::Fail('部分处置执行人不存在', 40202);
        }

        // 获取该处置存在的执行人
        $getNurseExecutorRes = NurseExecutorModel::getData(where   : ['nurse_id' => $nurseId],
                                                           orderBys: ['executor_level' => 'asc'],
                                                           keyBy   : 'executor_level');


        $insertData = [];
        $updateData = [];
        foreach ($executorIds as $level => $userId)
        {
            // 当前级别是否存在执行人
            $curOldExecutorInfo = $getNurseExecutorRes[$level] ?? [];

            // 无效执行人
            if (empty($userId) && (empty($curOldExecutorInfo) || empty($curOldExecutorInfo['tester_status'])))
            {
                continue;
            }

            // 修改旧的执行人记录
            if (!empty($curOldExecutorInfo))
            {
                $updateData[] = [
                    'where' => ['id' => $curOldExecutorInfo['id']],
                    'data'  => [
                        'user_id'         => $userId,
                        'executor_status' => $userId > 0 ? 1 : 0,
                    ],
                ];
            }
            else
            {
                // 新增当前级别与执行人
                $insertData[] = [
                    'nurse_id'        => $nurseId,
                    'recipe_id'       => $nurseRecipeId,
                    'item_id'         => $nurseItemId,
                    'user_id'         => $userId,
                    'executor_level'  => $level,
                    'executor_status' => 1,
                ];
            }

            unset($getNurseExecutorRes[$level]);
        }

        // 旧的执行人存在的修改为无效
        if (!empty($getNurseExecutorRes))
        {
            $updateData = array_merge($updateData, array_map(function ($item) {
                return [
                    'where' => ['id' => $item['id']],
                    'data'  => [
                        'executor_status' => 0,
                    ],
                ];
            }, $getNurseExecutorRes));
        }

        // 没有需要更新的数据
        if (empty($insertData) && empty($updateData))
        {
            return self::Success();
        }

        try
        {
            DB::beginTransaction();

            if (!empty($insertData))
            {
                NurseExecutorModel::insert($insertData);
            }

            if (!empty($updateData))
            {
                foreach ($updateData as $updateItem)
                {
                    NurseExecutorModel::updateOne($updateItem['where']['id'], $updateItem['data']);
                }
            }

            // 更新处置标记有执行人
            if ($getNurseRes['is_executor'] != 1)
            {
                NurseModel::updateOne($nurseId, ['is_executor' => 1]);
            }

            DB::commit();
        } catch (Throwable $throwable)
        {
            DB::rollBack();

            Log::error(__CLASS__ . '::' . __METHOD__ . ' 录入处置执行人异常', [
                'code'    => $throwable->getCode(),
                'message' => $throwable->getMessage(),
                'file'    => $throwable->getFile(),
                'line'    => $throwable->getLine(),
                'trace'   => $throwable->getTraceAsString(),
            ]);

            return self::Fail('录入处置执行人异常', 40204);
        }

        return self::Success();
    }

    /**
     * 格式化处置信息展示
     *
     * @param array $nurseList
     *
     * @return LogicResult
     */
    private static function FormatNurseInfo(array $nurseList): LogicResult
    {
        if (empty($nurseList))
        {
            return self::Success();
        }

        // 获取处置关联的宠物基础信息、宠物对应宠物主基础信息
        $petIds = array_unique(array_column($nurseList, 'pet_id'));
        if (!empty($petIds))
        {
            $getPetRes = PetLogic::GetPetBaseInfoByPetIds($petIds);
            if ($getPetRes->isFail())
            {
                return $getPetRes;
            }

            $getPetRes = $getPetRes->getData();
        }

        // 获取处置关联的执行人
        $nurseIds       = array_unique(array_column($nurseList, 'id'));
        $getExecutorRes = NurseExecutorModel::getNurseExecutor($nurseIds);

        $returnNurseList = [];
        foreach ($nurseList as $item)
        {
            // 处置关联会员信息
            $curNurseMemberInfo = $getPetRes[$item['pet_id']]['memberInfo'] ?? [];

            // 处置关联宠物信息
            $curNursePetInfo = $getPetRes[$item['pet_id']]['petInfo'] ?? [];

            // 处置关联执行人
            $curExecutorInfo = [];
            foreach ($getExecutorRes as $executorInfo)
            {
                if ($executorInfo['nurse_id'] != $item['id'])
                {
                    continue;
                }

                $tmpTesterInfo = [
                    'uid'   => $executorInfo['user_uid'],
                    'name'  => $executorInfo['user_name'],
                    'level' => $executorInfo['executor_level'],
                ];

                $curExecutorInfo[] = $tmpTesterInfo;
            }

            $tmpNurseItem = [
                'nurseCode'  => $item['nurse_code'],
                'memberInfo' => $curNurseMemberInfo,
                'petInfo'    => $curNursePetInfo,
                'doctor'     => [
                    'uid'  => $item['doctor_uid'],
                    'name' => $item['doctor_name'],
                ],
                'item'       => [
                    'uid'      => $item['item_uid'],
                    'name'     => $item['item_display_name'],
                    'itemName' => $item['item_name'],
                    'unit'     => $item['item_use_unit'],
                ],
                'quantity'   => 1,
                'price'      => formatDisplayNumber($item['price']),
                'status'     => NurseExecutionStatusEnum::FormatNurseExecutionStatus($item),
                'payStatus'  => [
                    'id'   => $item['is_paid'],
                    'name' => TestOrImagePaidStatusEnum::getDescription($item['is_paid']),
                ],
                'createTime' => formatDisplayDateTime($item['created_at']),
                'execDate'   => formatDisplayDateTime($item['execution_date']),
                'executor'   => $curExecutorInfo,
                'editAble'   => true, // 默认可编辑
                'remark'     => $item['remark'] ?? '',
            ];

            $returnNurseList[] = $tmpNurseItem;
        }

        return self::Success($returnNurseList);
    }
}
