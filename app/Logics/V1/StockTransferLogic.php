<?php

namespace App\Logics\V1;

use DB;
use Log;
use Arr;
use Throwable;
use App\Support\Item\ItemHelper;
use App\Support\Stock\StockQuantityConversionHelper;
use App\Enums\PageEnum;
use App\Enums\PurchaseTypeEnum;
use App\Enums\StockReduceTypeEnum;
use App\Enums\StockRefundTypeEnum;
use App\Enums\BusinessCodePrefixEnum;
use App\Enums\PurchaseOrderStatusEnum;
use App\Enums\StockOutboundStatusEnum;
use App\Logics\Logic;
use App\Logics\LogicResult;
use App\Models\StockRefundModel;
use App\Models\StockRefundItemModel;
use App\Models\PurchaseOrderModel;
use App\Models\PurchaseOrderItemModel;

class StockTransferLogic extends Logic
{
    /**
     * 获取采购单列表
     *
     * @param array $searchParams
     * @param array $publicParams
     *
     * @return LogicResult
     */
    public static function GetStockTransferLists(array $searchParams, array $publicParams): LogicResult
    {
        if (empty($publicParams))
        {
            return self::Fail('获取调拨单列表，缺少公共必选参数', 400);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        if (empty($hospitalId))
        {
            return self::Fail('获取调拨单列表，缺少公共必选参数', 400);
        }

        // 业务参数
        $iPage     = intval(Arr::get($searchParams, 'page', 0)) ?? PageEnum::DefaultPageIndex->value;
        $iPageSize = intval(Arr::get($searchParams, 'count', 0)) ?? PageEnum::DefaultPageSize->value;

        // 获取调拨单列表
        $searchParams['purchaseType']    = PurchaseTypeEnum::Transfer->value;
        $searchParams['allotHospitalId'] = $hospitalId;
        $getTransferOrderListRes         = PurchaseOrderModel::getTransferOrderListData($searchParams, $iPage, $iPageSize);
        if (empty($getTransferOrderListRes))
        {
            return self::Success(['total' => 0, 'data' => []]);
        }

        $totalCount      = $getTransferOrderListRes['total'] ?? 0;
        $transferListRes = $getTransferOrderListRes['data'] ?? [];
        if (empty($totalCount) || empty($transferListRes))
        {
            return self::Success(['total' => 0, 'data' => []]);
        }

        $getFormatTransferListRes = self::FormatTransferOrderStructure($transferListRes);
        if ($getFormatTransferListRes->isFail())
        {
            return $getFormatTransferListRes;
        }

        return self::Success(['total' => $totalCount, 'data' => $getFormatTransferListRes->getData()]);
    }

    /**
     * 获取有效调拨单
     *
     * @param int   $transferOrderId
     * @param array $publicParams
     *
     * @return LogicResult
     */
    public static function GetValidTransferOrder(int $transferOrderId, array $publicParams): LogicResult
    {
        if (empty($transferOrderId))
        {
            return self::Fail('获取有效调拨单，缺少领用单ID必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('获取有效调拨单，缺少公共必选参数', 400);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        if (empty($hospitalId))
        {
            return self::Fail('获取有效耗材领用单，缺少医院ID必选参数', 400);
        }

        // 获取调拨单信息
        $getTransferOrderRes = PurchaseOrderModel::getData(where: [
                                                                      'id'                => $transferOrderId,
                                                                      'allot_hospital_id' => $hospitalId,
                                                                      'status'            => PurchaseOrderStatusEnum::Approved->value
                                                                  ]);
        $getTransferOrderRes = $getTransferOrderRes ? current($getTransferOrderRes) : [];
        if (empty($getTransferOrderRes))
        {
            return self::Fail('调拨单不存在', 44000);
        }

        return self::Success($getTransferOrderRes);
    }

    /**
     * 获取调拨单详情
     *
     * @param int   $transferOrderId
     * @param array $publicParams
     *
     * @return LogicResult
     */
    public static function GetTransferOrderDetail(int $transferOrderId, array $publicParams): LogicResult
    {
        if (empty($transferOrderId))
        {
            return self::Fail('获取调拨单详情，缺少调拨单ID必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('获取调拨单详情，缺少公共必选参数', 400);
        }

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        if (empty($hospitalId))
        {
            return self::Fail('获取调拨单详情，缺少公共医院ID必选参数', 400);
        }

        // 获取调拨单信息
        $getTransferOrderRes = PurchaseOrderModel::getTransferOrderListData(['purchaseOrderId' => $transferOrderId, 'allotHospitalId' => $hospitalId]);
        $getTransferOrderRes = !empty($getTransferOrderRes['data']) ? current($getTransferOrderRes['data']) : [];
        if (empty($getTransferOrderRes))
        {
            return self::Fail('调拨单不存在', 44000);
        }

        // 获取调拨单商品明细
        $getTransferOrderItemRes = PurchaseOrderItemModel::getData(where: ['purchase_order_id' => $transferOrderId, 'status' => 1]);
        if (empty($getTransferOrderItemRes))
        {
            return self::Fail('调拨单商品不存在', 44001);
        }

        // 获取调拨单商品信息
        $itemIds                = array_column($getTransferOrderItemRes, 'item_id');
        $getTransferItemInfoRes = ItemLogic::GetItemFullInfo(itemIds: $itemIds, itemStatus: [], publicParams: $publicParams, withItemPrice: true, withItemStock: true);
        if ($getTransferItemInfoRes->isFail())
        {
            return $getTransferItemInfoRes;
        }

        // 获取调拨单出库信息
        $transferItemOutboundInfo = [];
        $getStockRefundRes        = StockRefundModel::getTransferRelationRefund($getTransferOrderRes['purchase_code']);
        if (!empty($getStockRefundRes))
        {
            $getStockRefundItemRes = StockRefundItemModel::getData(where: ['stock_refund_id' => $getStockRefundRes['id'], 'status' => 1]);
            foreach ($getStockRefundItemRes as $curRefundItem)
            {
                $curItemId = $curRefundItem['item_id'];
                if (!isset($transferItemOutboundInfo[$curItemId]))
                {
                    $transferItemOutboundInfo[$curItemId] = [
                        'outbound_pack_quantity' => 0,
                        'outbound_bulk_quantity' => 0,
                    ];
                }

                $transferItemOutboundInfo[$curItemId]['pack_refund_price']      = $curRefundItem['pack_refund_price'];
                $transferItemOutboundInfo[$curItemId]['bulk_refund_price']      = $curRefundItem['bulk_refund_price'];
                $transferItemOutboundInfo[$curItemId]['outbound_pack_quantity'] += $curRefundItem['outbound_pack_quantity'];
                $transferItemOutboundInfo[$curItemId]['outbound_bulk_quantity'] += $curRefundItem['outbound_bulk_quantity'];
            }
        }

        // 格式化调拨单商品信息
        $getFormatTransferOrderRes = self::FormatTransferOrderStructure([$getTransferOrderRes]);
        if ($getFormatTransferOrderRes->isFail())
        {
            return $getFormatTransferOrderRes;
        }

        // 格式化调拨单商品信息
        $getFormatPurchaseItemRes = self::FormatTransferItemStructure($getTransferOrderItemRes, $getTransferItemInfoRes->getData(), $transferItemOutboundInfo);
        if ($getFormatPurchaseItemRes->isFail())
        {
            return $getFormatPurchaseItemRes;
        }

        $getFormatTransferOrderRes          = current($getFormatTransferOrderRes->getData());
        $getFormatTransferOrderRes['items'] = $getFormatPurchaseItemRes->getData();

        return self::Success($getFormatTransferOrderRes);
    }

    /**
     * 调拨出库
     *
     * @param int   $transferOrderId
     * @param array $publicParams
     *
     * @return LogicResult
     * @throws Throwable
     */
    public static function TransferOrderOutbound(int $transferOrderId, array $publicParams): LogicResult
    {
        if (empty($transferOrderId))
        {
            return self::Fail('调拨出库，缺少调拨单ID必选参数', 400);
        }
        if (empty($publicParams))
        {
            return self::Fail('调拨出库，缺少公共必选参数', 400);
        }

        // 公共参数
        $hospitalId      = intval(Arr::get($publicParams, '_hospitalId'));
        $hospitalBrandId = intval(Arr::get($publicParams, '_hospitalBrandId'));
        $hospitalOrgId   = intval(Arr::get($publicParams, '_hospitalOrgId'));
        $userId          = intval(Arr::get($publicParams, '_userId'));
        if (empty($hospitalId) || empty($hospitalBrandId) || empty($hospitalOrgId) || empty($userId))
        {
            return self::Fail('调拨出库，缺少公共必选参数', 400);
        }

        // 获取调拨单信息
        $getTransferOrderRes = self::GetValidTransferOrder($transferOrderId, $publicParams);
        if ($getTransferOrderRes->isFail())
        {
            return $getTransferOrderRes;
        }
        $getTransferOrderRes = $getTransferOrderRes->getData();

        // 获取调拨单商品明细
        $getTransferOrderItemRes = PurchaseOrderItemModel::getData(where: ['purchase_order_id' => $transferOrderId, 'status' => 1]);
        if (empty($getTransferOrderItemRes))
        {
            return self::Fail('调拨单商品不存在', 44001);
        }

        // 调拨出库关联退货单，调拨出库只可以出一次，一次性全部出完
        $getStockRefundRes = StockRefundModel::getTransferRelationRefund($getTransferOrderRes['purchase_code']);
        if (!empty($getStockRefundRes))
        {
            return self::Fail('调拨单已全部出库，不可重复操作', 44002);
        }

        // 出库信息
        $outboundItemData = [];
        foreach ($getTransferOrderItemRes as $curTransferItem)
        {
            if (empty($curTransferItem['item_id']) || empty($curTransferItem['item_barcode']))
            {
                return self::Fail('调拨出库，商品信息异常', 45004);
            }
            if ($curTransferItem['pack_quantity'] <= 0 && $curTransferItem['bulk_quantity'] <= 0)
            {
                return self::Fail('调拨出库，商品数量异常', 45004);
            }

            $outboundItemData[] = [
                'itemId'           => $curTransferItem['item_id'],
                'itemBarcode'      => $curTransferItem['item_barcode'],
                'packQuantity'     => $curTransferItem['pack_quantity'],
                'bulkQuantity'     => $curTransferItem['bulk_quantity'],
                'relationCode'     => $getTransferOrderRes['purchase_code'],
                'relationId'       => $transferOrderId,
                'relationDetailId' => $curTransferItem['id'],
                'reduceType'       => StockReduceTypeEnum::Transfer->value,
                'remark'           => StockReduceTypeEnum::getDescription(StockReduceTypeEnum::Transfer->value),
            ];
        }

        try
        {
            DB::beginTransaction();

            // 调拨出库
            $getReduceStockRes = StockItemShelfReduceLogic::ReduceStockShelfQuantity($outboundItemData, $publicParams);
            if ($getReduceStockRes->isFail())
            {
                DB::rollBack();

                return $getReduceStockRes;
            }

            // 创建退货单
            $insertRefundData = [
                'refund_code'          => generateBusinessCodeNumber(BusinessCodePrefixEnum::DBCK),
                'refund_type'          => StockRefundTypeEnum::Transfer->value,
                'purchase_code'        => $getTransferOrderRes['purchase_code'],
                'org_id'               => $hospitalOrgId,
                'brand_id'             => $hospitalBrandId,
                'hospital_id'          => $hospitalId,
                'refund_price'         => $getTransferOrderRes['purchase_price'],
                'outbound_price'       => 0,
                'status'               => 2, // 调拨单出库默认为审核通过
                'outbound_status'      => StockOutboundStatusEnum::FullyOutbound->value,
                'created_by'           => $userId,
                'submitted_by'         => $userId,
                'submitted_at'         => getCurrentTimeWithMilliseconds(),
                'approved_at'          => getCurrentTimeWithMilliseconds(),
                'outbound_complete_at' => getCurrentTimeWithMilliseconds(),
            ];
            $stockRefundId    = StockRefundModel::insertOne($insertRefundData);

            // 创建退货单明细
            $totalOutboundPrice   = 0;
            $insertRefundItemData = [];
            foreach ($getTransferOrderItemRes as $curTransferItem)
            {
                $curReduceRes    = $getReduceStockRes->getData($curTransferItem['item_id']);
                $curReduceDetail = $curReduceRes['reduceDetailInfo'];
                if (empty($curReduceRes) || empty($curReduceDetail))
                {
                    DB::rollBack();

                    return self::Fail("调拨出库，商品【{$curTransferItem['item_barcode']}】出库失败", 45004);
                }

                foreach ($curReduceDetail as $curDetail)
                {
                    $curPackQuantity   = $curDetail['packQuantity'];
                    $curBulkQuantity   = $curDetail['bulkQuantity'];
                    $curPackDailyPrice = $curReduceRes['packDailyPrice'];
                    $curBulkDailyPrice = $curReduceRes['bulkDailyPrice'];

                    $insertRefundItemData[] = [
                        'uid'                    => generateUUID(),
                        'stock_refund_id'        => $stockRefundId,
                        'item_id'                => $curTransferItem['item_id'],
                        'item_barcode'           => $curTransferItem['item_barcode'],
                        'item_bulk_ratio'        => $curTransferItem['item_bulk_ratio'],
                        'expired_date'           => $curDetail['expiredDate'],
                        'pack_refund_price'      => $curPackQuantity > 0 ? $curPackDailyPrice : 0,
                        'bulk_refund_price'      => $curBulkQuantity > 0 ? $curBulkDailyPrice : 0,
                        'pack_quantity'          => $curPackQuantity,
                        'bulk_quantity'          => $curBulkQuantity,
                        'outbound_pack_quantity' => $curPackQuantity,
                        'outbound_bulk_quantity' => $curBulkQuantity,
                        'is_gift'                => $curTransferItem['is_gift'],
                        'is_outbound_complete'   => 1,
                    ];

                    // 总出库金额
                    $curOutboundPackPrice = numberMul([$curPackQuantity, $curPackDailyPrice], 4);
                    $curOutboundBulkPrice = numberMul([$curBulkQuantity, $curBulkDailyPrice], 4);
                    $totalOutboundPrice   = numberAdd([$totalOutboundPrice, $curOutboundPackPrice, $curOutboundBulkPrice], 4);
                }
            }

            StockRefundItemModel::insert($insertRefundItemData);

            // 回写退货单出库金额
            StockRefundModel::updateOne($stockRefundId, ['outbound_price' => $totalOutboundPrice]);

            DB::commit();

            return self::Success();
        } catch (Throwable $throwable)
        {
            DB::rollBack();
            Log::error(__CLASS__ . '::' . __METHOD__ . ' 调拨出库异常', [
                'code'    => $throwable->getCode(),
                'message' => $throwable->getMessage(),
                'file'    => $throwable->getFile(),
                'line'    => $throwable->getLine(),
                'trace'   => $throwable->getTraceAsString(),
            ]);

            return self::Fail('调拨出库异常', 45004);
        }
    }

    /**
     * 格式化调拨单列表
     *
     * @param array $transferListRes
     *
     * @return LogicResult
     */
    private static function FormatTransferOrderStructure(array $transferListRes): LogicResult
    {
        if (empty($transferListRes))
        {
            return self::Success();
        }

        $returnTransferList = [];
        foreach ($transferListRes as $curTransferOrder)
        {
            $tmpTransferOrder = [
                'purchaseCode'   => $curTransferOrder['purchase_code'],
                'hospital'       => [
                    'uid'  => $curTransferOrder['hospital_uid'],
                    'name' => $curTransferOrder['hospital_alias_name'],
                ],
                'outboundStatus' => [
                    'id'   => $curTransferOrder['stock_refund_outbound_status'] ?? 0,
                    'name' => StockOutboundStatusEnum::getDescription($curTransferOrder['stock_refund_outbound_status'] ?? 0),
                ],
                'purchasePrice'  => formatDisplayNumber($curTransferOrder['purchase_price'], 4),
                'createTime'     => formatDisplayDateTime($curTransferOrder['created_at']),
                'remark'         => $curTransferOrder['remark'],
            ];

            $returnTransferList[] = $tmpTransferOrder;
        }

        return self::Success($returnTransferList);
    }

    /**
     * 格式化调拨单商品信息
     *
     * @param array $transferItemRes
     * @param array $itemInfoList
     * @param array $transferItemOutboundInfo
     *
     * @return LogicResult
     */
    private static function FormatTransferItemStructure(array $transferItemRes, array $itemInfoList, array $transferItemOutboundInfo = []): LogicResult
    {
        if (empty($transferItemRes) || empty($itemInfoList))
        {
            return self::Success();
        }

        // 按照商品ID数组
        $itemInfoList = array_column($itemInfoList, null, 'id');

        $returnTransferItemList = [];
        foreach ($transferItemRes as $curTransferItem)
        {
            // 商品信息
            $curItemInfo = $itemInfoList[$curTransferItem['item_id']] ?? [];
            if (empty($curItemInfo))
            {
                continue;
            }

            // 获取出库数量
            $curItemOutboundInfo     = $transferItemOutboundInfo[$curTransferItem['item_id']] ?? [];
            $curItemOutboundComplete = 0;
            if (!empty($curItemOutboundInfo))
            {
                $curTransferTotalBulk = StockQuantityConversionHelper::convertToTotalBulkQuantity($curTransferItem['pack_quantity'],
                                                                                                  $curTransferItem['bulk_quantity'],
                                                                                                  $curTransferItem['item_bulk_ratio']);
                $curOutboundTotalBulk = StockQuantityConversionHelper::convertToTotalBulkQuantity($curItemOutboundInfo['outbound_pack_quantity'],
                                                                                                  $curItemOutboundInfo['outbound_bulk_quantity'],
                                                                                                  $curTransferItem['item_bulk_ratio']);
                if ($curOutboundTotalBulk >= $curTransferTotalBulk)
                {
                    $curItemOutboundComplete = 1;
                }
            }

            $tmpTransferItem = [
                'uid'                  => $curTransferItem['uid'],
                'itemUid'              => $curItemInfo['uid'],
                'itemBarcode'          => $curTransferItem['item_barcode'],
                'itemName'             => ItemHelper::ItemDisplayName($curItemInfo),
                'packQuantity'         => formatDisplayNumber($curTransferItem['pack_quantity']),
                'bulkQuantity'         => formatDisplayNumber($curTransferItem['bulk_quantity']),
                'packRefundPrice'      => $curItemOutboundInfo['pack_refund_price'] ?? 0,
                'bulkRefundPrice'      => $curItemOutboundInfo['bulk_refund_price'] ?? 0,
                'outboundPackQuantity' => formatDisplayNumber($curItemOutboundInfo['outbound_pack_quantity'] ?? 0),
                'outboundBulkQuantity' => formatDisplayNumber($curItemOutboundInfo['outbound_bulk_quantity'] ?? 0),
                'isOutboundComplete'   => $curItemOutboundComplete,
                'itemInfo'             => ItemHelper::FormatItemInfoStructure($curItemInfo),
            ];

            $returnTransferItemList[] = $tmpTransferItem;
        }

        return self::Success($returnTransferItemList);
    }
}
