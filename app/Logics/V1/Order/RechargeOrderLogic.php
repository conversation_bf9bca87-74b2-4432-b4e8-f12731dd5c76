<?php

namespace App\Logics\V1\Order;

use Throwable;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Logics\Logic;
use App\Logics\LogicResult;
use App\Traits\VersionTrait;
use App\Logics\V1\Order\Interfaces\OrderItemInterface;
use App\Enums\BusinessCodePrefixEnum;
use App\Enums\PaySubOrderTypeEnum;
use App\Enums\SheetStatusEnum;
use App\Enums\PaySubOrderExtProcessStatusEnum;
use App\Models\PaySubOrderModel;
use App\Models\PaySubOrderExtModel;
use App\Models\BalanceRechargeOrderModel;
use App\Models\BalanceRechargeSheetModel;
use App\Models\BalanceRechargeSheetItemModel;
use App\Support\PayOrder\PaySubOrderExtHelper;
use App\Support\PayOrder\PayOrderHelper;
use App\Support\Orders\RechargeOrderHelper;
use App\Events\RechargePaidEvent;

class RechargeOrderLogic extends Logic implements OrderItemInterface
{
    use VersionTrait;

    /**
     * 后置创建余额充值订单并支付
     *
     * @param array $paySubOrdersExtData
     * @param bool  $isInternalCall
     *
     * @return LogicResult
     * @throws Throwable
     */
    public static function PostCreateOrders(array $paySubOrdersExtData, bool $isInternalCall = false): LogicResult
    {
        if (empty($paySubOrdersExtData))
        {
            return self::Success();
        }

        $subOrderCheckRes = PaySubOrderExtHelper::CheckSubOrderExtValid($paySubOrdersExtData);
        if ($subOrderCheckRes->isFail())
        {
            return $subOrderCheckRes;
        }
        $payOrderCode     = $subOrderCheckRes->getData('payOrderCode');
        $paySubOrderCodes = $subOrderCheckRes->getData('paySubOrderCodes');
        $type             = $subOrderCheckRes->getData('type');
        $sheetCodes       = $subOrderCheckRes->getData('sheetCodes');
        $hospitalId       = $subOrderCheckRes->getData('hospitalId');

        if ($type != PaySubOrderTypeEnum::Recharge->value)
        {
            return self::Fail('非余额充值业务，无法创建充值订单', 501000);
        }

        $payOrderRes = PayOrderHelper::GetValidPayOrder(
            payOrderCode: $payOrderCode,
            hospitalId:   $hospitalId,
            useWritePdo:  $isInternalCall,
        );
        if ($payOrderRes->isFail())
        {
            return $payOrderRes;
        }
        $payOrder = $payOrderRes->getData();
        if ($payOrder['status'] != SheetStatusEnum::Paid->value)
        {
            return self::Fail('结算单未支付，无法创建充值订单', 501010);
        }

        $paySubOrders = PaySubOrderModel::GetPaySubOrderExtByCodes($paySubOrderCodes,
                                                                   $hospitalId,
                                                                   true,
                                                                   $isInternalCall);
        if (count($paySubOrders) != count($paySubOrderCodes))
        {
            return self::Fail('结算子单数据异常，无法创建充值订单', 501001);
        }
        if (array_any($paySubOrders, fn($value) => $value['status'] != SheetStatusEnum::Paid->value))
        {
            return self::Fail('结算子单未支付，无法创建充值订单', 501011);
        }

        $buildOrdersRes = self::PostBuildRechargeOrder(
            $paySubOrdersExtData,
            $paySubOrders,
            $sheetCodes,
            $hospitalId,
            $isInternalCall
        );

        if ($buildOrdersRes->isFail())
        {
            return $buildOrdersRes;
        }

        $ordersData   = $buildOrdersRes->getData('ordersData', []);
        $extOperation = $buildOrdersRes->getData('extOperation');

        try
        {
            DB::beginTransaction();

            //插入订单主信息
            foreach ($ordersData as $orderData)
            {
                $orderId = BalanceRechargeOrderModel::insertOne($orderData);
                if (empty($orderId))
                {
                    DB::rollBack();

                    return self::Fail('充值订单创建失败', 501490);
                }
            }

            //修改业务购买单状态为已支付
            if (is_callable($extOperation))
            {
                $extOperationResult = $extOperation();
                if ($extOperationResult->isFail())
                {
                    DB::rollBack();

                    return $extOperationResult;
                }
            }

            //修改pay_sub_order_ext状态
            $updateExtRes = PaySubOrderExtModel::on()
                                               ->whereIn('pay_sub_order_code', $paySubOrderCodes)
                                               ->where('process_status',
                                                       PaySubOrderExtProcessStatusEnum::Unprocessed->value)
                                               ->update([
                                                            'process_status' => PaySubOrderExtProcessStatusEnum::Processed->value,
                                                        ]);
            if (empty($updateExtRes) || $updateExtRes != count($paySubOrderCodes))
            {
                DB::rollBack();

                return self::Fail('充值订单更新结算子单失败', 501496);
            }

            DB::commit();
        } catch (Throwable $throwable)
        {
            DB::rollBack();

            Log::error(__CLASS__ . '::' . __METHOD__ . ' 创建充值订单异常', [
                'code'    => $throwable->getCode(),
                'message' => $throwable->getMessage(),
                'file'    => $throwable->getFile(),
                'line'    => $throwable->getLine(),
                'trace'   => $throwable->getTraceAsString(),
            ]);

            return self::Fail('创建充值订单异常', 501499);
        }

        return self::Success();
    }

    /**
     * 后置创建余额充值订单并支付
     *
     * 创建余额充值订单
     * 创建充值记录
     * 会员账户充值
     * 余额充值购买单支付状态修改
     * 余额充值购买单支付成功事件发布
     *
     * @param array $paySubOrdersExtData
     * @param array $paySubOrders
     * @param       $sheetCodes
     * @param int   $hospitalId
     * @param bool  $isInternalCall
     *
     * @return LogicResult
     */
    public static function PostBuildRechargeOrder(
        array $paySubOrdersExtData, array $paySubOrders, $sheetCodes, int $hospitalId, bool $isInternalCall = false
    ): LogicResult
    {
        if (empty($paySubOrdersExtData) || empty($paySubOrders) || empty($sheetCodes) || empty($hospitalId))
        {
            self::Fail('缺少必选参数，无法创建充值订单', 400);
        }
        if (count($paySubOrdersExtData) != count($paySubOrders) || count($paySubOrders) != count($sheetCodes))
        {
            return self::Fail('必选参数数据异常，无法创建充值订单', 400);
        }

        $sheets = BalanceRechargeSheetModel::GetSheetBySheetCodes($sheetCodes, $hospitalId, true, $isInternalCall);

        $sheetIds = array_column($sheets, 'id');

        // 批量获取明细
        $sheetItems = BalanceRechargeSheetItemModel::getData(
            where:    ['status' => 1],
            whereIn:  ['sheet_id' => $sheetIds],
            orderBys: ['id' => 'asc']
        );
        if (empty($sheetItems))
        {
            return self::Fail('充值购买单明细数据异常，无法创建充值订单', 400);
        }
        // 按充值购买单ID对详情进行分组
        $sheetGroupItems = collect($sheetItems)
            ->groupBy('sheet_id')
            ->toArray();

        $orderData         = [];
        $sheetIdToPaidInfo = [];
        foreach ($paySubOrdersExtData as $value)
        {
            $sheetCode       = $value['sheet_code'];
            $paySubOrderCode = $value['pay_sub_order_code'];

            $curSubOrder = $paySubOrders[$paySubOrderCode] ?? null;
            $curSheet    = $sheets[$sheetCode] ?? null;
            if (empty($curSubOrder))
            {
                return self::Fail('结算子单数据异常，无法创建充值订单', 400);
            }
            if (empty($curSheet))
            {
                return self::Fail('挂号单数据异常，无法创建充值订单', 400);
            }

            $curSheetOriginItems = $sheetGroupItems[$curSheet['id']] ?? [];
            if (empty($curSheetOriginItems) || count($curSheetOriginItems) != 1)
            {
                return self::Fail('单个充值购买单明细数据异常，无法创建充值订单', 400);
            }
            $curSheetOriginItem = current($curSheetOriginItems);

            $orderCode = generateBusinessCodeNumber(BusinessCodePrefixEnum::YECZDD);

            //用来更新充值购买单
            if (!isset($sheetIdToPaidInfo[$curSheet['id']]))
            {
                $sheetIdToPaidInfo[$curSheet['id']] = [
                    'sheet_code'          => $curSheet['sheet_code'],
                    'recharge_order_code' => $orderCode,
                    'hospital_id'         => $curSubOrder['hospital_id'],
                    'paid_at'             => $curSubOrder['paid_at'],
                ];
            }

            $tmpOrderData = [
                'pay_sub_order_code'     => $paySubOrderCode,
                'order_code'             => $orderCode,
                'org_id'                 => $curSubOrder['org_id'],
                'brand_id'               => $curSubOrder['brand_id'],
                'hospital_id'            => $curSubOrder['hospital_id'],
                'member_id'              => $curSubOrder['member_id'],
                'relation_id'            => $curSheet['id'],
                'relation_item_id'       => $curSheetOriginItem['id'],
                'activity_id'            => $curSheetOriginItem['activity_id'],
                'quantity'               => $curSheetOriginItem['quantity'],
                'balance_recharge'       => $curSheetOriginItem['balance_recharge'],
                'balance_gift'           => $curSheetOriginItem['balance_gift'],
                'sale_price'             => $curSubOrder['sale_price'],
                'reduce_price'           => $curSubOrder['reduce_price'],
                'deal_price'             => $curSubOrder['deal_price'],
                'coupon_price'           => $curSubOrder['coupon_price'],
                'cash_coupon_price'      => $curSubOrder['cash_coupon_price'],
                'discount_price'         => $curSubOrder['discount_price'],
                'third_party_price'      => $curSubOrder['third_party_price'],
                'balance_price'          => $curSubOrder['balance_price'],
                'deposit_price'          => $curSubOrder['deposit_price'],
                'balance_recharge_price' => $curSubOrder['balance_recharge_price'],
                'balance_gift_price'     => $curSubOrder['balance_gift_price'],
                'pay_price'              => $curSubOrder['pay_price'],
                'third_party_pay_mode'   => $curSubOrder['third_party_pay_mode'],
                'pay_channel'            => $curSubOrder['pay_channel'],
                'pay_mode'               => $curSubOrder['pay_mode'],
                'status'                 => SheetStatusEnum::Paid->value,
                'created_by'             => $curSubOrder['created_by'],
                'sold_by'                => $curSheet['sold_by'],
                'cashier_by'             => $curSubOrder['cashier_by'],
                'order_time'             => $curSubOrder['order_time'],
                'paid_at'                => $curSubOrder['paid_at'],
                'created_at'             => $curSubOrder['created_at'],
            ];

            $orderData[] = $tmpOrderData;
        }

        $extOperation = function () use ($sheetIdToPaidInfo) {
            try
            {
                DB::beginTransaction();

                //更新充值购买单支付状态
                foreach ($sheetIdToPaidInfo as $sheetId => $paidInfo)
                {
                    $res = BalanceRechargeSheetModel::on()
                                                    ->where('id', $sheetId)
                                                    ->where('status', '=', SheetStatusEnum::Paying->value)
                                                    ->update([
                                                                 'status'  => SheetStatusEnum::Paid->value,
                                                                 'paid_at' => $paidInfo['paid_at'],
                                                             ]);

                    if (empty($res))
                    {
                        DB::rollBack();

                        return self::Fail('更新挂号单支付状态失败', 500970);
                    }

                    //余额充值
                    $rechargeRes = MemberBalanceLogic::RechargeMemberBalance(0,
                                                                             $paidInfo['recharge_order_code'],
                                                                             $paidInfo['hospital_id']);
                    if ($rechargeRes->isFail())
                    {
                        DB::rollBack();

                        return $rechargeRes;
                    }

                    //发布事件
                    event(new RechargePaidEvent($paidInfo['sheet_code'], self::getVersion()));
                }

                DB::commit();

                return self::Success();
            } catch (Throwable $throwable)
            {
                DB::rollBack();

                Log::error(__CLASS__ . '::' . __METHOD__ . ' 更新充值购买单支付状态异常', [
                    'code'    => $throwable->getCode(),
                    'message' => $throwable->getMessage(),
                    'file'    => $throwable->getFile(),
                    'line'    => $throwable->getLine(),
                    'trace'   => $throwable->getTraceAsString(),
                ]);

                return self::Fail('更新充值购买单支付状态异常', 500971);
            }
        };

        return self::Success([
                                 'ordersData'   => $orderData,
                                 'extOperation' => $extOperation,
                             ]);
    }

    /**
     * 批量获取充值订单详情
     *
     * @param array    $paySubOrderCodes
     * @param array    $orderCodes
     * @param array    $orderIds
     * @param int|null $hospitalId
     * @param string|null $keyByCode
     *
     * @return array
     */
    public static function GetOrdersItemDetail(
        array $paySubOrderCodes = [], array $orderCodes = [], array $orderIds = [], ?int $hospitalId = null,
        ?string $keyByCode = null
    ): array
    {
        if (empty($paySubOrderCodes) && empty($orderCodes) && empty($orderIds))
        {
            return [];
        }

        $where   = [['status', '>', SheetStatusEnum::Cancelled->value]];
        $whereIn = [];
        if (!empty($paySubOrderCodes))
        {
            $whereIn['pay_sub_order_code'] = $paySubOrderCodes;
        }
        if (!empty($orderCodes))
        {
            $whereIn['order_code'] = $orderCodes;
        }
        if (!empty($orderIds))
        {
            $whereIn['id'] = $orderIds;
        }
        if (!empty($hospitalId))
        {
            $where[] = ['hospital_id', '=', $hospitalId];
        }

        $orders = BalanceRechargeOrderModel::getData(
            where:    $where,
            whereIn:  $whereIn,
            orderBys: ['id' => 'asc'],
        );
        if (empty($orders))
        {
            return [];
        }

        return RechargeOrderHelper::FormatOrderDetailsStructure($orders, $keyByCode);
    }
}
