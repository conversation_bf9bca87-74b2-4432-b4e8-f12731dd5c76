<?php

namespace App\Logics\V1\Order;

use Throwable;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Logics\Logic;
use App\Logics\LogicResult;
use App\Traits\VersionTrait;
use App\Logics\V1\Order\Interfaces\OrderItemInterface;
use App\Enums\BusinessCodePrefixEnum;
use App\Enums\PaySubOrderTypeEnum;
use App\Enums\SheetStatusEnum;
use App\Enums\ItemTypeEnum;
use App\Enums\PayStatusEnum;
use App\Enums\RegistrationStatusEnum;
use App\Enums\PaySubOrderExtProcessStatusEnum;
use App\Models\PaySubOrderModel;
use App\Models\PaySubOrderExtModel;
use App\Models\RegistrationsModel;
use App\Models\RecipeModel;
use App\Models\RecipeItemModel;
use App\Models\TestModel;
use App\Models\ImagesModel;
use App\Models\NurseModel;
use App\Models\MedicalOrderModel;
use App\Models\MedicalOrderItemModel;
use App\Models\MedicalOrderItemDetailModel;
use App\Support\PayOrder\PaySubOrderExtHelper;
use App\Support\PayOrder\PayOrderHelper;
use App\Support\Common\PriceSplitHelper;
use App\Support\Orders\MedicalOrderHelper;
use App\Events\RegistrationPaidEvent;
use App\Events\RecipePaidEvent;

class MedicalOrderLogic extends Logic implements OrderItemInterface
{
    use VersionTrait;

    /**
     * 后置创建医疗订单并支付
     *
     * 挂号单、处方单
     *
     * @param array $paySubOrdersExtData
     * @param bool  $isInternalCall
     *
     * @return LogicResult
     * @throws Throwable
     */
    public static function PostCreateOrders(array $paySubOrdersExtData, bool $isInternalCall = false): LogicResult
    {
        if (empty($paySubOrdersExtData))
        {
            return self::Success();
        }

        $subOrderCheckRes = PaySubOrderExtHelper::CheckSubOrderExtValid($paySubOrdersExtData);
        if ($subOrderCheckRes->isFail())
        {
            return $subOrderCheckRes;
        }
        $payOrderCode     = $subOrderCheckRes->getData('payOrderCode');
        $paySubOrderCodes = $subOrderCheckRes->getData('paySubOrderCodes');
        $type             = $subOrderCheckRes->getData('type');
        $sheetCodes       = $subOrderCheckRes->getData('sheetCodes');
        $hospitalId       = $subOrderCheckRes->getData('hospitalId');

        if (!PaySubOrderTypeEnum::IsMedicalOrder($type))
        {
            return self::Fail('非医疗业务，无法创建医疗订单', 501000);
        }

        $payOrderRes = PayOrderHelper::GetValidPayOrder(
            payOrderCode: $payOrderCode,
            hospitalId:   $hospitalId,
            useWritePdo:  $isInternalCall,
        );
        if ($payOrderRes->isFail())
        {
            return $payOrderRes;
        }
        $payOrder = $payOrderRes->getData();
        if ($payOrder['status'] != SheetStatusEnum::Paid->value)
        {
            return self::Fail('结算单未支付，无法创建医疗订单', 501010);
        }

        $paySubOrders = PaySubOrderModel::GetPaySubOrderExtByCodes($paySubOrderCodes,
                                                                   $hospitalId,
                                                                   true,
                                                                   $isInternalCall);
        if (count($paySubOrders) != count($paySubOrderCodes))
        {
            return self::Fail('结算子单数据异常，无法创建医疗订单', 501001);
        }
        if (array_any($paySubOrders, fn($value) => $value['status'] != SheetStatusEnum::Paid->value))
        {
            return self::Fail('结算子单未支付，无法创建医疗订单', 501011);
        }

        $buildOrdersRes = match ($type)
        {
            PaySubOrderTypeEnum::Registration->value => self::PostBuildRegistrationOrder(
                $paySubOrdersExtData,
                $paySubOrders,
                $sheetCodes,
                $hospitalId,
                $isInternalCall
            ),
            PaySubOrderTypeEnum::Recipe->value => self::PostBuildRecipeOrder(
                $paySubOrdersExtData,
                $paySubOrders,
                $sheetCodes,
                $hospitalId,
                $isInternalCall
            ),
        };

        if ($buildOrdersRes->isFail())
        {
            return $buildOrdersRes;
        }

        $ordersData   = $buildOrdersRes->getData('ordersData', []);
        $extOperation = $buildOrdersRes->getData('extOperation');

        try
        {
            DB::beginTransaction();

            //插入订单主信息
            foreach ($ordersData as $orderData)
            {
                $orderItemData = $orderData['items'] ?? [];
                if (isset($orderData['items']))
                {
                    unset($orderData['items']);
                }

                $orderId = MedicalOrderModel::insertOne($orderData);
                if (empty($orderId))
                {
                    DB::rollBack();

                    return self::Fail('医疗订单创建失败', 501490);
                }

                //循环插入订单商品
                foreach ($orderItemData as $itemData)
                {
                    $orderItemDetailData = $itemData['details'] ?? [];
                    if (isset($itemData['details']))
                    {
                        unset($itemData['details']);
                    }

                    $itemData['order_id'] = $orderId;

                    $orderItemId = MedicalOrderItemModel::insertOne($itemData);
                    if (empty($orderItemId))
                    {
                        DB::rollBack();

                        return self::Fail('医疗订单商品创建失败', 501491);
                    }

                    //如果存在订单商品详情，则批量插入订单商品详情
                    if (!empty($orderItemDetailData))
                    {
                        foreach ($orderItemDetailData as $key => $detailData)
                        {
                            $orderItemDetailData[$key]['order_id']      = $orderId;
                            $orderItemDetailData[$key]['order_item_id'] = $orderItemId;
                        }

                        MedicalOrderItemDetailModel::insert($orderItemDetailData);
                    }
                }
            }

            //修改业务购买单状态为已支付
            if (is_callable($extOperation))
            {
                $extOperationResult = $extOperation();
                if ($extOperationResult->isFail())
                {
                    DB::rollBack();

                    return $extOperationResult;
                }
            }


            //修改pay_sub_order_ext状态
            $updateExtRes = PaySubOrderExtModel::on()
                                               ->whereIn('pay_sub_order_code', $paySubOrderCodes)
                                               ->where('process_status',
                                                       PaySubOrderExtProcessStatusEnum::Unprocessed->value)
                                               ->update([
                                                            'process_status' => PaySubOrderExtProcessStatusEnum::Processed->value,
                                                        ]);
            if (empty($updateExtRes) || $updateExtRes != count($paySubOrderCodes))
            {
                DB::rollBack();

                return self::Fail('医疗订单更新结算子单失败', 501496);
            }

            DB::commit();
        } catch (Throwable $throwable)
        {
            DB::rollBack();

            Log::error(__CLASS__ . '::' . __METHOD__ . ' 创建医疗订单异常', [
                'code'    => $throwable->getCode(),
                'message' => $throwable->getMessage(),
                'file'    => $throwable->getFile(),
                'line'    => $throwable->getLine(),
                'trace'   => $throwable->getTraceAsString(),
            ]);

            return self::Fail('创建医疗订单异常', 501499);
        }

        return self::Success();
    }

    /**
     * 后置创建挂号订单并支付
     *
     * 创建挂号医疗订单、订单商品
     * 挂号单支付状态修改
     * 挂号单支付成功事件发布
     *
     * @param array $paySubOrdersExtData
     * @param array $paySubOrders
     * @param       $sheetCodes
     * @param int   $hospitalId
     * @param bool  $isInternalCall
     *
     * @return LogicResult
     */
    public static function PostBuildRegistrationOrder(
        array $paySubOrdersExtData, array $paySubOrders, $sheetCodes, int $hospitalId, bool $isInternalCall = false
    ): LogicResult
    {
        if (empty($paySubOrdersExtData) || empty($paySubOrders) || empty($sheetCodes) || empty($hospitalId))
        {
            self::Fail('缺少必选参数，无法创建挂号订单', 400);
        }
        if (count($paySubOrdersExtData) != count($paySubOrders) || count($paySubOrders) != count($sheetCodes))
        {
            return self::Fail('必选参数数据异常，无法创建挂号订单', 400);
        }

        $sheets = RegistrationsModel::GetRegistrationByRecipeCodes($sheetCodes, $hospitalId, true, $isInternalCall);

        $orderData                = [];
        $registrationIdToPaidInfo = [];
        foreach ($paySubOrdersExtData as $value)
        {
            $sheetCode       = $value['sheet_code'];
            $paySubOrderCode = $value['pay_sub_order_code'];

            $curSubOrder = $paySubOrders[$paySubOrderCode] ?? null;
            $curSheet    = $sheets[$sheetCode] ?? null;
            if (empty($curSubOrder))
            {
                return self::Fail('结算子单数据异常，无法创建挂号订单', 400);
            }
            if (empty($curSheet))
            {
                return self::Fail('挂号单数据异常，无法创建挂号订单', 400);
            }

            $orderCode = generateBusinessCodeNumber(BusinessCodePrefixEnum::YLDD);

            //用来更新挂号单
            if (!isset($registrationIdToPaidInfo[$curSheet['id']]))
            {
                $registrationIdToPaidInfo[$curSheet['id']] = [
                    'registration_code' => $curSheet['registration_code'],
                    'order_code'        => $orderCode,
                    'paid_at'           => $curSubOrder['paid_at'],
                ];
            }

            $tmpOrderData = [
                'pay_sub_order_code'     => $paySubOrderCode,
                'order_code'             => $orderCode,
                'org_id'                 => $curSubOrder['org_id'],
                'brand_id'               => $curSubOrder['brand_id'],
                'hospital_id'            => $curSubOrder['hospital_id'],
                'member_id'              => $curSubOrder['member_id'],
                'pet_id'                 => $curSheet['pet_id'],
                'type'                   => PaySubOrderTypeEnum::Registration->value,
                'relation_id'            => $curSheet['id'],
                'sale_price'             => $curSubOrder['sale_price'],
                'reduce_price'           => $curSubOrder['reduce_price'],
                'deal_price'             => $curSubOrder['deal_price'],
                'coupon_price'           => $curSubOrder['coupon_price'],
                'cash_coupon_price'      => $curSubOrder['cash_coupon_price'],
                'discount_price'         => $curSubOrder['discount_price'],
                'third_party_price'      => $curSubOrder['third_party_price'],
                'balance_price'          => $curSubOrder['balance_price'],
                'deposit_price'          => $curSubOrder['deposit_price'],
                'balance_recharge_price' => $curSubOrder['balance_recharge_price'],
                'balance_gift_price'     => $curSubOrder['balance_gift_price'],
                'pay_price'              => $curSubOrder['pay_price'],
                'third_party_pay_mode'   => $curSubOrder['third_party_pay_mode'],
                'pay_channel'            => $curSubOrder['pay_channel'],
                'pay_mode'               => $curSubOrder['pay_mode'],
                'status'                 => SheetStatusEnum::Paid->value,
                'created_by'             => $curSubOrder['created_by'],
                'sold_by'                => $curSheet['created_by'],//挂号人
                'cashier_by'             => $curSubOrder['cashier_by'],
                'order_time'             => $curSubOrder['order_time'],
                'paid_at'                => $curSubOrder['paid_at'],
                'created_at'             => $curSubOrder['created_at'],
            ];

            $orderItemData   = [];
            $orderItemData[] = [
                'uid'                    => generateUUID(),
                'order_id'               => 0,
                'hospital_id'            => $curSubOrder['hospital_id'],
                'relation_id'            => $curSheet['id'],
                'item_type'              => ItemTypeEnum::Registration->value,
                'item_suit_id'           => 0,
                'item_id'                => $curSheet['item_id'],
                'unit_type'              => 0,
                'quantity'               => 1,
                'sale_price'             => $curSheet['sale_price'],
                'reduce_price'           => '0.00',//TODO:有商品活动时，需要拆分活动优惠
                'deal_price'             => $curSheet['sale_price'],
                'coupon_price'           => '0.00',//TODO:有代金券时，需要拆分代金券抵扣
                'cash_coupon_price'      => '0.00',
                'discount_price'         => '0.00',//后置均摊金额
                'third_party_price'      => '0.00',//后置均摊金额
                'balance_price'          => '0.00',//下单时后置均摊金额
                'deposit_price'          => '0.00',//后置均摊金额
                'balance_recharge_price' => '0.00',//下单时后置均摊金额
                'balance_gift_price'     => '0.00',//下单时后置均摊金额
                'pay_price'              => $curSheet['sale_price'],//下单时后置修改
                'suit_group'             => 0,
                'is_suit'                => 0,
                'suit_unique_uid'        => '',
                'status'                 => 1,
                'created_by'             => $curSubOrder['created_by'],
                'created_at'             => $curSubOrder['created_at'],
            ];

            if (bccomp($curSubOrder['discount_price'], 0, 2) != 0)
            {
                $splitRes = PriceSplitHelper::CommonSplitPrice($orderItemData,
                                                               'discount_price',
                                                               $curSubOrder['discount_price']);
                if ($splitRes->isFail())
                {
                    return $splitRes;
                }

                $orderItemData = $splitRes->getData();
            }
            if (bccomp($curSubOrder['third_party_price'], 0, 2) != 0)
            {
                $splitRes = PriceSplitHelper::CommonSplitPrice($orderItemData,
                                                               'third_party_price',
                                                               $curSubOrder['third_party_price']);
                if ($splitRes->isFail())
                {
                    return $splitRes;
                }

                $orderItemData = $splitRes->getData();
            }
            if (bccomp($curSubOrder['balance_recharge_price'], 0, 2) != 0)
            {
                $splitRes = PriceSplitHelper::CommonSplitPrice($orderItemData,
                                                               'balance_recharge_price',
                                                               $curSubOrder['balance_recharge_price']);
                if ($splitRes->isFail())
                {
                    return $splitRes;
                }

                $orderItemData = $splitRes->getData();
            }
            if (bccomp($curSubOrder['balance_price'], 0, 2) != 0)
            {
                $splitRes = PriceSplitHelper::CommonSplitPrice($orderItemData,
                                                               'balance_price',
                                                               $curSubOrder['balance_price']);
                if ($splitRes->isFail())
                {
                    return $splitRes;
                }

                $orderItemData = $splitRes->getData();
            }

            $tmpOrderData['items'] = $orderItemData;

            $orderData[] = $tmpOrderData;
        }

        $extOperation = function () use ($registrationIdToPaidInfo) {
            try
            {
                DB::beginTransaction();

                //更新挂号支付状态
                foreach ($registrationIdToPaidInfo as $registrationId => $paidInfo)
                {
                    $res = RegistrationsModel::on()
                                             ->where('id', $registrationId)
                                             ->where([
                                                         ['pay_status', '!=', PayStatusEnum::Paid->value],
                                                         ['status', '>', RegistrationStatusEnum::Invalid->value],
                                                     ])
                                             ->update([
                                                          'order_code' => $paidInfo['order_code'],
                                                          'pay_status' => PayStatusEnum::Paid->value,
                                                          'paid_at'    => $paidInfo['paid_at'],
                                                      ]);

                    if (empty($res))
                    {
                        DB::rollBack();

                        return self::Fail('更新挂号单支付状态失败', 500970);
                    }

                    //发布事件
                    event(new RegistrationPaidEvent($paidInfo['registration_code'], self::getVersion()));
                }

                DB::commit();

                return self::Success();
            } catch (Throwable $throwable)
            {
                DB::rollBack();

                Log::error(__CLASS__ . '::' . __METHOD__ . ' 更新挂号单支付状态异常', [
                    'code'    => $throwable->getCode(),
                    'message' => $throwable->getMessage(),
                    'file'    => $throwable->getFile(),
                    'line'    => $throwable->getLine(),
                    'trace'   => $throwable->getTraceAsString(),
                ]);

                return self::Fail('更新挂号单支付状态异常', 500971);
            }
        };

        return self::Success([
                                 'ordersData'   => $orderData,
                                 'extOperation' => $extOperation,
                             ]);
    }

    /**
     * 后置创建处方订单并支付
     *
     * 创建处方医疗订单、订单商品、订单商品详情
     * 处方单支付状态修改
     * 处方单化验、影像、处置支付状态修改
     * 处方单支付成功事件发布
     *
     * @param array $paySubOrdersExtData
     * @param array $paySubOrders
     * @param       $sheetCodes
     * @param int   $hospitalId
     * @param bool  $isInternalCall
     *
     * @return LogicResult
     */
    public static function PostBuildRecipeOrder(
        array $paySubOrdersExtData, array $paySubOrders, $sheetCodes, int $hospitalId, bool $isInternalCall = false
    ): LogicResult
    {
        if (empty($paySubOrdersExtData) || empty($paySubOrders) || empty($sheetCodes) || empty($hospitalId))
        {
            self::Fail('缺少必选参数，无法创建处方订单', 400);
        }
        if (count($paySubOrdersExtData) != count($paySubOrders) || count($paySubOrders) != count($sheetCodes))
        {
            return self::Fail('必选参数数据异常，无法创建处方订单', 400);
        }

        $sheets = RecipeModel::GetRecipeByRecipeCodes($sheetCodes, $hospitalId, true, true);
        if (count($sheets) != count($sheetCodes))
        {
            return self::Fail('处方单数据异常，无法创建处方订单', 400);
        }
        $recipeIds = array_column($sheets, 'id');

        // 批量获取明细
        $sheetItems = RecipeItemModel::getData(
            where:    ['status' => 1],
            whereIn:  ['recipe_id' => $recipeIds],
            orderBys: ['id' => 'asc']
        );
        if (empty($sheetItems))
        {
            return self::Fail('处方明细数据异常，无法创建处方订单', 400);
        }
        // 按处方对详情进行分组
        $sheetGroupItems = collect($sheetItems)
            ->groupBy('recipe_id')
            ->toArray();

        $orderData          = [];
        $recipeIdToPaidInfo = [];
        foreach ($paySubOrdersExtData as $value)
        {
            $sheetCode       = $value['sheet_code'];
            $paySubOrderCode = $value['pay_sub_order_code'];

            $curSubOrder = $paySubOrders[$paySubOrderCode] ?? null;
            $curSheet    = $sheets[$sheetCode] ?? null;
            if (empty($curSubOrder))
            {
                return self::Fail('结算子单数据异常，无法创建处方订单', 400);
            }
            if (empty($curSheet))
            {
                return self::Fail('处方单数据异常，无法创建处方订单', 400);
            }

            $orderCode = generateBusinessCodeNumber(BusinessCodePrefixEnum::YLDD);

            //用来更新处方单
            if (!isset($recipeIdToPaidInfo[$curSheet['id']]))
            {
                $recipeIdToPaidInfo[$curSheet['id']] = [
                    'recipe_code' => $curSheet['recipe_code'],
                    'paid_at'     => $curSubOrder['paid_at'],
                ];
            }

            $tmpOrderData = [
                'pay_sub_order_code'     => $paySubOrderCode,
                'order_code'             => $orderCode,
                'org_id'                 => $curSubOrder['org_id'],
                'brand_id'               => $curSubOrder['brand_id'],
                'hospital_id'            => $curSubOrder['hospital_id'],
                'member_id'              => $curSubOrder['member_id'],
                'pet_id'                 => $curSheet['pet_id'],
                'type'                   => PaySubOrderTypeEnum::Recipe->value,
                'relation_id'            => $curSheet['id'],
                'sale_price'             => $curSubOrder['sale_price'],
                'reduce_price'           => $curSubOrder['reduce_price'],
                'deal_price'             => $curSubOrder['deal_price'],
                'coupon_price'           => $curSubOrder['coupon_price'],
                'cash_coupon_price'      => $curSubOrder['cash_coupon_price'],
                'discount_price'         => $curSubOrder['discount_price'],
                'third_party_price'      => $curSubOrder['third_party_price'],
                'balance_price'          => $curSubOrder['balance_price'],
                'deposit_price'          => $curSubOrder['deposit_price'],
                'balance_recharge_price' => $curSubOrder['balance_recharge_price'],
                'balance_gift_price'     => $curSubOrder['balance_gift_price'],
                'pay_price'              => $curSubOrder['pay_price'],
                'third_party_pay_mode'   => $curSubOrder['third_party_pay_mode'],
                'pay_channel'            => $curSubOrder['pay_channel'],
                'pay_mode'               => $curSubOrder['pay_mode'],
                'status'                 => SheetStatusEnum::Paid->value,
                'created_by'             => $curSubOrder['created_by'],
                'sold_by'                => $curSheet['doctor_id'],//处方开具医生
                'cashier_by'             => $curSubOrder['cashier_by'],
                'order_time'             => $curSubOrder['order_time'],
                'paid_at'                => $curSubOrder['paid_at'],
                'created_at'             => $curSubOrder['created_at'],
            ];

            $curSheetOriginItems = $sheetGroupItems[$curSheet['id']] ?? [];
            if (empty($curSheetOriginItems))
            {
                return self::Fail('单个处方明细数据异常，无法创建处方订单', 400);
            }

            //扩展字段的详情备用
            $paySubOrdersExtItems     = array_column($value['items'] ?? [], null, 'uid');
            $paySubOrdersExtItemMores = array_column(
                array_reduce(
                    $value['items'] ?? [],
                    fn($carry, $item) => array_merge($carry,
                                                     $item['suitItems'] ?? []),
                    []
                ),
                null,
                'uid');

            $orderItemData       = [];
            $orderItemDetailData = [];

            foreach ($curSheetOriginItems as $item)
            {
                // 判断是组合子项目还是普通项目
                $isSuitItem = ($item['suit_unique_uid'] != '' && $item['is_suit'] == 0);

                $paySubOrdersExtItem = ($isSuitItem
                    ? ($paySubOrdersExtItemMores[$item['uid']] ?? null)
                    : ($paySubOrdersExtItems[$item['uid']] ?? null));

                if (empty($paySubOrdersExtItem))
                {
                    return self::Fail('处方单明细扩展数据异常，无法创建处方订单', 400);
                }

                $isPreciseMetering = ($paySubOrdersExtItem['itemInfo']['isPreciseMetering'] ?? false) ? 1 : 0;

                // 默认拆分 1 份
                $splitItemNum = 1;
                if ($isPreciseMetering == 0 && is_numeric($item['quantity']) && (int) $item['quantity'] == $item['quantity'])
                {
                    $splitItemNum = $item['quantity'];
                }

                //数量按份数拆分
                $perQuantity = bcdiv($item['quantity'], $splitItemNum, 2);
                //$item['price']为单价，非精确计量的直接用，精确计量的需要用小计作为单价，因为精确计量的需要看作是一个整体
                $perPrice = $isPreciseMetering ? bcmul($item['price'], $item['quantity'], 2) : $item['price'];

                for ($i = 0; $i < $splitItemNum; $i ++)
                {
                    if ($isSuitItem)
                    {
                        // 组合子项目明细
                        $orderItemDetailData[$item['suit_unique_uid']][] = [
                            'uid'                    => generateUUID(),
                            'order_id'               => 0,
                            'order_item_id'          => 0,
                            'hospital_id'            => $curSubOrder['hospital_id'],
                            'relation_id'            => $item['id'],
                            'item_type'              => PaySubOrderExtHelper::DecideSubOrderExtOriginItemType($curSubOrder['type'],
                                                                                                              $item),
                            'item_suit_id'           => $item['item_suit_id'],
                            'item_id'                => $item['item_id'],
                            'unit_type'              => $item['unit_type'],
                            'is_precise_metering'    => $isPreciseMetering,
                            'quantity'               => $perQuantity,
                            'sale_price'             => $perPrice,
                            'reduce_price'           => '0.00',
                            'deal_price'             => $perPrice,
                            'coupon_price'           => '0.00',
                            'cash_coupon_price'      => '0.00',
                            'discount_price'         => '0.00',
                            'third_party_price'      => '0.00',
                            'balance_price'          => '0.00',
                            'deposit_price'          => '0.00',
                            'balance_recharge_price' => '0.00',
                            'balance_gift_price'     => '0.00',
                            'pay_price'              => $perPrice,
                            'suit_unique_uid'        => $item['suit_unique_uid'],
                            'status'                 => 1,
                            'created_at'             => $curSubOrder['created_at'],
                        ];
                    }
                    else
                    {
                        // 普通项目
                        $orderItemData[] = [
                            'uid'                    => generateUUID(),
                            'order_id'               => 0,
                            'hospital_id'            => $curSubOrder['hospital_id'],
                            'relation_id'            => $item['id'],
                            'item_type'              => PaySubOrderExtHelper::DecideSubOrderExtOriginItemType($curSubOrder['type'],
                                                                                                              $item),
                            'item_suit_id'           => $item['item_suit_id'],
                            'item_id'                => $item['item_id'],
                            'unit_type'              => $item['unit_type'],
                            'is_precise_metering'    => $isPreciseMetering,
                            'quantity'               => $perQuantity,
                            'sale_price'             => $perPrice,
                            'reduce_price'           => '0.00',
                            'deal_price'             => $perPrice,
                            'coupon_price'           => '0.00',
                            'cash_coupon_price'      => '0.00',
                            'discount_price'         => '0.00',
                            'third_party_price'      => '0.00',
                            'balance_price'          => '0.00',
                            'deposit_price'          => '0.00',
                            'balance_recharge_price' => '0.00',
                            'balance_gift_price'     => '0.00',
                            'pay_price'              => $perPrice,
                            'suit_group'             => $item['suit_group'],
                            'is_suit'                => $item['is_suit'],
                            'suit_unique_uid'        => $item['suit_unique_uid'],
                            'status'                 => 1,
                            'created_by'             => $curSubOrder['created_by'],
                            'created_at'             => $curSubOrder['created_at'],
                        ];
                    }
                }
            }

            if (bccomp($curSubOrder['discount_price'], 0, 2) != 0)
            {
                $splitRes = PriceSplitHelper::CommonSplitPrice($orderItemData,
                                                               'discount_price',
                                                               $curSubOrder['discount_price']);
                if ($splitRes->isFail())
                {
                    return $splitRes;
                }

                $orderItemData = $splitRes->getData();
            }
            if (bccomp($curSubOrder['third_party_price'], 0, 2) != 0)
            {
                $splitRes = PriceSplitHelper::CommonSplitPrice($orderItemData,
                                                               'third_party_price',
                                                               $curSubOrder['third_party_price']);
                if ($splitRes->isFail())
                {
                    return $splitRes;
                }

                $orderItemData = $splitRes->getData();
            }
            if (bccomp($curSubOrder['balance_recharge_price'], 0, 2) != 0)
            {
                $splitRes = PriceSplitHelper::CommonSplitPrice($orderItemData,
                                                               'balance_recharge_price',
                                                               $curSubOrder['balance_recharge_price']);
                if ($splitRes->isFail())
                {
                    return $splitRes;
                }

                $orderItemData = $splitRes->getData();
            }
            if (bccomp($curSubOrder['balance_price'], 0, 2) != 0)
            {
                $splitRes = PriceSplitHelper::CommonSplitPrice($orderItemData,
                                                               'balance_price',
                                                               $curSubOrder['balance_price']);
                if ($splitRes->isFail())
                {
                    return $splitRes;
                }

                $orderItemData = $splitRes->getData();
            }

            //再组合商品详情到商品
            foreach ($orderItemData as $key => $value)
            {
                if ($value['is_suit'] == 1 && isset($orderItemDetailData[$value['suit_unique_uid']]))
                {
                    $orderItemData[$key]['details'] = $orderItemDetailData[$value['suit_unique_uid']];
                }
            }

            $tmpOrderData['items'] = $orderItemData;

            $orderData[] = $tmpOrderData;

        }

        $extOperation = function () use ($recipeIdToPaidInfo) {
            try
            {
                DB::beginTransaction();

                //批量更新处方支付状态
                $recipePaidInfo = current($recipeIdToPaidInfo);
                $recipeRes      = RecipeModel::on()
                                             ->whereIn('id', array_keys($recipeIdToPaidInfo))
                                             ->where([
                                                         ['is_paid', '!=', PayStatusEnum::Paid->value],
                                                         ['status', '=', '1'],
                                                     ])
                                             ->update([
                                                          'is_paid' => PayStatusEnum::Paid->value,
                                                          'paid_at' => $recipePaidInfo['paid_at'],
                                                      ]);

                if (empty($recipeRes) || $recipeRes != count(array_keys($recipeIdToPaidInfo)))
                {
                    DB::rollBack();

                    return self::Fail('更新处方支付状态失败', 500970);
                }

                //批量更新化验
                TestModel::on()
                         ->whereIn('recipe_id', array_keys($recipeIdToPaidInfo))
                         ->where([
                                     ['is_paid', '!=', PayStatusEnum::Paid->value],
                                     ['status', '=', '1'],
                                 ])
                         ->update([
                                      'is_paid' => PayStatusEnum::Paid->value,
                                  ]);

                //批量更新影像
                ImagesModel::on()
                           ->whereIn('recipe_id', array_keys($recipeIdToPaidInfo))
                           ->where([
                                       ['is_paid', '!=', PayStatusEnum::Paid->value],
                                       ['status', '=', '1'],
                                   ])
                           ->update([
                                        'is_paid' => PayStatusEnum::Paid->value,
                                    ]);

                //批量更新处置
                NurseModel::on()
                          ->whereIn('recipe_id', array_keys($recipeIdToPaidInfo))
                          ->where([
                                      ['is_paid', '!=', PayStatusEnum::Paid->value],
                                      ['status', '=', '1'],
                                  ])
                          ->update([
                                       'is_paid' => PayStatusEnum::Paid->value,
                                   ]);


                //发布事件
                foreach ($recipeIdToPaidInfo as $paidInfo)
                {
                    event(new RecipePaidEvent($paidInfo['recipe_code'], self::getVersion()));
                }

                DB::commit();

                return self::Success();
            } catch (Throwable $throwable)
            {
                DB::rollBack();

                Log::error(__CLASS__ . '::' . __METHOD__ . ' 更新处方单支付状态异常', [
                    'code'    => $throwable->getCode(),
                    'message' => $throwable->getMessage(),
                    'file'    => $throwable->getFile(),
                    'line'    => $throwable->getLine(),
                    'trace'   => $throwable->getTraceAsString(),
                ]);

                return self::Fail('更新处方单支付状态异常', 500971);
            }
        };

        return self::Success([
                                 'ordersData'   => $orderData,
                                 'extOperation' => $extOperation,
                             ]);
    }

    /**
     * 批量获取医疗订单详情
     *
     * @param array    $paySubOrderCodes
     * @param array    $orderCodes
     * @param array    $orderIds
     * @param int|null $hospitalId
     * @param string|null $keyByCode
     *
     * @return array
     */
    public static function GetOrdersItemDetail(
        array $paySubOrderCodes = [], array $orderCodes = [], array $orderIds = [], ?int $hospitalId = null,
        ?string $keyByCode = null
    ): array
    {
        if (empty($paySubOrderCodes) && empty($orderCodes) && empty($orderIds))
        {
            return [];
        }

        $where   = [['status', '>', SheetStatusEnum::Cancelled->value]];
        $whereIn = [];
        if (!empty($paySubOrderCodes))
        {
            $whereIn['pay_sub_order_code'] = $paySubOrderCodes;
        }
        if (!empty($orderCodes))
        {
            $whereIn['order_code'] = $orderCodes;
        }
        if (!empty($orderIds))
        {
            $whereIn['id'] = $orderIds;
        }
        if (!empty($hospitalId))
        {
            $where[] = ['hospital_id', '=', $hospitalId];
        }

        $orders = MedicalOrderModel::getData(
            where:    $where,
            whereIn:  $whereIn,
            orderBys: ['id' => 'asc'],
        );
        if (empty($orders))
        {
            return [];
        }

        $orderIds = array_column($orders, 'id');

        $orderItems = MedicalOrderItemModel::getData(
            where:    ['status' => 1],
            whereIn:  ['order_id' => $orderIds],
            orderBys: ['id' => 'asc']
        );
        if (empty($orderItems))
        {
            return [];
        }

        $orderItemDetails = MedicalOrderItemDetailModel::getData(
            where:    ['status' => 1],
            whereIn:  ['order_id' => $orderIds],
            orderBys: ['id' => 'asc']
        );

        return MedicalOrderHelper::FormatOrderDetailsStructure($orders, $orderItems, $orderItemDetails, $keyByCode);
    }
}
