<?php

namespace App\Logics\V1\Order;

use Throwable;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Logics\Logic;
use App\Logics\LogicResult;
use App\Traits\VersionTrait;
use App\Logics\V1\Order\Interfaces\OrderItemInterface;
use App\Enums\BusinessCodePrefixEnum;
use App\Enums\PaySubOrderTypeEnum;
use App\Enums\SheetStatusEnum;
use App\Enums\PaySubOrderExtProcessStatusEnum;
use App\Models\PaySubOrderModel;
use App\Models\PaySubOrderExtModel;
use App\Models\RetailSheetModel;
use App\Models\RetailSheetItemModel;
use App\Models\RetailOrderModel;
use App\Models\RetailOrderItemModel;
use App\Support\PayOrder\PaySubOrderExtHelper;
use App\Support\PayOrder\PayOrderHelper;
use App\Support\Common\PriceSplitHelper;
use App\Support\Orders\RetailOrderHelper;
use App\Events\RetailPaidEvent;

class RetailOrderLogic extends Logic implements OrderItemInterface
{
    use VersionTrait;

    /**
     * 后置创建零售订单并支付
     *
     * @param array $paySubOrdersExtData
     * @param bool  $isInternalCall
     *
     * @return LogicResult
     * @throws Throwable
     */
    public static function PostCreateOrders(array $paySubOrdersExtData, bool $isInternalCall = false): LogicResult
    {
        if (empty($paySubOrdersExtData))
        {
            return self::Success();
        }

        $subOrderCheckRes = PaySubOrderExtHelper::CheckSubOrderExtValid($paySubOrdersExtData);
        if ($subOrderCheckRes->isFail())
        {
            return $subOrderCheckRes;
        }
        $payOrderCode     = $subOrderCheckRes->getData('payOrderCode');
        $paySubOrderCodes = $subOrderCheckRes->getData('paySubOrderCodes');
        $type             = $subOrderCheckRes->getData('type');
        $sheetCodes       = $subOrderCheckRes->getData('sheetCodes');
        $hospitalId       = $subOrderCheckRes->getData('hospitalId');

        if ($type != PaySubOrderTypeEnum::Retail->value)
        {
            return self::Fail('非零售业务，无法创建零售订单', 501000);
        }

        $payOrderRes = PayOrderHelper::GetValidPayOrder(
            payOrderCode: $payOrderCode,
            hospitalId:   $hospitalId,
            useWritePdo:  $isInternalCall,
        );
        if ($payOrderRes->isFail())
        {
            return $payOrderRes;
        }
        $payOrder = $payOrderRes->getData();
        if ($payOrder['status'] != SheetStatusEnum::Paid->value)
        {
            return self::Fail('结算单未支付，无法创建零售订单', 501010);
        }

        $paySubOrders = PaySubOrderModel::GetPaySubOrderExtByCodes($paySubOrderCodes,
                                                                   $hospitalId,
                                                                   true,
                                                                   $isInternalCall);
        if (count($paySubOrders) != count($paySubOrderCodes))
        {
            return self::Fail('结算子单数据异常，无法创建零售订单', 501001);
        }
        if (array_any($paySubOrders, fn($value) => $value['status'] != SheetStatusEnum::Paid->value))
        {
            return self::Fail('结算子单未支付，无法创建零售订单', 501011);
        }

        $buildOrdersRes = self::PostBuildRetailOrder(
            $paySubOrdersExtData,
            $paySubOrders,
            $sheetCodes,
            $hospitalId,
            $isInternalCall
        );

        if ($buildOrdersRes->isFail())
        {
            return $buildOrdersRes;
        }

        $ordersData   = $buildOrdersRes->getData('ordersData', []);
        $extOperation = $buildOrdersRes->getData('extOperation');

        try
        {
            DB::beginTransaction();

            //插入订单主信息
            foreach ($ordersData as $orderData)
            {
                $orderItemData = $orderData['items'] ?? [];
                if (isset($orderData['items']))
                {
                    unset($orderData['items']);
                }

                $orderId = RetailOrderModel::insertOne($orderData);
                if (empty($orderId))
                {
                    DB::rollBack();

                    return self::Fail('零售订单创建失败', 501490);
                }

                //批量插入订单商品
                foreach ($orderItemData as $key => $itemData)
                {
                    $orderItemData[$key]['order_id'] = $orderId;
                }

                RetailOrderItemModel::insert($orderItemData);
            }

            //修改业务购买单状态为已支付
            if (is_callable($extOperation))
            {
                $extOperationResult = $extOperation();
                if ($extOperationResult->isFail())
                {
                    DB::rollBack();

                    return $extOperationResult;
                }
            }


            //修改pay_sub_order_ext状态
            $updateExtRes = PaySubOrderExtModel::on()
                                               ->whereIn('pay_sub_order_code', $paySubOrderCodes)
                                               ->where('process_status',
                                                       PaySubOrderExtProcessStatusEnum::Unprocessed->value)
                                               ->update([
                                                            'process_status' => PaySubOrderExtProcessStatusEnum::Processed->value,
                                                        ]);
            if (empty($updateExtRes) || $updateExtRes != count($paySubOrderCodes))
            {
                DB::rollBack();

                return self::Fail('零售订单更新结算子单失败', 501496);
            }

            DB::commit();
        } catch (Throwable $throwable)
        {
            DB::rollBack();

            Log::error(__CLASS__ . '::' . __METHOD__ . ' 创建零售订单异常', [
                'code'    => $throwable->getCode(),
                'message' => $throwable->getMessage(),
                'file'    => $throwable->getFile(),
                'line'    => $throwable->getLine(),
                'trace'   => $throwable->getTraceAsString(),
            ]);

            return self::Fail('创建零售订单异常', 501499);
        }

        return self::Success();
    }

    /**
     * 后置创建零售订单并支付
     *
     * 创建零售订单、订单商品
     * 零售购买单单支付状态修改
     * 零售购买单支付成功事件发布
     *
     * @param array $paySubOrdersExtData
     * @param array $paySubOrders
     * @param       $sheetCodes
     * @param int   $hospitalId
     * @param bool  $isInternalCall
     *
     * @return LogicResult
     */
    public static function PostBuildRetailOrder(
        array $paySubOrdersExtData, array $paySubOrders, $sheetCodes, int $hospitalId, bool $isInternalCall = false
    ): LogicResult
    {
        if (empty($paySubOrdersExtData) || empty($paySubOrders) || empty($sheetCodes) || empty($hospitalId))
        {
            self::Fail('缺少必选参数，无法创建零售订单', 400);
        }
        if (count($paySubOrdersExtData) != count($paySubOrders) || count($paySubOrders) != count($sheetCodes))
        {
            return self::Fail('必选参数数据异常，无法创建零售订单', 400);
        }

        $sheets = RetailSheetModel::GetSheetBySheetCodes($sheetCodes, $hospitalId, true, true);
        if (count($sheets) != count($sheetCodes))
        {
            return self::Fail('零售购买单数据异常，无法创建零售订单', 400);
        }
        $sheetIds = array_column($sheets, 'id');

        // 批量获取明细
        $sheetItems = RetailSheetItemModel::getData(
            where:    ['status' => 1],
            whereIn:  ['sheet_id' => $sheetIds],
            orderBys: ['id' => 'asc']
        );
        if (empty($sheetItems))
        {
            return self::Fail('零售购买单明细数据异常，无法创建零售订单', 400);
        }
        // 按零售购买单ID对详情进行分组
        $sheetGroupItems = collect($sheetItems)
            ->groupBy('sheet_id')
            ->toArray();

        $orderData         = [];
        $sheetIdToPaidInfo = [];
        foreach ($paySubOrdersExtData as $value)
        {
            $sheetCode       = $value['sheet_code'];
            $paySubOrderCode = $value['pay_sub_order_code'];

            $curSubOrder = $paySubOrders[$paySubOrderCode] ?? null;
            $curSheet    = $sheets[$sheetCode] ?? null;
            if (empty($curSubOrder))
            {
                return self::Fail('结算子单数据异常，无法创建零售订单', 400);
            }
            if (empty($curSheet))
            {
                return self::Fail('零售购买单数据异常，无法创建零售订单', 400);
            }

            $orderCode = generateBusinessCodeNumber(BusinessCodePrefixEnum::LSDD);

            //用来更新零售购买单
            if (!isset($sheetIdToPaidInfo[$curSheet['id']]))
            {
                $sheetIdToPaidInfo[$curSheet['id']] = [
                    'sheet_code' => $curSheet['sheet_code'],
                    'paid_at'    => $curSubOrder['paid_at'],
                ];
            }

            $tmpOrderData = [
                'pay_sub_order_code'     => $paySubOrderCode,
                'order_code'             => $orderCode,
                'org_id'                 => $curSubOrder['org_id'],
                'brand_id'               => $curSubOrder['brand_id'],
                'hospital_id'            => $curSubOrder['hospital_id'],
                'member_id'              => $curSubOrder['member_id'],
                'relation_id'            => $curSheet['id'],
                'sale_price'             => $curSubOrder['sale_price'],
                'reduce_price'           => $curSubOrder['reduce_price'],
                'deal_price'             => $curSubOrder['deal_price'],
                'coupon_price'           => $curSubOrder['coupon_price'],
                'cash_coupon_price'      => $curSubOrder['cash_coupon_price'],
                'discount_price'         => $curSubOrder['discount_price'],
                'third_party_price'      => $curSubOrder['third_party_price'],
                'balance_price'          => $curSubOrder['balance_price'],
                'deposit_price'          => $curSubOrder['deposit_price'],
                'balance_recharge_price' => $curSubOrder['balance_recharge_price'],
                'balance_gift_price'     => $curSubOrder['balance_gift_price'],
                'pay_price'              => $curSubOrder['pay_price'],
                'third_party_pay_mode'   => $curSubOrder['third_party_pay_mode'],
                'pay_channel'            => $curSubOrder['pay_channel'],
                'pay_mode'               => $curSubOrder['pay_mode'],
                'status'                 => SheetStatusEnum::Paid->value,
                'created_by'             => $curSubOrder['created_by'],
                'sold_by'                => $curSheet['sold_by'],
                'cashier_by'             => $curSubOrder['cashier_by'],
                'order_time'             => $curSubOrder['order_time'],
                'paid_at'                => $curSubOrder['paid_at'],
                'created_at'             => $curSubOrder['created_at'],
            ];

            $curSheetOriginItems = $sheetGroupItems[$curSheet['id']] ?? [];
            if (empty($curSheetOriginItems))
            {
                return self::Fail('单个零售购买单明细数据异常，无法创建零售订单', 400);
            }

            //扩展字段的详情备用
            $paySubOrdersExtItems = array_column($value['items'] ?? [], null, 'uid');

            $orderItemData = [];
            foreach ($curSheetOriginItems as $item)
            {
                $paySubOrdersExtItem = $paySubOrdersExtItems[$item['uid']] ?? null;

                if (empty($paySubOrdersExtItem))
                {
                    return self::Fail('零售购买单明细扩展数据异常，无法创建零售订单', 400);
                }

                $isPreciseMetering = ($paySubOrdersExtItem['itemInfo']['isPreciseMetering'] ?? false) ? 1 : 0;

                // 默认拆分 1 份
                $splitItemNum = 1;
                if ($isPreciseMetering == 0 && is_numeric($item['quantity']) && (int) $item['quantity'] == $item['quantity'])
                {
                    $splitItemNum = $item['quantity'];
                }

                //数量按份数拆分
                $perQuantity = bcdiv($item['quantity'], $splitItemNum, 2);
                //$item['price']为单价，非精确计量的直接用，精确计量的需要用小计作为单价，因为精确计量的需要看作是一个整体
                $perPrice = $isPreciseMetering ? bcmul($item['price'], $item['quantity'], 2) : $item['price'];

                for ($i = 0; $i < $splitItemNum; $i ++)
                {
                    // 普通项目
                    $orderItemData[] = [
                        'uid'                    => generateUUID(),
                        'order_id'               => 0,
                        'hospital_id'            => $curSubOrder['hospital_id'],
                        'relation_id'            => $item['id'],
                        'item_id'                => $item['item_id'],
                        'item_barcode'           => $item['item_barcode'],
                        'unit_type'              => $item['unit_type'],
                        'is_precise_metering'    => $isPreciseMetering,
                        'quantity'               => $perQuantity,
                        'sale_price'             => $perPrice,
                        'reduce_price'           => '0.00',
                        'deal_price'             => $perPrice,
                        'coupon_price'           => '0.00',
                        'cash_coupon_price'      => '0.00',
                        'discount_price'         => '0.00',
                        'third_party_price'      => '0.00',
                        'balance_price'          => '0.00',
                        'deposit_price'          => '0.00',
                        'balance_recharge_price' => '0.00',
                        'balance_gift_price'     => '0.00',
                        'pay_price'              => $perPrice,
                        'status'                 => 1,
                        'created_by'             => $curSubOrder['created_by'],
                        'created_at'             => $curSubOrder['created_at'],
                    ];
                }
            }

            if (bccomp($curSubOrder['discount_price'], 0, 2) != 0)
            {
                $splitRes = PriceSplitHelper::CommonSplitPrice($orderItemData,
                                                               'discount_price',
                                                               $curSubOrder['discount_price']);
                if ($splitRes->isFail())
                {
                    return $splitRes;
                }

                $orderItemData = $splitRes->getData();
            }
            if (bccomp($curSubOrder['third_party_price'], 0, 2) != 0)
            {
                $splitRes = PriceSplitHelper::CommonSplitPrice($orderItemData,
                                                               'third_party_price',
                                                               $curSubOrder['third_party_price']);
                if ($splitRes->isFail())
                {
                    return $splitRes;
                }

                $orderItemData = $splitRes->getData();
            }
            if (bccomp($curSubOrder['balance_recharge_price'], 0, 2) != 0)
            {
                $splitRes = PriceSplitHelper::CommonSplitPrice($orderItemData,
                                                               'balance_recharge_price',
                                                               $curSubOrder['balance_recharge_price']);
                if ($splitRes->isFail())
                {
                    return $splitRes;
                }

                $orderItemData = $splitRes->getData();
            }
            if (bccomp($curSubOrder['balance_price'], 0, 2) != 0)
            {
                $splitRes = PriceSplitHelper::CommonSplitPrice($orderItemData,
                                                               'balance_price',
                                                               $curSubOrder['balance_price']);
                if ($splitRes->isFail())
                {
                    return $splitRes;
                }

                $orderItemData = $splitRes->getData();
            }

            $tmpOrderData['items'] = $orderItemData;

            $orderData[] = $tmpOrderData;

        }

        $extOperation = function () use ($sheetIdToPaidInfo) {
            try
            {
                DB::beginTransaction();

                //批量更新零售购买单支付状态
                $sheetPaidInfo = current($sheetIdToPaidInfo);
                $sheetRes      = RetailSheetModel::on()
                                                 ->whereIn('id', array_keys($sheetIdToPaidInfo))
                                                 ->where([
                                                             ['status', '!=', SheetStatusEnum::Paid->value],
                                                         ])
                                                 ->update([
                                                              'status'  => SheetStatusEnum::Paid->value,
                                                              'paid_at' => $sheetPaidInfo['paid_at'],
                                                          ]);

                if (empty($sheetRes) || $sheetRes != count(array_keys($sheetIdToPaidInfo)))
                {
                    DB::rollBack();

                    return self::Fail('更新零售购买单支付状态失败', 500970);
                }

                //发布事件
                foreach ($sheetIdToPaidInfo as $paidInfo)
                {
                    event(new RetailPaidEvent($paidInfo['sheet_code'], self::getVersion()));
                }

                DB::commit();

                return self::Success();
            } catch (Throwable $throwable)
            {
                DB::rollBack();

                Log::error(__CLASS__ . '::' . __METHOD__ . ' 更新零售购买单支付状态异常', [
                    'code'    => $throwable->getCode(),
                    'message' => $throwable->getMessage(),
                    'file'    => $throwable->getFile(),
                    'line'    => $throwable->getLine(),
                    'trace'   => $throwable->getTraceAsString(),
                ]);

                return self::Fail('更新零售购买单支付状态异常', 500971);
            }
        };

        return self::Success([
                                 'ordersData'   => $orderData,
                                 'extOperation' => $extOperation,
                             ]);
    }

    /**
     * 批量获取零售订单详情
     *
     * @param array       $paySubOrderCodes
     * @param array       $orderCodes
     * @param array       $orderIds
     * @param int|null    $hospitalId
     * @param string|null $keyByCode
     *
     * @return array
     */
    public static function GetOrdersItemDetail(
        array   $paySubOrderCodes = [], array $orderCodes = [], array $orderIds = [], ?int $hospitalId = null,
        ?string $keyByCode = null
    ): array
    {
        if (empty($paySubOrderCodes) && empty($orderCodes) && empty($orderIds))
        {
            return [];
        }

        $where   = [['status', '>', SheetStatusEnum::Cancelled->value]];
        $whereIn = [];
        if (!empty($paySubOrderCodes))
        {
            $whereIn['pay_sub_order_code'] = $paySubOrderCodes;
        }
        if (!empty($orderCodes))
        {
            $whereIn['order_code'] = $orderCodes;
        }
        if (!empty($orderIds))
        {
            $whereIn['id'] = $orderIds;
        }
        if (!empty($hospitalId))
        {
            $where[] = ['hospital_id', '=', $hospitalId];
        }

        $orders = RetailOrderModel::getData(
            where:    $where,
            whereIn:  $whereIn,
            orderBys: ['id' => 'asc'],
        );
        if (empty($orders))
        {
            return [];
        }

        $orderIds = array_column($orders, 'id');

        $orderItems = RetailOrderItemModel::getData(
            where:    ['status' => 1],
            whereIn:  ['order_id' => $orderIds],
            orderBys: ['id' => 'asc']
        );
        if (empty($orderItems))
        {
            return [];
        }

        return RetailOrderHelper::FormatOrderDetailsStructure($orders, $orderItems, $keyByCode);
    }
}
