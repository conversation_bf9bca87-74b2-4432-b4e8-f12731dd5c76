<?php

namespace App\Logics\V1\Order\Interfaces;

interface OrderItemInterface
{
    /**
     * 获取订单详情数据
     *
     * @param array       $paySubOrderCodes
     * @param array       $orderCodes
     * @param array       $orderIds
     * @param int|null    $hospitalId
     * @param string|null $keyByCode
     *
     * @return array
     */
    public static function GetOrdersItemDetail(
        array   $paySubOrderCodes = [], array $orderCodes = [], array $orderIds = [], ?int $hospitalId = null,
        ?string $keyByCode = null
    ): array;
}
