<?php

namespace App\Logics\V1;

use Throwable;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Logics\Logic;
use App\Logics\LogicResult;
use App\Traits\VersionTrait;
use App\Enums\PayResultEnum;
use App\Enums\SheetStatusEnum;
use App\Models\PayResponseRecordsModel;
use App\Models\PayRequestRecordsModel;
use App\Models\PayOrderModel;
use App\Models\PaySubOrderModel;
use App\Support\PayOrder\PayOrderHelper;
use App\Logics\Payment\PaymentLogic;
use App\Events\PayOrderPaidEvent;

class PayLogic extends Logic
{
    use VersionTrait;

    /**
     * 呼起支付
     *
     * 同步成功的支付方式
     * 异步成功的支付方式
     *
     * @param array $params
     * @param array $publicParams
     * @param bool  $internalCall 是否内部代码呼起支付 true:是 false:否
     *
     * @return LogicResult
     * @throws Throwable
     */
    public static function GoPay(array $params, array $publicParams, bool $internalCall = false): LogicResult
    {
        $payOrderCode = trim(Arr::get($params, 'payOrderCode', ''));
        $payPrice     = trim(Arr::get($params, 'payPrice', ''));

        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));

        if (empty($payOrderCode))
        {
            return self::Fail('调用支付，缺少结算单号必选参数', 400);
        }
        if (!is_numeric($payPrice) || bccomp($payPrice, '0', 2) < 0)
        {
            return self::Fail('调用支付，支付金额参数错误', 400);
        }
        if (empty($hospitalId))
        {
            return self::Fail('调用支付，缺少医院ID必选参数', 400);
        }

        $payOrderRes = PayOrderHelper::GetValidPayOrder(
            payOrderCode: $payOrderCode,
            hospitalId:   $hospitalId,
            useWritePdo:  $internalCall, //内部调用涉及到同步延迟的，需要使用主库读
        );
        if ($payOrderRes->isFail())
        {
            return $payOrderRes;
        }
        $payOrder = $payOrderRes->getData();

        if (bccomp($payOrder['pay_price'], $payPrice, 2) != 0)
        {
            return self::Fail('调用支付，支付金额与结算单金额不一致', 400);
        }

        if ($payOrder['status'] != SheetStatusEnum::Unpaid->value)
        {
            return self::Fail('调用支付，结算单状态错误', 400);
        }

        $payRequestData = [
            'payOrderCode' => $payOrderCode,
            'payPrice'     => $payPrice,
            'payChannelId' => $payOrder['pay_channel'],
            'payModeId'    => $payOrder['pay_mode'],
        ];

        //调用支付前设置结算单为支付中
        $setRes = PayOrderHelper::SetPayOrderToPaying($payOrder['id']);
        if (!$setRes)
        {
            return self::Fail('调用支付，设置结算单状态错误', 602650);
        }

        //兼容了0元支付渠道pay_channel=0，pay_mode=0
        $requestPayRes = PaymentLogic::RequestPay($payRequestData, $publicParams);
        if ($requestPayRes->isFail())
        {
            PayOrderHelper::SetPayOrderToUnpaid($payOrder['id']);

            return $requestPayRes;
        }
        $requestPayData = $requestPayRes->getData();

        //如果是同步可获得支付结果的
        $paySuccess = null;
        if ($requestPayData['isDirect'] ?? false)
        {
            //同步支付的，到此已认为完成了支付调用，接下来直接调用支付确认即可
            $confirmPayRes = self::ConfirmPay($requestPayData);
            if ($confirmPayRes->isFail())
            {
                return $confirmPayRes;
            }
            $paySuccess = true;
        }

        //TODO:在线支付的，需要调用对方接口调用支付-注意区分同步支付和异步支付，同步支付需要调用支付结果

        $result = [
            'isDirect'     => $requestPayData['isDirect'] ?? false,
            'payOrderCode' => $payOrderCode,
            'payPrice'     => $payPrice,
            'paySuccess'   => $paySuccess,
        ];

        return self::Success($result);
    }

    /**
     * 支付结果确认
     *
     * 两种调用途径：
     * 1.内部支付成功的调用
     * 2.外部回调结果的调用
     *
     * @param array $params
     *
     * @return LogicResult
     * @throws Throwable
     */
    public static function ConfirmPay(array $params): LogicResult
    {
        //获取一下参数
        $payOrderCode = trim(Arr::get($params, 'payOrderCode', ''));
        $payChannelId = intval(Arr::get($params, 'payChannelId', 0));

        //必选参数验证
        if (empty($payOrderCode))
        {
            return self::Fail('结算单号参数必选', 400);
        }
        //兼容0元支付逻辑
        if ($payChannelId < 0)
        {
            return self::Fail('支付渠道参数必选', 400);
        }

        //调用支付结果判断逻辑
        $payResultRes = PaymentLogic::ResponsePay($params);
        if ($payResultRes->isFail())
        {
            return $payResultRes;
        }
        $payResultData = $payResultRes->getData();

        //判断如果是支付结果是成功，则调用支付成功逻辑
        if ($payResultData['payResult'] == PayResultEnum::Success->value)
        {
            $paySuccessRes = self::PaySuccess($payResultData);
            if ($paySuccessRes->isFail())
            {
                return $paySuccessRes;
            }
        }
        elseif ($payResultData['payResult'] == PayResultEnum::Fail->value)
        {
            //TODO:结果是支付失败的处理,是否更新支付请求记录的状态
            return self::Fail('支付失败', 602941);
        }
        else
        {
            return self::Fail('支付结果未知或正在处理中,请联系管理员', 602940);
        }


        return self::Success($payResultData);
    }

    /**
     * 支付成功处理
     *
     * @param array $params
     *
     * @return LogicResult
     * @throws Throwable
     */
    public static function PaySuccess(array $params): LogicResult
    {
        $payOrderCode     = trim(Arr::get($params, 'payOrderCode', ''));
        $responseRecordId = Arr::get($params, 'responseRecordId', 0);

        if (empty($payOrderCode) || empty($responseRecordId))
        {
            return self::Fail('缺少支付成功必选参数', 400);
        }

        //获取响应记录
        $responseRecord = PayResponseRecordsModel::getOne($responseRecordId, true);

        if (empty($responseRecord))
        {
            return self::Fail('支付响应记录不存在', 602901);
        }
        if ($responseRecord['pay_order_code'] != $payOrderCode)
        {
            return self::Fail('支付响应记录与结算单不匹配', 602901);
        }
        if ($responseRecord['type'] != PaymentLogic::PAY_DIRECTION_PAY)
        {
            return self::Fail('支付响应记录非支付类型', 602901);
        }
        if ($responseRecord['status'] != PayResultEnum::Success->value)
        {
            return self::Fail('支付响应记录非成功状态', 602902);
        }
        if ($responseRecord['process_status'] != 0)
        {
            return self::Fail('支付响应记录已被处理过', 602902);
        }

        //获取最后一条符合的支付请求记录
        $payRequestRecords = PayRequestRecordsModel::getData(
            where:     [
                           'pay_order_code' => $payOrderCode,
                           'pay_channel'    => $responseRecord['pay_channel'],
                           'pay_mode'       => $responseRecord['pay_mode'],
                           'type'           => $responseRecord['type'],
                       ],
            orderBys:  ['id' => 'desc'],
            pageIndex: 1,
            pageSize:  1
        );
        if (empty($payRequestRecords))
        {
            return self::Fail('支付响应匹配的支付请求记录不存在', 602910);
        }
        $payRequestRecord = current($payRequestRecords);
        if ($payRequestRecord['status'] != 0)
        {
            return self::Fail('支付请求记录已被处理过', 602910);
        }
        $payPrice = $payRequestRecord['pay_price'];

        //查询结算单，并准备修改状态
        $payOrderRes = PayOrderHelper::GetValidPayOrder(
            payOrderCode: $payOrderCode,
            hospitalId:   $payRequestRecord['hospital_id'],
            useWritePdo:  true,
        );
        if ($payOrderRes->isFail())
        {
            return $payOrderRes;
        }
        $payOrder = $payOrderRes->getData();
        if ($payOrder['status'] != SheetStatusEnum::Paying->value)
        {
            return self::Fail('支付回调的结算单状态错误', 602920);
        }
        if (bccomp($responseRecord['pay_price'], $payOrder['pay_price'], 2) != 0)
        {
            return self::Fail('支付回调的金额与结算单金额不一致', 602920);
        }


        try
        {
            DB::beginTransaction();

            //修改结算主订单
            $updateOrderResult = PayOrderModel::on()
                                              ->where([
                                                          'pay_order_code' => $payOrderCode,
                                                          'status'         => SheetStatusEnum::Paying->value,
                                                      ])
                                              ->update([
                                                           'status'     => SheetStatusEnum::Paid->value,
                                                           'cashier_by' => $payRequestRecord['cashier_by'],
                                                           'paid_at'    => $responseRecord['paid_at'],
                                                       ]);
            if (empty($updateOrderResult))
            {
                DB::rollBack();

                return self::Fail('支付成功，更新结算单失败', 602930);
            }

            //修改结算子订单
            $updateSubOrderResult = PaySubOrderModel::on()
                                                    ->where([
                                                                'pay_order_code' => $payOrderCode,
                                                            ])
                                                    ->whereIn('status',
                                                              [
                                                                  SheetStatusEnum::Unpaid->value,
                                                                  SheetStatusEnum::Paying->value
                                                              ])
                                                    ->update([
                                                                 'status'     => SheetStatusEnum::Paid->value,
                                                                 'cashier_by' => $payRequestRecord['cashier_by'],
                                                                 'paid_at'    => $responseRecord['paid_at'],
                                                             ]);
            if (empty($updateSubOrderResult))
            {
                DB::rollBack();

                return self::Fail('支付成功，更新结算子单失败', 602931);
            }

            //更新支付请求记录
            PayRequestRecordsModel::updateOne($payRequestRecord['id'], [
                'pay_response_id' => $responseRecordId,
                'status'          => 1,
            ]);

            //更新支付响应记录
            PayResponseRecordsModel::updateOne($responseRecordId, [
                'process_status' => 1,
            ]);

            //发布事件
            event(new PayOrderPaidEvent($payOrderCode, $payPrice, self::getVersion()));

            DB::commit();
        } catch (Throwable $throwable)
        {
            DB::rollBack();

            Log::error(__CLASS__ . '::' . __METHOD__ . ' 支付成功，更新结算单异常', [
                'code'    => $throwable->getCode(),
                'message' => $throwable->getMessage(),
                'file'    => $throwable->getFile(),
                'line'    => $throwable->getLine(),
                'trace'   => $throwable->getTraceAsString(),
            ]);

            return self::Fail('支付成功，更新结算单异常', 602932);
        }

        //TODO:结算单支付成功的事件发出
        return self::Success();
    }
}
