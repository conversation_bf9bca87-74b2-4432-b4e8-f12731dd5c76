<?php

namespace App\Logics\V1\BuySheet;

use Throwable;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Arr;
use App\Logics\Logic;
use App\Logics\LogicResult;
use App\Logics\V1\BuySheet\Interfaces\CheckoutInterface;
use App\Logics\V1\BuySheet\Interfaces\LockInterface;
use App\Logics\V1\BuySheet\Interfaces\DetailInterface;
use App\Enums\SheetBusinessTypeEnum;
use App\Models\MemberModel;
use App\Models\SheetUnionModel;
use App\Models\UsersModel;
use App\Support\SheetUnion\SheetUnionHelper;
use App\Support\Member\MemberHelper;
use App\Support\User\HospitalUserHelper;
use App\Support\Concurrent\ConcurrentTask;


/**
 * 统一购买单处理逻辑
 */
class SheetUnionLogic extends Logic
{
    /**
     * 购买单业务类型与逻辑类映射
     *
     * @var array
     */
    const array SHEET_BUSINESS_LOGIC_MAP = [
        SheetBusinessTypeEnum::Recharge->value     => BalanceRechargeSheetLogic::class,
        SheetBusinessTypeEnum::Registration->value => RegistrationSheetLogic::class,
        SheetBusinessTypeEnum::Recipe->value       => RecipeSheetLogic::class,
        SheetBusinessTypeEnum::Retail->value       => RetailSheetLogic::class,
        SheetBusinessTypeEnum::Beauty->value       => BeautyServiceSheetLogic::class,
    ];

    /**
     * 获取待支付购买单会员选项
     *
     * @param array $publicParams
     *
     * @return LogicResult
     */
    public static function GetSheetMemberOptions(array $publicParams): LogicResult
    {
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        if (empty($hospitalId))
        {
            return self::Fail('获取待支付购买单会员选项，缺少医院ID必选参数', 400);
        }

        $memberIds = SheetUnionModel::GetSheetMemberOptions($hospitalId);
        if (empty($memberIds))
        {
            return self::Success([]);
        }

        return self::Success(MemberHelper::GetMemberOptionsByMemberIds($memberIds));
    }

    /**
     * 获取待支付购买单创建人选项
     *
     * @param array $publicParams
     *
     * @return LogicResult
     */
    public static function GetCreateUsersOptions(array $publicParams): LogicResult
    {
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        if (empty($hospitalId))
        {
            return self::Fail('获取待支付购买单会员选项，缺少医院ID必选参数', 400);
        }

        $userIds = SheetUnionModel::GetSheetCreateUsersOptions($hospitalId);
        if (empty($userIds))
        {
            return self::Success([]);
        }

        return self::Success(HospitalUserHelper::GetUserOptionsByUserIds($userIds, $hospitalId));
    }

    /**
     * 获取待支付购买单列表
     *
     * @param array $params
     * @param array $publicParams
     *
     * @return LogicResult
     */
    public static function GetUnpaidSheetList(array $params, array $publicParams): LogicResult
    {
        $memberId      = intval(Arr::get($params, 'memberId', 0));
        $memberUid     = trim(Arr::get($params, 'memberUid', ''));
        $createUserId  = intval(Arr::get($params, 'createUserId', 0));
        $createUserUid = trim(Arr::get($params, 'createUserUid', ''));
        $type          = trim(Arr::get($params, 'type', ''));
        $startDate     = trim(Arr::get($params, 'startDate', ''));
        $endDate       = trim(Arr::get($params, 'endDate', ''));
        //公共参数
        $hospitalId    = intval(Arr::get($publicParams, '_hospitalId'));
        $hospitalOrgId = intval(Arr::get($publicParams, '_hospitalOrgId'));
        if (empty($hospitalId) || empty($hospitalOrgId))
        {
            return self::Fail('获取待支付购买单列表，缺少医院ID、组织ID必选参数', 400);
        }

        if (!empty($memberUid) || !empty($memberId))
        {
            $member = MemberModel::getOneByIdOrUid(id: $memberId, uid: $memberUid, orgId: $hospitalOrgId);
            if (empty($member))
            {
                return self::Fail('会员不存在', 30001);
            }
            $memberId = $member->id;
        }
        if (!empty($createUserUid) || !empty($createUserId))
        {
            $createUser = UsersModel::getOneByIdOrUid(id: $createUserId, uid: $createUserUid);
            if (empty($createUser))
            {
                return self::Fail('创建人不存在', 10100);
            }
            $createUserId = $createUser->id;
        }
        if (!empty($type) && !SheetBusinessTypeEnum::exists($type))
        {
            return self::Fail('购买单业务类型参数错误', 400);
        }
        if (!empty($startDate) && strtotime($startDate) === false)
        {
            return self::Fail('开始日期格式错误', 400);
        }
        if (!empty($endDate) && strtotime($endDate) === false)
        {
            return self::Fail('结束日期格式错误', 400);
        }
        if (!empty($startDate) && !empty($endDate) && strtotime($startDate) > strtotime($endDate))
        {
            return self::Fail('开始日期不能大于结束日期', 400);
        }

        //构建查询条件
        $filters       = [];
        $businessTypes = [];
        if (!empty($memberId))
        {
            $filters['member_id'] = $memberId;
        }
        if (!empty($createUserId))
        {
            $filters['created_by'] = $createUserId;
        }
        if (!empty($startDate))
        {
            $filters['created_start_time'] = $startDate . ' 00:00:00';
        }
        if (!empty($endDate))
        {
            $filters['created_end_time'] = $endDate . ' 23:59:59';
        }
        if (!empty($type))
        {
            $businessTypes[] = $type;
        }

        $listRes = SheetUnionModel::GetUnpaidSheetList($hospitalId, $filters, $businessTypes);

        if ($listRes->isEmpty())
        {
            return self::Success(['total' => 0, 'data' => []]);
        }

        $list = $listRes
            ->map(function ($item) {
                return (array) $item;
            })
            ->toArray();

        return self::Success([
                                 'total' => count($list),
                                 'data'  => SheetUnionHelper::FormatSheetListStructure($list, $publicParams)
                             ]);
    }

    /**
     * 进行结算数据准备
     *
     * @param array $sheetsBusinessMap
     * @param array $publicParams
     *
     * @return LogicResult
     */
    public static function DoCheckoutPrepare(array $sheetsBusinessMap, array $publicParams): LogicResult
    {
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));

        if (empty($sheetsBusinessMap))
        {
            return self::Fail('缺少购买单必选参数', 400);
        }
        if (empty($hospitalId))
        {
            return self::Fail('缺少医院ID必选参数', 400);
        }

        // 批量获取各个业务的结算详情
        $task = ConcurrentTask::new();
        foreach ($sheetsBusinessMap as $businessType => $sheetParams)
        {
            $logic = self::GetSheetLogicClass($businessType);
            if (!self::CheckSheetLogicIsSupportCheckout($logic))
            {
                return self::Fail('结算逻辑处理器注册异常', 600100);
            }

            $task->addTask($businessType,
                fn() => $logic::DoCheckoutPrepare($sheetParams['codes'], $sheetParams['total'], $publicParams));
        }

        DB::disconnect();
        $getDoCheckoutPrepareRes = $task->run();
        if (empty($getDoCheckoutPrepareRes))
        {
            return self::Fail('结算数据准备失败', 600200);
        }

        $doCheckoutPrepareRes = [];
        foreach ($getDoCheckoutPrepareRes as $type => $result)
        {
            if ($result->isException())
            {
                Log::error(__CLASS__ . '::' . __METHOD__ . ' 统一购买单：进行结算数据准备，数据准备异常', [
                    'code'    => $result->getException()
                                        ->getCode(),
                    'message' => $result->getException()
                                        ->getMessage(),
                    'file'    => $result->getException()
                                        ->getFile(),
                    'line'    => $result->getException()
                                        ->getLine(),
                    'trace'   => $result->getException()
                                        ->getTraceAsString(),
                ]);

                return self::Fail('结算数据准备失败，数据准备异常', 600200);
            }

            $tmpLogicRes = $result->getData();
            if ($tmpLogicRes->isFail())
            {
                return $tmpLogicRes;
            }

            $doCheckoutPrepareRes[] = [
                'type'       => [
                    'uid'  => $type,
                    'name' => SheetBusinessTypeEnum::getDescription($type) ?? null,
                ],
                'data'       => $tmpLogicRes->getData('data'),
                'totalPrice' => $tmpLogicRes->getData('totalPrice'),
            ];
        }

        return self::Success($doCheckoutPrepareRes);
    }

    /**
     * 购买单锁定
     *
     * 结算单下单时锁定购买单
     *
     * @param array $sheetsBusinessMap
     * @param array $publicParams
     *
     * @return LogicResult
     * @throws Throwable
     */
    public static function DoLockSheet(array $sheetsBusinessMap, array $publicParams): LogicResult
    {
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));

        if (empty($sheetsBusinessMap))
        {
            return self::Fail('缺少购买单必选参数', 400);
        }
        if (empty($hospitalId))
        {
            return self::Fail('缺少医院ID必选参数', 400);
        }

        // 批量锁定各个业务的购买单
        try
        {
            DB::beginTransaction();

            foreach ($sheetsBusinessMap as $businessType => $sheetParams)
            {
                $logic = self::GetSheetLogicClass($businessType);
                if (!self::CheckSheetLogicIsSupportLock($logic))
                {
                    DB::rollBack();

                    return self::Fail('结算逻辑处理器注册异常', 600100);
                }

                $lockRes = $logic::DoLockSheet($sheetParams['codes'], $publicParams);
                if ($lockRes->isFail())
                {
                    DB::rollBack();

                    return $lockRes;
                }
            }

            DB::commit();

            return self::Success();
        } catch (Throwable $throwable)
        {
            DB::rollBack();

            Log::error(__CLASS__ . '::' . __METHOD__ . ' 结算锁定业务购买单失败', [
                'code'    => $throwable->getCode(),
                'message' => $throwable->getMessage(),
                'file'    => $throwable->getFile(),
                'line'    => $throwable->getLine(),
                'trace'   => $throwable->getTraceAsString(),
            ]);

            return self::Fail('结算锁定业务购买单失败', 601092);
        }
    }

    /**
     * 购买单解锁
     *
     * 取消支付时解锁购买单
     *
     * @param array $sheetsBusinessMap
     * @param array $publicParams
     *
     * @return LogicResult
     * @throws Throwable
     */
    public static function DoUnlockSheet(array $sheetsBusinessMap, array $publicParams): LogicResult
    {
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        if (empty($sheetsBusinessMap))
        {
            return self::Fail('缺少购买单必选参数', 400);
        }
        if (empty($hospitalId))
        {
            return self::Fail('缺少医院ID必选参数', 400);
        }

        // 批量解锁各个业务的购买单
        try
        {
            DB::beginTransaction();

            foreach ($sheetsBusinessMap as $businessType => $sheetParams)
            {
                $logic = self::GetSheetLogicClass($businessType);
                if (!self::CheckSheetLogicIsSupportLock($logic))
                {
                    DB::rollBack();

                    return self::Fail('结算逻辑处理器注册异常', 600100);
                }

                $unlockRes = $logic::DoUnlockSheet($sheetParams['codes'], $publicParams);
                if ($unlockRes->isFail())
                {
                    DB::rollBack();

                    return $unlockRes;
                }
            }

            DB::commit();

            return self::Success();
        } catch (Throwable $throwable)
        {
            DB::rollBack();

            Log::error(__CLASS__ . '::' . __METHOD__ . ' 解锁业务购买单失败', [
                'code'    => $throwable->getCode(),
                'message' => $throwable->getMessage(),
                'file'    => $throwable->getFile(),
                'line'    => $throwable->getLine(),
                'trace'   => $throwable->getTraceAsString(),
            ]);

            return self::Fail('解锁业务购买单失败', 601093);
        }
    }

    /**
     * 获取购买单摘要信息
     *
     * @param array $sheetsBusinessMap
     * @param array $publicParams
     *
     * @return LogicResult
     */
    public static function GetSheetSummary(array $sheetsBusinessMap, array $publicParams): LogicResult
    {
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));

        if (empty($sheetsBusinessMap))
        {
            return self::Fail('缺少购买单必选参数', 400);
        }
        if (empty($hospitalId))
        {
            return self::Fail('缺少医院ID必选参数', 400);
        }

        // 批量获取各个业务的详情摘要
        $task = ConcurrentTask::new();
        foreach ($sheetsBusinessMap as $businessType => $sheetParams)
        {
            $logic = self::GetSheetLogicClass($businessType);
            if (!self::CheckSheetLogicIsSupportDetail($logic))
            {
                return self::Fail('结算逻辑处理器注册异常', 600100);
            }

            $task->addTask($businessType,
                fn() => $logic::GetSheetSummary($sheetParams['codes'], $publicParams));
        }

        DB::disconnect();
        $getSheetSummaryRes = $task->run();
        if (empty($getSheetSummaryRes))
        {
            return self::Fail('结算单详情获取失败', 601300);
        }

        $sheetSummaryRes = [];
        foreach ($getSheetSummaryRes as $type => $result)
        {
            if ($result->isException())
            {
                Log::error(__CLASS__ . '::' . __METHOD__ . ' 统一购买单：结算单详情获取异常', [
                    'code'    => $result->getException()
                                        ->getCode(),
                    'message' => $result->getException()
                                        ->getMessage(),
                    'file'    => $result->getException()
                                        ->getFile(),
                    'line'    => $result->getException()
                                        ->getLine(),
                    'trace'   => $result->getException()
                                        ->getTraceAsString(),
                ]);

                return self::Fail('结算单详情获取异常', 601301);
            }

            $tmpLogicRes = $result->getData();
            if ($tmpLogicRes->isFail())
            {
                return $tmpLogicRes;
            }

            $sheetSummaryRes[] = [
                'type'       => [
                    'uid'  => $type,
                    'name' => SheetBusinessTypeEnum::getDescription($type) ?? null,
                ],
                'data'       => $tmpLogicRes->getData('data'),
                'totalPrice' => $tmpLogicRes->getData('totalPrice'),
            ];
        }

        return self::Success($sheetSummaryRes);
    }

    /**
     * 检查逻辑类是否支持结算
     *
     * @param string|null $sheetLogic
     *
     * @return bool
     */
    private static function CheckSheetLogicIsSupportCheckout(?string $sheetLogic): bool
    {
        if (!$sheetLogic)
        {
            return false;
        }

        return is_subclass_of($sheetLogic, CheckoutInterface::class);
    }

    /**
     * 检查逻辑类是否支持锁定
     *
     * @param string|null $sheetLogic
     *
     * @return bool
     */
    private static function CheckSheetLogicIsSupportLock(?string $sheetLogic): bool
    {
        if (!$sheetLogic)
        {
            return false;
        }

        return is_subclass_of($sheetLogic, LockInterface::class);
    }

    /**
     * 检查逻辑类是否支持获取摘要
     *
     * @param string|null $sheetLogic
     *
     * @return bool
     */
    private static function CheckSheetLogicIsSupportDetail(?string $sheetLogic): bool
    {
        if (!$sheetLogic)
        {
            return false;
        }

        return is_subclass_of($sheetLogic, DetailInterface::class);
    }

    /**
     * 获取购买单业务逻辑类
     *
     * @param string $businessType
     *
     * @return string|null
     */
    private static function GetSheetLogicClass(string $businessType): ?string
    {
        return self::SHEET_BUSINESS_LOGIC_MAP[$businessType] ?? null;
    }
}
