<?php

namespace App\Logics\V1\BuySheet\Interfaces;

use App\Logics\LogicResult;

interface CheckoutInterface
{
    /**
     * 结算预备数据
     *
     * @param array  $sheetCodes
     * @param string $totalPrice
     * @param array  $publicParams
     *
     * @return LogicResult
     */
    public static function DoCheckoutPrepare(array $sheetCodes, string $totalPrice, array $publicParams): LogicResult;
}
