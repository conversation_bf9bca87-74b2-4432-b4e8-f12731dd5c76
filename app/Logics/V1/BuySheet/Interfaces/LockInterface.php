<?php

namespace App\Logics\V1\BuySheet\Interfaces;

use App\Logics\LogicResult;

interface LockInterface
{
    /**
     * 业务购买单锁定
     *
     * 设置为支付中
     *
     * @param array $sheetCodes
     * @param array $publicParams
     *
     * @return LogicResult
     */
    public static function DoLockSheet(array $sheetCodes, array $publicParams): LogicResult;

    /**
     * 业务购买单解锁
     *
     * 设置为待支付
     *
     * @param array $sheetCodes
     * @param array $publicParams
     *
     * @return LogicResult
     */
    public static function DoUnlockSheet(array $sheetCodes, array $publicParams): LogicResult;
}
