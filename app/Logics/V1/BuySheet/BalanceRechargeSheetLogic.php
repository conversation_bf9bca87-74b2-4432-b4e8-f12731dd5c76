<?php

namespace App\Logics\V1\BuySheet;

use Throwable;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Arr;
use App\Logics\LogicResult;
use App\Logics\V1\BuySheet\Interfaces\CheckoutInterface;
use App\Logics\V1\BuySheet\Interfaces\LockInterface;
use App\Logics\V1\BuySheet\Interfaces\DetailInterface;
use App\Enums\BusinessCodePrefixEnum;
use App\Enums\SheetStatusEnum;
use App\Models\MemberModel;
use App\Models\UsersModel;
use App\Models\BalanceRechargeSheetModel;
use App\Models\BalanceRechargeSheetItemModel;
use App\Support\Recharge\RechargeSheetHelper;
use App\Logics\V1\HospitalUserLogic;
use App\Logics\V1\BalanceRechargeActivityLogic;

/**
 * 会员余额充值单
 */
class BalanceRechargeSheetLogic extends BuySheetExpiredBase implements CheckoutInterface, LockInterface, DetailInterface
{
    /**
     * 单次批量结算的结算单数量限制
     */
    const int CHECKOUT_SHEET_NUM_LIMIT = 1;

    /**
     * 余额购买单自动过期时间（秒）
     *
     * @var int
     */
    protected const int AUTO_EXPIRED_AFTER_SECONDS = 3600;

    public static function GenerateSheetCode(): string
    {
        return generateBusinessCodeNumber(BusinessCodePrefixEnum::CZGMD);
    }

    public static function CreateSheet(array $params, array $publicParams): LogicResult
    {
        // 公共参数
        $hospitalOrgId   = intval(Arr::get($publicParams, '_hospitalOrgId'));
        $hospitalBrandId = intval(Arr::get($publicParams, '_hospitalBrandId'));
        $hospitalId      = intval(Arr::get($publicParams, '_hospitalId'));
        $userId          = intval(Arr::get($publicParams, '_userId'));
        // 业务参数
        $memberUid  = trim(Arr::get($params, 'memberUid', ''));
        $memberId   = intval(Arr::get($params, 'memberId', 0));
        $sellerUid  = trim(Arr::get($params, 'sellerUid', ''));
        $sellerId   = intval(Arr::get($params, 'sellerId', 0));
        $items      = Arr::get($params, 'items', []);
        $totalPrice = trim(Arr::get($params, 'totalPrice', '0.00'));

        if (empty($hospitalOrgId) || empty($hospitalBrandId) || empty($hospitalId))
        {
            return self::Fail('创建充值单，缺少医院必选参数', 400);
        }
        if (empty($userId))
        {
            return self::Fail('创建充值单，缺少登录用户ID必选参数', 400);
        }
        if (empty($memberUid) && empty($memberId))
        {
            return self::Fail('创建充值单，缺少会员UID/ID必选参数', 400);
        }
        if (empty($sellerUid) || empty($sellerId))
        {
            return self::Fail('创建充值单，缺少销售员UID/ID必选参数', 400);
        }
        if (empty($items) || !isset($items[0]))
        {
            return self::Fail('创建充值单，缺少充值明细必选参数', 400);
        }
        if (count($items) > 1)
        {
            return self::Fail('创建充值单，充值明细只能有一条', 400);
        }
        if (!is_numeric($totalPrice) || bccomp($totalPrice, '0', 2) != 1)
        {
            return self::Fail('创建充值单，缺少总金额必选参数', 400);
        }

        $member = MemberModel::getOneByIdOrUid(id: $memberId, uid: $memberUid, orgId: $hospitalOrgId);
        $seller = UsersModel::getOneByIdOrUid(id: $sellerId, uid: $sellerUid);
        if (empty($member))
        {
            return self::Fail('会员不存在', 30001);
        }
        if (empty($seller))
        {
            return self::Fail('销售员不存在', 10100);
        }

        $memberId = $member->id;
        $sellerId = $seller->id;

        $hospitalUserRes = HospitalUserLogic::GetUserHospitalUser($sellerId, $hospitalId);
        if ($hospitalUserRes->isFail())
        {
            return $hospitalUserRes;
        }

        // 充值明细：仅支持单活动充值多次
        $rechargeItem    = $items[0];
        $activityUid     = trim(Arr::get($rechargeItem, 'activityUid', ''));
        $activityId      = intval(Arr::get($rechargeItem, 'activityId', 0));
        $quantity        = intval(Arr::get($rechargeItem, 'quantity', 0));
        $balanceRecharge = trim(Arr::get($rechargeItem, 'balanceRecharge', '0.00'));
        $balanceGift     = trim(Arr::get($rechargeItem, 'balanceGift', '0.00'));
        if (empty($activityUid) && empty($activityId))
        {
            return self::Fail('创建充值单，缺少活动UID/Id必选参数', 500000);
        }
        if ($quantity <= 0)
        {
            return self::Fail('创建充值单，数量必须大于0', 500001);
        }

        // 验证充值活动
        $rechargeActivityRes = BalanceRechargeActivityLogic::VerifyRechargeActivity($hospitalOrgId,
                                                                                    $hospitalId,
                                                                                    $activityId,
                                                                                    $activityUid,
                                                                                    $memberId,
                                                                                    $quantity);
        if ($rechargeActivityRes->isFail())
        {
            return $rechargeActivityRes;
        }

        $activityId = $rechargeActivityRes->getData('id');

        // 验证金额
        if (bccomp($balanceRecharge, $rechargeActivityRes->getData('balanceRecharge'), 2) != 0)
        {
            return self::Fail('创建充值单，充值金额不正确', 500030);
        }
        if (bccomp($balanceGift, $rechargeActivityRes->getData('balanceGift', 2)) != 0)
        {
            return self::Fail('创建充值单，赠送金额不正确', 500030);
        }
        if (bccomp($totalPrice, numberMul([$quantity, $balanceRecharge]), 2) != 0)
        {
            return self::Fail('创建充值单，总金额不正确', 500030);
        }


        $sheetCode = self::GenerateSheetCode();
        // 构造购买单数据
        $sheetData = [
            'sheet_code'  => $sheetCode,
            'org_id'      => $hospitalOrgId,
            'brand_id'    => $hospitalBrandId,
            'hospital_id' => $hospitalId,
            'member_id'   => $memberId,
            'price'       => $totalPrice,
            'status'      => 1,
            'created_by'  => $userId,
            'sold_by'     => $sellerId,
            'order_time'  => getCurrentTimeWithMilliseconds(),
        ];

        // 处理自动过期字段
        if (self::IS_AUTO_EXPIRED && self::AUTO_EXPIRED_AFTER_SECONDS > 0)
        {
            $sheetData[self::AUTO_EXPIRED_FIELD] = self::CalculateExpiredAt();
        }

        $sheetItemData = [
            [
                'uid'              => generateUUID(),
                'sheet_code'       => $sheetCode,
                'sheet_id'         => 0,
                'hospital_id'      => $hospitalId,
                'member_id'        => $memberId,
                'activity_id'      => $activityId,
                'quantity'         => $quantity,
                'balance_recharge' => $balanceRecharge,
                'balance_gift'     => $balanceGift,
                'price'            => numberMul([$quantity, $balanceRecharge]),//因为只能同时购买一种套餐，所以总价=数量*单价
                'status'           => 1,
                'created_by'       => $userId,
            ]
        ];

        $sheetId = BalanceRechargeSheetModel::DoCreateSheet($sheetData, $sheetItemData);
        if (empty($sheetId))
        {
            return self::Fail('创建充值单失败', 500040);
        }

        return self::Success(self::GenerateCreateSheetResult($sheetCode, $totalPrice));
    }

    /* 开始结算逻辑 */

    public static function DoCheckoutPrepare(array $sheetCodes, string $totalPrice, array $publicParams): LogicResult
    {
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));

        if (empty($sheetCodes))
        {
            return self::Fail('缺少充值单编码必选参数', 400);
        }
        if (count($sheetCodes) > self::CHECKOUT_SHEET_NUM_LIMIT)
        {
            return self::Fail('单次结算充值单数量超过限制(' . self::CHECKOUT_SHEET_NUM_LIMIT . ')', 600080);
        }
        if (empty($totalPrice))
        {
            return self::Fail('缺少总金额必选参数', 400);
        }
        if (empty($hospitalId))
        {
            return self::Fail('缺少医院ID必选参数', 400);
        }

        $sheets = BalanceRechargeSheetModel::GetSheetBySheetCodes($sheetCodes, $hospitalId);
        if (empty($sheets))
        {
            return self::Fail('充值单不存在', 600000);
        }

        // 基本验证
        $memberId      = null;
        $calTotalPrice = 0;
        $sheetIds      = [];
        foreach ($sheetCodes as $sheetCode)
        {
            $sheet = $sheets[$sheetCode] ?? null;
            if (empty($sheet))
            {
                return self::Fail('充值单不存在', 600000);
            }

            if (self::CheckSheetExpired($sheet['expired_at']))
            {
                return self::Fail('充值单已过期，不允许结算', 600030);
            }

            if ($sheet['status'] != SheetStatusEnum::Unpaid->value)
            {
                return self::Fail('充值单状态不允许结算', 600101);
            }

            if (empty($memberId))
            {
                $memberId = $sheet['member_id'];
            }
            else if ($memberId != $sheet['member_id'])
            {
                return self::Fail('充值单会员不一致，不允许结算', 600102);
            }

            $sheetIds[]    = $sheet['id'];
            $calTotalPrice = numberAdd([$calTotalPrice, $sheet['price']], 2);
        }
        if (bccomp($calTotalPrice, $totalPrice, 2) != 0)
        {
            return self::Fail('充值单总金额与传入的总金额不一致', 600103);
        }


        // 批量获取零售购买单明细
        $sheetItems = BalanceRechargeSheetItemModel::getData(
            where:    ['status' => 1],
            whereIn:  ['sheet_id' => $sheetIds],
            orderBys: ['id' => 'asc']
        );
        if (empty($sheetItems))
        {
            return self::Fail('充值单明细不存在', 600001);
        }
        // 按服务单对详情进行分组
        $sheetGroupItems = collect($sheetItems)
            ->groupBy('sheet_id')
            ->toArray();

        $sheetDetailsRes = RechargeSheetHelper::FormatDetailStructure($sheets,
                                                                      $publicParams,
                                                                      true,
                                                                      'sheet_code');
        if ($sheetDetailsRes->isFail())
        {
            return $sheetDetailsRes;
        }
        $sheetDetailsData = $sheetDetailsRes->getData();

        $sheetItemsRes = RechargeSheetHelper::FormatSheetItemStructure($sheetItems, $publicParams, false, 'uid');
        if ($sheetItemsRes->isFail())
        {
            return $sheetItemsRes;
        }
        $sheetItemsData = $sheetItemsRes->getData();

        // 开始构建结算数据
        $errorMsg = [];
        $result   = [];
        foreach ($sheetCodes as $sheetCode)
        {
            $curSheetDetail = $sheetDetailsData[$sheetCode] ?? null;
            if (empty($curSheetDetail))
            {
                $errorMsg[] = '充值单【' . $sheetCode . '】，信息不存在';
                continue;
            }

            $curSheetOriginItems = $sheetGroupItems[$curSheetDetail['id']] ?? [];
            if (empty($curSheetOriginItems))
            {
                $errorMsg[] = '充值单【' . $sheetCode . '】，明细不存在';
                continue;
            }

            $curSheetItems = [];
            foreach ($curSheetOriginItems as $value)
            {
                $tempItem = $sheetItemsData[$value['uid']] ?? null;
                if (empty($tempItem))
                {
                    $errorMsg[] = '充值单【' . $sheetCode . '】，明细信息不完整';
                    continue;
                }
                $curSheetItems[] = $tempItem;
            }

            if (isset($curSheetDetail['id']))
            {
                unset($curSheetDetail['id']);
            }

            $curSheetDetail['items'] = $curSheetItems;
            $result[]                = $curSheetDetail;
        }

        if (!empty($errorMsg))
        {
            return self::Fail(implode("；\n", $errorMsg), 600002);
        }

        return self::success([
                                 'totalPrice' => $calTotalPrice,
                                 'data'       => $result,
                             ]);
    }

    public static function DoLockSheet(array $sheetCodes, array $publicParams): LogicResult
    {
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        if (empty($sheetCodes))
        {
            return self::Fail('缺少充值单编码必选参数', 400);
        }
        if (empty($hospitalId))
        {
            return self::Fail('缺少医院ID必选参数', 400);
        }

        $sheets = BalanceRechargeSheetModel::GetSheetBySheetCodes($sheetCodes, $hospitalId);
        if (empty($sheets))
        {
            return self::Fail('充值单不存在', 600000);
        }

        // 基本验证
        $memberId = null;
        $sheetIds = [];
        foreach ($sheetCodes as $sheetCode)
        {
            $sheet = $sheets[$sheetCode] ?? null;

            if (empty($sheet) || $sheet['status'] == SheetStatusEnum::Cancelled->value)
            {
                return self::Fail('充值单不存在', 600000);
            }

            if ($sheet['status'] != SheetStatusEnum::Unpaid->value)
            {
                return self::Fail('充值单状态不允许锁定', 500940);
            }

            if (self::CheckSheetExpired($sheet['expired_at']))
            {
                return self::Fail('充值单已过期不允许锁定', 500942);
            }

            if (empty($memberId))
            {
                $memberId = $sheet['member_id'];
            }
            else if ($memberId != $sheet['member_id'])
            {
                return self::Fail('充值单会员不一致，不允许锁定', 500941);
            }

            $sheetIds[] = $sheet['id'];
        }

        try
        {
            DB::beginTransaction();

            $lockRes = BalanceRechargeSheetModel::on()
                                                ->whereIn('id', $sheetIds)
                                                ->where(['status' => SheetStatusEnum::Unpaid->value])
                                                ->update(['status' => SheetStatusEnum::Paying->value]);

            if (empty($lockRes) || $lockRes != count(array_unique($sheetIds)))
            {
                // 不符合预期，回滚并返回失败
                DB::rollBack();

                return self::Fail('充值单锁定失败', 500946);
            }

            // 如果一切正常，提交事务
            DB::commit();

            return self::Success();
        } catch (Throwable $throwable)
        {
            // 异常自动回滚
            DB::rollBack();

            Log::error(__CLASS__ . '::' . __METHOD__ . ' 充值单锁定失败', [
                'code'    => $throwable->getCode(),
                'message' => $throwable->getMessage(),
                'file'    => $throwable->getFile(),
                'line'    => $throwable->getLine(),
                'trace'   => $throwable->getTraceAsString(),
            ]);

            return self::Fail('充值单锁定异常', 500947);
        }
    }

    public static function DoUnlockSheet(array $sheetCodes, array $publicParams): LogicResult
    {
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));

        if (empty($sheetCodes))
        {
            return self::Fail('缺少充值单编码必选参数', 400);
        }
        if (empty($hospitalId))
        {
            return self::Fail('缺少医院ID必选参数', 400);
        }

        $sheets = BalanceRechargeSheetModel::GetSheetBySheetCodes($sheetCodes, $hospitalId);
        if (empty($sheets))
        {
            return self::Fail('充值单不存在', 600000);
        }

        // 基本验证
        $memberId = null;
        $sheetIds = [];
        foreach ($sheetCodes as $sheetCode)
        {
            $sheet = $sheets[$sheetCode] ?? null;
            if (empty($sheet) || $sheet['status'] == SheetStatusEnum::Cancelled->value)
            {
                return self::Fail('充值单不存在', 600000);
            }

            if ($sheet['pay_status'] != SheetStatusEnum::Paying->value)
            {
                return self::Fail('充值单状态不允许解锁', 500950);
            }
            if (empty($memberId))
            {
                $memberId = $sheet['member_id'];
            }
            else if ($memberId != $sheet['member_id'])
            {
                return self::Fail('充值单会员不一致，不允许解锁', 500951);
            }

            $sheetIds[] = $sheet['id'];
        }

        try
        {
            DB::beginTransaction();

            $unlockRes = BalanceRechargeSheetModel::on()
                                                  ->whereIn('id', $sheetIds)
                                                  ->where(['status' => SheetStatusEnum::Paying->value])
                                                  ->update(['status' => SheetStatusEnum::Unpaid->value]);

            if (empty($unlockRes) || $unlockRes != count(array_unique($sheetIds)))
            {
                // 不符合预期，回滚并返回失败
                DB::rollBack();

                return self::Fail('充值单解锁失败', 500956);
            }

            // 符合预期，提交事务
            DB::commit();

            return self::Success();
        } catch (Throwable $throwable)
        {
            // 捕获任何异常，确保回滚
            DB::rollBack();

            Log::error(__CLASS__ . '::' . __METHOD__ . ' 充值单解锁异常', [
                'code'    => $throwable->getCode(),
                'message' => $throwable->getMessage(),
                'file'    => $throwable->getFile(),
                'line'    => $throwable->getLine(),
                'trace'   => $throwable->getTraceAsString(),
            ]);

            return self::Fail('充值单解锁异常', 500957);
        }
    }

    public static function GetSheetSummary(array $sheetCodes, array $publicParams): LogicResult
    {
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));

        if (empty($sheetCodes))
        {
            return self::Fail('缺少充值单编码必选参数', 400);
        }
        if (count($sheetCodes) > self::CHECKOUT_SHEET_NUM_LIMIT)
        {
            return self::Fail('单次结算充值单数量超过限制(' . self::CHECKOUT_SHEET_NUM_LIMIT . ')', 601301);
        }
        if (empty($hospitalId))
        {
            return self::Fail('缺少医院ID必选参数', 400);
        }

        $sheets = BalanceRechargeSheetModel::GetSheetBySheetCodes($sheetCodes, $hospitalId);
        if (empty($sheets))
        {
            return self::Fail('充值单不存在', 500900);
        }

        // 基本验证
        $calTotalPrice = 0;
        foreach ($sheetCodes as $sheetCode)
        {
            $sheet = $sheets[$sheetCode] ?? null;
            if (empty($sheet) || $sheet['status'] == SheetStatusEnum::Cancelled->value)
            {
                return self::Fail('充值单不存在', 500900);
            }

            $calTotalPrice = numberAdd([$calTotalPrice, $sheet['price']], 2);
        }

        $sheetDetailsRes = RechargeSheetHelper::FormatDetailStructure($sheets,
                                                                      $publicParams,
                                                                      true,
                                                                      'sheet_code');
        if ($sheetDetailsRes->isFail())
        {
            return $sheetDetailsRes;
        }
        $sheetDetailsData = $sheetDetailsRes->getData();

        // 开始构建结算数据
        $errorMsg = [];
        $result   = [];
        foreach ($sheetCodes as $sheetCode)
        {
            $curSheetDetail = $sheetDetailsData[$sheetCode] ?? null;
            if (empty($curSheetDetail))
            {
                $errorMsg[] = '充值单【' . $sheetCode . '】，信息不存在';
                continue;
            }

            if (isset($curSheetDetail['id']))
            {
                unset($curSheetDetail['id']);
            }

            $result[] = $curSheetDetail;
        }

        if (!empty($errorMsg))
        {
            return self::Fail(implode("；\n", $errorMsg), 500902);
        }

        return self::success([
                                 'totalPrice' => $calTotalPrice,
                                 'data'       => $result,
                             ]);
    }
}
