<?php

namespace App\Logics\V1\BuySheet;

use Throwable;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Logics\LogicResult;
use App\Logics\V1\BuySheet\Interfaces\CheckoutInterface;
use App\Logics\V1\BuySheet\Interfaces\LockInterface;
use App\Logics\V1\BuySheet\Interfaces\DetailInterface;
use App\Enums\BusinessCodePrefixEnum;
use App\Enums\SheetStatusEnum;
use App\Enums\PageEnum;
use App\Models\MemberPetsModel;
use App\Models\MemberModel;
use App\Models\UsersModel;
use App\Models\BeautyServiceSheetModel;
use App\Models\BeautyServiceSheetItemModel;
use App\Support\Concurrent\ConcurrentTask;
use App\Support\Beauty\BeautySheetHelper;
use App\Support\Member\MemberHelper;
use App\Support\User\HospitalUserHelper;

class BeautyServiceSheetLogic extends BuySheetCommonBase implements CheckoutInterface, LockInterface, DetailInterface
{
    public static function GenerateSheetCode(): string
    {
        return generateBusinessCodeNumber(BusinessCodePrefixEnum::XMGMD);
    }

    public static function CreateSheet(array $params, array $publicParams): LogicResult
    {
        //公共参数
        $hospitalOrgId   = intval(Arr::get($publicParams, '_hospitalOrgId'));
        $hospitalBrandId = intval(Arr::get($publicParams, '_hospitalBrandId'));
        $hospitalId      = intval(Arr::get($publicParams, '_hospitalId'));
        $userId          = intval(Arr::get($publicParams, '_userId'));

        //业务参数
        $petId      = intval(Arr::get($params, 'petId', 0));
        $petUid     = trim(Arr::get($params, 'petUid', ''));
        $items      = Arr::get($params, 'items', []);
        $totalPrice = trim(Arr::get($params, 'totalPrice', '0.00'));

        //特殊参数
        //是否创建并写数据库，否则返回待写库的数据
        $isCreate = boolval(Arr::get($params, 'isCreate', true));

        if (empty($hospitalOrgId) || empty($hospitalBrandId) || empty($hospitalId))
        {
            return self::Fail('创建洗美服务单，缺少医院必选参数', 400);
        }
        if (empty($userId))
        {
            return self::Fail('创建洗美服务单，缺少登录用户ID必选参数', 400);
        }
        if (empty($petId) && empty($petUid))
        {
            return self::Fail('创建洗美服务单，缺少宠物ID/UID必选参数', 400);
        }
        if (empty($items))
        {
            return self::Fail('创建洗美服务单，缺少服务明细必选参数', 400);
        }
        if (!is_numeric($totalPrice) || bccomp($totalPrice, '0', 2) != 1)
        {
            return self::Fail('创建洗美服务单，缺少总金额必选参数', 400);
        }

        $pet = MemberPetsModel::getOneByIdOrUid(id: $petId, uid: $petUid, orgId: $hospitalOrgId);
        if (empty($pet))
        {
            return self::Fail('宠物不存在', 32000);
        }
        $petId    = $pet->id;
        $memberId = $pet->member_id;

        //验证服务项目
        $verifyItemsRes = BeautySheetHelper::GetValidSheetItems($petId, $items, $publicParams);
        if ($verifyItemsRes->isFail())
        {
            return $verifyItemsRes;
        }

        //服务单商品种类
        $addItems     = $verifyItemsRes->getData('item', []);
        $addSuitItems = $verifyItemsRes->getData('suit', []);
        if (empty($addItems) && empty($addSuitItems))
        {
            return self::Fail('洗美服务单中商品全部无效', 500204);
        }

        //服务单开具商品的基本信息
        $addItemsInfo     = $verifyItemsRes->getData('itemsInfo', []);
        $addSuitItemsInfo = $verifyItemsRes->getData('suitItemsInfo', []);
        if (empty($addItemsInfo) && empty($addSuitItemsInfo))
        {
            return self::Fail('洗美服务单中商品全部不存在', 500204);
        }

        //严格验证服务单中不同类型商品
        $task = ConcurrentTask::new();
        if (!empty($addItems))
        {
            $task->addTask('items',
                fn() => BeautySheetHelper::GetAddItems($memberId,
                                                       $petId,
                                                       $addItems,
                                                       $addItemsInfo,
                                                       $publicParams));
        }
        if (!empty($addSuitItems))
        {
            $task->addTask('suits',
                fn() => BeautySheetHelper::GetAddSuits($memberId,
                                                       $petId,
                                                       $addSuitItems,
                                                       $addSuitItemsInfo,
                                                       $publicParams));
        }

        DB::disconnect();
        $getAddRes = $task->run();
        if (empty($getAddRes))
        {
            return self::Fail('洗美服务单中商品全部不存在', 400);
        }

        //严格验证服务单中不同类型商品出现异常
        foreach ($getAddRes as $result)
        {
            if ($result->isException())
            {
                Log::error(__CLASS__ . '::' . __METHOD__ . ' 保存洗美服务单，严格验证商品信息异常', [
                    'code'    => $result->getException()
                                        ->getCode(),
                    'message' => $result->getException()
                                        ->getMessage(),
                    'file'    => $result->getException()
                                        ->getFile(),
                    'line'    => $result->getException()
                                        ->getLine(),
                    'trace'   => $result->getException()
                                        ->getTraceAsString(),
                ]);

                return self::Fail('保存洗美服务单，商品信息错误', 400);
            }
        }

        //服务单信息
        $totalCalcPrice = 0;
        $itemData       = [];

        $addItemsRes     = isset($getAddRes['items']) ? $getAddRes['items']->getData() : [];
        $addSuitItemsRes = isset($getAddRes['suits']) ? $getAddRes['suits']->getData() : [];

        if ($addItemsRes instanceof LogicResult)
        {
            if ($addItemsRes->isFail())
            {
                return $addItemsRes;
            }

            $totalCalcPrice = numberAdd([$totalCalcPrice, $addItemsRes->getData('totalPrice', 0)]);
            $itemData       = array_merge($itemData, $addItemsRes->getData('items', []));
        }

        if ($addSuitItemsRes instanceof LogicResult)
        {
            if ($addSuitItemsRes->isFail())
            {
                return $addSuitItemsRes;
            }

            $totalCalcPrice = numberAdd([$totalCalcPrice, $addSuitItemsRes->getData('totalPrice', 0)]);
            $suitItemsData  = $addSuitItemsRes->getData('suitItems', []);
            foreach ($suitItemsData as $suitInfo)
            {
                if (empty($suitInfo))
                {
                    continue;
                }

                $itemData[] = $suitInfo;
            }
        }

        if (bccomp($totalCalcPrice, $totalPrice, 2) != 0)
        {
            return self::Fail('洗美服务单中商品价格有变动，请刷新重试', 500292);
        }

        // 服务单主数据
        $sheetCode  = self::GenerateSheetCode();
        $insertData = [
            'sheet_code'  => $sheetCode,
            'org_id'      => $hospitalOrgId,
            'brand_id'    => $hospitalBrandId,
            'hospital_id' => $hospitalId,
            'member_id'   => $memberId,
            'pet_id'      => $petId,
            'price'       => $totalPrice,
            'status'      => 1,
            'created_by'  => $userId,
            'sold_by'     => $userId,
            'order_time'  => getCurrentTimeWithMilliseconds(),
        ];

        //按照原始顺序排列数据
        $insertItemsData = [];
        foreach ($items as $value)
        {
            foreach ($itemData as $key => $item)
            {
                // 当前商品顺序与提交不一致
                if ($value['itemUid'] != $item['item_uid'] || ($value['isSuit'] ? 1 : 0) != $item['is_suit'])
                {
                    continue;
                }

                // 删除商品uid
                unset($item['item_uid']);

                if ($item['is_suit'])
                {
                    $suitItems = $item['suit_items'];
                    unset($item['suit_items']);
                    $insertItemsData[] = $item;
                    foreach ($suitItems as $suitItem)
                    {
                        // 删除商品uid
                        if (isset($suitItem['item_uid']))
                        {
                            unset($suitItem['item_uid']);
                        }

                        $insertItemsData[] = $suitItem;
                    }
                }
                else
                {
                    $insertItemsData[] = $item;
                }

                unset($itemData[$key]);
                break;
            }
        }

        // 非新增模式情况下，返回组装好写入的数据
        if (!$isCreate)
        {
            return self::Success([
                                     'itemsData'  => $insertItemsData,
                                     'totalPrice' => $totalPrice,
                                 ]);
        }


        $insertRes = BeautyServiceSheetModel::DoCreateSheet($insertData, $insertItemsData);
        if (empty($insertRes))
        {
            return self::Fail('创建洗美服务单失败', 500298);
        }

        return self::Success(self::GenerateCreateSheetResult($sheetCode, $totalPrice));
    }

    public static function EditSheet(array $params, array $publicParams): LogicResult
    {
        //业务参数
        $sheetCode  = trim(Arr::get($params, 'sheetCode', ''));
        $sheetId    = intval(Arr::get($params, 'sheetId', 0));
        $items      = Arr::get($params, 'items', []);
        $totalPrice = trim(Arr::get($params, 'totalPrice', '0.00'));

        // 公共参数
        $hospitalId      = intval(Arr::get($publicParams, '_hospitalId'));
        $hospitalOrgId   = intval(Arr::get($publicParams, '_hospitalOrgId'));
        $hospitalBrandId = intval(Arr::get($publicParams, '_hospitalBrandId'));
        $userId          = intval(Arr::get($publicParams, '_userId'));

        if (empty($hospitalOrgId) || empty($hospitalBrandId) || empty($hospitalId))
        {
            return self::Fail('编辑洗美服务单，缺少医院必选参数', 400);
        }
        if (empty($userId))
        {
            return self::Fail('编辑洗美服务单，缺少登录用户ID必选参数', 400);
        }
        if (empty($sheetCode) && empty($sheetId))
        {
            return self::Fail('编辑洗美服务单，缺少宠物ID/UID必选参数', 400);
        }
        if (empty($items))
        {
            return self::Fail('编辑洗美服务单，缺少服务明细必选参数', 400);
        }
        if (!is_numeric($totalPrice) || bccomp($totalPrice, '0', 2) != 1)
        {
            return self::Fail('编辑洗美服务单，缺少总金额必选参数', 400);
        }

        $sheetRes = BeautySheetHelper::GetValidSheet($sheetCode, $sheetId, $hospitalId);
        if ($sheetRes->isFail())
        {
            return $sheetRes;
        }

        $sheet   = $sheetRes->getData();
        $sheetId = $sheet['id'];

        // 是否可编辑
        $checkEditRes = self::CheckSheetEditOrDelete($sheet, $publicParams);
        if ($checkEditRes->isFail())
        {
            return $checkEditRes;
        }

        // 获取扁平化数据
        $getItemsDataRes = self::CreateSheet(
            ['petId' => $sheet['pet_id'], 'items' => $items, 'totalPrice' => $totalPrice, 'isCreate' => false],
            $publicParams
        );
        if ($getItemsDataRes->isFail())
        {
            return $getItemsDataRes;
        }

        $totalCalcPrice = $getItemsDataRes->getData('totalPrice', 0);
        $itemsData      = $getItemsDataRes->getData('itemsData', []);
        if (empty($itemsData))
        {
            return self::Fail('编辑洗美服务单失败，服务单明细无效', 500380);
        }

        // 获取服务单旧的明细
        $selectField   = [
            'uid',
            'hospital_id',
            'member_id',
            'pet_id',
            'item_suit_id',
            'item_id',
            'price',
            'quantity',
            'remark',
            'suit_group',
            'is_suit',
            'suit_unique_uid',
            'status',
            'created_by',
        ];
        $getOldItemRes = BeautyServiceSheetItemModel::getData($selectField, ['sheet_id' => $sheetId, 'status' => 1]);
        if (empty($getOldItemRes))
        {
            return self::Fail('编辑洗美服务单失败，服务单明细获取失败', 500380);
        }

        // 使用uid为key
        $newItems = array_column($itemsData, null, 'uid');//此uid必然存在，新数据会生成新的uid,老数据会带过来数据库的uid
        $oldItems = array_column($getOldItemRes, null, 'uid');

        // 新增的数据
        $insertItems = [];

        // 更新的数据
        $updateItems = [];

        // 删除的数据
        $deleteItemsUids = [];

        // 找出需要新增的项目（新的数据存在，老的数据不存在）
        $newItemUids = array_diff(array_keys($newItems), array_keys($oldItems));
        array_walk($newItemUids, function ($uid) use (&$insertItems, $newItems) {
            $insertItems[] = $newItems[$uid];
        });

        // 找出可能需要更新的项目（两边都存在的项目）
        $commonUids = array_intersect(array_keys($newItems), array_keys($oldItems));
        foreach ($commonUids as $uid)
        {
            // 如果无差异，跳过
            $newItem = $newItems[$uid];
            $oldItem = $oldItems[$uid];
            if (empty(array_diff_assoc($newItem, $oldItem)))
            {
                continue;
            }

            // 比较两个数组，找出差异
            $curDiffInfo = [];
            foreach ($newItem as $key => $value)
            {
                // 只比较旧数据中存在的字段
                if (array_key_exists($key, $oldItem) && $value != $oldItem[$key])
                {
                    $curDiffInfo[$key] = $value;
                }
            }

            // 如果有差异，添加到更新列表
            if (!empty($curDiffInfo))
            {
                $curDiffInfo['uid'] = $uid;
                $updateItems[]      = $curDiffInfo;
            }
        }

        // 找出需要删除的项目（在旧数据中存在但新数据中不存在）
        $deleteItemsUids = array_diff(array_keys($oldItems), array_keys($newItems));

        // 商品无变动，关联业务表也不会变更
        if (empty($insertItems) && empty($updateItems) && empty($deleteItemsUids))
        {
            return self::Success();
        }

        if (bccomp($totalCalcPrice, $totalPrice, 2) != 0)
        {
            return self::Fail('洗美服务单中商品价格有变动，请刷新重试', 500292);
        }

        $sheetData = [
            'price' => $totalPrice,
        ];

        $updateRes = BeautyServiceSheetModel::DoEditSheet($sheetId,
                                                          $sheetData,
                                                          $insertItems,
                                                          $updateItems,
                                                          $deleteItemsUids,
                                                          $userId);
        if (!$updateRes)
        {
            return self::Fail('更新洗美服务单失败', 500380);
        }

        return self::Success();
    }

    public static function DeleteSheet(array $params, array $publicParams): LogicResult
    {
        //业务参数
        $sheetCode = trim(Arr::get($params, 'sheetCode', ''));
        $sheetId   = intval(Arr::get($params, 'sheetId', 0));

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        $userId     = intval(Arr::get($publicParams, '_userId'));

        if (empty($hospitalId))
        {
            return self::Fail('删除洗美服务单，缺少医院必选参数', 400);
        }
        if (empty($userId))
        {
            return self::Fail('删除洗美服务单，缺少登录用户ID必选参数', 400);
        }
        if (empty($sheetCode) && empty($sheetId))
        {
            return self::Fail('删除洗美服务单，缺少宠物ID/UID必选参数', 400);
        }

        $sheetRes = BeautySheetHelper::GetValidSheet($sheetCode, $sheetId, $hospitalId);
        if ($sheetRes->isFail())
        {
            return $sheetRes;
        }

        $sheet   = $sheetRes->getData();
        $sheetId = $sheet['id'];

        // 是否可删除
        $checkEditRes = self::CheckSheetEditOrDelete($sheet, $publicParams);
        if ($checkEditRes->isFail())
        {
            return $checkEditRes;
        }

        $updateData = [
            'cancel_by' => $userId,
            'cancel_at' => getCurrentTimeWithMilliseconds(),
        ];

        $updateRes = BeautyServiceSheetModel::DoSoftDelete($sheetId,
                                                           SheetStatusEnum::Cancelled->value,
                                                           SheetStatusEnum::Unpaid->value,
                                                           $updateData);

        if (empty($updateRes))
        {
            return self::Fail('删除洗美服务单失败', 500390);
        }

        return self::Success();
    }

    public static function CheckSheetEditOrDelete(array $sheet, array $publicParams): LogicResult
    {
        return BeautySheetHelper::CheckSheetEditOrDelete($sheet, $publicParams);
    }

    public static function GetSheetMemberOptions(int|null $status, array $publicParams): LogicResult
    {
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        if (empty($hospitalId))
        {
            return self::Fail('获取洗美服务单会员选项，缺少医院ID必选参数', 400);
        }

        $memberIdsList = BeautyServiceSheetModel::GetSheetMemberOptions($hospitalId, $status);
        if ($memberIdsList->isEmpty())
        {
            return self::Success([]);
        }

        $memberIds = $memberIdsList->pluck('member_id')
                                   ->unique()
                                   ->toArray();

        return self::Success(MemberHelper::GetMemberOptionsByMemberIds($memberIds));
    }

    public static function GetCreateUsersOptions(int|null $status, array $publicParams): LogicResult
    {
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        if (empty($hospitalId))
        {
            return self::Fail('获取洗美服务单创建人选项，缺少医院ID必选参数', 400);
        }

        $userIdsList = BeautyServiceSheetModel::GetSheetCreateUsersOptions($hospitalId, $status);
        if ($userIdsList->isEmpty())
        {
            return self::Success([]);
        }

        $userIds = $userIdsList->pluck('created_by')
                               ->unique()
                               ->toArray();

        return self::Success(HospitalUserHelper::GetUserOptionsByUserIds($userIds, $hospitalId));
    }

    public static function GetUnpaidSheetList(array $params, array $publicParams): LogicResult
    {
        $memberId      = intval(Arr::get($params, 'memberId', 0));
        $memberUid     = trim(Arr::get($params, 'memberUid', ''));
        $createUserId  = intval(Arr::get($params, 'createUserId', 0));
        $createUserUid = trim(Arr::get($params, 'createUserUid', ''));
        $startDate     = trim(Arr::get($params, 'startDate', ''));
        $endDate       = trim(Arr::get($params, 'endDate', ''));
        $page          = intval(Arr::get($params, 'page', PageEnum::DefaultPageIndex->value));
        $count         = intval(Arr::get($params, 'count', PageEnum::DefaultPageSize->value));
        //公共参数
        $hospitalId    = intval(Arr::get($publicParams, '_hospitalId'));
        $hospitalOrgId = intval(Arr::get($publicParams, '_hospitalOrgId'));
        if (empty($hospitalId) || empty($hospitalOrgId))
        {
            return self::Fail('获取洗美服务单待支付列表，缺少医院ID、组织ID必选参数', 400);
        }

        if (!empty($memberUid) || !empty($memberId))
        {
            $member = MemberModel::getOneByIdOrUid(id: $memberId, uid: $memberUid, orgId: $hospitalOrgId);
            if (empty($member))
            {
                return self::Fail('会员不存在', 30001);
            }
            $memberId = $member->id;
        }
        if (!empty($createUserUid) || !empty($createUserId))
        {
            $createUser = UsersModel::getOneByIdOrUid(id: $createUserId, uid: $createUserUid);
            if (empty($createUser))
            {
                return self::Fail('创建人不存在', 10100);
            }
            $createUserId = $createUser->id;
        }
        if (!empty($startDate) && strtotime($startDate) === false)
        {
            return self::Fail('开始日期格式错误', 400);
        }
        if (!empty($endDate) && strtotime($endDate) === false)
        {
            return self::Fail('结束日期格式错误', 400);
        }
        if (!empty($startDate) && !empty($endDate) && strtotime($startDate) > strtotime($endDate))
        {
            return self::Fail('开始日期不能大于结束日期', 400);
        }

        //构建查询条件
        $getWhere = [
            ['hospital_id', '=', $hospitalId],
            ['status', '=', SheetStatusEnum::Unpaid->value],
        ];
        if (!empty($memberId))
        {
            $getWhere[] = ['member_id', '=', $memberId];
        }
        if (!empty($createUserId))
        {
            $getWhere[] = ['created_by', '=', $createUserId];
        }
        if (!empty($startDate))
        {
            $getWhere[] = ['created_at', '>=', $startDate . ' 00:00:00'];
        }
        if (!empty($endDate))
        {
            $getWhere[] = ['created_at', '<=', $endDate . ' 23:59:59'];
        }


        // 获取条目总数
        $total = BeautyServiceSheetModel::getTotalNumber(where: $getWhere);
        if (empty($total) || $total <= 0)
        {
            return self::Success(['total' => 0, 'data' => []]);
        }

        // 获取条目数据
        $list = BeautyServiceSheetModel::getData(
            where:     $getWhere,
            orderBys:  ['created_at' => 'desc'],
            pageIndex: $page,
            pageSize:  $count
        );

        if (empty($list))
        {
            return self::Success(['total' => 0, 'data' => []]);
        }

        return self::Success([
                                 'total' => $total,
                                 'data'  => BeautySheetHelper::FormatSheetListStructure($list, $publicParams)
                             ]);
    }

    public static function GetSheetDetail(array $params, array $publicParams): LogicResult
    {
        $sheetCode = trim(Arr::get($params, 'sheetCode', ''));
        $sheetId   = intval(Arr::get($params, 'sheetId', 0));
        //公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));

        if (empty($sheetCode) && empty($sheetId))
        {
            return self::Fail('获取洗美服务单详情，缺少单据ID/编码必选参数', 400);
        }
        if (empty($hospitalId))
        {
            return self::Fail('获取洗美服务单详情，缺少医院ID必选参数', 400);
        }

        $sheetRes = BeautySheetHelper::GetValidSheet($sheetCode, $sheetId, $hospitalId);
        if ($sheetRes->isFail())
        {
            return $sheetRes;
        }
        $sheet   = $sheetRes->getData();
        $sheetId = $sheet['id'];

        $sheetItems = BeautyServiceSheetItemModel::getData(where: ['sheet_id' => $sheetId, 'status' => 1]);
        if (empty($sheetItems))
        {
            return self::Fail('洗美服务单明细不存在', 500311);
        }

        $detailsRes = BeautySheetHelper::FormatDetailStructure([$sheet], $publicParams);
        if ($detailsRes->isFail())
        {
            return $detailsRes;
        }
        $detailRes  = $detailsRes->getData();
        $detailData = is_array($detailRes) && !empty($detailRes) ? current($detailRes) : null;
        if (empty($detailData))
        {
            return self::Fail('洗美服务单详情不存在', 500310);
        }

        $itemsRes = BeautySheetHelper::FormatSheetItemStructure($sheetItems, $publicParams, true);
        if ($itemsRes->isFail())
        {
            return $itemsRes;
        }
        $itemsData = $itemsRes->getData();
        if (empty($itemsData))
        {
            return self::Fail('洗美服务单明细不存在', 500311);
        }

        $detailData['items'] = $itemsData;

        return self::Success($detailData);
    }

    /* 开始结算逻辑 */

    public static function DoCheckoutPrepare(array $sheetCodes, string $totalPrice, array $publicParams): LogicResult
    {
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));

        if (empty($sheetCodes))
        {
            return self::Fail('缺少洗美服务单编码必选参数', 400);
        }
        if (empty($totalPrice))
        {
            return self::Fail('缺少总金额必选参数', 400);
        }
        if (empty($hospitalId))
        {
            return self::Fail('缺少医院ID必选参数', 400);
        }

        $sheets = BeautyServiceSheetModel::GetSheetBySheetCodes($sheetCodes, $hospitalId);
        if (empty($sheets))
        {
            return self::Fail('洗美服务单不存在', 600000);
        }

        // 基本验证
        $memberId      = null;
        $calTotalPrice = 0;
        $sheetIds      = [];
        foreach ($sheetCodes as $sheetCode)
        {
            $sheet = $sheets[$sheetCode] ?? null;
            if (empty($sheet))
            {
                return self::Fail('洗美服务单不存在', 600000);
            }

            if ($sheet['status'] != SheetStatusEnum::Unpaid->value)
            {
                return self::Fail('洗美服务单状态不允许结算', 600101);
            }

            if (empty($memberId))
            {
                $memberId = $sheet['member_id'];
            }
            else if ($memberId != $sheet['member_id'])
            {
                return self::Fail('洗美服务单会员不一致，不允许结算', 600102);
            }

            $calTotalPrice = numberAdd([$calTotalPrice, $sheet['price']], 2);
            $sheetIds[]    = $sheet['id'];
        }
        if (bccomp($calTotalPrice, $totalPrice, 2) != 0)
        {
            return self::Fail('洗美服务单总金额与传入的总金额不一致', 600103);
        }

        // 批量获取洗美服务单明细
        $sheetItems = BeautyServiceSheetItemModel::getData(
            where:    ['status' => 1],
            whereIn:  ['sheet_id' => $sheetIds],
            orderBys: ['id' => 'asc']
        );
        if (empty($sheetItems))
        {
            return self::Fail('洗美服务单明细不存在', 600001);
        }
        // 按服务单对详情进行分组
        $sheetGroupItems = collect($sheetItems)
            ->groupBy('sheet_id')
            ->toArray();

        $sheetDetailsRes = BeautySheetHelper::FormatDetailStructure($sheets, $publicParams, true, 'sheet_code');
        if ($sheetDetailsRes->isFail())
        {
            return $sheetDetailsRes;
        }
        $sheetDetailsData = $sheetDetailsRes->getData();

        $sheetItemsRes = BeautySheetHelper::FormatSheetItemStructure($sheetItems, $publicParams, false, 'uid');
        if ($sheetItemsRes->isFail())
        {
            return $sheetItemsRes;
        }
        $sheetItemsData = $sheetItemsRes->getData();

        // 开始构建结算数据
        $errorMsg = [];
        $result   = [];
        foreach ($sheetCodes as $sheetCode)
        {
            $curSheetDetail = $sheetDetailsData[$sheetCode] ?? null;
            if (empty($curSheetDetail))
            {
                $errorMsg[] = '洗美服务单【' . $sheetCode . '】，信息不存在';
                continue;
            }

            $curSheetOriginItems = $sheetGroupItems[$curSheetDetail['id']] ?? [];
            if (empty($curSheetOriginItems))
            {
                $errorMsg[] = '洗美服务单【' . $sheetCode . '】，明细不存在';
                continue;
            }

            $curSheetItems = [];
            foreach ($curSheetOriginItems as $value)
            {
                //组合子项目过滤
                if ($value['suit_unique_uid'] != '' && $value['is_suit'] == 0)
                {
                    continue;
                }

                $tempItem = $sheetItemsData[$value['uid']] ?? null;
                if (empty($tempItem))
                {
                    $errorMsg[] = '洗美服务单【' . $sheetCode . '】，明细信息不完整';
                    continue;
                }
                $curSheetItems[] = $tempItem;
            }

            if (isset($curSheetDetail['id']))
            {
                unset($curSheetDetail['id']);
            }

            $curSheetDetail['items'] = $curSheetItems;
            $result[]                = $curSheetDetail;
        }

        if (!empty($errorMsg))
        {
            return self::Fail(implode("；\n", $errorMsg), 600002);
        }

        return self::success([
                                 'totalPrice' => $calTotalPrice,
                                 'data'       => $result,
                             ]);
    }

    public static function DoLockSheet(array $sheetCodes, array $publicParams): LogicResult
    {
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));

        if (empty($sheetCodes))
        {
            return self::Fail('缺少洗美服务单编码必选参数', 400);
        }
        if (empty($hospitalId))
        {
            return self::Fail('缺少医院ID必选参数', 400);
        }

        $sheets = BeautyServiceSheetModel::GetSheetBySheetCodes($sheetCodes, $hospitalId);
        if (empty($sheets))
        {
            return self::Fail('洗美服务单不存在', 600000);
        }

        // 基本验证
        $memberId = null;
        $sheetIds = [];
        foreach ($sheetCodes as $sheetCode)
        {
            $sheet = $sheets[$sheetCode] ?? null;
            if (empty($sheet))
            {
                return self::Fail('洗美服务单不存在', 600000);
            }

            if ($sheet['status'] != SheetStatusEnum::Unpaid->value)
            {
                return self::Fail('购买单当前状态不允许锁定操作', 500940);
            }

            if (empty($memberId))
            {
                $memberId = $sheet['member_id'];
            }
            else if ($memberId != $sheet['member_id'])
            {
                return self::Fail('洗美服务单会员不一致，不允许锁定', 500941);
            }

            $sheetIds[] = $sheet['id'];
        }


        try
        {
            DB::beginTransaction();

            $lockRes = BeautyServiceSheetModel::on()
                                              ->whereIn('id', $sheetIds)
                                              ->where(['status' => SheetStatusEnum::Unpaid->value])
                                              ->update(['status' => SheetStatusEnum::Paying->value]);

            if (empty($lockRes) || $lockRes != count(array_unique($sheetIds)))
            {
                // 不符合预期，回滚并返回失败
                DB::rollBack();

                return self::Fail('洗美服务单锁定失败', 500946);
            }

            // 如果一切正常，提交事务
            DB::commit();

            return self::Success();
        } catch (Throwable $throwable)
        {
            // 异常自动回滚
            DB::rollBack();

            Log::error(__CLASS__ . '::' . __METHOD__ . ' 洗美服务单锁定失败', [
                'code'    => $throwable->getCode(),
                'message' => $throwable->getMessage(),
                'file'    => $throwable->getFile(),
                'line'    => $throwable->getLine(),
                'trace'   => $throwable->getTraceAsString(),
            ]);

            return self::Fail('洗美服务单锁定异常', 500947);
        }
    }

    public static function DoUnlockSheet(array $sheetCodes, array $publicParams): LogicResult
    {
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));

        if (empty($sheetCodes))
        {
            return self::Fail('缺少洗美服务单编码必选参数', 400);
        }
        if (empty($hospitalId))
        {
            return self::Fail('缺少医院ID必选参数', 400);
        }

        $sheets = BeautyServiceSheetModel::GetSheetBySheetCodes($sheetCodes, $hospitalId);
        if (empty($sheets))
        {
            return self::Fail('洗美服务单不存在', 600000);
        }

        // 基本验证
        $memberId = null;
        $sheetIds = [];
        foreach ($sheetCodes as $sheetCode)
        {
            $sheet = $sheets[$sheetCode] ?? null;
            if (empty($sheet))
            {
                return self::Fail('洗美服务单不存在', 600000);
            }

            if ($sheet['status'] != SheetStatusEnum::Paying->value)
            {
                return self::Fail('洗美服务单状态不允许解锁', 500950);
            }

            if (empty($memberId))
            {
                $memberId = $sheet['member_id'];
            }
            else if ($memberId != $sheet['member_id'])
            {
                return self::Fail('洗美服务单会员不一致，不允许解锁', 500951);
            }

            $sheetIds[] = $sheet['id'];
        }

        try
        {
            DB::beginTransaction();

            $unlockRes = BeautyServiceSheetModel::on()
                                                ->whereIn('id', $sheetIds)
                                                ->where(['status' => SheetStatusEnum::Paying->value])
                                                ->update(['status' => SheetStatusEnum::Unpaid->value]);

            if (empty($unlockRes) || $unlockRes != count(array_unique($sheetIds)))
            {
                // 不符合预期，回滚并返回失败
                DB::rollBack();

                return self::Fail('洗美服务单解锁失败', 500956);
            }

            // 符合预期，提交事务
            DB::commit();

            return self::Success();
        } catch (Throwable $throwable)
        {
            // 捕获任何异常，确保回滚
            DB::rollBack();

            Log::error(__CLASS__ . '::' . __METHOD__ . ' 洗美服务单解锁异常', [
                'code'    => $throwable->getCode(),
                'message' => $throwable->getMessage(),
                'file'    => $throwable->getFile(),
                'line'    => $throwable->getLine(),
                'trace'   => $throwable->getTraceAsString(),
            ]);

            return self::Fail('洗美服务单解锁异常', 500957);
        }
    }

    public static function GetSheetSummary(array $sheetCodes, array $publicParams): LogicResult
    {
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));

        if (empty($sheetCodes))
        {
            return self::Fail('缺少洗美服务单编码必选参数', 400);
        }
        if (empty($hospitalId))
        {
            return self::Fail('缺少医院ID必选参数', 400);
        }

        $sheets = BeautyServiceSheetModel::GetSheetBySheetCodes($sheetCodes, $hospitalId);
        if (empty($sheets))
        {
            return self::Fail('洗美服务单不存在', 500900);
        }

        // 基本验证
        $calTotalPrice = 0;
        foreach ($sheetCodes as $sheetCode)
        {
            $sheet = $sheets[$sheetCode] ?? null;
            if (empty($sheet) || $sheet['status'] == SheetStatusEnum::Cancelled->value)
            {
                return self::Fail('洗美服务单不存在', 500900);
            }
            $calTotalPrice = numberAdd([$calTotalPrice, $sheet['price']], 2);
        }

        $sheetDetailsRes = BeautySheetHelper::FormatDetailStructure($sheets, $publicParams, true, 'sheet_code');
        if ($sheetDetailsRes->isFail())
        {
            return $sheetDetailsRes;
        }
        $sheetDetailsData = $sheetDetailsRes->getData();

        // 开始构建结算数据
        $errorMsg = [];
        $result   = [];
        foreach ($sheetCodes as $sheetCode)
        {
            $curSheetDetail = $sheetDetailsData[$sheetCode] ?? null;
            if (empty($curSheetDetail))
            {
                $errorMsg[] = '洗美服务单【' . $sheetCode . '】，信息不存在';
                continue;
            }

            if (isset($curSheetDetail['id']))
            {
                unset($curSheetDetail['id']);
            }

            $result[] = $curSheetDetail;
        }

        if (!empty($errorMsg))
        {
            return self::Fail(implode("；\n", $errorMsg), 500902);
        }

        return self::success([
                                 'totalPrice' => $calTotalPrice,
                                 'data'       => $result,
                             ]);
    }
}
