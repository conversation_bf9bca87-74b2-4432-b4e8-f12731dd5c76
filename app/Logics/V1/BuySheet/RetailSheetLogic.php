<?php

namespace App\Logics\V1\BuySheet;

use Throwable;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Logics\LogicResult;
use App\Logics\V1\BuySheet\Interfaces\CheckoutInterface;
use App\Logics\V1\BuySheet\Interfaces\LockInterface;
use App\Logics\V1\BuySheet\Interfaces\DetailInterface;
use App\Enums\SheetStatusEnum;
use App\Enums\ItemUnitTypeEnum;
use App\Enums\StockReduceTypeEnum;
use App\Enums\BusinessCodePrefixEnum;
use App\Models\RetailSheetModel;
use App\Models\RetailSheetItemModel;
use App\Models\MemberModel;
use App\Models\UsersModel;
use App\Support\Retail\RetailSheetHelper;
use App\Support\Stock\StockVerifierHelper;
use App\Logics\V1\HospitalUserLogic;
use App\Logics\V1\ItemLogic;
use App\Logics\V1\StockItemShelfReduceLogic;
use App\Models\StockItemShelfReduceModel;

class RetailSheetLogic extends BuySheetCommonBase implements CheckoutInterface, LockInterface, DetailInterface
{
    public static function GenerateSheetCode(): string
    {
        return generateBusinessCodeNumber(BusinessCodePrefixEnum::LSGMD);
    }

    public static function CreateSheet(array $params, array $publicParams): LogicResult
    {
        //公共参数
        $hospitalOrgId   = intval(Arr::get($publicParams, '_hospitalOrgId'));
        $hospitalBrandId = intval(Arr::get($publicParams, '_hospitalBrandId'));
        $hospitalId      = intval(Arr::get($publicParams, '_hospitalId'));
        $userId          = intval(Arr::get($publicParams, '_userId'));

        //业务参数
        $memberId   = intval(Arr::get($params, 'memberId', 0));
        $memberUid  = trim(Arr::get($params, 'memberUid', ''));
        $sellerId   = intval(Arr::get($params, 'sellerId', 0));
        $sellerUid  = trim(Arr::get($params, 'sellerUid', ''));
        $items      = Arr::get($params, 'items', []);
        $totalPrice = trim(Arr::get($params, 'totalPrice', '0.00'));

        //特殊参数
        //是否创建并写数据库，否则返回待写库的数据
        $isCreate = boolval(Arr::get($params, 'isCreate', true));

        if (empty($hospitalOrgId) || empty($hospitalBrandId) || empty($hospitalId))
        {
            return self::Fail('创建零售购买单，缺少医院必选参数', 400);
        }
        if (empty($userId))
        {
            return self::Fail('创建零售购买单，缺少登录用户ID必选参数', 400);
        }
        /*
        if (empty($memberId) && empty($memberUid))
        {
            return self::Fail('创建零售购买单，缺少会员ID/UID必选参数', 400);
        }
        */
        if (empty($sellerId) && empty($sellerUid))
        {
            return self::Fail('创建零售购买单，缺少销售员ID/UID必选参数', 400);
        }
        if (empty($items))
        {
            return self::Fail('创建零售购买单，缺少商品明细必选参数', 400);
        }
        if (!is_numeric($totalPrice) || bccomp($totalPrice, '0', 2) != 1)
        {
            return self::Fail('创建零售购买单，缺少总金额必选参数', 400);
        }

        if (!empty($memberUid) || !empty($memberId))
        {
            $member = MemberModel::getOneByIdOrUid(id: $memberId, uid: $memberUid, orgId: $hospitalOrgId);
            if (empty($member))
            {
                return self::Fail('会员不存在', 30002);
            }
            $memberId = $member->id;
        }

        $seller = UsersModel::getOneByIdOrUid(id: $sellerId, uid: $sellerUid);
        if (empty($seller))
        {
            return self::Fail('销售员不存在', 10100);
        }
        $sellerId = $seller->id;

        $hospitalUserRes = HospitalUserLogic::GetUserHospitalUser($sellerId, $hospitalId);
        if ($hospitalUserRes->isFail())
        {
            return $hospitalUserRes;
        }

        //验证商品明细
        $verifyItemsRes = RetailSheetHelper::GetValidSheetItems($items, $publicParams);
        if ($verifyItemsRes->isFail())
        {
            return $verifyItemsRes;
        }

        //购买单内商品种类
        $addItems = $verifyItemsRes->getData('item', []);
        if (empty($addItems))
        {
            return self::Fail('零售购买单中商品全部无效', 500601);
        }

        //购买单开具商品的基本信息
        $addItemsInfo = $verifyItemsRes->getData('itemsInfo', []);
        if (empty($addItemsInfo))
        {
            return self::Fail('零售购买单中商品全部不存在', 500601);
        }

        //严格验证购买单中不同类型商品
        $getAddRes = RetailSheetHelper::GetAddItems($addItems, $addItemsInfo, $publicParams);
        if ($getAddRes->isFail())
        {
            return $getAddRes;
        }

        //购买单信息
        $totalCalcPrice = 0;
        $itemData       = [];

        $addItemsRes    = $getAddRes->getData();
        $totalCalcPrice = numberAdd([$totalCalcPrice, $addItemsRes['totalPrice']]);
        $itemData       = array_merge($itemData, $addItemsRes['items']);

        if (bccomp($totalCalcPrice, $totalPrice, 2) != 0)
        {
            return self::Fail('零售购买单中商品价格有变动，请刷新重试', 500692);
        }

        // 验证购买单商品库存
        $verifyItemStockList = [];
        foreach ($itemData as $curRetailItem)
        {
            $curItemId = $curRetailItem['item_id'];
            if ($curRetailItem['unit_type'] == ItemUnitTypeEnum::UNIT_TYPE_PACK->value)
            {
                $verifyItemStockList[$curItemId]['pack_quantity'] = $curRetailItem['quantity'];
            }
            else
            {
                $verifyItemStockList[$curItemId]['bulk_quantity'] = $curRetailItem['quantity'];
            }
        }

        // 验证库存
        $getStockValidateRes = StockVerifierHelper::ValidateStockRequirements($verifyItemStockList, $publicParams);
        if ($getStockValidateRes->isFail())
        {
            return $getStockValidateRes;
        }

        // 购买单主数据
        $sheetCode  = self::GenerateSheetCode();
        $insertData = [
            'sheet_code'  => $sheetCode,
            'org_id'      => $hospitalOrgId,
            'brand_id'    => $hospitalBrandId,
            'hospital_id' => $hospitalId,
            'member_id'   => $memberId,
            'price'       => $totalPrice,
            'status'      => 1,
            'created_by'  => $userId,
            'sold_by'     => $sellerId,
            'order_time'  => getCurrentTimeWithMilliseconds(),
        ];

        //按照原始顺序排列数据
        $insertItemsData = [];
        foreach ($items as $value)
        {
            foreach ($itemData as $key => $item)
            {
                // 当前商品顺序与提交不一致
                if ($value['itemUid'] != $item['item_uid'])
                {
                    continue;
                }

                // 删除商品uid
                unset($item['item_uid']);

                $insertItemsData[] = $item;

                unset($itemData[$key]);
                break;
            }
        }

        // 非新增模式情况下，返回组装好写入的数据
        if (!$isCreate)
        {
            return self::Success([
                                     'itemsData'  => $insertItemsData,
                                     'totalPrice' => $totalPrice,
                                 ]);
        }


        $insertRes = RetailSheetModel::DoCreateSheet($insertData, $insertItemsData);
        if (empty($insertRes))
        {
            return self::Fail('创建零售购买单失败', 500698);
        }

        return self::Success(self::GenerateCreateSheetResult($sheetCode, $totalPrice));
    }

    public static function EditSheet(array $params, array $publicParams): LogicResult
    {
        //业务参数
        $sheetCode  = trim(Arr::get($params, 'sheetCode', ''));
        $sheetId    = intval(Arr::get($params, 'sheetId', 0));
        $sellerId   = intval(Arr::get($params, 'sellerId', 0));
        $sellerUid  = trim(Arr::get($params, 'sellerUid', ''));
        $items      = Arr::get($params, 'items', []);
        $totalPrice = trim(Arr::get($params, 'totalPrice', '0.00'));

        // 公共参数
        $hospitalId      = intval(Arr::get($publicParams, '_hospitalId'));
        $hospitalOrgId   = intval(Arr::get($publicParams, '_hospitalOrgId'));
        $hospitalBrandId = intval(Arr::get($publicParams, '_hospitalBrandId'));
        $userId          = intval(Arr::get($publicParams, '_userId'));

        if (empty($hospitalOrgId) || empty($hospitalBrandId) || empty($hospitalId))
        {
            return self::Fail('编辑零售购买单，缺少医院必选参数', 400);
        }
        if (empty($userId))
        {
            return self::Fail('编辑零售购买单，缺少登录用户ID必选参数', 400);
        }
        if (empty($sheetCode) && empty($sheetId))
        {
            return self::Fail('编辑零售购买单，缺少宠物ID/UID必选参数', 400);
        }
        if (empty($sellerId) && empty($sellerUid))
        {
            return self::Fail('编辑零售购买单，缺少销售员ID/UID必选参数', 400);
        }
        if (empty($items))
        {
            return self::Fail('编辑零售购买单，缺少商品明细必选参数', 400);
        }
        if (!is_numeric($totalPrice) || bccomp($totalPrice, '0', 2) != 1)
        {
            return self::Fail('编辑零售购买单，缺少总金额必选参数', 400);
        }

        $sheetRes = RetailSheetHelper::GetValidSheet($sheetCode, $sheetId, $hospitalId);
        if ($sheetRes->isFail())
        {
            return $sheetRes;
        }

        $sheet   = $sheetRes->getData();
        $sheetId = $sheet['id'];

        // 是否可编辑
        $checkEditRes = self::CheckSheetEditOrDelete($sheet, $publicParams);
        if ($checkEditRes->isFail())
        {
            return $checkEditRes;
        }

        // 获取扁平化数据
        $getItemsDataRes = self::CreateSheet([
                                                 'memberId'   => $sheet['member_id'],
                                                 'sellerId'   => $sellerId,
                                                 'items'      => $items,
                                                 'totalPrice' => $totalPrice,
                                                 'isCreate'   => false
                                             ],
                                             $publicParams);
        if ($getItemsDataRes->isFail())
        {
            return $getItemsDataRes;
        }

        $totalCalcPrice = $getItemsDataRes->getData('totalPrice', 0);
        $itemsData      = $getItemsDataRes->getData('itemsData', []);
        if (empty($itemsData))
        {
            return self::Fail('编辑零售购买单失败，服务单明细无效', 500780);
        }

        // 获取购买单旧的明细
        $selectField   = [
            'uid',
            'item_id',
            'unit_type',
            'price',
            'quantity',
            'status',
            'created_by',
        ];
        $getOldItemRes = RetailSheetItemModel::getData($selectField, ['sheet_id' => $sheetId, 'status' => 1]);
        if (empty($getOldItemRes))
        {
            return self::Fail('编辑零售购买单失败，购买单明细获取失败', 500780);
        }

        // 使用uid为key
        $newItems = array_column($itemsData, null, 'uid');//此uid必然存在，新数据会生成新的uid,老数据会带过来数据库的uid
        $oldItems = array_column($getOldItemRes, null, 'uid');

        // 新增的数据
        $insertItems = [];

        // 更新的数据
        $updateItems = [];

        // 删除的数据
        $deleteItemsUids = [];

        // 找出需要新增的项目（新的数据存在，老的数据不存在）
        $newItemUids = array_diff(array_keys($newItems), array_keys($oldItems));
        array_walk($newItemUids, function ($uid) use (&$insertItems, $newItems) {
            $insertItems[] = $newItems[$uid];
        });

        // 找出可能需要更新的项目（两边都存在的项目）
        $commonUids = array_intersect(array_keys($newItems), array_keys($oldItems));
        foreach ($commonUids as $uid)
        {
            // 如果无差异，跳过
            $newItem = $newItems[$uid];
            $oldItem = $oldItems[$uid];
            if (empty(array_diff_assoc($newItem, $oldItem)))
            {
                continue;
            }

            // 比较两个数组，找出差异
            $curDiffInfo = [];
            foreach ($newItem as $key => $value)
            {
                // 只比较旧数据中存在的字段
                if (array_key_exists($key, $oldItem) && $value != $oldItem[$key])
                {
                    $curDiffInfo[$key] = $value;
                }
            }

            // 如果有差异，添加到更新列表
            if (!empty($curDiffInfo))
            {
                $curDiffInfo['uid'] = $uid;
                $updateItems[]      = $curDiffInfo;
            }
        }

        // 找出需要删除的项目（在旧数据中存在但新数据中不存在）
        $deleteItemsUids = array_diff(array_keys($oldItems), array_keys($newItems));

        // 商品无变动，关联业务表也不会变更
        if (empty($insertItems) && empty($updateItems) && empty($deleteItemsUids))
        {
            return self::Success();
        }

        if (bccomp($totalCalcPrice, $totalPrice, 2) != 0)
        {
            return self::Fail('零售购买单中商品价格有变动，请刷新重试', 500692);
        }

        $sheetData = [
            'price' => $totalPrice,
        ];

        $updateRes = RetailSheetModel::DoEditSheet($sheetId,
                                                   $sheetData,
                                                   $insertItems,
                                                   $updateItems,
                                                   $deleteItemsUids,
                                                   $userId);
        if (!$updateRes)
        {
            return self::Fail('更新零售购买单失败', 500780);
        }

        return self::Success();
    }

    public static function DeleteSheet(array $params, array $publicParams): LogicResult
    {
        //业务参数
        $sheetCode = trim(Arr::get($params, 'sheetCode', ''));
        $sheetId   = intval(Arr::get($params, 'sheetId', 0));

        // 公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        $userId     = intval(Arr::get($publicParams, '_userId'));

        if (empty($hospitalId))
        {
            return self::Fail('删除零售购买单，缺少医院必选参数', 400);
        }
        if (empty($userId))
        {
            return self::Fail('删除零售购买单，缺少登录用户ID必选参数', 400);
        }
        if (empty($sheetCode) && empty($sheetId))
        {
            return self::Fail('删除零售购买单，缺少宠物ID/UID必选参数', 400);
        }

        $sheetRes = RetailSheetHelper::GetValidSheet($sheetCode, $sheetId, $hospitalId);
        if ($sheetRes->isFail())
        {
            return $sheetRes;
        }

        $sheet   = $sheetRes->getData();
        $sheetId = $sheet['id'];

        // 是否可删除
        $checkEditRes = self::CheckSheetEditOrDelete($sheet, $publicParams);
        if ($checkEditRes->isFail())
        {
            return $checkEditRes;
        }

        $updateData = [
            'cancel_by' => $userId,
            'cancel_at' => getCurrentTimeWithMilliseconds(),
        ];

        $updateRes = RetailSheetModel::DoSoftDelete($sheetId,
                                                    SheetStatusEnum::Cancelled->value,
                                                    SheetStatusEnum::Unpaid->value,
                                                    $updateData);

        if (empty($updateRes))
        {
            return self::Fail('删除零售购买单失败', 500790);
        }

        return self::Success();
    }

    public static function CheckSheetEditOrDelete(array $sheet, array $publicParams): LogicResult
    {
        return RetailSheetHelper::CheckSheetEditOrDelete($sheet, $publicParams);
    }

    public static function GetSheetDetail(array $params, array $publicParams): LogicResult
    {
        $sheetCode = trim(Arr::get($params, 'sheetCode', ''));
        $sheetId   = intval(Arr::get($params, 'sheetId', 0));
        //公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));

        if (empty($sheetCode) && empty($sheetId))
        {
            return self::Fail('获取零售购买单详情，缺少单据ID/编码必选参数', 400);
        }
        if (empty($hospitalId))
        {
            return self::Fail('获取零售购买单详情，缺少医院ID必选参数', 400);
        }

        $sheetRes = RetailSheetHelper::GetValidSheet($sheetCode, $sheetId, $hospitalId);
        if ($sheetRes->isFail())
        {
            return $sheetRes;
        }
        $sheet   = $sheetRes->getData();
        $sheetId = $sheet['id'];

        $sheetItems = RetailSheetItemModel::getData(where: ['sheet_id' => $sheetId, 'status' => 1]);
        if (empty($sheetItems))
        {
            return self::Fail('零售购买单明细不存在', 500711);
        }

        $detailsRes = RetailSheetHelper::FormatDetailStructure([$sheet], $publicParams);
        if ($detailsRes->isFail())
        {
            return $detailsRes;
        }
        $detailRes  = $detailsRes->getData();
        $detailData = is_array($detailRes) && !empty($detailRes) ? current($detailRes) : null;
        if (empty($detailData))
        {
            return self::Fail('零售购买单详情不存在', 500710);
        }

        $itemsRes = RetailSheetHelper::FormatSheetItemStructure($sheetItems, $publicParams, true);
        if ($itemsRes->isFail())
        {
            return $itemsRes;
        }
        $itemsData = $itemsRes->getData();
        if (empty($itemsData))
        {
            return self::Fail('零售购买单明细不存在', 500711);
        }

        $detailData['items'] = $itemsData;

        return self::Success($detailData);
    }

    /* 开始结算逻辑 */

    public static function DoCheckoutPrepare(array $sheetCodes, string $totalPrice, array $publicParams): LogicResult
    {
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));

        if (empty($sheetCodes))
        {
            return self::Fail('缺少零售购买单编码必选参数', 400);
        }
        if (empty($totalPrice))
        {
            return self::Fail('缺少总金额必选参数', 400);
        }
        if (empty($hospitalId))
        {
            return self::Fail('缺少医院ID必选参数', 400);
        }

        $sheets = RetailSheetModel::GetSheetBySheetCodes($sheetCodes, $hospitalId);
        if (empty($sheets))
        {
            return self::Fail('零售购买单不存在', 600000);
        }

        // 基本验证
        $memberId      = null;
        $calTotalPrice = 0;
        $sheetIds      = [];
        foreach ($sheetCodes as $sheetCode)
        {
            $sheet = $sheets[$sheetCode] ?? null;
            if (empty($sheet))
            {
                return self::Fail('零售购买单不存在', 600000);
            }

            if ($sheet['status'] != SheetStatusEnum::Unpaid->value)
            {
                return self::Fail('零售购买单状态不允许结算', 600101);
            }

            if ($sheet['member_id'] == 0)
            {
                if ($memberId !== null)
                {
                    return self::Fail('零售购买单多个匿名会员，不允许结算', 600102);
                }

                $memberId = 0;
            }
            else
            {
                if (empty($memberId))
                {
                    $memberId = $sheet['member_id'];
                }
                else if ($memberId != $sheet['member_id'])
                {
                    return self::Fail('零售购买单会员不一致，不允许结算', 600102);
                }
            }

            $calTotalPrice = numberAdd([$calTotalPrice, $sheet['price']], 2);
            $sheetIds[]    = $sheet['id'];
        }
        if (bccomp($calTotalPrice, $totalPrice, 2) != 0)
        {
            return self::Fail('零售购买单总金额与传入的总金额不一致', 600103);
        }

        // 批量获取零售购买单明细
        $sheetItems = RetailSheetItemModel::getData(where  : ['status' => 1],
                                                    whereIn: ['sheet_id' => $sheetIds], orderBys: ['id' => 'asc']);
        if (empty($sheetItems))
        {
            return self::Fail('零售购买单明细不存在', 600001);
        }
        // 按服务单对详情进行分组
        $sheetGroupItems = collect($sheetItems)
            ->groupBy('sheet_id')
            ->toArray();

        $sheetDetailsRes = RetailSheetHelper::FormatDetailStructure($sheets, $publicParams, true, 'sheet_code');
        if ($sheetDetailsRes->isFail())
        {
            return $sheetDetailsRes;
        }
        $sheetDetailsData = $sheetDetailsRes->getData();

        $sheetItemsRes = RetailSheetHelper::FormatSheetItemStructure($sheetItems, $publicParams, false, 'uid');
        if ($sheetItemsRes->isFail())
        {
            return $sheetItemsRes;
        }
        $sheetItemsData = $sheetItemsRes->getData();

        // 开始构建结算数据
        $errorMsg = [];
        $result   = [];
        foreach ($sheetCodes as $sheetCode)
        {
            $curSheetDetail = $sheetDetailsData[$sheetCode] ?? null;
            if (empty($curSheetDetail))
            {
                $errorMsg[] = '零售购买单【' . $sheetCode . '】，信息不存在';
                continue;
            }

            $curSheetOriginItems = $sheetGroupItems[$curSheetDetail['id']] ?? [];
            if (empty($curSheetOriginItems))
            {
                $errorMsg[] = '零售购买单【' . $sheetCode . '】，明细不存在';
                continue;
            }

            $curSheetItems = [];
            foreach ($curSheetOriginItems as $value)
            {
                $tempItem = $sheetItemsData[$value['uid']] ?? null;
                if (empty($tempItem))
                {
                    $errorMsg[] = '零售购买单【' . $sheetCode . '】，明细信息不完整';
                    continue;
                }
                $curSheetItems[] = $tempItem;
            }

            if (isset($curSheetDetail['id']))
            {
                unset($curSheetDetail['id']);
            }

            $curSheetDetail['items'] = $curSheetItems;
            $result[]                = $curSheetDetail;
        }

        if (!empty($errorMsg))
        {
            return self::Fail(implode("；\n", $errorMsg), 600002);
        }

        return self::success([
                                 'totalPrice' => $calTotalPrice,
                                 'data'       => $result,
                             ]);
    }

    public static function DoLockSheet(array $sheetCodes, array $publicParams): LogicResult
    {
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));

        if (empty($sheetCodes))
        {
            return self::Fail('缺少零售购买单编码必选参数', 400);
        }
        if (empty($hospitalId))
        {
            return self::Fail('缺少医院ID必选参数', 400);
        }

        $sheets = RetailSheetModel::GetSheetBySheetCodes($sheetCodes, $hospitalId);
        if (empty($sheets))
        {
            return self::Fail('零售购买单不存在', 600000);
        }
        // 基本验证
        $memberId = null;
        $sheetIds = [];
        foreach ($sheetCodes as $sheetCode)
        {
            $sheet = $sheets[$sheetCode] ?? null;
            if (empty($sheet))
            {
                return self::Fail('零售购买单不存在', 600000);
            }
            if ($sheet['status'] != SheetStatusEnum::Unpaid->value)
            {
                return self::Fail('零售购买单当前状态不允许锁定操作', 500940);
            }

            if ($sheet['member_id'] == 0)
            {
                if ($memberId !== null)
                {
                    return self::Fail('零售购买单会员不一致，不允许锁定', 500941);
                }

                $memberId = 0;
            }
            else
            {
                if (empty($memberId))
                {
                    $memberId = $sheet['member_id'];
                }
                else if ($memberId != $sheet['member_id'])
                {
                    return self::Fail('零售购买单会员不一致，不允许锁定', 500941);
                }
            }

            $sheetIds[] = $sheet['id'];
        }

        try
        {
            DB::beginTransaction();

            $lockRes = RetailSheetModel::on()
                                       ->whereIn('id', $sheetIds)
                                       ->where(['status' => SheetStatusEnum::Unpaid->value])
                                       ->update(['status' => SheetStatusEnum::Paying->value]);

            if (empty($lockRes) || $lockRes != count(array_unique($sheetIds)))
            {
                // 不符合预期，回滚并返回失败
                DB::rollBack();

                return self::Fail('零售购买单锁定失败', 500946);
            }

            // 如果一切正常，提交事务
            DB::commit();

            return self::Success();
        } catch (Throwable $throwable)
        {
            // 异常自动回滚
            DB::rollBack();

            Log::error(__CLASS__ . '::' . __METHOD__ . ' 零售购买单锁定异常', [
                'code'    => $throwable->getCode(),
                'message' => $throwable->getMessage(),
                'file'    => $throwable->getFile(),
                'line'    => $throwable->getLine(),
                'trace'   => $throwable->getTraceAsString(),
            ]);

            return self::Fail('零售购买单锁定异常', 500947);
        }
    }

    public static function DoUnlockSheet(array $sheetCodes, array $publicParams): LogicResult
    {
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));

        if (empty($sheetCodes))
        {
            return self::Fail('缺少零售购买单编码必选参数', 400);
        }
        if (empty($hospitalId))
        {
            return self::Fail('缺少医院ID必选参数', 400);
        }

        $sheets = RetailSheetModel::GetSheetBySheetCodes($sheetCodes, $hospitalId);
        if (empty($sheets))
        {
            return self::Fail('零售购买单不存在', 600000);
        }

        // 基本验证
        $memberId = null;
        $sheetIds = [];
        foreach ($sheetCodes as $sheetCode)
        {
            $sheet = $sheets[$sheetCode] ?? null;
            if (empty($sheet))
            {
                return self::Fail('零售购买单不存在', 600000);
            }
            if ($sheet['status'] != SheetStatusEnum::Paying->value)
            {
                return self::Fail('零售购买单状态不允许解锁', 500950);
            }

            if ($sheet['member_id'] == 0)
            {
                if ($memberId !== null)
                {
                    return self::Fail('零售购买单会员不一致，不允许解锁', 500951);
                }

                $memberId = 0;
            }
            else
            {
                if (empty($memberId))
                {
                    $memberId = $sheet['member_id'];
                }
                else if ($memberId != $sheet['member_id'])
                {
                    return self::Fail('零售购买单会员不一致，不允许解锁', 500951);
                }
            }

            $sheetIds[] = $sheet['id'];
        }

        try
        {
            DB::beginTransaction();
            $unlockRes = RetailSheetModel::on()
                                         ->whereIn('id', $sheetIds)
                                         ->where(['status' => SheetStatusEnum::Paying->value])
                                         ->update(['status' => SheetStatusEnum::Unpaid->value]);

            if (empty($unlockRes) || $unlockRes != count(array_unique($sheetIds)))
            {
                // 不符合预期，回滚并返回失败
                DB::rollBack();

                return self::Fail('零售购买单解锁失败', 500956);
            }

            // 符合预期，提交事务
            DB::commit();

            return self::Success();
        } catch (Throwable $throwable)
        {
            // 捕获任何异常，确保回滚
            DB::rollBack();

            Log::error(__CLASS__ . '::' . __METHOD__ . ' 零售购买单解锁异常', [
                'code'    => $throwable->getCode(),
                'message' => $throwable->getMessage(),
                'file'    => $throwable->getFile(),
                'line'    => $throwable->getLine(),
                'trace'   => $throwable->getTraceAsString(),
            ]);

            return self::Fail('零售购买单解锁异常', 500957);
        }
    }

    public static function GetSheetSummary(array $sheetCodes, array $publicParams): LogicResult
    {
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));

        if (empty($sheetCodes))
        {
            return self::Fail('缺少零售购买单编码必选参数', 400);
        }
        if (empty($hospitalId))
        {
            return self::Fail('缺少医院ID必选参数', 400);
        }

        $sheets = RetailSheetModel::GetSheetBySheetCodes($sheetCodes, $hospitalId);
        if (empty($sheets))
        {
            return self::Fail('零售购买单不存在', 500900);
        }

        // 基本验证
        $calTotalPrice = 0;
        foreach ($sheetCodes as $sheetCode)
        {
            $sheet = $sheets[$sheetCode] ?? null;
            if (empty($sheet) || $sheet['status'] == SheetStatusEnum::Cancelled->value)
            {
                return self::Fail('零售购买单不存在', 500900);
            }
            $calTotalPrice = numberAdd([$calTotalPrice, $sheet['price']], 2);
        }

        $sheetDetailsRes = RetailSheetHelper::FormatDetailStructure($sheets, $publicParams, true, 'sheet_code');
        if ($sheetDetailsRes->isFail())
        {
            return $sheetDetailsRes;
        }
        $sheetDetailsData = $sheetDetailsRes->getData();

        // 开始构建数据
        $errorMsg = [];
        $result   = [];
        foreach ($sheetCodes as $sheetCode)
        {
            $curSheetDetail = $sheetDetailsData[$sheetCode] ?? null;
            if (empty($curSheetDetail))
            {
                $errorMsg[] = '零售购买单【' . $sheetCode . '】，信息不存在';
                continue;
            }

            if (isset($curSheetDetail['id']))
            {
                unset($curSheetDetail['id']);
            }

            $result[] = $curSheetDetail;
        }

        if (!empty($errorMsg))
        {
            return self::Fail(implode("；\n", $errorMsg), 500902);
        }

        return self::success([
                                 'totalPrice' => $calTotalPrice,
                                 'data'       => $result,
                             ]);
    }

    /**
     * 零售购买单出库
     *
     * @param array $params
     * @param array $publicParams
     *
     * @return LogicResult
     * @throws Throwable
     * @noinspection PhpUnused
     */
    public static function RetailSheetOutbound(array $params, array $publicParams): LogicResult
    {
        $sheetCode = trim(Arr::get($params, 'sheetCode', ''));
        $sheetId   = intval(Arr::get($params, 'sheetId', 0));

        //公共参数
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));
        $userId     = intval(Arr::get($publicParams, '_userId'));
        if (empty($hospitalId))
        {
            return self::Fail('执行零售购买单出库，缺少医院ID必选参数', 400);
        }
        if (empty($userId))
        {
            return self::Fail('执行零售购买单出库，缺少登录用户ID必选参数', 400);
        }

        // 业务参数
        if (empty($sheetCode) && empty($sheetId))
        {
            return self::Fail('执行零售购买单出库，缺少单据ID/编码必选参数', 400);
        }

        // 获取购买单
        $getSheetRes = RetailSheetHelper::GetValidSheet($sheetCode, $sheetId, $hospitalId);
        if ($getSheetRes->isFail())
        {
            return $getSheetRes;
        }

        // 购买单状态
        $getSheetRes = $getSheetRes->getData();
        $sheetId     = $getSheetRes['id'];
        if ($getSheetRes['status'] != SheetStatusEnum::Paid->value)
        {
            return self::Fail('执行零售购买单出库，零售购买单未支付', 500710);
        }

        // 获取购买单明细
        $getSheetItems = RetailSheetItemModel::getData(where: ['sheet_id' => $sheetId, 'status' => 1]);
        if (empty($getSheetItems))
        {
            return self::Fail('执行零售购买单出库，零售购买单明细不存在', 500711);
        }

        // 获取商品信息
        $itemIds        = array_unique(array_column($getSheetItems, 'item_id'));
        $getItemInfoRes = ItemLogic::GetItemFullInfo(itemIds: $itemIds, publicParams: $publicParams);
        if ($getItemInfoRes->isFail())
        {
            return $getItemInfoRes;
        }

        $getItemInfoRes = array_column($getItemInfoRes->getData(), null, 'id');
        if (count($itemIds) != count($getItemInfoRes))
        {
            return self::Fail('部分商品信息不存在', 33000);
        }

        // 是否存在相关业务数据出库记录
        $getReduceTotalRes = StockItemShelfReduceModel::getTotalNumber(where: [
                                                                                  'type'        => StockReduceTypeEnum::Retail->value,
                                                                                  'relation_id' => $getSheetRes['id'],
                                                                                  'status'      => 1
                                                                              ]);
        if ($getReduceTotalRes > 0)
        {
            return self::Fail('执行零售购买单出库，请勿重复出库', 44002);
        }

        // 组合商品出库信息
        $outboundItemData = [];
        foreach ($getSheetItems as $curRetailItem)
        {
            $curItemId      = $curRetailItem['item_id'];
            $curItemInfo    = $getItemInfoRes[$curItemId] ?? [];
            $curItemBarcode = $curItemInfo['item_barcode_info']['item_barcode'] ?? '';
            if (empty($curItemInfo) || empty($curItemBarcode))
            {
                continue;
            }

            if (bccomp($curRetailItem['quantity'], '0', 2) <= 0)
            {
                continue;
            }

            $outboundItemData[] = [
                'itemId'           => $curItemInfo['id'],
                'itemBarcode'      => $curItemBarcode,
                'reduceType'       => StockReduceTypeEnum::Retail->value,
                'reduceSubType'    => 0,
                'relationCode'     => $getSheetRes['sheet_code'],
                'relationId'       => $getSheetRes['id'],
                'relationDetailId' => $curRetailItem['id'],
                'packQuantity'     => $curRetailItem['unit_type'] == ItemUnitTypeEnum::UNIT_TYPE_PACK->value ? $curRetailItem['quantity'] : 0,
                'bulkQuantity'     => $curRetailItem['unit_type'] == ItemUnitTypeEnum::UNIT_TYPE_BULK->value ? $curRetailItem['quantity'] : 0,
                'remark'           => StockReduceTypeEnum::getDescription(StockReduceTypeEnum::Retail->value),
            ];
        }

        if (empty($outboundItemData))
        {
            return self::Fail('无有效出库明细记录', 44001);
        }

        try
        {
            DB::beginTransaction();

            // 执行出库操作
            $getOutboundRes = StockItemShelfReduceLogic::ReduceStockShelfQuantity($outboundItemData, $publicParams);
            if ($getOutboundRes->isFail())
            {
                DB::rollBack();

                return $getOutboundRes;
            }

            DB::commit();

            return self::Success();

        } catch (Throwable $throwable)
        {
            DB::rollBack();

            Log::error(__CLASS__ . '::' . __METHOD__ . ' 执行出零售库操作异常', [
                'code'    => $throwable->getCode(),
                'message' => $throwable->getMessage(),
                'file'    => $throwable->getFile(),
                'line'    => $throwable->getLine(),
                'trace'   => $throwable->getTraceAsString(),
            ]);

            return self::Fail('执行出零售库操作异常', 45004);
        }
    }
}
