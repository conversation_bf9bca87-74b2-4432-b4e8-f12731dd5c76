<?php

namespace App\Logics\V1\BuySheet;

use Throwable;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Arr;
use App\Logics\Logic;
use App\Logics\LogicResult;
use App\Logics\V1\BuySheet\Interfaces\CheckoutInterface;
use App\Logics\V1\BuySheet\Interfaces\LockInterface;
use App\Logics\V1\BuySheet\Interfaces\DetailInterface;
use App\Enums\PayStatusEnum;
use App\Models\RecipeModel;
use App\Models\RecipeItemModel;
use App\Support\Recipe\RecipeHelper;

class RecipeSheetLogic extends Logic implements CheckoutInterface, LockInterface, DetailInterface
{
    public static function DoCheckoutPrepare(array $sheetCodes, string $totalPrice, array $publicParams): LogicResult
    {
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));

        if (empty($sheetCodes))
        {
            return self::Fail('缺少处方单编码必选参数', 400);
        }
        if (empty($totalPrice))
        {
            return self::Fail('缺少总金额必选参数', 400);
        }
        if (empty($hospitalId))
        {
            return self::Fail('缺少医院ID必选参数', 400);
        }

        $sheets = RecipeModel::GetRecipeByRecipeCodes($sheetCodes, $hospitalId);
        if (empty($sheets))
        {
            return self::Fail('处方单不存在', 600000);
        }

        // 基本验证
        $memberId      = null;
        $calTotalPrice = 0;
        $recipeIds     = [];
        foreach ($sheetCodes as $sheetCode)
        {
            $sheet = $sheets[$sheetCode] ?? null;
            if (empty($sheet) || $sheet['status'] != 1)
            {
                return self::Fail('处方单不存在', 600000);
            }

            if ($sheet['is_paid'] != PayStatusEnum::Unpaid->value)
            {
                return self::Fail('处方单状态不允许结算', 600101);
            }

            if (empty($memberId))
            {
                $memberId = $sheet['member_id'];
            }
            else if ($memberId != $sheet['member_id'])
            {
                return self::Fail('处方单会员不一致，不允许结算', 600102);
            }

            $calTotalPrice = numberAdd([$calTotalPrice, $sheet['price']], 2);
            $recipeIds[]   = $sheet['id'];
        }
        if (bccomp($calTotalPrice, $totalPrice, 2) != 0)
        {
            return self::Fail('处方单总金额与传入的总金额不一致', 600103);
        }

        // 批量获取处方单明细
        $sheetItems = RecipeItemModel::getData(
            where:    ['status' => 1],
            whereIn:  ['recipe_id' => $recipeIds],
            orderBys: ['id' => 'asc']
        );
        if (empty($sheetItems))
        {
            return self::Fail('处方单明细不存在', 600001);
        }
        // 按处方对详情进行分组
        $sheetGroupItems = collect($sheetItems)
            ->groupBy('recipe_id')
            ->toArray();

        $sheetDetailsRes = RecipeHelper::FormatRecipeDetailStructure($sheets, $publicParams, true, 'recipe_code');
        if ($sheetDetailsRes->isFail())
        {
            return $sheetDetailsRes;
        }
        $sheetDetailsData = $sheetDetailsRes->getData('data');

        $sheetItemsRes = RecipeHelper::FormatRecipeItemStructure($sheetItems,
                                                                 $publicParams,
                                                                 false,
                                                                 false,
                                                                 false,
                                                                 'uid');
        if ($sheetItemsRes->isFail())
        {
            return $sheetItemsRes;
        }
        $sheetItemsData = $sheetItemsRes->getData('itemList');

        // 开始构建结算数据
        $errorMsg = [];
        $result   = [];
        foreach ($sheetCodes as $sheetCode)
        {
            $curSheetDetail = $sheetDetailsData[$sheetCode] ?? null;
            if (empty($curSheetDetail))
            {
                $errorMsg[] = '处方单【' . $sheetCode . '】，信息不存在';
                continue;
            }

            $curSheetOriginItems = $sheetGroupItems[$curSheetDetail['id']] ?? [];
            if (empty($curSheetOriginItems))
            {
                $errorMsg[] = '处方单【' . $sheetCode . '】，明细不存在';
                continue;
            }

            $curSheetItems = [];
            foreach ($curSheetOriginItems as $value)
            {
                //组合子项目过滤
                if ($value['suit_unique_uid'] != '' && $value['is_suit'] == 0)
                {
                    continue;
                }

                $tempItem = $sheetItemsData[$value['uid']] ?? null;
                if (empty($tempItem))
                {
                    $errorMsg[] = '处方单【' . $sheetCode . '】，明细信息不完整';
                    continue;
                }
                $curSheetItems[] = $tempItem;
            }

            if (isset($curSheetDetail['id']))
            {
                unset($curSheetDetail['id']);
            }

            $curSheetDetail['items'] = $curSheetItems;
            $result[]                = $curSheetDetail;
        }

        if (!empty($errorMsg))
        {
            return self::Fail(implode("；\n", $errorMsg), 600002);
        }

        return self::success([
                                 'totalPrice' => $calTotalPrice,
                                 'data'       => $result,
                             ]);
    }

    public static function DoLockSheet(array $sheetCodes, array $publicParams): LogicResult
    {
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));

        if (empty($sheetCodes))
        {
            return self::Fail('缺少处方单编码必选参数', 400);
        }
        if (empty($hospitalId))
        {
            return self::Fail('缺少医院ID必选参数', 400);
        }

        $sheets = RecipeModel::GetRecipeByRecipeCodes($sheetCodes, $hospitalId);
        if (empty($sheets))
        {
            return self::Fail('处方单不存在', 600000);
        }
        // 基本验证
        $memberId = null;
        $sheetIds = [];
        foreach ($sheetCodes as $sheetCode)
        {
            $sheet = $sheets[$sheetCode] ?? null;
            if (empty($sheet) || $sheet['status'] != 1)
            {
                return self::Fail('处方单不存在', 600000);
            }
            if ($sheet['is_paid'] != PayStatusEnum::Unpaid->value)
            {
                return self::Fail('处方单状态不允许锁定', 500940);
            }
            if (empty($memberId))
            {
                $memberId = $sheet['member_id'];
            }
            else if ($memberId != $sheet['member_id'])
            {
                return self::Fail('处方单会员不一致，不允许锁定', 500941);
            }

            $sheetIds[] = $sheet['id'];
        }

        try
        {
            DB::beginTransaction();
            $lockRes = RecipeModel::on()
                                  ->whereIn('id', $sheetIds)
                                  ->where(['is_paid' => PayStatusEnum::Unpaid->value])
                                  ->update(['is_paid' => PayStatusEnum::Paying->value]);

            if (empty($lockRes) || $lockRes != count(array_unique($sheetIds)))
            {
                // 不符合预期，回滚并返回失败
                DB::rollBack();

                return self::Fail('处方单锁定失败', 500946);
            }
            // 如果一切正常，提交事务
            DB::commit();

            return self::Success();
        } catch (Throwable $throwable)
        {
            // 异常自动回滚
            DB::rollBack();

            Log::error(__CLASS__ . '::' . __METHOD__ . ' 处方单锁定失败', [
                'code'    => $throwable->getCode(),
                'message' => $throwable->getMessage(),
                'file'    => $throwable->getFile(),
                'line'    => $throwable->getLine(),
                'trace'   => $throwable->getTraceAsString(),
            ]);

            return self::Fail('处方单锁定异常', 500947);
        }
    }

    public static function DoUnlockSheet(array $sheetCodes, array $publicParams): LogicResult
    {
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));

        if (empty($sheetCodes))
        {
            return self::Fail('缺少处方单编码必选参数', 400);
        }
        if (empty($hospitalId))
        {
            return self::Fail('缺少医院ID必选参数', 400);
        }

        $sheets = RecipeModel::GetRecipeByRecipeCodes($sheetCodes, $hospitalId);
        if (empty($sheets))
        {
            return self::Fail('处方单不存在', 600000);
        }

        // 基本验证
        $memberId = null;
        $sheetIds = [];
        foreach ($sheetCodes as $sheetCode)
        {
            $sheet = $sheets[$sheetCode] ?? null;
            if (empty($sheet) || $sheet['status'] != 1)
            {
                return self::Fail('处方单不存在', 600000);
            }

            if ($sheet['is_paid'] != PayStatusEnum::Paying->value)
            {
                return self::Fail('处方单状态不允许解锁', 500950);
            }
            if (empty($memberId))
            {
                $memberId = $sheet['member_id'];
            }
            else if ($memberId != $sheet['member_id'])
            {
                return self::Fail('处方单会员不一致，不允许解锁', 500951);
            }

            $sheetIds[] = $sheet['id'];
        }
        try
        {
            DB::beginTransaction();
            $unlockRes = RecipeModel::on()
                                    ->whereIn('id', $sheetIds)
                                    ->where(['is_paid' => PayStatusEnum::Paying->value])
                                    ->update(['is_paid' => PayStatusEnum::Unpaid->value]);

            if (empty($unlockRes) || $unlockRes != count(array_unique($sheetIds)))
            {
                // 不符合预期，回滚并返回失败
                DB::rollBack();

                return self::Fail('处方单解锁失败', 500956);
            }

            // 符合预期，提交事务
            DB::commit();

            return self::Success();
        } catch (Throwable $throwable)
        {
            // 捕获任何异常，确保回滚
            DB::rollBack();

            Log::error(__CLASS__ . '::' . __METHOD__ . ' 处方单解锁异常', [
                'code'    => $throwable->getCode(),
                'message' => $throwable->getMessage(),
                'file'    => $throwable->getFile(),
                'line'    => $throwable->getLine(),
                'trace'   => $throwable->getTraceAsString(),
            ]);

            return self::Fail('处方单解锁异常', 500957);
        }
    }

    public static function GetSheetSummary(array $sheetCodes, array $publicParams): LogicResult
    {
        $hospitalId = intval(Arr::get($publicParams, '_hospitalId'));

        if (empty($sheetCodes))
        {
            return self::Fail('缺少处方单编码必选参数', 400);
        }
        if (empty($hospitalId))
        {
            return self::Fail('缺少医院ID必选参数', 400);
        }

        $sheets = RecipeModel::GetRecipeByRecipeCodes($sheetCodes, $hospitalId);
        if (empty($sheets))
        {
            return self::Fail('处方单不存在', 500900);
        }

        // 基本验证
        $calTotalPrice = 0;
        foreach ($sheetCodes as $sheetCode)
        {
            $sheet = $sheets[$sheetCode] ?? null;
            if (empty($sheet) || $sheet['status'] != 1)
            {
                return self::Fail('处方单不存在', 500900);
            }
            $calTotalPrice = numberAdd([$calTotalPrice, $sheet['price']], 2);
        }

        $sheetDetailsRes = RecipeHelper::FormatRecipeDetailStructure($sheets, $publicParams, true, 'recipe_code');
        if ($sheetDetailsRes->isFail())
        {
            return $sheetDetailsRes;
        }
        $sheetDetailsData = $sheetDetailsRes->getData('data');

        // 开始构建数据
        $errorMsg = [];
        $result   = [];
        foreach ($sheetCodes as $sheetCode)
        {
            $curSheetDetail = $sheetDetailsData[$sheetCode] ?? null;
            if (empty($curSheetDetail))
            {
                $errorMsg[] = '处方单【' . $sheetCode . '】，信息不存在';
                continue;
            }

            if (isset($curSheetDetail['id']))
            {
                unset($curSheetDetail['id']);
            }

            $result[] = $curSheetDetail;
        }

        if (!empty($errorMsg))
        {
            return self::Fail(implode("；\n", $errorMsg), 500902);
        }

        return self::success([
                                 'totalPrice' => $calTotalPrice,
                                 'data'       => $result,
                             ]);
    }
}
