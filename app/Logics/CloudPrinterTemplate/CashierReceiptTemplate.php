<?php

namespace App\Logics\CloudPrinterTemplate;

/**
 * HIS收银小票模板
 *
 * $template       = (new \App\Logics\CloudPrinterTemplate\CashierReceiptTemplate())
 * ->setHospitalName('XX公司')
 * ->setHospitalPhone('022-66666666')
 * ->setHospitalAddress('天津市和平区xx大街2号')
 * ->setOperator('张三')
 * ->setTime('2025-08-26 11:01:21')
 * ->setOrderCode('8888888888888')
 * ->setMemberName('未来不是梦')
 * ->setMemberPhone('186****9999')
 * ->setPetName('大白')
 * ->setMemberBalance('125.00')
 * ->setItems([
 * ['name' => '生化十项', 'price' => '25', 'quantity' => '1次', 'total' => '125'],
 * ['name' => '腹部超声检查', 'price' => '50', 'quantity' => '1次', 'total' => '50'],
 * ['name' => '硝酸咪康唑乳膏', 'price' => '50', 'quantity' => '1支', 'total' => '50'],
 * ['name' => '特拉唑嗪片', 'price' => '80', 'quantity' => '1袋', 'total' => '80'],
 * ])
 * ->setTotalPrice('125.00')
 * ->setTotalPayPrice('125.00')
 * ->build();
 * var_dump(\App\Services\GPrinter\GPrinter::print('000000000000000', $template));
 */
class CashierReceiptTemplate extends BaseTemplate implements ReceiptInterface
{
    private string $title = '收银签购单';
    private string $tips = '温馨提示：请当面核对钱物，药品类以及处置类商品一经售出，不予退换，开具发票凭销售小票30日内有效。';
    private bool $isReprint = false;
    private string $rechargeTips = '';
    private string $qrcode = '';
    private string $hospitalName = '';
    private string $hospitalPhone = '';
    private string $hospitalAddress = '';
    private string $operator = '';
    private string $time = '';
    private string $orderCode = '';
    private string $memberName = '';
    private string $memberPhone = '';
    private string $petName;
    private string $memberBalance = '';
    private array $items = [];
    private string $totalPrice = '0.00';
    private string $totalCouponPrice = '0.00';
    private string $totalDiscountPrice = '0.00';
    private string $totalPaymentPrice = '0.00';
    private string $totalThirdPartyPrice = '0.00';
    private string $totalRechargeBalance = '0.00';
    private string $totalGiftBalance = '0.00';
    private string $totalPayPrice = '0.00';

    public function __construct()
    {

    }

    public function setHospitalName(string $hospitalName): self
    {
        $this->hospitalName = $hospitalName;

        return $this;
    }

    public function setHospitalPhone(string $hospitalPhone): self
    {
        $this->hospitalPhone = $hospitalPhone;

        return $this;
    }

    public function setHospitalAddress(string $hospitalAddress): self
    {
        $this->hospitalAddress = $hospitalAddress;

        return $this;
    }

    public function setOperator(string $operator): self
    {
        $this->operator = $operator;

        return $this;
    }

    public function setTime(string $time): self
    {
        $this->time = $time;

        return $this;
    }

    public function setOrderCode(string $orderCode): self
    {
        $this->orderCode = $orderCode;

        return $this;
    }

    public function setMemberName(string $memberName): self
    {
        $this->memberName = $memberName;

        return $this;
    }

    public function setMemberPhone(string $memberPhone): self
    {
        $this->memberPhone = $memberPhone;

        return $this;
    }

    public function setPetName(string $petName): self
    {
        $this->petName = $petName;

        return $this;
    }

    public function setMemberBalance(string $memberBalance): self
    {
        $this->memberBalance = $memberBalance;

        return $this;
    }

    public function setItems(array $items): self
    {
        $this->items = $items;

        return $this;
    }

    public function setTotalPrice(string $totalPrice): self
    {
        $this->totalPrice = $totalPrice;

        return $this;
    }

    public function setTotalCouponPrice(string $totalCouponPrice): self
    {
        $this->totalCouponPrice = $totalCouponPrice;

        return $this;
    }

    public function setTotalDiscountPrice(string $totalDiscountPrice): self
    {
        $this->totalDiscountPrice = $totalDiscountPrice;

        return $this;
    }

    public function setTotalPaymentPrice(string $totalPaymentPrice): self
    {
        $this->totalPaymentPrice = $totalPaymentPrice;

        return $this;
    }

    public function setTotalThirdPartyPrice(string $totalThirdPartyPrice): self
    {
        $this->totalThirdPartyPrice = $totalThirdPartyPrice;

        return $this;
    }

    public function setTotalRechargeBalance(string $totalRechargeBalance): self
    {
        $this->totalRechargeBalance = $totalRechargeBalance;

        return $this;
    }

    public function setTotalGiftBalance(string $totalGiftBalance): self
    {
        $this->totalGiftBalance = $totalGiftBalance;

        return $this;
    }

    public function setTotalPayPrice(string $totalPayPrice): self
    {
        $this->totalPayPrice = $totalPayPrice;

        return $this;
    }

    public function setQrcode(string $qrcode): self
    {
        $this->qrcode = $qrcode;

        return $this;
    }

    public function setRechargeTips(string $rechargeTips): self
    {
        $this->rechargeTips = $rechargeTips;

        return $this;
    }

    public function setReprint(bool $isReprint): self
    {
        $this->isReprint = $isReprint;

        return $this;
    }

    public function build(): string
    {
        //拼接顶部
        $template = <<<TOP
<gpWord Align=1 Bold=0 Wsize=1 Hsize=1 Reverse=0 Underline=0>{$this->title}</gpWord>
{$this->hospitalName}
收银员：{$this->operator}
时  间：{$this->time}
流水号：{$this->orderCode}
姓  名：{$this->memberName}
手  机：{$this->memberPhone}
余  额：{$this->memberBalance}
--------------------------------
项目  单价     数量    小计

TOP;

        //拼接项目
        foreach ($this->items as $value)
        {
            $nameLen     = mb_strlen($value['name'], 'gb2312');
            $priceLen    = mb_strlen($value['price'], 'gb2312');
            $quantityLen = mb_strlen($value['quantity'], 'gb2312');
            $totalLen    = mb_strlen($value['total'], 'gb2312');

            $template .= <<<ITEM
{$value['name']}

ITEM;
            $template .= str_repeat(' ', 6);
            if (9 - $priceLen > 0)
            {
                $template .= $value['price'] . str_repeat(' ', 9 - $priceLen);
            }
            else
            {
                $template .= $value['price'];
            }

            if (8 - $quantityLen > 0)
            {
                $template .= $value['quantity'] . str_repeat(' ', 8 - $quantityLen);
            }
            else
            {
                $template .= $value['quantity'];
            }

            if (9 - $totalLen > 0)
            {
                $template .= $value['total'] . str_repeat(' ', 9 - $totalLen);
            }
            else
            {
                $template .= $value['total'];
            }
        }

        //拼接价格
        $template .= <<<PRICE
--------------------------------
合    计：           {$this->formatPrice($this->totalPrice)}
代 金 券：           {$this->formatPrice($this->totalCouponPrice, true)}
优惠金额：           {$this->formatPrice($this->totalDiscountPrice, true)}
应收金额：           {$this->formatPrice($this->totalPaymentPrice)}
第三方支付：         {$this->formatPrice($this->totalThirdPartyPrice)}
储值余额支付：       {$this->formatPrice($this->totalRechargeBalance)}
赠送余额支付：       {$this->formatPrice($this->totalGiftBalance)}
现金支付：           {$this->formatPrice($this->totalPayPrice)}
--------------------------------

PRICE;

        //拼接余额充值提示
        if (!empty($this->rechargeTips))
        {
            $template .= <<<RECHARGE_TIPS
{$this->rechargeTips}

RECHARGE_TIPS;
        }
        else
        {
            //拼接底部信息
            $template .= <<<BOTTOM
{$this->hospitalName}
咨询电话：{$this->hospitalPhone}
门店地址：{$this->hospitalAddress}
{$this->tips}

BOTTOM;
        }

        //拼接重新打印时间
        if ($this->isReprint)
        {
            $reprintTime = date('Y-m-d H:i:s');
            $template    .= <<<REPRINT_TIME
重打时间：{$reprintTime}

REPRINT_TIME;
        }

        //拼接二维码
        if (!empty($this->qrcode))
        {
            $template .= <<<QRCODE

<gpWord Align=1 Bold=1 Wsize=0 Hsize=0 Reverse=0 Underline=0>▼扫码有好礼▼</gpWord><gpQRCode Align=1 Size=9 Error=M>{$this->qrcode}</gpQRCode>
QRCODE;
        }

        //拼接结束符
        $template .= <<<END
<gpBr/>
<gpCut/>
END;

        return $template;
    }

    public function formatPrice(float $price, bool $showNegative = false): string
    {
        // 强制负号
        if ($showNegative)
        {
            $price = - abs($price);
        }
        else
        {
            $price = abs($price);
        }

        // %11.2f = 总宽度 11，保留 2 位小数
        return sprintf("%11.2f", $price);
    }

}
