<?php

namespace App\Logics\Internal;

use App\Logics\Logic;
use App\Logics\LogicResult;
use App\Facades\TokenFacade;
use App\Support\Mqtt\MqttHisTopicHelper;

class MqttAuthLogic extends Logic
{
    public static function CheckLogin(string $username, string $password): LogicResult
    {
        if (empty($username) || empty($password))
        {
            return self::Fail('用户名或密码错误', 400);
        }

        $sessionData = TokenFacade::all($username);
        if (empty($sessionData))
        {
            return self::Fail('登录状态不存在', 401);
        }

        if (!isset($sessionData['userUid']) || $password !== $sessionData['userUid'])
        {
            return self::Fail('账户不匹配', 500);
        }

        return self::Success();
    }

    public static function CheckAuth(string $username, string $action, string $topic): LogicResult
    {
        if (empty($username) || empty($action) || empty($topic))
        {
            return self::Fail('用户名或操作或主题错误', 400);
        }

        $sessionData = TokenFacade::all($username);
        if (empty($sessionData))
        {
            return self::Fail('登录状态不存在', 401);
        }

        if ($action != 'subscribe')
        {
            return self::Fail('当前操作无权限', 500);
        }

        $orgUid      = $sessionData['hospitalOrgUid'] ?? '';
        $brandCode   = $sessionData['hospitalBrandCode'] ?? '';
        $hospitalUid = $sessionData['hospitalUid'] ?? '';
        $userUid     = $sessionData['userUid'] ?? '';
        if (empty($orgUid) || empty($brandCode) || empty($hospitalUid) || empty($userUid))
        {
            return self::Fail('用户登录异常', 500);
        }

        $publicParams = [
            '_hospitalOrgUid'    => $orgUid,
            '_hospitalBrandCode' => $brandCode,
            '_hospitalUid'       => $hospitalUid,
            '_userUid'           => $userUid,
        ];

        $topics = MqttHisTopicHelper::GetHisUserSubscribeTopics($publicParams);
        if (empty($topics))
        {
            return self::Fail('主题权限集为空', 500);
        }

        if (!in_array($topic, $topics))
        {
            return self::Fail('申请的主题无权限', 500);
        }

        return self::Success();
    }
}
