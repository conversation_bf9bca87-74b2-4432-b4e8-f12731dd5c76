<?php

namespace App\Listeners;

use Exception;
use Illuminate\Support\Facades\Log;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use App\Traits\VersionTrait;
use App\Enums\RabbitMqExchangeAndQueueEnum;
use App\Events\RetailPaidEvent;
use App\Models\RetailSheetModel;
use App\Enums\SheetStatusEnum;

class RetailOrderOutboundListener implements ShouldQueue
{
    use InteractsWithQueue, VersionTrait;

    public string $queue = RabbitMqExchangeAndQueueEnum::RetailOrderOutboundQueue->value;
    public int $tries = 3;

    public array $backoff = [1, 3, 5];

    /**
     * Handle the event.
     * @throws Exception
     */
    public function handle(RetailPaidEvent $event): void
    {
        $sheetCode = $event?->sheetCode;
        $version   = $event?->version ?? null;
        if (empty($sheetCode))
        {
            $this->fail('sheetCode is empty');
        }

        // 获取零售购买单
        $getRetailSheetRes = RetailSheetModel::getData(where: ['sheet_code' => $sheetCode, 'status' => SheetStatusEnum::Paid->value]);
        $getRetailSheetRes = !empty($getRetailSheetRes) ? current($getRetailSheetRes) : null;
        if (empty($getRetailSheetRes))
        {
            //记录日志，抛出异常
            Log::channel('event')
               ->error(__CLASS__ . ' 获取零售购买单不存在', [
                   'event'   => get_class($event),
                   'version' => $version,
                   'data'    => ['sheetCode' => $sheetCode]
               ]);

            throw new Exception(__CLASS__ . ' get retail sheet not exists');
        }

        //根据事件中的版本号，调用对应版本号下的处理逻辑
        $LogicClass = self::getLogicByVersion('BuySheet\\RetailSheetLogic', $version);
        if (empty($LogicClass))
        {
            //记录日志，抛出异常
            Log::channel('event')
               ->error(__CLASS__ . ' BuySheet/RetailSheetLogic class not found', [
                   'event'   => get_class($event),
                   'version' => $version,
               ]);

            throw new Exception(__CLASS__ . ' RecipeLogic logic class not found');
        }

        $publicParams = [
            '_userId'          => $getRetailSheetRes['created_by'], // TODO 系统管理员
            '_hospitalId'      => $getRetailSheetRes['hospital_id'],
            '_hospitalBrandId' => $getRetailSheetRes['brand_id'],
            '_hospitalOrgId'   => $getRetailSheetRes['org_id'],
        ];
        $result       = $LogicClass::RetailSheetOutbound(['sheetId' => $getRetailSheetRes['id'], 'sheetCode' => $sheetCode], $publicParams);
        if ($result->isFail())
        {
            Log::channel('event')
               ->error(__CLASS__ . ' create stock pending order failed', [
                   'event'   => get_class($event),
                   'version' => $version,
                   'code'    => $result->getCode(),
                   'message' => $result->getMessage(),
               ]);

            throw new Exception($result->getMessage(), $result->getCode());
        }
    }
}
