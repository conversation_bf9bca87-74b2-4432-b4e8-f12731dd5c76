<?php

namespace App\Listeners;

use Illuminate\Support\Facades\Log;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use App\Traits\VersionTrait;
use App\Enums\RabbitMqExchangeAndQueueEnum;
use App\Events\PayOrderPaidEvent;
use App\Enums\PaySubOrderTypeEnum;
use App\Support\PayOrder\PaySubOrderExtHelper;

class PaidRetailOrderListener implements ShouldQueue
{
    use InteractsWithQueue, VersionTrait;

    public string $queue = RabbitMqExchangeAndQueueEnum::PaidRetailOrderQueue->value;
    public int $tries = 3;

    public array $backoff = [1, 3, 5];

    /**
     * Handle the event.
     */
    public function handle(PayOrderPaidEvent $event): void
    {
        $payOrderCode = $event?->payOrderCode;
        $version      = $event?->version ?? null;

        if (empty($payOrderCode))
        {
            $this->fail('payOrderCode is empty');
        }

        $paySubOrdersExtData = PaySubOrderExtHelper::GetSubOrderExtData(
            payOrderCode: $payOrderCode,
            type:         PaySubOrderTypeEnum::Retail->value,
        );

        //如果为空则无需处理，返回处理成功
        if (empty($paySubOrdersExtData))
        {
            return;
        }

        //根据事件中的版本号，调用对应版本号下的处理逻辑
        $LogicClass = self::getLogicByVersion('Order\\RetailOrderLogic', $version);
        if (empty($LogicClass))
        {
            //记录日志，抛出异常
            Log::channel('event')
               ->error(__CLASS__ . ' RetailOrderLogic logic class not found', [
                   'event'   => get_class($event),
                   'version' => $version,
               ]);

            throw new \Exception(__CLASS__ . ' RetailOrderLogic logic class not found');
        }

        $result = $LogicClass::PostCreateOrders($paySubOrdersExtData, true);
        if ($result->isFail())
        {
            Log::channel('event')
               ->error(__CLASS__ . ' create retail order failed', [
                   'event'   => get_class($event),
                   'version' => $version,
                   'code'    => $result->getCode(),
                   'message' => $result->getMessage(),
               ]);

            throw new \Exception($result->getMessage(), $result->getCode());
        }

    }
}
