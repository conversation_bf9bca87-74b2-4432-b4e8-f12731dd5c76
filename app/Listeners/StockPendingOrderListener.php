<?php

namespace App\Listeners;

use Exception;
use Illuminate\Support\Facades\Log;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use App\Traits\VersionTrait;
use App\Enums\RabbitMqExchangeAndQueueEnum;
use App\Events\RecipePaidEvent;
use App\Models\RecipeModel;

class StockPendingOrderListener implements ShouldQueue
{
    use InteractsWithQueue, VersionTrait;

    public string $queue = RabbitMqExchangeAndQueueEnum::StockPendingOrderQueue->value;
    public int $tries = 3;

    public array $backoff = [1, 3, 5];

    /**
     * Handle the event.
     * @throws Exception
     */
    public function handle(RecipePaidEvent $event): void
    {
        $recipeCode = $event?->recipeCode;
        $version    = $event?->version ?? null;
        if (empty($recipeCode))
        {
            $this->fail('recipeCode is empty');
        }

        // 获取处方
        $getRecipeRes = RecipeModel::getData(where: ['recipe_code' => $recipeCode, 'status' => 1]);
        $getRecipeRes = !empty($getRecipeRes) ? current($getRecipeRes) : null;
        if (empty($getRecipeRes))
        {
            //记录日志，抛出异常
            Log::channel('event')
               ->error(__CLASS__ . ' 获取处方不存在', [
                   'event'   => get_class($event),
                   'version' => $version,
                   'data'    => ['recipeCode' => $recipeCode]
               ]);

            throw new Exception(__CLASS__ . ' RecipeLogic logic class not found');
        }

        //根据事件中的版本号，调用对应版本号下的处理逻辑
        $LogicClass = self::getLogicByVersion('StockPendingLogic', $version);
        if (empty($LogicClass))
        {
            //记录日志，抛出异常
            Log::channel('event')
               ->error(__CLASS__ . ' StockPendingLogic class not found', [
                   'event'   => get_class($event),
                   'version' => $version,
               ]);

            throw new Exception(__CLASS__ . ' RecipeLogic logic class not found');
        }

        $publicParams = [
            '_userId'          => $getRecipeRes['doctor_id'], // TODO 系统管理员
            '_hospitalId'      => $getRecipeRes['hospital_id'],
            '_hospitalBrandId' => $getRecipeRes['brand_id'],
            '_hospitalOrgId'   => $getRecipeRes['org_id'],
        ];
        $result       = $LogicClass::AddStockPendingFromRecipeId($getRecipeRes['id'], $publicParams);
        if ($result->isFail())
        {
            Log::channel('event')
               ->error(__CLASS__ . ' create stock pending order failed', [
                   'event'   => get_class($event),
                   'version' => $version,
                   'code'    => $result->getCode(),
                   'message' => $result->getMessage(),
               ]);

            throw new Exception($result->getMessage(), $result->getCode());
        }
    }
}
