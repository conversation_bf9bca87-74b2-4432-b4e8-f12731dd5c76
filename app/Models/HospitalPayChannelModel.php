<?php

namespace App\Models;

class HospitalPayChannelModel extends Model
{
    protected $table = 'his_hospital_pay_channel';

    /**
     * 获取医院可用的支付渠道和支付方式
     *
     * @param int $hospitalId
     *
     * @return array
     */
    public static function GetHospitalPayChannelAndModel(int $hospitalId): array
    {
        if (empty($hospitalId))
        {
            return [];
        }

        return self::on()
                   ->from(self::table() . ' AS hpc')
                   ->select(
                       'pm.*',
                       'pcsm.pay_channel_id',
                       'pc.name as pay_channel_name',
                       'pc.alias_name as pay_channel_alias_name',
                       'pc.icon as pay_channel_icon',
                       'pc.is_mode_selectable'
                   )
                   ->leftJoin(PayChannelModel::table() . ' AS pc', 'hpc.pay_channel_id', '=', 'pc.id')
                   ->leftJoin(PayChannelSupportModeModel::table() . ' AS pcsm', 'pc.id', '=', 'pcsm.pay_channel_id')
                   ->leftJoin(HospitalPayChannelSupportModeModel::table() . ' AS hpcsm', function ($join) {
                       $join->on('pcsm.id', '=', 'hpcsm.pay_channel_support_mode_id')
                            ->where('pc.is_mode_selectable', '=', 1)
                            ->where('hpcsm.hospital_id', '=', 1);
                   })
                   ->leftJoin(PayModeModel::table() . ' AS pm', 'pcsm.pay_mode_id', '=', 'pm.id')
                   ->where('hpc.hospital_id', 1)
                   ->where('hpc.status', 1)
                   ->where('pc.status', 1)
                   ->where('pcsm.status', 1)
                   ->where(function ($q) {
                       $q->where('pc.is_mode_selectable', 0)
                         ->orWhere(function ($q2) {
                             $q2->where('pc.is_mode_selectable', 1)
                                ->where('hpcsm.status', 1);
                         });
                   })
                   ->where('pm.status', 1)
                   ->orderBy('pcsm.pay_channel_id', 'asc')
                   ->orderByDesc('hpcsm.order_by')
                   ->orderByDesc('pm.order_by')
                   ->orderBy('pm.id', 'asc')
                   ->get()
                   ->toArray();
    }
}
