<?php

namespace App\Models;

use DB;
use App\Enums\StockRefundTypeEnum;
use App\Models\Traits\CreateUserOptionsTrait;

/**
 * 仓储-退货单
 */
class StockRefundModel extends Model
{
    use CreateUserOptionsTrait;

    protected $table = 'stock_refund';

    /**
     * 获取采购单列表
     *
     * @param array $arrParams
     * @param int   $iPage
     * @param int   $iPageSize
     *
     * @return array
     */
    public static function getStockRefundListData(array $arrParams, int $iPage = 1, int $iPageSize = 10): array
    {
        $getWhere   = [];
        $getOrWhere = []; // 多个维度查询条件，需要与其他条件使用and

        // 必须指定医院查询
        if (empty($arrParams['hospitalId']))
        {
            return [];
        }

        // 医院ID
        $getWhere[] = ['stock_refund.hospital_id', '=', $arrParams['hospitalId']];

        // 多维度关键字查询
        if (!empty($arrParams['keywords']))
        {
            // 1.采购单编号
            $getOrWhere[] = ['stock_refund.refund_code', 'like', '%' . $arrParams['keywords'] . '%'];

            // 2.商品条码
            $getOrWhere[] = ['stock_refund_item.item_barcode', 'like', '%' . $arrParams['keywords'] . '%'];
        }

        // 采购单ID
        if (!empty($arrParams['stockRefundId']))
        {
            $getWhere[] = ['stock_refund.id', '=', $arrParams['stockRefundId']];
        }

        // 退货单创建时间
        if (!empty($arrParams['startDate']) && strtotime($arrParams['startDate']) !== false)
        {
            $getWhere[] = ['stock_refund.created_at', '>=', $arrParams['startDate'] . ' 00:00:00'];
        }
        if (!empty($arrParams['endDate']) && strtotime($arrParams['endDate']) !== false)
        {
            $getWhere[] = ['stock_refund.created_at', '<=', $arrParams['endDate'] . ' 23:59:59'];
        }

        // 退货单状态
        if (isset($arrParams['refundStatus']) && is_numeric($arrParams['refundStatus']))
        {
            $getWhere[] = ['stock_refund.status', '=', $arrParams['refundStatus']];
        }

        // 退货单创建人
        if (!empty($arrParams['createUserUid']) || !empty($arrParams['createUserId']))
        {
            if (!empty($arrParams['createUserId']))
            {
                $getWhere[] = ['stock_refund.created_by', '=', $arrParams['createUserId']];
            }
            else
            {
                $getWhere[] = ['user.uid', '=', $arrParams['createUserUid']];
            }
        }

        // 退货类型
        if (!empty($arrParams['refundType']))
        {
            $getWhere[] = ['stock_refund.refund_type', '=', $arrParams['refundType']];
        }

        // 退货单出库状态
        if (isset($arrParams['outboundStatus']) && is_numeric($arrParams['outboundStatus']))
        {
            $getWhere[] = ['stock_refund.status', '=', $arrParams['outboundStatus']];
        }

        $builderQuery = self::on()
                            ->select([
                                         'stock_refund.*',
                                         'user.uid as user_uid',
                                         'user.name as user_name',
                                     ])
                            ->leftJoin((new UsersModel()->getTable()) . ' as user', 'stock_refund.created_by', '=', 'user.id')
                            ->when(!empty($getOrWhere), function ($query) use ($getOrWhere) {
                                $query->leftJoin((new StockRefundItemModel()->getTable()) . ' as stock_refund_item',
                                                 'stock_refund.id',
                                                 '=',
                                                 'stock_refund_item.stock_refund_id');
                            })
                            ->when($getWhere, function ($query) use ($getWhere) {
                                $query->where($getWhere);
                            })
                            ->when($getOrWhere, function ($query) use ($getOrWhere) {
                                $query->where(function ($subQuery) use ($getOrWhere) {
                                    $subQuery->orWhere($getOrWhere);
                                });
                            })
                            ->orderBy('stock_refund.id', 'desc');

        // 获取条目总数
        $totalCount = $builderQuery->count(DB::raw('DISTINCT stock_refund.id'));
        if ($totalCount <= 0)
        {
            return [];
        }

        // 分页
        if ($iPage > 0 && $iPageSize > 0)
        {
            $builderQuery->offset(($iPage - 1) * $iPageSize)
                         ->limit($iPageSize);
        }

        // 获取条目数据
        $getStockRefundList = $builderQuery->groupBy(['stock_refund.id'])
                                           ->get()
                                           ->toArray();

        return ['total' => $totalCount, 'data' => $getStockRefundList];
    }

    /**
     * 获取调拨单关联的退货单
     *
     * @param string $purchaseCode
     *
     * @return object|null
     */
    public static function getTransferRelationRefund(string $purchaseCode): object|null
    {
        if (empty($purchaseCode))
        {
            return null;
        }

        $refundWhere = [
            'refund_type'   => StockRefundTypeEnum::Transfer->value,
            'purchase_code' => $purchaseCode,
            'status'        => 2, // 调拨单出库默认为审核通过
        ];

        return self::on()
                   ->where($refundWhere)
                   ->first();
    }
}
