<?php

namespace App\Models;

use Illuminate\Support\Arr;
use DB;

/**
 * 预约记录
 * Class AppointmentModel
 * @package App\Models
 */
class AppointmentModel extends Model
{
    protected $table = 'appointment';

    /**
     *
     * @param array $arrParams
     * @param int   $iPage
     * @param int   $iPageSize
     *
     * @return array
     */
    public static function getAppointmentListData(array $arrParams, int $iPage = 1, int $iPageSize = 10): array
    {
        // 必须有公参
        if (empty($arrParams))
        {
            return [];
        }

        $hospitalId      = $arrParams['_hospitalId'];
        $hospitalBrandId = $arrParams['_hospitalBrandId'];
        $hospitalOrgId   = $arrParams['_hospitalOrgId'];
        if (empty($hospitalId) || empty($hospitalBrandId) || empty($hospitalOrgId))
        {
            return [];
        }

        // 存在单条件多维度查询条件
        $memberIds     = [];
        $matchedPetIds = []; // 新增：记录匹配的宠物ID，用于精确过滤
        $keyword       = trim(Arr::get($arrParams, 'keyword', ''));
        if (!empty($keyword))
        {
            // 如果医院品牌互通，那么获取整个组织下所有互通的品牌下医院的数据。否则不互通只获取当前品牌下所有医院。默认品牌下医院必须为互通
            $brandIsChain = HisBrandModel::getBrandIsShare($hospitalBrandId);

            $hospitalBrandNotWhere = [];
            if (!empty($brandIsChain))
            {
                $hospitalBrandWhere = [['org_id', '=', $hospitalOrgId]];

                // 获取当前组织下不互通的品牌
                $getNotShareBrandRes = HisBrandModel::getShareBrandByOrgId($hospitalOrgId, [$hospitalBrandId], 0);
                if ($getNotShareBrandRes->isNotEmpty())
                {
                    $hospitalBrandNotWhere['brand_id'] = $getNotShareBrandRes->pluck('id')
                                                                             ->toArray();
                }
            }
            else
            {
                $hospitalBrandWhere = [['brand_id', '=', $hospitalBrandId]];
            }

            // 基础条件
            $memberBaseWhere = array_merge($hospitalBrandWhere, [['status', '=', 1], ['delete_status', '=', 0]]);

            // 1. 优先验证是否手机号查询
            if (checkValidCellphone($keyword))
            {
                $phoneWhere     = array_merge($memberBaseWhere, [['phone', '=', $keyword]]);
                $membersByPhone = MemberModel::getData(fields    : ['id'],
                                                       where     : $phoneWhere,
                                                       whereNotIn: $hospitalBrandNotWhere);
                if (!empty($membersByPhone))
                {
                    $memberIds = array_merge($memberIds, array_column($membersByPhone, 'id'));
                }
            }
            else
            {
                // 匹配宠物名称、病历号
                $petStatusWhere = array_merge($hospitalBrandWhere, [['status', '=', 1]]);

                // 使用OR条件组合宠物名称和病历号查询
                $petLikeWhereOr = [
                    ['name', 'like', '%' . $keyword . '%'],
                    ['record_number', 'like', '%' . $keyword . '%']
                ];

                $membersByPet = MemberPetsModel::getData(fields    : ['member_id', 'id'], // 同时获取宠物ID
                                                         where     : $petStatusWhere,
                                                         orWhere   : $petLikeWhereOr,
                                                         whereNotIn: $hospitalBrandNotWhere,
                                                         pageSize  : 50);
                if (!empty($membersByPet))
                {
                    $memberIds     = array_merge($memberIds, array_column($membersByPet, 'member_id'));
                    $matchedPetIds = array_column($membersByPet, 'id'); // 记录匹配的宠物ID
                }

                // 去重
                $memberIds = array_unique($memberIds);

                // 限制最多50个结果
                $memberIds = array_slice($memberIds, 0, 100);
            }
        }

        // 单个条件查询
        $withWhere = [['appointment.status', '=', 1]];

        // 预约类型
        if (!empty($arrParams['appointmentType']))
        {
            $withWhere[] = ['appointment.type_id', '=', $arrParams['appointmentType']];
        }

        // 预约的医生/美容师
        if (!empty($arrParams['appointmentDoctorId']) || !empty($arrParams['appointmentDoctorUid']))
        {
            if (!empty($arrParams['appointmentDoctorId']))
            {
                $withWhere[] = ['appointment.exec_id', '=', $arrParams['appointmentDoctorId']];
            }
            else
            {
                $withWhere[] = ['user.uid', '=', $arrParams['appointmentDoctorUid']];
            }
        }

        // 预约时间
        if (!empty($arrParams['appointmentDate']) && strtotime($arrParams['appointmentDate']) !== false)
        {
            $appointmentDate = $arrParams['appointmentDate'];
            $withWhere[]     = ['appointment.start_time', '>=', $appointmentDate . ' 00:00:00'];
            $withWhere[]     = ['appointment.start_time', '<=', $appointmentDate . ' 23:59:59'];
        }

        $builderQuery = self::on()
                            ->select(['appointment.*', 'user.uid as exec_uid', 'user.name as exec_name'])
                            ->leftJoin((new AppointmentPetModel()->getTable()) . ' as appointment_pet', 'appointment.id', '=', 'appointment_pet.appointment_id')
                            ->leftJoin((new UsersModel()->getTable()) . ' as user', 'appointment.exec_id', '=', 'user.id')
                            ->where($withWhere)
                            ->when(!empty($keyword), function ($query) use ($keyword, $memberIds) {
                                // 如果有关键词搜索，使用OR条件组合多种搜索方式
                                $query->where(function ($subQuery) use ($keyword, $memberIds) {
                                    // 1. 通过会员ID匹配
                                    if (!empty($memberIds))
                                    {
                                        $subQuery->whereIn('appointment.member_id', $memberIds);
                                    }

                                    // 2. 如果不是手机号，直接在预约表中搜索salutation字段
                                    if (!checkValidCellphone($keyword))
                                    {
                                        $subQuery->orWhere('appointment.salutation', 'like', '%' . $keyword . '%');
                                    }
                                });
                            })
                            ->when(!empty($matchedPetIds), function ($query) use ($matchedPetIds) {
                                $query->whereIn('appointment_pet.pet_id', $matchedPetIds);
                            })
                            ->orderBy('appointment.id', 'desc');

        // 获取条目总数
        $totalCount = $builderQuery->count(DB::raw('DISTINCT appointment.id'));
        if ($totalCount <= 0)
        {
            return [];
        }

        // 分页
        if ($iPage > 0 && $iPageSize > 0)
        {
            $builderQuery->offset(($iPage - 1) * $iPageSize)
                         ->limit($iPageSize);
        }

        // 获取条目数据
        $getAppointmentList = $builderQuery->groupBy(['appointment.id'])
                                           ->get()
                                           ->toArray();

        return ['total' => $totalCount, 'data' => $getAppointmentList];
    }

    /**
     * 获取预约统计数据
     *
     * @param int    $hospitalId 医院ID
     * @param string $startDate  开始日期 (Y-m-d)
     * @param string $endDate    结束日期 (Y-m-d)
     *
     * @return array
     */
    public static function getAppointmentStatistics(int $hospitalId, string $startDate, string $endDate): array
    {
        if (empty($hospitalId) || empty($startDate) || empty($endDate))
        {
            return [];
        }

        // 验证日期格式
        if (!checkDateIsValid($startDate) || !checkDateIsValid($endDate))
        {
            return [];
        }

        // 确保开始日期不大于结束日期
        if (strtotime($startDate) > strtotime($endDate))
        {
            return [];
        }


        return self::on()
                   ->selectRaw('DATE(start_time) as appointment_date, COUNT(*) as appointment_count')
                   ->where(['hospital_id' => $hospitalId, 'status' => 1])
                   ->whereRaw('DATE(start_time) >= ?', [$startDate])
                   ->whereRaw('DATE(start_time) <= ?', [$endDate])
                   ->groupByRaw('DATE(start_time)')
                   ->orderByRaw('DATE(start_time) ASC')
                   ->get()
                   ->toArray();
    }
}
