<?php

namespace App\Models;

use Illuminate\Support\Facades\DB;

/**
 * MeiliSearch 同步商品数据模型
 * Class MeiLiSearchItemModel
 * @package App\Models
 */
class MeiLiSearchItemModel extends Model
{
    /**
     * 获取普通商品数据
     *
     * @param string|int $orgId   机构ID
     * @param array      $itemIds 商品ID数组，为空时获取所有
     *
     * @return array
     */
    public static function getItemList(string|int $orgId, array $itemIds = []): array
    {
        $itemWhere = ' 1=1 ';
        if (!empty($itemIds))
        {
            $itemWhere .= ' AND i.id IN (' . implode(',', $itemIds) . ')';
        }

        $sql = <<<SQL
SELECT
  i.id,
  i.uid,
  i.org_id,
  IFNULL(ib.item_barcode, '') AS barcode,
  i.`name`,
  i.english_name,
  i.basis_name,
  i.alias_name,
  i.mem_code,
  1 AS item_type,
  0 AS is_suit,
  IFNULL(GROUP_CONCAT(DISTINCT ispc.category_id ORDER BY ispc.category_id ASC SEPARATOR ','), '') AS item_support_pet_category,
  i.first_sale_type_id,
  ist.`name` AS first_sale_type_name,
  ist.alias_name AS first_sale_type_alias_name,
  i.second_sale_type_id,
  ist2.`name` AS second_sale_type_name,
  ist2.alias_name AS second_sale_type_alias_name,
  i.drug_type_id,
  idt.`name` AS drug_type_name,
  idt.alias_name AS drug_type_alias_name,
  i.first_category_id,
  ic.`name` AS first_category_name,
  ic.alias_name AS first_category_alias_name,
  i.second_category_id,
  ic2.`name` AS second_category_name,
  ic2.alias_name AS second_category_alias_name,
  i.brand_id,
  ibd.`name` AS brand_name,
  ibd.chinese_name AS brand_chinese_name,
  ibd.english_name AS brand_english_name,
  i.pack_unit,
  i.pack_unit_id,
  i.bulk_unit,
  i.bulk_unit_id,
  i.bulk_ratio,
  i.use_unit,
  i.use_unit_id,
  i.use_ratio,
  CONCAT(i.bulk_ratio, i.bulk_unit, '/', i.pack_unit) AS pack_spec,
  CONCAT(i.use_ratio, i.use_unit, '/', i.bulk_unit) AS bulk_spec,
  i.is_licensed,
  i.is_precise_metering,
  i.is_recipe_allow,
  i.is_retail_allow,
  i.is_pack_sale_allow,
  i.is_shelf_life,
  i.shelf_life,
  i.shelf_life_day,
  i.over_life_day,
  i.is_receive_allow,
  i.`desc`,
  i.remark,
  i.is_purchasable,
  IFNULL(GROUP_CONCAT(DISTINCT hipr.hospital_id ORDER BY hipr.hospital_id ASC SEPARATOR ','), '') AS purchase_hospital_ids,
  i.`status`,
  i.created_at,
  i.updated_at
FROM
  item AS i
  LEFT JOIN item_barcode AS ib ON i.id = ib.item_id AND ib.`status` = 1
  LEFT JOIN item_sale_type AS ist ON i.first_sale_type_id = ist.id AND ist.is_leaf = 0
  LEFT JOIN item_sale_type AS ist2 ON i.second_sale_type_id = ist2.id AND ist2.is_leaf = 1
  LEFT JOIN item_drug_type AS idt ON i.drug_type_id = idt.id
  LEFT JOIN item_category AS ic ON i.first_category_id = ic.id AND ic.is_leaf = 0
  LEFT JOIN item_category AS ic2 ON i.second_category_id = ic2.id AND ic2.is_leaf = 1
  LEFT JOIN item_brand AS ibd ON i.brand_id = ibd.id
  LEFT JOIN item_support_pet_category AS ispc ON ispc.item_type = 1 AND i.id = ispc.item_id AND ispc.`status` = 1
  LEFT JOIN hospital_item_purchase_records AS hipr ON i.id = hipr.item_id and hipr.status = 1
WHERE
  i.org_id = $orgId AND $itemWhere
GROUP BY
  i.id;
SQL;

        return DB::select($sql);
    }

    /**
     * 获取洗美商品数据
     *
     * @param string|int $orgId
     * @param array      $itemIds
     *
     * @return array
     */
    public static function getBeautyItemList(string|int $orgId, array $itemIds = []): array
    {
        $itemWhere = ' 1=1 ';
        if (!empty($itemIds))
        {
            $itemWhere .= ' AND i.id IN (' . implode(',', $itemIds) . ')';
        }

        $sql = <<<SQL
SELECT
  i.id,
  i.uid,
  i.org_id,
  i.`name`,
  i.english_name,
  i.alias_name,
  i.mem_code,
  4 AS item_type,
  0 AS is_suit,
  IFNULL(GROUP_CONCAT(DISTINCT ispc.category_id ORDER BY ispc.category_id ASC SEPARATOR ','), '') AS item_support_pet_category,
  i.first_sale_type_id,
  ist.`name` AS first_sale_type_name,
  ist.alias_name AS first_sale_type_alias_name,
  i.second_sale_type_id,
  ist2.`name` AS second_sale_type_name,
  ist2.alias_name AS second_sale_type_alias_name,
  i.first_category_id,
  ic.`name` AS first_category_name,
  ic.alias_name AS first_category_alias_name,
  i.second_category_id,
  ic2.`name` AS second_category_name,
  ic2.alias_name AS second_category_alias_name,
  i.use_unit,
  i.use_unit_id,
  0 AS is_recipe_allow,
  0 AS is_retail_allow,
  i.`desc`,
  i.remark,
  i.`status`,
  i.created_at,
  i.updated_at
FROM
  item_beauty AS i
  LEFT JOIN item_sale_type AS ist ON i.first_sale_type_id = ist.id AND ist.is_leaf = 0
  LEFT JOIN item_sale_type AS ist2 ON i.second_sale_type_id = ist2.id AND ist2.is_leaf = 1
  LEFT JOIN item_category AS ic ON i.first_category_id = ic.id AND ic.is_leaf = 0
  LEFT JOIN item_category AS ic2 ON i.second_category_id = ic2.id AND ic2.is_leaf = 1
  LEFT JOIN item_support_pet_category AS ispc ON ispc.item_type = 4 AND i.id = ispc.item_id AND ispc.`status` = 1
WHERE
  i.org_id = $orgId AND $itemWhere
GROUP BY
  i.id;
SQL;

        return DB::select($sql);
    }

    /**
     * 获取组合商品数据
     *
     * @param string|int $orgId
     * @param array      $itemIds
     *
     * @return array
     */
    public static function getSuitItemList(string|int $orgId, array $itemIds = []): array
    {
        $itemWhere = ' 1=1 ';
        if (!empty($itemIds))
        {
            $itemWhere .= ' AND i.id IN (' . implode(',', $itemIds) . ')';
        }

        $sql = <<<SQL
SELECT
  i.id,
  i.uid,
  i.org_id,
  i.`name`,
  i.english_name,
  i.alias_name,
  i.mem_code,
  2 AS item_type,
  1 AS is_suit,
  1 AS is_recipe_allow,
  IFNULL(GROUP_CONCAT(DISTINCT ispc.category_id ORDER BY ispc.category_id ASC SEPARATOR ','), '') AS item_support_pet_category,
  i.sale_type_id as first_sale_type_id,
  ist.`name` AS first_sale_type_name,
  ist.alias_name AS sale_type_alias_name,
  i.use_unit,
  i.use_unit_id,
  i.is_print_items,
  i.is_use_items_price,
  i.`desc`,
  i.remark,
  i.`status`,
  i.created_at,
  i.updated_at
FROM
  item_suit AS i
  LEFT JOIN item_sale_type AS ist ON i.sale_type_id = ist.id AND ist.is_leaf = 0
  LEFT JOIN item_support_pet_category AS ispc ON ispc.item_type = 1 AND i.id = ispc.item_id AND ispc.`status` = 1
WHERE
  i.org_id = $orgId and $itemWhere
GROUP BY
  i.id;
SQL;

        $getSuitItemsRes = DB::select($sql);
        if (empty($getSuitItemsRes))
        {
            return [];
        }

        // 获取关联的 item_suit_relation
        $suitItemIds      = array_map(fn($item) => $item->id, $getSuitItemsRes);
        $getSuitRelations = DB::table('item_suit_relation')
                              ->whereIn('suit_id', $suitItemIds)
                              ->where('status', 1)
                              ->get();
        if (empty($getSuitRelations))
        {
            return [];
        }

        $relatedAllItemIds   = [];
        $suitRelationItemIds = [];
        foreach ($getSuitRelations as $relation)
        {
            $itemId   = intval($relation->item_id);
            $itemType = intval($relation->item_type);
            if (empty($relatedAllItemIds[$itemType]) || !in_array($itemId, $relatedAllItemIds[$itemType]))
            {
                $relatedAllItemIds[$itemType][] = $itemId;
            }

            $suitRelationItemIds[$relation->suit_id][] = $itemId;
        }

        // 获取组合内的商品信息
        $getRelationItemInfo = [
            1 => self::getItemList($orgId, $relatedAllItemIds[1] ?? []),
            2 => [],
            4 => self::getBeautyItemList($orgId, $relatedAllItemIds[4] ?? []),
        ];

        if (empty($getRelationItemInfo))
        {
            return [];
        }

        // 将关联的商品信息填充进每个组合中
        foreach ($getSuitItemsRes as $suitItem)
        {
            $suitId               = $suitItem->id;
            $suitItem->suit_items = [];

            if (!isset($suitRelationItemIds[$suitId]))
            {
                continue;
            }

            foreach ($getSuitRelations as $relation)
            {
                if ($relation->suit_id != $suitId)
                {
                    continue;
                }

                $itemType = intval($relation->item_type);
                $itemId   = intval($relation->item_id);
                if (!isset($getRelationItemInfo[$itemType]))
                {
                    continue;
                }

                foreach ($getRelationItemInfo[$itemType] as $item)
                {
                    if ($item->id == $itemId)
                    {
                        $item->suit_relation_id = $relation->id;
                        $item->quantity         = $relation->num;
                        $item->unit_type        = $relation->unit_type;
                        $suitItem->suit_items[] = $item;
                        break;
                    }
                }
            }
        }
        unset($suitItem);

        return $getSuitItemsRes;
    }
}
