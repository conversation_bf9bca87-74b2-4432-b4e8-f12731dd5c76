<?php

namespace App\Models;

/**
 * 处置执行人
 */
class NurseExecutorModel extends Model
{
    protected $table = 'his_nurse_executor';

    /**
     * 获取处置执行人
     *
     * @param array    $nurseIds
     * @param int|null $executorStatus
     *
     * @return array
     */
    public static function getNurseExecutor(array $nurseIds, ?int $executorStatus = 1): array
    {
        if (empty($nurseIds))
        {
            return [];
        }

        return self::on()
                   ->select(['his_nurse_executor.*', 'his_user.uid as user_uid', 'his_user.name as user_name'])
                   ->leftJoin(new UsersModel()->getTable() . ' as his_user',
                              'his_user.id',
                              '=',
                              'his_nurse_executor.user_id')
                   ->when(is_int($executorStatus), function ($query) use ($executorStatus) {
                       $query->where(['his_nurse_executor.executor_status' => $executorStatus]);
                   })
                   ->whereIn('his_nurse_executor.nurse_id', $nurseIds)
                   ->get()
                   ->toArray();
    }
}
