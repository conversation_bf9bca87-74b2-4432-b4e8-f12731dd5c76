<?php

namespace App\Models;


use DB;

/**
 * 处方库存占用记录表
 */
class RecipesStockOccupyModel extends Model
{
    protected $table = 'his_recipes_stock_occupy';

    public static function getTotalRecipeOccupyStock(int $hospitalId, array $itemIds, array $excludeRecipeIds = []): array
    {
        if (empty($hospitalId) || empty($itemIds))
        {
            return [];
        }

        $getTotalRecipeOccupyStockRes = self::on()
                                            ->select([
                                                         'item_id',
                                                         DB::raw('SUM(occupy_pack_quantity) as occupy_pack_quantity'),
                                                         DB::raw('SUM(occupy_bulk_quantity) as occupy_bulk_quantity')
                                                     ])
                                            ->where(['hospital_id' => $hospitalId, 'status' => 1])
                                            ->whereIn('item_id', $itemIds)
                                            ->when(!empty($excludeRecipeIds), function ($query) use ($excludeRecipeIds) {
                                                $query->whereNotIn('recipe_id', $excludeRecipeIds);
                                            })
                                            ->groupBy('item_id')
                                            ->get()
                                            ->keyBy('item_id')
                                            ->toArray();
        if (empty($getTotalRecipeOccupyStockRes))
        {
            return [];
        }

        return $getTotalRecipeOccupyStockRes;
    }
}
