<?php

namespace App\Models;

class PaySubOrderModel extends Model
{
    protected $table = 'pay_sub_order';

    /**
     * 获取结算子单[通过支付子单号]
     *
     * @param array    $paySubOrderCodes
     * @param int|null $hospitalId
     * @param bool     $keyByCode
     * @param bool     $useWritePdo
     *
     * @return array
     */
    public static function GetPaySubOrderExtByCodes(
        array $paySubOrderCodes, ?int $hospitalId = null, bool $keyByCode = false, bool $useWritePdo = false
    ): array
    {
        return self::on()
                   ->whereIn('pay_sub_order_code', $paySubOrderCodes)
                   ->when(!empty($hospitalId), function ($query) use ($hospitalId) {
                       $query->where(['hospital_id' => $hospitalId]);
                   })
                   ->when($useWritePdo, function ($query) {
                       $query->useWritePdo();
                   })
                   ->get()
                   ->when($keyByCode, function ($collection) {
                       return $collection->keyBy('pay_sub_order_code');
                   })
                   ->toArray();
    }
}
