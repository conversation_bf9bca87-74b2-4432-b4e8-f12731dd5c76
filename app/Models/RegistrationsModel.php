<?php

namespace App\Models;

use Illuminate\Support\Facades\DB;
use Illuminate\Database\Query\Builder;
use App\Enums\SheetStatusEnum;
use App\Models\Interfaces\SheetUnionInterface;
use App\Models\Traits\SheetStatusConvertTrait;
use Throwable;

/**
 * 挂号记录
 */
class RegistrationsModel extends Model implements SheetUnionInterface
{
    use SheetStatusConvertTrait;

    protected $table = 'his_registrations';

    /**
     * 完整查询字段
     *
     * @return array
     */
    public static function getSelectFields(): array
    {
        return [
            'id',
            DB::raw("registration_code as sheet_code"),
            DB::raw("'registration' as business_type"),
            'hospital_id',
            'member_id',
            'pet_id',
            DB::raw("pay_price as price"),
            self::CommonConvertStatusToSheetSelectField(),
            'created_by',
            DB::raw("doctor_id as sold_by"),
            DB::raw("0 as cancel_by"),
            DB::raw("created_at as order_time"),
            DB::raw("'' as paid_at"),
            DB::raw("'' as cancel_at"),
            'created_at',
            'updated_at',
        ];
    }

    /**
     * 获取当前业务的待付款 SQL 构造器
     *
     * @param int         $hospitalId
     * @param int|null    $memberId
     * @param int|null    $createdBy
     * @param string|null $createdStartTime
     * @param string|null $createdEndTime
     *
     * @return Builder
     */
    public static function getUnpaidQuery(
        int     $hospitalId,
        ?int    $memberId = null,
        ?int    $createdBy = null,
        ?string $createdStartTime = null,
        ?string $createdEndTime = null
    ): Builder
    {

        return DB::table(self::table())
                 ->select(self::getSelectFields())
                 ->where([
                             'hospital_id' => $hospitalId,
                         ])
                 ->where(self::CommonConvertSheetStatusToWhere(SheetStatusEnum::Unpaid->value))
                 ->when($memberId, function ($query) use ($memberId) {
                     $query->where('member_id', $memberId);
                 })
                 ->when($createdBy, function ($query) use ($createdBy) {
                     $query->where('created_by', $createdBy);
                 })
                 ->when($createdStartTime, function ($query) use ($createdStartTime) {
                     $query->where('created_at', '>=', $createdStartTime);
                 })
                 ->when($createdEndTime, function ($query) use ($createdEndTime) {
                     $query->where('created_at', '<=', $createdEndTime);
                 });
    }

    /**
     * 获取挂号单
     *
     * @param string $registrationCode
     * @param string $registrationUid
     * @param int    $registrationId
     * @param int    $hospitalId
     * @param bool   $useWritePdo
     *
     * @return object|null
     */
    public static function GetRegistrationByCodeOrUidOrId(
        string $registrationCode = '',
        string $registrationUid = '',
        int    $registrationId = 0,
        int    $hospitalId = 0,
        bool   $useWritePdo = false
    ): object|null
    {
        if (empty($registrationCode) && empty($registrationUid) && empty($registrationId))
        {
            return null;
        }

        return self::on()
                   ->when(!empty($registrationCode), function ($query) use ($registrationCode) {
                       $query->where(['registration_code' => $registrationCode]);
                   })
                   ->when(!empty($registrationUid), function ($query) use ($registrationUid) {
                       $query->where(['uid' => $registrationUid]);
                   })
                   ->when(!empty($registrationId), function ($query) use ($registrationId) {
                       $query->where(['id' => $registrationId]);
                   })
                   ->when(!empty($hospitalId), function ($query) use ($hospitalId) {
                       $query->where(['hospital_id' => $hospitalId]);
                   })
                   ->when($useWritePdo, function ($query) {
                       $query->useWritePdo();
                   })
                   ->first();
    }

    /**
     * 批量获取挂号单
     *
     * @param array $registrationCodes
     * @param int   $hospitalId
     * @param bool  $keyByCode
     * @param bool  $useWritePdo
     *
     * @return array
     */
    public static function GetRegistrationByRecipeCodes(
        array $registrationCodes, int $hospitalId, bool $keyByCode = true, bool $useWritePdo = false
    ): array
    {
        if (empty($registrationCodes) || empty($hospitalId))
        {
            return [];
        }

        return self::on()
                   ->where(['hospital_id' => $hospitalId])
                   ->whereIn('registration_code', $registrationCodes)
                   ->when($useWritePdo, function ($query) {
                       $query->useWritePdo();
                   })
                   ->get()
                   ->when($keyByCode, function ($collection) {
                       return $collection->keyBy('registration_code');
                   })
                   ->toArray();
    }

    /**
     * 软删除
     *
     * @param int   $sheetId
     * @param int   $updateStatus
     * @param int   $beforeStatus
     * @param array $updateData
     *
     * @return int
     * @throws Throwable
     */
    public static function DoSoftDelete(
        int   $sheetId,
        int   $updateStatus,
        int   $beforeStatus,
        array $updateData = []
    ): int
    {
        if (empty($sheetId))
        {
            return 0;
        }

        try
        {
            DB::beginTransaction();

            $updateData = array_merge(['status' => $updateStatus], $updateData);

            // 删除挂号记录
            $affectRow = self::on()
                             ->where(['id' => $sheetId, 'status' => $beforeStatus])
                             ->update($updateData);
            if (empty($affectRow))
            {
                DB::rollBack();

                return 0;
            }

            // 作废门诊状态
            $affectRow = OutpatientModel::on()
                                        ->where(['registration_id' => $sheetId, 'invalid' => 0])
                                        ->update(['invalid' => 1]);
            if (empty($affectRow))
            {
                DB::rollBack();

                return 0;
            }

            DB::commit();

            return 1;
        } catch (Throwable)
        {
            DB::rollBack();

            return 0;
        }
    }
}
