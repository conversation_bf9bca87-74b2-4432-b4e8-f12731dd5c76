<?php

namespace App\Models;

use DB;
use App\Enums\StockOutboundStatusEnum;

/**
 * 采购单
 */
class PurchaseOrderModel extends Model
{
    protected $table = 'purchase_order';

    /**
     * 获取采购单列表
     *
     * @param array $arrParams
     * @param int   $iPage
     * @param int   $iPageSize
     *
     * @return array
     */
    public static function getPurchaseOrderListData(array $arrParams, int $iPage = 1, int $iPageSize = 10): array
    {
        $getWhere   = [];
        $getOrWhere = []; // 多个维度查询条件，需要与其他条件使用and

        // 必须指定医院查询
        if (empty($arrParams['hospitalId']))
        {
            return [];
        }

        // 医院ID
        $getWhere[] = ['purchase_order.hospital_id', '=', $arrParams['hospitalId']];

        // 多维度关键字查询
        if (!empty($arrParams['keywords']))
        {
            // 1.采购单编号
            $getOrWhere[] = ['purchase_order.purchase_code', 'like', '%' . $arrParams['keywords'] . '%'];

            // 2.商品条码
            $getOrWhere[] = ['purchase_order_item.item_barcode', 'like', '%' . $arrParams['keywords'] . '%'];
        }

        // 采购单ID
        if (!empty($arrParams['purchaseOrderId']))
        {
            $getWhere[] = ['purchase_order.id', '=', $arrParams['purchaseOrderId']];
        }

        // 采购单创建时间
        if (!empty($arrParams['startDate']) && strtotime($arrParams['startDate']) !== false)
        {
            $getWhere[] = ['purchase_order.created_at', '>=', $arrParams['startDate'] . ' 00:00:00'];
        }
        if (!empty($arrParams['endDate']) && strtotime($arrParams['endDate']) !== false)
        {
            $getWhere[] = ['purchase_order.created_at', '<=', $arrParams['endDate'] . ' 23:59:59'];
        }

        // 采购单状态
        if (isset($arrParams['purchaseStatus']) && is_numeric($arrParams['purchaseStatus']))
        {
            $getWhere[] = ['purchase_order.status', '=', $arrParams['purchaseStatus']];
        }

        // 采购单签收状态
        if (isset($arrParams['receivedStatus']) && is_numeric($arrParams['receivedStatus']))
        {
            $getWhere[] = ['purchase_order.received_status', '=', $arrParams['receivedStatus']];
        }

        // 采购单创建人
        if (!empty($arrParams['createUserUid']) || !empty($arrParams['createUserId']))
        {
            if (!empty($arrParams['createUserId']))
            {
                $getWhere[] = ['purchase_order.created_by', '=', $arrParams['createUserId']];
            }
            else
            {
                $getWhere[] = ['user.uid', '=', $arrParams['createUserUid']];
            }
        }

        // 采购类型
        if (!empty($arrParams['purchaseType']))
        {
            $getWhere[] = ['purchase_order.purchase_type', '=', $arrParams['purchaseType']];
        }

        // 采购供应商
        if (!empty($arrParams['supplierUid']) || !empty($arrParams['supplierId']))
        {
            if (!empty($arrParams['supplierId']))
            {
                $getWhere[] = ['purchase_order.supplier_id', '=', $arrParams['supplierId']];
            }
            else
            {
                $getWhere[] = ['supplier.uid', '=', $arrParams['supplierUid']];
            }
        }

        // 调拨来源门店
        if (!empty($arrParams['allotHospitalUid']) || !empty($arrParams['allotHospitalId']))
        {
            if (!empty($arrParams['allotHospitalId']))
            {
                $getWhere[] = ['purchase_order.allot_hospital_id', '=', $arrParams['allotHospitalId']];
            }
            else
            {
                $getWhere[] = ['hospital.uid', '=', $arrParams['allotHospitalUid']];
            }
        }

        $builderQuery = self::on()
                            ->select([
                                         'purchase_order.*',
                                         'supplier.uid as supplier_uid',
                                         'supplier.name as supplier_name',
                                         'hospital.uid as hospital_uid',
                                         'hospital.alias_name as hospital_alias_name',
                                         'user.uid as user_uid',
                                         'user.name as user_name',
                                     ])
                            ->leftJoin((new PurchaseSupplierModel()->getTable()) . ' as supplier', 'purchase_order.supplier_id', '=', 'supplier.id')
                            ->leftJoin((new HospitalModel()->getTable()) . ' as hospital', 'purchase_order.allot_hospital_id', '=', 'hospital.id')
                            ->leftJoin((new UsersModel()->getTable()) . ' as user', 'purchase_order.created_by', '=', 'user.id')
                            ->when(!empty($getOrWhere), function ($query) use ($getOrWhere) {
                                $query->leftJoin((new PurchaseOrderItemModel()->getTable()) . ' as purchase_order_item',
                                                 'purchase_order.id',
                                                 '=',
                                                 'purchase_order_item.purchase_order_id');
                            })
                            ->when($getWhere, function ($query) use ($getWhere) {
                                $query->where($getWhere);
                            })
                            ->when($getOrWhere, function ($query) use ($getOrWhere) {
                                $query->where(function ($subQuery) use ($getOrWhere) {
                                    $subQuery->orWhere($getOrWhere);
                                });
                            })
                            ->orderBy('purchase_order.id', 'desc');

        // 获取条目总数
        $totalCount = $builderQuery->count(DB::raw('DISTINCT purchase_order.id'));
        if ($totalCount <= 0)
        {
            return [];
        }

        // 分页
        if ($iPage > 0 && $iPageSize > 0)
        {
            $builderQuery->offset(($iPage - 1) * $iPageSize)
                         ->limit($iPageSize);
        }

        // 获取条目数据
        $purchaseOrderList = $builderQuery->groupBy(['purchase_order.id'])
                                          ->get()
                                          ->toArray();

        return ['total' => $totalCount, 'data' => $purchaseOrderList];
    }

    /**
     * 获取调拨单列表
     *
     * @param array $arrParams
     * @param int   $iPage
     * @param int   $iPageSize
     *
     * @return array
     */
    public static function getTransferOrderListData(array $arrParams, int $iPage = 1, int $iPageSize = 10): array
    {
        $getWhere   = [];
        $getOrWhere = []; // 多个维度查询条件，需要与其他条件使用and

        // 必须指定医院查询（此医院ID对应purchase_order.allot_hospital_id，也就是调拨源门店是本医院）
        if (empty($arrParams['allotHospitalId']))
        {
            return [];
        }

        // 医院ID
        $getWhere[] = ['purchase_order.allot_hospital_id', '=', $arrParams['allotHospitalId']];

        // 多维度关键字查询
        if (!empty($arrParams['keywords']))
        {
            // 1.采购单编号
            $getOrWhere[] = ['purchase_order.purchase_code', 'like', '%' . $arrParams['keywords'] . '%'];

            // 2.商品条码
            $getOrWhere[] = ['purchase_order_item.item_barcode', 'like', '%' . $arrParams['keywords'] . '%'];
        }

        // 采购单ID
        if (!empty($arrParams['purchaseOrderId']))
        {
            $getWhere[] = ['purchase_order.id', '=', $arrParams['purchaseOrderId']];
        }

        // 采购类型
        if (!empty($arrParams['purchaseType']))
        {
            $getWhere[] = ['purchase_order.purchase_type', '=', $arrParams['purchaseType']];
        }

        // 采购单创建时间
        if (!empty($arrParams['startDate']) && strtotime($arrParams['startDate']) !== false)
        {
            $getWhere[] = ['purchase_order.created_at', '>=', $arrParams['startDate'] . ' 00:00:00'];
        }
        if (!empty($arrParams['endDate']) && strtotime($arrParams['endDate']) !== false)
        {
            $getWhere[] = ['purchase_order.created_at', '<=', $arrParams['endDate'] . ' 23:59:59'];
        }

        // 采购单状态
        if (isset($arrParams['purchaseStatus']) && is_numeric($arrParams['purchaseStatus']))
        {
            $getWhere[] = ['purchase_order.status', '=', $arrParams['purchaseStatus']];
        }

        // 退货单出库状态
        if (isset($arrParams['outboundStatus']) && is_numeric($arrParams['outboundStatus']))
        {
            // 未出库状态筛选，退货单不存在
            if ($arrParams['outboundStatus'] == StockOutboundStatusEnum::NotOutbound->value)
            {
                $getWhere[] = [
                    function ($query) use ($arrParams) {
                        $query->where(['stock_refund.id' => null])
                              ->orWhere(['stock_refund.outbound_status' => StockOutboundStatusEnum::NotOutbound->value]);
                    }
                ];
            }
            else
            {
                $getWhere[] = ['stock_refund.status', '=', $arrParams['outboundStatus']];
            }
        }

        $builderQuery = self::on()
                            ->select([
                                         'purchase_order.*',
                                         'supplier.uid as supplier_uid',
                                         'supplier.name as supplier_name',
                                         'hospital.uid as hospital_uid',
                                         'hospital.alias_name as hospital_alias_name',
                                         'stock_refund.id as stock_refund_id',
                                         'stock_refund.outbound_status as stock_refund_outbound_status',
                                     ])
                            ->leftJoin((new PurchaseSupplierModel()->getTable()) . ' as supplier', 'purchase_order.supplier_id', '=', 'supplier.id')
                            ->leftJoin((new HospitalModel()->getTable()) . ' as hospital', 'purchase_order.hospital_id', '=', 'hospital.id')
                            ->leftJoin((new StockRefundModel()->getTable()) . ' as stock_refund', 'purchase_order.purchase_code', '=', 'stock_refund.purchase_code')
                            ->when(!empty($getOrWhere), function ($query) use ($getOrWhere) {
                                $query->leftJoin((new PurchaseOrderItemModel()->getTable()) . ' as purchase_order_item',
                                                 'purchase_order.id',
                                                 '=',
                                                 'purchase_order_item.purchase_order_id');

                                $query->where(function ($subQuery) use ($getOrWhere) {
                                    $subQuery->orWhere($getOrWhere);
                                });
                            })
                            ->when($getWhere, function ($query) use ($getWhere) {
                                $query->where($getWhere);
                            })
                            ->orderBy('purchase_order.id', 'desc');

        // 获取条目总数
        $totalCount = $builderQuery->count(DB::raw('DISTINCT purchase_order.id'));
        if ($totalCount <= 0)
        {
            return [];
        }

        // 分页
        if ($iPage > 0 && $iPageSize > 0)
        {
            $builderQuery->offset(($iPage - 1) * $iPageSize)
                         ->limit($iPageSize);
        }

        // 获取条目数据
        $purchaseOrderList = $builderQuery->groupBy(['purchase_order.id'])
                                          ->get()
                                          ->toArray();

        return ['total' => $totalCount, 'data' => $purchaseOrderList];
    }
}
