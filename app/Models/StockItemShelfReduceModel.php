<?php

namespace App\Models;

use DB;

/**
 * 仓储-库存动态-减
 */
class StockItemShelfReduceModel extends Model
{
    protected $table = 'stock_item_shelf_reduce';

    /**
     * 获取出库记录列表数据
     *
     * @param array $arrParams
     * @param int   $page
     * @param int   $pageSize
     *
     * @return array
     */
    public static function getReduceStockRecordListData(array $arrParams, int $page = 1, int $pageSize = 10): array
    {
        // 必须指定医院查询
        if (empty($arrParams['hospitalId']))
        {
            return [];
        }

        // 医院ID
        $getWhere[] = ['stock_item_shelf_reduce.hospital_id', '=', $arrParams['hospitalId']];
        $getWhere[] = ['stock_item_shelf_reduce.status', '=', 1];

        // 基础查询条件
        $query = self::on()
                     ->select([
                                  'stock_item_shelf_reduce.*',
                                  'item.uid as item_uid',
                                  'item.pack_unit',
                                  'item.bulk_unit',
                                  'item.shelf_life_day',
                                  DB::raw(ItemModel::getItemDisplayNameField()),
                                  'user.uid as user_uid',
                                  'user.name as user_name',
                              ])
                     ->leftJoin(ItemModel::table() . ' as item', 'stock_item_shelf_reduce.item_id', '=', 'item.id')
                     ->leftJoin(UsersModel::table() . ' as user', 'stock_item_shelf_reduce.created_by', '=', 'user.id')
                     ->where($getWhere);

        // 商品条码或商品名称关键词搜索
        if (!empty($arrParams['keywords']))
        {
            $keywords = $arrParams['keywords'];
            $query->where(function ($subQuery) use ($keywords) {
                $subQuery->where('stock_item_shelf_reduce.item_barcode', 'like', '%' . $keywords . '%');
                ItemModel::getItemNameSearchCondition($subQuery, $keywords);
            });
        }

        // 出库关联业务单号
        if (!empty($arrParams['orderCode']))
        {
            $query->where('stock_item_shelf_reduce.relation_code', 'like', '%' . $arrParams['orderCode'] . '%');
        }

        // 出库类型
        if (isset($arrParams['type']) && is_numeric($arrParams['type']))
        {
            $query->where('stock_item_shelf_reduce.type', $arrParams['type']);
        }

        // 出库日期范围
        if (!empty($arrParams['startDate']))
        {
            $query->where('stock_item_shelf_reduce.created_at', '>=', $arrParams['startDate'] . ' 00:00:00');
        }
        if (!empty($arrParams['endDate']))
        {
            $query->where('stock_item_shelf_reduce.created_at', '<=', $arrParams['endDate'] . ' 23:59:59');
        }

        // 出库人
        if (!empty($arrParams['createUserId']) || !empty($arrParams['createUserUid']))
        {
            if (!empty($arrParams['createUserId']))
            {
                $query->where('user.id', '=', $arrParams['createUserId']);
            }
            else
            {
                $query->where('user.uid', '=', $arrParams['createUserUid']);
            }
        }

        // 获取总数
        $totalCount = $query->count();
        if ($totalCount <= 0)
        {
            return [];
        }

        // 分页查询
        if ($page > 0 && $pageSize > 0)
        {
            $query->offset(($page - 1) * $pageSize)
                  ->limit($pageSize);
        }

        // 排序
        $query->orderBy('stock_item_shelf_reduce.created_at', 'desc');

        // 获取数据
        $stockAddRecordList = $query->get()
                                    ->toArray();

        return ['total' => $totalCount, 'data' => $stockAddRecordList];
    }
}
