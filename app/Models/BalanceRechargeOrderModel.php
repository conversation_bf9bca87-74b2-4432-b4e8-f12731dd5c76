<?php

namespace App\Models;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Collection;

/**
 * 客户充值订单
 */
class BalanceRechargeOrderModel extends Model
{
    protected $table = 'member_balance_recharge_orders';

    /**
     * 获取充值订单[通过订单号或ID]
     *
     * @param string $orderCode
     * @param int    $orderId
     * @param int    $hospitalId
     *
     * @return object|null
     */
    public static function GetOrderByCodeOrId(
        string $orderCode = '',
        int    $orderId = 0,
        int    $hospitalId = 0
    ): object|null
    {
        if (empty($orderCode) && empty($orderId))
        {
            return null;
        }

        return self::on()
                   ->when(!empty($orderCode), function ($query) use ($orderCode) {
                       $query->where(['order_code' => $orderCode]);
                   })
                   ->when(!empty($orderId), function ($query) use ($orderId) {
                       $query->where(['id' => $orderId]);
                   })
                   ->when(!empty($hospitalId), function ($query) use ($hospitalId) {
                       $query->where(['hospital_id' => $hospitalId]);
                   })
                   ->first();
    }

    /**
     * 获取医院充值活动使用次数
     *
     * @param int   $hospitalId    医院ID
     * @param array $activitiesIds 充值活动ID
     *
     * @return Collection
     */
    public static function GetHospitalRechargeActivitiesUsedCount(int $hospitalId, array $activitiesIds): Collection
    {
        return self::on()
                   ->select('activity_id', DB::raw('SUM(quantity) as count'))
                   ->where('hospital_id', $hospitalId)
                   ->where('status', '>', 0)
                   ->whereIn('activity_id', $activitiesIds)
                   ->groupBy('activity_id')
                   ->get()
                   ->pluck('count', 'activity_id');
    }

    /**
     * 获取客户充值活动使用次数
     *
     * @param int   $memberId
     * @param array $activitiesIds
     *
     * @return Collection
     */
    public static function GetMemberRechargeActivitiesUsedCount(int $memberId, array $activitiesIds): Collection
    {
        return self::on()
                   ->select('activity_id', DB::raw('SUM(quantity) as count'))
                   ->where('member_id', $memberId)
                   ->where('status', '>', 0)
                   ->whereIn('activity_id', $activitiesIds)
                   ->groupBy('activity_id')
                   ->get()
                   ->pluck('count', 'activity_id');
    }
}
