<?php

namespace App\Models;

use DB;
use App\Enums\StockGroupTypeEnum;

/**
 * 仓储-库存库存表
 */
class StockItemShelfModel extends Model
{
    protected $table = 'stock_item_shelf';

    /**
     * 库存告警数量，低于该数量则告警
     */
    private const int WarningQuantity = 10;

    /**
     * 获取有效库存条件
     *
     * @param        $query
     * @param string $tableAlias
     *
     * @return mixed
     */
    public static function getValidStockItemShelfCondition($query, string $tableAlias = 'stock_item_shelf'): mixed
    {
        $nowDate = getNowDateTime('Y-m-d');

        return $query->where(function ($q) use ($nowDate, $tableAlias) {
            $q->whereNull("$tableAlias.expired_date")
              ->orWhere("$tableAlias.expired_date", '0000-00-00')
              ->orWhere("$tableAlias.expired_date", '>', $nowDate);
        });
    }

    /**
     * 获取有库存的条件
     *
     * @param        $query
     * @param string $tableAlias
     *
     * @return mixed
     */
    public static function getValidStockItemQuantityCondition($query, string $tableAlias = 'stock_item_shelf'): mixed
    {
        return $query->where(function ($q) use ($tableAlias) {
            $q->where("$tableAlias.effective_pack_quantity", '>', 0)
              ->orWhere("$tableAlias.effective_bulk_quantity", '>', 0);
        });
    }

    /**
     * 获取商品有效库存汇总
     *
     * @param int   $hospitalId
     * @param array $itemIds
     * @param array $filterCondition 指定过滤条件
     * @param bool  $isValid
     *
     * @return array
     */
    public static function getStockItemTotalEffectiveStock(int $hospitalId, array $itemIds, array $filterCondition = [], bool $isValid = true): array
    {
        if (empty($hospitalId) || empty($itemIds))
        {
            return [];
        }

        // 获取库存条件
        $where = [['hospital_id', '=', $hospitalId], ['status', '=', 1]];

        // 有库存的
        $where[] = [
            function ($query) {
                StockItemShelfModel::getValidStockItemQuantityCondition($query);
            }
        ];

        // 未过期的
        if (!empty($isValid))
        {
            $where[] = [
                function ($query) {
                    StockItemShelfModel::getValidStockItemShelfCondition($query);
                }
            ];
        }

        // 指定货位
        if (!empty($filterCondition['shelf_code']))
        {
            $where[] = ['shelf_code', '=', $filterCondition['shelf_code']];
        }

        // 指定效期
        if (!empty($filterCondition['produce_date']))
        {
            $where[] = ['produce_date', '=', $filterCondition['produce_date']];
        }
        if (!empty($filterCondition['expired_date']))
        {
            $where[] = ['expired_date', '=', $filterCondition['expired_date']];
        }

        $getStockItemQuantityRes = self::on()
                                       ->select([
                                                    'item_id',
                                                    'item_barcode',
                                                    'bulk_ratio',
                                                    DB::raw('sum(effective_pack_quantity) as pack_quantity'),
                                                    DB::raw('sum(effective_bulk_quantity) as bulk_quantity')
                                                ])
                                       ->where($where)
                                       ->whereIn('item_id', $itemIds)
                                       ->groupBy(['item_id'])
                                       ->get()
                                       ->toArray();
        if (empty($getStockItemQuantityRes))
        {
            return [];
        }

        return $getStockItemQuantityRes;
    }

    /**
     * 获取商品有效库存，并且按照指定分组进行分组
     *
     * @param int                $hospitalId
     * @param array              $itemIds
     * @param StockGroupTypeEnum $groupType
     * @param array              $filterCondition
     * @param bool               $isValid
     *
     * @return array
     */
    public static function getStockItemEffectiveStockGrouped(int $hospitalId, array $itemIds, StockGroupTypeEnum $groupType, array $filterCondition = [], bool $isValid = true): array
    {
        if (empty($hospitalId) || empty($itemIds))
        {
            return [];
        }

        // 基础条件
        $where = [['hospital_id', '=', $hospitalId], ['status', '=', 1]];

        // 有库存的
        $where[] = [
            function ($query) {
                self::getValidStockItemQuantityCondition($query);
            }
        ];

        // 未过期的
        if ($isValid)
        {
            $where[] = [
                function ($query) {
                    self::getValidStockItemShelfCondition($query);
                }
            ];
        }

        // 过滤条件
        if (!empty($filterCondition['shelf_code']))
        {
            $where[] = ['shelf_code', '=', $filterCondition['shelf_code']];
        }
        if (!empty($filterCondition['expired_date']))
        {
            $where[] = ['expired_date', '=', $filterCondition['expired_date']];
        }

        $query = self::on()
                     ->select($groupType->getSelectFields())
                     ->where($where)
                     ->whereIn('item_id', $itemIds);

        // 根据分组类型添加分组
        $groupByFields = $groupType->getGroupByFields();
        if (!empty($groupByFields))
        {
            $query->groupBy($groupByFields);
        }

        // 排序
        $query->orderBy('expired_date')
              ->orderBy('created_at');

        $result = $query->get()
                        ->toArray();

        if (empty($result))
        {
            return [];
        }

        return $result;
    }

    /**
     * 获取商品有效库存明细，并且按照效期排序
     *
     * @param int   $hospitalId
     * @param array $itemIds
     * @param array $filterCondition
     * @param bool  $isValid
     *
     * @return array
     */
    public static function getStockItemEffectiveStockDetail(int $hospitalId, array $itemIds, array $filterCondition = [], bool $isValid = true): array
    {
        if (empty($hospitalId) || empty($itemIds))
        {
            return [];
        }

        // 基础条件
        $where = [['hospital_id', '=', $hospitalId], ['status', '=', 1]];

        // 有库存的
        $where[] = [
            function ($query) {
                self::getValidStockItemQuantityCondition($query);
            }
        ];

        // 未过期的
        if (!empty($isValid))
        {
            $where[] = [
                function ($query) {
                    self::getValidStockItemShelfCondition($query);
                }
            ];
        }

        // 指定货位
        if (!empty($filterCondition['shelf_code']))
        {
            $where[] = ['shelf_code', '=', $filterCondition['shelf_code']];
        }

        // 指定效期
        if (!empty($filterCondition['produce_date']))
        {
            $where[] = ['produce_date', '=', $filterCondition['produce_date']];
        }
        if (!empty($filterCondition['expired_date']))
        {
            $where[] = ['expired_date', '=', $filterCondition['expired_date']];
        }

        $getStockItemRes = self::on()
                               ->select()
                               ->where($where)
                               ->whereIn('item_id', $itemIds)
                               ->orderBy('expired_date') // 效期优先
                               ->orderBy('created_at') // 同效期，按照创建时间
                               ->get()
                               ->toArray();
        if (empty($getStockItemRes))
        {
            return [];
        }

        $returnStockItemList = [];
        foreach ($getStockItemRes as $curStockItem)
        {
            $returnStockItemList[$curStockItem['item_id']][] = $curStockItem;
        }

        return $returnStockItemList ?? [];
    }

    /**
     * 获取有库存的商品ID列表
     *
     * @param array $arrParams 搜索参数
     * @param int   $page      页码
     * @param int   $pageSize  每页条数
     *
     * @return array
     */
    public static function getEffectiveStockItemListData(array $arrParams, int $page = 1, int $pageSize = 10): array
    {
        // 必须指定医院查询
        if (empty($arrParams['hospitalId']))
        {
            return [];
        }

        // 医院ID
        $getWhere[] = ['stock_item_shelf.hospital_id', '=', $arrParams['hospitalId']];
        $getWhere[] = ['stock_item_shelf.status', '=', 1];

        // 有库存的
        $getWhere[] = [
            function ($query) {
                self::getValidStockItemQuantityCondition($query);
            }
        ];

        // 多维度关键字查询
        $keywords = $arrParams['keywords'] ?? '';

        $builderQuery = self::on()
                            ->select(['stock_item_shelf.item_id'])
                            ->leftJoin(new ItemModel()->getTable() . ' as item', 'stock_item_shelf.item_id', '=', 'item.id')
                            ->when($getWhere, function ($query) use ($getWhere) {
                                $query->where($getWhere);
                            })
                            ->when(!empty($keywords), function ($query) use ($keywords) {
                                $query->where(function ($subQuery) use ($keywords) {
                                    $subQuery->where('stock_item_shelf.item_barcode', 'like', '%' . $keywords . '%');
                                    ItemModel::getItemNameSearchCondition($subQuery, $keywords);
                                });
                            })
                            ->orderBy('stock_item_shelf.id', 'desc');

        // 获取条目总数
        $totalCount = $builderQuery->count(DB::raw('distinct stock_item_shelf.item_id'));
        if ($totalCount <= 0)
        {
            return [];
        }

        // 分页
        if ($page > 0 && $pageSize > 0)
        {
            $builderQuery->offset(($page - 1) * $pageSize)
                         ->limit($pageSize);
        }

        // 获取条目数据
        $stockItemList = $builderQuery->groupBy(['stock_item_shelf.item_id'])
                                      ->get()
                                      ->toArray();

        return ['total' => $totalCount, 'data' => $stockItemList];
    }

    /**
     * 获取有库存并且效期小于预警数量的商品列表
     *
     * @param array $arrParams
     * @param int   $page
     * @param int   $pageSize
     *
     * @return array
     */
    public static function getEffectiveStockWarningItemListData(array $arrParams, int $page = 1, int $pageSize = 10): array
    {
        // 必须指定医院查询
        if (empty($arrParams['hospitalId']))
        {
            return [];
        }

        // 医院ID
        $getWhere[] = ['stock_item_shelf.hospital_id', '=', $arrParams['hospitalId']];
        $getWhere[] = ['stock_item_shelf.status', '=', 1];

        // 有库存的
        $getWhere[] = [
            function ($query) {
                self::getValidStockItemQuantityCondition($query);
            }
        ];

        $builderQuery = self::on()
                            ->select([
                                         'stock_item_shelf.item_id',
                                         DB::raw('SUM(stock_item_shelf.effective_pack_quantity + stock_item_shelf.effective_bulk_quantity) as total_quantity')
                                     ])
                            ->leftJoin(new ItemModel()->getTable() . ' as item', 'stock_item_shelf.item_id', '=', 'item.id')
                            ->when($getWhere, function ($query) use ($getWhere) {
                                $query->where($getWhere);
                            })
                            ->when(!empty($arrParams['keywords']), function ($query) use ($arrParams) {
                                $keywords = $arrParams['keywords'];
                                $query->where(function ($subQuery) use ($keywords) {
                                    $subQuery->where('stock_item_shelf.item_barcode', 'like', '%' . $keywords . '%');
                                    ItemModel::getItemNameSearchCondition($subQuery, $keywords);
                                });
                            })
                            ->groupBy(['stock_item_shelf.item_id'])
                            ->havingRaw('total_quantity < ?', [self::WarningQuantity])
                            ->orderBy('stock_item_shelf.id', 'desc');

        // 获取条目总数
        $totalCount = $builderQuery->count();
        if ($totalCount <= 0)
        {
            return [];
        }

        // 分页
        if ($page > 0 && $pageSize > 0)
        {
            $builderQuery->offset(($page - 1) * $pageSize)
                         ->limit($pageSize);
        }

        // 获取条目数据
        $stockItemList = $builderQuery->get()
                                      ->toArray();

        return ['total' => $totalCount, 'data' => $stockItemList];
    }

    /**
     * 获取临/过期预警商品列表数据
     *
     * @param array $arrParams 搜索参数
     * @param int   $page      页码
     * @param int   $pageSize  每页条数
     *
     * @return array
     */
    public static function getExpireWarningItemListData(array $arrParams, int $page = 1, int $pageSize = 10): array
    {
        // 必须指定医院查询
        if (empty($arrParams['hospitalId']))
        {
            return [];
        }

        // 医院ID
        $getWhere[] = ['stock_item_shelf.hospital_id', '=', $arrParams['hospitalId']];
        $getWhere[] = ['stock_item_shelf.status', '=', 1];
        $getWhere[] = ['stock_item_shelf.expired_date', 'IS NOT', null];

        // 有库存的
        $getWhere[] = [
            function ($query) {
                self::getValidStockItemQuantityCondition($query);
            }
        ];

        // 多维度关键字查询将在查询构建器中处理

        // 过期日期
        if (!empty($arrParams['expireStartDate']) && strtotime($arrParams['expireStartDate']) !== false)
        {
            $getWhere[] = ['stock_item_shelf.expired_date', '>=', $arrParams['expireStartDate']];
        }
        if (!empty($arrParams['expireEndDate']) && strtotime($arrParams['expireEndDate']) !== false)
        {
            $getWhere[] = ['stock_item_shelf.expired_date', '<=', $arrParams['expireEndDate']];
        }

        // 临/过期类型
        if (!empty($arrParams['expireType']))
        {
            // 临期
            if ($arrParams['expireType'] == 1)
            {
                $havingRaw = 'expire_days > 0 AND expire_days <= ifnull(item.over_life_day, 0)';
            }
            // 过期
            else
            {
                $havingRaw = 'expire_days <= 0';
            }
        }
        else
        {
            // 默认显示临期和过期的商品
            $havingRaw = 'expire_days <= IFNULL(item.over_life_day, 0)';
        }

        $builderQuery = self::on()
                            ->select([
                                         'stock_item_shelf.item_id',
                                         'stock_item_shelf.item_barcode',
                                         'stock_item_shelf.shelf_code',
                                         'stock_item_shelf.expired_date',
                                         'item.over_life_day',
                                         DB::raw('SUM(stock_item_shelf.effective_pack_quantity) as total_pack_quantity'),
                                         DB::raw('SUM(stock_item_shelf.effective_bulk_quantity) as total_bulk_quantity'),
                                         DB::raw('SUM(stock_item_shelf.effective_pack_quantity + stock_item_shelf.effective_bulk_quantity) as total_quantity'),
                                         DB::raw('DATEDIFF(stock_item_shelf.expired_date, CURDATE()) as expire_days')
                                     ])
                            ->leftJoin(new ItemModel()->getTable() . ' as item', 'stock_item_shelf.item_id', '=', 'item.id')
                            ->when($getWhere, function ($query) use ($getWhere) {
                                $query->where($getWhere);
                            })
                            ->when(!empty($arrParams['keywords']), function ($query) use ($arrParams) {
                                $keywords = $arrParams['keywords'];
                                $query->where(function ($subQuery) use ($keywords) {
                                    $subQuery->where('stock_item_shelf.item_barcode', 'like', '%' . $keywords . '%');
                                    ItemModel::getItemNameSearchCondition($subQuery, $keywords);
                                });
                            })
                            ->groupBy(['stock_item_shelf.item_id', 'stock_item_shelf.shelf_code', 'stock_item_shelf.expired_date'])
                            ->havingRaw($havingRaw)
                            ->orderBy('stock_item_shelf.expired_date');

        // 获取条目总数
        $totalCount = $builderQuery->count();
        if ($totalCount <= 0)
        {
            return [];
        }

        // 分页
        if ($page > 0 && $pageSize > 0)
        {
            $builderQuery->offset(($page - 1) * $pageSize)
                         ->limit($pageSize);
        }

        // 获取条目数据
        $stockItemList = $builderQuery->get()
                                      ->toArray();

        return ['total' => $totalCount, 'data' => $stockItemList];
    }
}
