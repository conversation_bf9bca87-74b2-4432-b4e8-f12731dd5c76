<?php

namespace App\Models;

use App\Enums\TestOrImageStatusEnum;
use DB;

/**
 * 化验项目
 */
class TestModel extends Model
{
    protected $table = 'his_tests';

    /**
     * 获取化验列表
     *
     * @param array $arrParams
     * @param int   $iPage
     * @param int   $iPageSize
     *
     * @return array
     */
    public static function getTestListData(array $arrParams, int $iPage = 0, int $iPageSize = 0): array
    {
        $getWhere       = [];
        $getOrWhere     = []; // 多个维度查询条件，需要与其他条件使用and
        $getItemOrWhere = []; // 商品名称多个维度，需要与其他条件使用and

        // 多维度关键字查询
        if (!empty($arrParams['keywords']))
        {
            // 1. 手机号查询
            if (checkValidCellphone($arrParams['keywords']))
            {
                $getWhere[] = ['member.phone', '=', $arrParams['keywords']];
            }
            else
            {
                // 2.客户名称
                $getOrWhere[] = ['member.name', 'like', '%' . $arrParams['keywords'] . '%'];

                // 3.化验编号
                $getOrWhere[] = ['his_tests.test_code', 'like', '%' . $arrParams['keywords'] . '%'];

                // 4.宠物名称
                $getOrWhere[] = ['member_pet.name', 'like', '%' . $arrParams['keywords'] . '%'];

                // 5.宠物病历号
                $getOrWhere[] = ['member_pet.record_number', 'like', '%' . $arrParams['keywords'] . '%'];
            }
        }

        // 医院ID
        if (!empty($arrParams['hospitalId']))
        {
            $getWhere[] = ['his_tests.hospital_id', '=', $arrParams['hospitalId']];
        }

        // 化验ID
        if (!empty($arrParams['testId']))
        {
            $getWhere[] = ['his_tests.id', '=', $arrParams['testId']];
        }

        // 病历ID
        if (!empty($arrParams['caseId']))
        {
            $getWhere[] = ['his_tests.case_id', '=', $arrParams['caseId']];
        }

        // 病历编号
        if (!empty($arrParams['caseCode']))
        {
            $getCaseRes = CasesModel::getData(['id'], ['case_code' => $arrParams['caseCode']]);
            if (!empty($getCaseRes))
            {
                $getWhere[] = ['his_tests.case_id', '=', current($getCaseRes)['id']];
            }
            else
            {
                $getWhere[] = ['his_tests.case_id', '=', - 1];
            }
        }

        // 化验编号
        if (!empty($arrParams['testCode']))
        {
            $getWhere[] = ['his_tests.test_code', 'like', '%' . $arrParams['testCode'] . '%'];
        }

        // 化验项目名称
        if (!empty($arrParams['name']))
        {
            $getItemOrWhere[] = [
                function ($query) use ($arrParams) {
                    ItemModel::getItemNameSearchCondition($query, $arrParams['name']);
                }
            ];
        }

        // 检测开具时间
        if (!empty($arrParams['startDate']) && strtotime($arrParams['startDate']) !== false)
        {
            $getWhere[] = ['his_tests.created_at', '>=', $arrParams['startDate'] . ' 00:00:00'];
        }
        if (!empty($arrParams['endDate']) && strtotime($arrParams['endDate']) !== false)
        {
            $getWhere[] = ['his_tests.created_at', '<=', $arrParams['endDate'] . ' 23:59:59'];
        }

        // 检测开具医生
        if (!empty($arrParams['doctorUid']))
        {
            $getWhere[] = ['user.uid', '=', $arrParams['doctorUid']];
        }

        // 检测状态
        if (isset($arrParams['status']) && is_numeric($arrParams['status']))
        {
            $getStatusWhere = TestOrImageStatusEnum::getConditions($arrParams['status'], self::table());
            if (!empty($getStatusWhere))
            {
                $getWhere = array_merge($getWhere, $getStatusWhere);
            }
        }

        // 付款状态
        if (isset($arrParams['payStatus']) && is_numeric($arrParams['payStatus']))
        {
            $getWhere[] = ['his_tests.is_paid', '=', $arrParams['payStatus']];
        }

        // 检测员录入状态
        if (isset($arrParams['testerStatus']) && is_numeric($arrParams['testerStatus']))
        {
            $getWhere[] = ['his_tests.is_tester', '=', $arrParams['testerStatus']];
        }

        $builderQuery = self::on()
                            ->select([
                                         'his_tests.*',
                                         'item.name as item_name',
                                         DB::raw(ItemModel::getItemDisplayNameField()),
                                         'item.use_unit as item_use_unit',
                                         'item.uid as item_uid',
                                         'user.uid as doctor_uid',
                                         'user.name as doctor_name',
                                         'member.name as member_name',
                                         'member.uid as member_uid',
                                         'member_pet.name as pet_name',
                                         'member_pet.record_number as pet_record_number',
                                     ])
                            ->leftJoin((new MemberModel()->getTable()) . ' as member', 'member.id', '=', 'his_tests.member_id')
                            ->leftJoin((new MemberPetsModel()->getTable()) . ' as member_pet', 'member_pet.id', '=', 'his_tests.pet_id')
                            ->leftJoin((new ItemModel()->getTable()) . ' as item', 'item.id', '=', 'his_tests.item_id')
                            ->leftJoin((new UsersModel()->getTable()) . ' as user', 'user.id', '=', 'his_tests.doctor_id')
                            ->when($getWhere, function ($query) use ($getWhere) {
                                $query->where($getWhere);
                            })
                            ->when($getOrWhere, function ($query) use ($getOrWhere) {
                                $query->where(function ($subQuery) use ($getOrWhere) {
                                    $subQuery->orWhere($getOrWhere);
                                });
                            })
                            ->when($getItemOrWhere, function ($query) use ($getItemOrWhere) {
                                $query->where(function ($subQuery) use ($getItemOrWhere) {
                                    $subQuery->orWhere($getItemOrWhere);
                                });
                            })
                            ->orderBy('his_tests.id', 'desc');

        // 获取条目总数
        $totalCount = $builderQuery->count();
        if ($totalCount <= 0)
        {
            return [];
        }

        // 分页
        if ($iPage > 0 && $iPageSize > 0)
        {
            $builderQuery->offset(($iPage - 1) * $iPageSize)
                         ->limit($iPageSize);
        }

        // 获取条目数据
        $testList = $builderQuery->get()
                                 ->toArray();

        return ['total' => $totalCount, 'data' => $testList];
    }

    /**
     * 根据化验ID获取化验项信息
     *
     * @param array $testIds
     *
     * @return array
     */
    public static function getTestItemInfoByTestIds(array $testIds): array
    {
        if (empty($testIds))
        {
            return [];
        }

        return self::on()
                   ->select([
                                'his_tests.*',
                                'item.name as item_name',
                                DB::raw(ItemModel::getItemDisplayNameField()),
                                'item.use_unit as item_use_unit',
                                'item.uid as item_uid',
                            ])
                   ->leftJoin((new ItemModel()->getTable()) . ' as item', 'item.id', '=', 'his_tests.item_id')
                   ->whereIn('his_tests.id', $testIds)
                   ->orderBy('his_tests.id', 'desc')
                   ->get()
                   ->toArray();
    }

    /**
     * 获取病历下化验数量、金额
     *
     * @param int $caseId
     *
     * @return array
     */
    public static function getTestSummaryByCaseId(int $caseId): array
    {
        if (empty($caseId))
        {
            return [];
        }

        $getTestSummaryRes = self::on()
                                 ->select([
                                              DB::raw('COUNT(*) as test_count'),
                                              DB::raw('SUM(price) as total_price'),
                                              DB::raw('SUM(CASE WHEN is_paid = 1 THEN price ELSE 0 END) as paid_price'),
                                              DB::raw('SUM(CASE WHEN (is_paid = 0 OR is_paid = 2) THEN price ELSE 0 END) as unpaid_price'),
                                          ])
                                 ->where(['case_id' => $caseId, 'status' => 1])
                                 ->first();

        return $getTestSummaryRes ? $getTestSummaryRes->toArray() : [];
    }
}
