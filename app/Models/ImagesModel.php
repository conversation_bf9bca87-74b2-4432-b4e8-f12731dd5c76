<?php

namespace App\Models;

use DB;
use App\Enums\TestOrImageStatusEnum;

/**
 * 影像项目
 */
class ImagesModel extends Model
{
    protected $table = 'his_images';

    /**
     * 获取影像列表
     *
     * @param array $arrParams
     * @param int   $iPage
     * @param int   $iPageSize
     *
     * @return array
     */
    public static function getImageListData(array $arrParams, int $iPage = 1, int $iPageSize = 10): array
    {
        $getWhere       = [];
        $getOrWhere     = []; // 多个维度查询条件，需要与其他条件使用and
        $getItemOrWhere = []; // 商品名称多个维度，需要与其他条件使用and

        // 多维度关键字查询
        if (!empty($arrParams['keywords']))
        {
            // 1. 手机号查询
            if (checkValidCellphone($arrParams['keywords']))
            {
                $getWhere[] = ['member.phone', '=', $arrParams['keywords']];
            }
            else
            {
                // 2.客户名称
                $getOrWhere[] = ['member.name', 'like', '%' . $arrParams['keywords'] . '%'];

                // 3.影像编号
                $getOrWhere[] = ['his_images.image_code', 'like', '%' . $arrParams['keywords'] . '%'];

                // 4.宠物名称
                $getOrWhere[] = ['member_pet.name', 'like', '%' . $arrParams['keywords'] . '%'];

                // 5.宠物病历号
                $getOrWhere[] = ['member_pet.record_number', 'like', '%' . $arrParams['keywords'] . '%'];
            }
        }

        // 医院ID
        if (!empty($arrParams['hospitalId']))
        {
            $getWhere[] = ['his_images.hospital_id', '=', $arrParams['hospitalId']];
        }

        // 影像ID
        if (!empty($arrParams['imageId']))
        {
            $getWhere[] = ['his_images.id', '=', $arrParams['imageId']];
        }

        // 病历ID
        if (!empty($arrParams['caseId']))
        {
            $getWhere[] = ['his_images.case_id', '=', $arrParams['caseId']];
        }

        // 病历编号
        if (!empty($arrParams['caseCode']))
        {
            $getCaseRes = CasesModel::getData(['id'], ['case_code' => $arrParams['caseCode']]);
            if (!empty($getCaseRes))
            {
                $getWhere[] = ['his_images.case_id', '=', current($getCaseRes)['id']];
            }
            else
            {
                $getWhere[] = ['his_images.case_id', '=', - 1];
            }
        }

        // 影像编号
        if (!empty($arrParams['imageCode']))
        {
            $getWhere[] = ['his_images.image_code', 'like', '%' . $arrParams['imageCode'] . '%'];
        }

        // 影像项目名称
        if (!empty($arrParams['name']))
        {
            $getItemOrWhere[] = [
                function ($query) use ($arrParams) {
                    ItemModel::getItemNameSearchCondition($query, $arrParams['name']);
                }
            ];
        }

        // 检测开具时间
        if (!empty($arrParams['startDate']) && strtotime($arrParams['startDate']) !== false)
        {
            $getWhere[] = ['his_images.created_at', '>=', $arrParams['startDate'] . ' 00:00:00'];
        }
        if (!empty($arrParams['endDate']) && strtotime($arrParams['endDate']) !== false)
        {
            $getWhere[] = ['his_images.created_at', '<=', $arrParams['endDate'] . ' 23:59:59'];
        }

        // 检测开具医生
        if (!empty($arrParams['doctorUid']))
        {
            $getWhere[] = ['user.uid', '=', $arrParams['doctorUid']];
        }

        // 检测状态
        if (isset($arrParams['status']) && is_numeric($arrParams['status']))
        {
            $getStatusWhere = TestOrImageStatusEnum::getConditions($arrParams['status'], self::table());
            if (!empty($getStatusWhere))
            {
                $getWhere = array_merge($getWhere, $getStatusWhere);
            }
        }

        // 付款状态
        if (isset($arrParams['payStatus']) && is_numeric($arrParams['payStatus']))
        {
            $getWhere[] = ['his_images.is_paid', '=', $arrParams['payStatus']];
        }

        // 检测员录入状态
        if (isset($arrParams['testerStatus']) && is_numeric($arrParams['testerStatus']))
        {
            $getWhere[] = ['his_images.is_tester', '=', $arrParams['testerStatus']];
        }

        $builderQuery = self::on()
                            ->select([
                                         'his_images.*',
                                         'item.name as item_name',
                                         DB::raw(ItemModel::getItemDisplayNameField()),
                                         'item.use_unit as item_use_unit',
                                         'item.uid as item_uid',
                                         'user.uid as doctor_uid',
                                         'user.name as doctor_name',
                                         'member.name as member_name',
                                         'member.uid as member_uid',
                                         'member_pet.name as pet_name',
                                         'member_pet.record_number as pet_record_number',
                                     ])
                            ->leftJoin((new MemberModel()->getTable()) . ' as member', 'member.id', '=', 'his_images.member_id')
                            ->leftJoin((new MemberPetsModel()->getTable()) . ' as member_pet', 'member_pet.id', '=', 'his_images.pet_id')
                            ->leftJoin((new ItemModel()->getTable()) . ' as item', 'item.id', '=', 'his_images.item_id')
                            ->leftJoin((new UsersModel()->getTable()) . ' as user', 'user.id', '=', 'his_images.doctor_id')
                            ->when($getWhere, function ($query) use ($getWhere) {
                                $query->where($getWhere);
                            })
                            ->when($getOrWhere, function ($query) use ($getOrWhere) {
                                $query->where(function ($subQuery) use ($getOrWhere) {
                                    $subQuery->orWhere($getOrWhere);
                                });
                            })
                            ->when($getItemOrWhere, function ($query) use ($getItemOrWhere) {
                                $query->where(function ($subQuery) use ($getItemOrWhere) {
                                    $subQuery->orWhere($getItemOrWhere);
                                });
                            })
                            ->orderBy('his_images.id', 'desc');

        // 获取条目总数
        $totalCount = $builderQuery->count();
        if ($totalCount <= 0)
        {
            return [];
        }

        // 分页
        $builderQuery->offset(($iPage - 1) * $iPageSize)
                     ->limit($iPageSize);

        // 获取条目数据
        $imageList = $builderQuery->get()
                                  ->toArray();

        return ['total' => $totalCount, 'data' => $imageList];
    }

    /**
     * 根据化验ID获取化验项信息
     *
     * @param array $imageIds
     *
     * @return array
     */
    public static function getImageItemInfoByImageIds(array $imageIds): array
    {
        if (empty($imageIds))
        {
            return [];
        }

        return self::on()
                   ->select([
                                'his_images.*',
                                'item.name as item_name',
                                DB::raw(ItemModel::getItemDisplayNameField()),
                                'item.use_unit as item_use_unit',
                                'item.uid as item_uid',
                            ])
                   ->leftJoin((new ItemModel()->getTable()) . ' as item', 'item.id', '=', 'his_images.item_id')
                   ->whereIn('his_images.id', $imageIds)
                   ->orderBy('his_images.id', 'desc')
                   ->get()
                   ->toArray();
    }

    /**
     * 根据病历ID获取影像数量、金额
     *
     * @param int $caseId
     *
     * @return array
     */
    public static function getImageSummaryByCaseId(int $caseId): array
    {
        if (empty($caseId))
        {
            return [];
        }

        $getImageSummaryRes = self::on()
                                  ->select([
                                               DB::raw('COUNT(*) as image_count'),
                                               DB::raw('SUM(price) as total_price'),
                                               DB::raw('SUM(CASE WHEN is_paid = 1 THEN price ELSE 0 END) as paid_price'),
                                               DB::raw('SUM(CASE WHEN (is_paid = 0 OR is_paid = 2) THEN price ELSE 0 END) as unpaid_price'),
                                           ])
                                  ->where(['case_id' => $caseId, 'status' => 1])
                                  ->first();

        return $getImageSummaryRes ? $getImageSummaryRes->toArray() : [];
    }
}
