<?php

namespace App\Models;

/**
 * 化验耗材模板详情
 */
class TestsConsumablesTemplatesItemModel extends Model
{
    protected $table = 'his_tests_consumables_templates_item';

    /**
     * 根据化验项目ID获取所有相关的耗材商品
     * 支持多级别查询：医院级别 > 品牌级别 > 组织级别
     *
     * @param array $testItemIds 化验项目ID数组
     * @param int   $hospitalId  医院ID
     * @param int   $brandId     品牌ID
     * @param int   $orgId       组织ID
     *
     * @return array 二维数组 [testItemId => [consumables]]
     */
    public static function getConsumablesByTestItemIds(array $testItemIds, int $hospitalId, int $brandId, int $orgId): array
    {
        if (empty($testItemIds))
        {
            return [];
        }

        // 获取化验项目关联的耗材模板
        $getTemplates = TestsConsumablesTemplatesModel::getTestsConsumablesTemplateByItemId($testItemIds, $hospitalId, $brandId, $orgId);
        if (empty($getTemplates))
        {
            return [];
        }

        // 化验耗材模版关联的耗材
        $templateIds            = array_column($getTemplates, 'id');
        $getConsumablesItemsRes = self::on()
                                      ->whereIn('template_id', $templateIds)
                                      ->where('status', 1)
                                      ->get()
                                      ->toArray();
        if (empty($getConsumablesItemsRes))
        {
            return [];
        }

        // 按化验项ID分组耗材
        $result = [];
        foreach ($getTemplates as $testItemId => $template)
        {
            $templateId          = $template['id'];
            $result[$testItemId] = array_filter($getConsumablesItemsRes, function ($consumable) use ($templateId) {
                return $consumable['template_id'] == $templateId;
            });

            $result[$testItemId] = array_values($result[$testItemId]);
        }

        return $result;
    }
}
