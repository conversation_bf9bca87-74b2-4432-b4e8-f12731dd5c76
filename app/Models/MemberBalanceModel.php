<?php

namespace App\Models;

use Illuminate\Support\Collection;

class MemberBalanceModel extends Model
{
    protected $table = 'member_balance';

    /**
     * 获取会员余额
     *
     * @param int       $memberId
     * @param bool|null $isFrozen
     * @param bool      $onWritePdo
     *
     * @return object|null
     */
    public static function GetMemberBalance(int $memberId, ?bool $isFrozen = null, bool $onWritePdo = false
    ): object|null
    {
        return self::on()
                   ->where(['member_id' => $memberId])
                   ->when(is_bool($isFrozen), function ($query) use ($isFrozen) {
                       $query->where(['is_frozen' => $isFrozen]);
                   })
                   ->when($onWritePdo, function ($query) {
                       $query->useWritePdo();
                   })
                   ->first();
    }

    /**
     * 批量获取会员余额
     *
     * @param array     $memberIds
     * @param bool|null $isFrozen
     * @param bool      $onWritePdo
     *
     * @return Collection
     */
    public static function GetMembersBalance(array $memberIds, ?bool $isFrozen = null, bool $onWritePdo = false
    ): Collection
    {
        return self::on()
                   ->whereIn('member_id', $memberIds)
                   ->when(is_bool($isFrozen), function ($query) use ($isFrozen) {
                       $query->where(['is_frozen' => $isFrozen]);
                   })
                   ->when($onWritePdo, function ($query) {
                       $query->useWritePdo();
                   })
                   ->get();
    }
}
