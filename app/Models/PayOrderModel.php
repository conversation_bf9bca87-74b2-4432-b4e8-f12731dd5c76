<?php

namespace App\Models;

use Illuminate\Pagination\LengthAwarePaginator;
use App\Models\Traits\KeywordSearchMemberPet;

class PayOrderModel extends Model
{
    use KeywordSearchMemberPet;

    protected $table = 'pay_order';

    /**
     * 获取结算单[通过支付单号或ID]
     *
     * @param string $payOrderCode
     * @param int    $payOrderId
     * @param int    $hospitalId
     * @param bool   $useWritePdo
     *
     * @return object|null
     */
    public static function GetPayOrderByCodeOrId(
        string $payOrderCode = '',
        int    $payOrderId = 0,
        int    $hospitalId = 0,
        bool   $useWritePdo = false
    ): object|null
    {
        if (empty($payOrderCode) && empty($payOrderId))
        {
            return null;
        }

        return self::on()
                   ->when(!empty($payOrderCode), function ($query) use ($payOrderCode) {
                       $query->where(['pay_order_code' => $payOrderCode]);
                   })
                   ->when(!empty($payOrderId), function ($query) use ($payOrderId) {
                       $query->where(['id' => $payOrderId]);
                   })
                   ->when(!empty($hospitalId), function ($query) use ($hospitalId) {
                       $query->where(['hospital_id' => $hospitalId]);
                   })
                   ->when($useWritePdo, function ($query) {
                       $query->useWritePdo();
                   })
                   ->first();
    }

    /**
     * 获取结算单的所有创建人
     *
     * @param int $hospitalId
     *
     * @return array
     */
    public static function GetOrderCreateUsersOptions(int $hospitalId): array
    {
        if (empty($hospitalId))
        {
            return [];
        }

        return self::on()
                   ->select('created_by')
                   ->where(['hospital_id' => $hospitalId])
                   ->distinct()
                   ->orderByDesc('id')
                   ->pluck('created_by')
                   ->toArray();
    }

    /**
     * 获取结算单的所有收银人
     *
     * @param int $hospitalId
     *
     * @return array
     */
    public static function GetOrderCashierUsersOptions(int $hospitalId): array
    {
        if (empty($hospitalId))
        {
            return [];
        }

        return self::on()
                   ->select('cashier_by')
                   ->where(['hospital_id' => $hospitalId])
                   ->distinct()
                   ->orderByDesc('id')
                   ->pluck('cashier_by')
                   ->toArray();
    }

    /**
     * 搜索结算单
     *
     * @param string $keywords
     * @param array  $where
     * @param array  $orderBy
     * @param int    $page
     * @param int    $count
     *
     * @return LengthAwarePaginator
     */
    public static function SearchOrder(
        string $keywords = '', array $where = [], array $orderBy = ['o.id' => 'desc'], int $page = 1, int $count = 10
    ): LengthAwarePaginator
    {
        return self::on()
                   ->select(['o.*'])
                   ->from(self::table() . ' as o')
                   ->searchMember(keywords: $keywords, driverTable: 'o')
                   ->where($where)
                   ->when(!empty($orderBy), function ($query) use ($orderBy) {
                       foreach ($orderBy as $column => $direction)
                       {
                           $query->orderBy($column, $direction);
                       }
                   })
                   ->paginate(perPage: $count, page: $page);

    }
}
