<?php

namespace App\Models;

/**
 * 仓储-库存盘点单-详情
 */
class StockInventoryRecordDetailModel extends Model
{
    protected $table = 'stock_inventory_record_detail';

    /**
     * 获取盘点单详情
     *
     * @param int   $hospitalId
     * @param int   $inventoryRecordId
     * @param array $itemIds
     *
     * @return array
     */
    public static function getInventoryRecordDetail(int $hospitalId, int $inventoryRecordId, array $itemIds = []): array
    {
        if (empty($hospitalId) || empty($inventoryRecordId))
        {
            return [];
        }

        $getInventoryDetailRes = self::on()
                                     ->where(['id' => $inventoryRecordId, 'hospital_id' => $hospitalId, 'status' => 1])
                                     ->when(!empty($itemIds), function ($query) use ($itemIds) {
                                         $query->whereIn('item_id', $itemIds);
                                     })
                                     ->get()
                                     ->keyBy('item_id')
                                     ->toArray();
        if (empty($getInventoryDetailRes))
        {
            return [];
        }

        return $getInventoryDetailRes;
    }
}
