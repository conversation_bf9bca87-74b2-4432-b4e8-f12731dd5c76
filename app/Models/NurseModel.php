<?php

namespace App\Models;

use DB;
use App\Enums\NurseExecutionStatusEnum;
use App\Models\Traits\CreateUserOptionsTrait;

/**
 * 处置项目
 */
class NurseModel extends Model
{
    use CreateUserOptionsTrait;

    protected $table = 'his_nurse';

    /**
     * 获取处置列表
     *
     * @param array $arrParams
     * @param int   $iPage
     * @param int   $iPageSize
     *
     * @return array
     */
    public static function getNurseListData(array $arrParams, int $iPage = 1, int $iPageSize = 10): array
    {
        // 医院ID
        if (empty($arrParams['hospitalId']))
        {
            return [];
        }

        $getWhere       = [];
        $getOrWhere     = []; // 多个维度查询条件，需要与其他条件使用and
        $getItemOrWhere = []; // 商品名称多个维度，需要与其他条件使用and

        $getWhere[] = ['his_nurse.hospital_id', '=', $arrParams['hospitalId']];
        $getWhere[] = ['his_nurse.status', '=', 1];

        // 多维度关键字查询
        if (!empty($arrParams['keywords']))
        {
            // 1. 手机号查询
            if (checkValidCellphone($arrParams['keywords']))
            {
                $getWhere[] = ['member.phone', '=', $arrParams['keywords']];
            }
            else
            {
                // 2.客户名称
                $getOrWhere[] = ['member.name', 'like', '%' . $arrParams['keywords'] . '%'];

                // 3.处置编号
                $getOrWhere[] = ['his_nurse.image_code', 'like', '%' . $arrParams['keywords'] . '%'];

                // 4.宠物名称
                $getOrWhere[] = ['member_pet.name', 'like', '%' . $arrParams['keywords'] . '%'];

                // 5.宠物病历号
                $getOrWhere[] = ['member_pet.record_number', 'like', '%' . $arrParams['keywords'] . '%'];
            }
        }

        // 处置ID
        if (!empty($arrParams['nurseId']))
        {
            $getWhere[] = ['his_nurse.id', '=', $arrParams['nurseId']];
        }

        // 病历ID
        if (!empty($arrParams['caseId']))
        {
            $getWhere[] = ['his_nurse.case_id', '=', $arrParams['caseId']];
        }

        // 病历编号
        if (!empty($arrParams['caseCode']))
        {
            $getCaseRes = CasesModel::getData(['id'], ['case_code' => $arrParams['caseCode']]);
            if (!empty($getCaseRes))
            {
                $getWhere[] = ['his_nurse.case_id', '=', current($getCaseRes)['id']];
            }
            else
            {
                $getWhere[] = ['his_nurse.case_id', '=', - 1];
            }
        }

        // 处置编号
        if (!empty($arrParams['nurseCode']))
        {
            $getWhere[] = ['his_nurse.nurse_code', 'like', '%' . $arrParams['nurseCode'] . '%'];
        }

        // 处置项目名称
        if (!empty($arrParams['name']))
        {
            $getItemOrWhere[] = [
                function ($query) use ($arrParams) {
                    ItemModel::getItemNameSearchCondition($query, $arrParams['name']);
                }
            ];
        }

        // 处置开具时间
        if (!empty($arrParams['startDate']) && strtotime($arrParams['startDate']) !== false)
        {
            $getWhere[] = ['his_nurse.created_at', '>=', $arrParams['startDate'] . ' 00:00:00'];
        }
        if (!empty($arrParams['endDate']) && strtotime($arrParams['endDate']) !== false)
        {
            $getWhere[] = ['his_nurse.created_at', '<=', $arrParams['endDate'] . ' 23:59:59'];
        }

        // 检测开具医生
        if (!empty($arrParams['doctorUid']))
        {
            $getWhere[] = ['user.uid', '=', $arrParams['doctorUid']];
        }

        // 付款状态
        if (isset($arrParams['payStatus']) && is_numeric($arrParams['payStatus']))
        {
            $getWhere[] = ['his_nurse.is_paid', '=', $arrParams['payStatus']];
        }

        // 处置执行状态
        if (isset($arrParams['status']) && is_numeric($arrParams['status']))
        {
            $getStatusWhere = NurseExecutionStatusEnum::getConditions($arrParams['status'], self::table());
            if (!empty($getStatusWhere))
            {
                $getWhere = array_merge($getWhere, $getStatusWhere);
            }
        }

        $builderQuery = self::on()
                            ->select([
                                         'his_nurse.*',
                                         'item.name as item_name',
                                         DB::raw(ItemModel::getItemDisplayNameField()),
                                         'item.use_unit as item_use_unit',
                                         'item.uid as item_uid',
                                         'user.uid as doctor_uid',
                                         'user.name as doctor_name',
                                         'member.name as member_name',
                                         'member.uid as member_uid',
                                         'member_pet.name as pet_name',
                                         'member_pet.record_number as pet_record_number',
                                     ])
                            ->leftJoin((new MemberModel()->getTable()) . ' as member', 'member.id', '=', 'his_nurse.member_id')
                            ->leftJoin((new MemberPetsModel()->getTable()) . ' as member_pet', 'member_pet.id', '=', 'his_nurse.pet_id')
                            ->leftJoin((new ItemModel()->getTable()) . ' as item', 'item.id', '=', 'his_nurse.item_id')
                            ->leftJoin((new UsersModel()->getTable()) . ' as user', 'user.id', '=', 'his_nurse.doctor_id')
                            ->when($getWhere, function ($query) use ($getWhere) {
                                $query->where($getWhere);
                            })
                            ->when($getOrWhere, function ($query) use ($getOrWhere) {
                                $query->where(function ($subQuery) use ($getOrWhere) {
                                    $subQuery->orWhere($getOrWhere);
                                });
                            })
                            ->when($getItemOrWhere, function ($query) use ($getItemOrWhere) {
                                $query->where(function ($subQuery) use ($getItemOrWhere) {
                                    $subQuery->orWhere($getItemOrWhere);
                                });
                            })
                            ->orderBy('his_nurse.id', 'desc');

        // 获取条目总数
        $totalCount = $builderQuery->count();
        if ($totalCount <= 0)
        {
            return [];
        }

        // 分页
        $builderQuery->offset(($iPage - 1) * $iPageSize)
                     ->limit($iPageSize);

        // 获取条目数据
        $imageList = $builderQuery->get()
                                  ->toArray();

        return ['total' => $totalCount, 'data' => $imageList];
    }
}
