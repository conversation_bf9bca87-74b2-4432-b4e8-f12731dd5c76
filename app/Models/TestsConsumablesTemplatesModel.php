<?php

namespace App\Models;

/**
 * 化验耗材模板
 */
class TestsConsumablesTemplatesModel extends Model
{
    protected $table = 'his_tests_consumables_templates';

    /**
     * 批量根据化验项目ID获取耗材模板
     * 支持多级别查询：医院级别 > 品牌级别 > 组织级别
     *
     * @param array $testItemIds 化验项目ID数组
     * @param int   $hospitalId  医院ID
     * @param int   $brandId     品牌ID
     * @param int   $orgId       组织ID
     *
     * @return array 二维数组 [testItemId => template]
     */
    public static function getTestsConsumablesTemplateByItemId(array $testItemIds, int $hospitalId, int $brandId, int $orgId): array
    {
        if (empty($testItemIds))
        {
            return [];
        }

        // @formatter:off
        $getTemplates = self::on()
                            ->fromSub(function ($query) use ($testItemIds, $hospitalId, $brandId, $orgId) {
                                $query->select('*')
                                      ->selectRaw('ROW_NUMBER() OVER (
                                       PARTITION BY item_id
                                       ORDER BY CASE
                                           WHEN hospital_id = ? AND brand_id = ? THEN 1
                                           WHEN hospital_id = 0 AND brand_id = ? THEN 2
                                           WHEN hospital_id = 0 AND brand_id = 0 THEN 3
                                           ELSE 4
                                       END
                                   ) as rn',
                                                  [$hospitalId, $brandId, $brandId])
                                      ->from(new self()->getTable())
                                      ->whereIn('item_id', $testItemIds)
                                      ->where(['status' => 1])
                                      ->where(function ($subQuery) use ($hospitalId, $brandId, $orgId) {
                                          // 医院级别
                                          $subQuery->orWhere(function ($q) use ($hospitalId, $brandId, $orgId) {
                                              $q->where(['hospital_id' => $hospitalId, 'brand_id' => $brandId, 'org_id' => $orgId]);
                                          })
                                          // 品牌级别
                                          ->orWhere(function ($q) use ($brandId, $orgId) {
                                              $q->where(['hospital_id' => 0, 'brand_id' => $brandId, 'org_id' => $orgId]);
                                          })
                                          // 组织级别
                                          ->orWhere(function ($q) use ($orgId) {
                                              $q->where(['hospital_id' => 0, 'brand_id' => 0, 'org_id' => $orgId]);
                                          });
                                      });
                            }, 'ranked_templates')
                            ->where(['rn' => 1]) // 只取每个化验项的最高优先级记录，ROW_NUMBER排序总数会从1开始
                            ->get()
                            ->toArray();

        if (empty($getTemplates))
        {
            return [];
        }

        $result = [];
        foreach ($getTemplates as $template)
        {
            $result[$template['item_id']] = $template;
        }

        return $result;

        // @formatter:on
    }
}
