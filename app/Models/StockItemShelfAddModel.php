<?php

namespace App\Models;

use DB;

/**
 * 仓储-库存动态-加
 */
class StockItemShelfAddModel extends Model
{
    protected $table = 'stock_item_shelf_add';

    /**
     * 根据指定条件，获取入库数量
     *
     * @param int   $hospitalId
     * @param array $searchParams
     *
     * @return array
     */
    public static function getStockItemShelfAddQuantity(int $hospitalId, array $searchParams): array
    {
        if (empty($hospitalId))
        {
            return [];
        }

        $query = self::on()
                     ->where(['hospital_id' => $hospitalId]);
        if (isset($searchParams['type']) && is_numeric($searchParams['type']))
        {
            $query->where('type', $searchParams['type']);
        }
        if (isset($searchParams['subType']) && is_numeric($searchParams['subType']))
        {
            $query->where('sub_type', $searchParams['subType']);
        }
        if (!empty($searchParams['relationCode']))
        {
            $query->where('relation_code', $searchParams['relationCode']);
        }
        if (!empty($searchParams['relationId']))
        {
            $query->where('relation_id', $searchParams['relationId']);
        }
        if (!empty($searchParams['relationDetailId']))
        {
            $query->where('relation_detail_id', $searchParams['relationDetailId']);
        }
        if (!empty($searchParams['itemId']))
        {
            $query->where('item_id', $searchParams['itemId']);
        }
        if (!empty($searchParams['itemBarcode']))
        {
            $query->where('item_barcode', $searchParams['itemBarcode']);
        }
        if (!empty($searchParams['shelfCode']))
        {
            $query->where('shelf_code', $searchParams['shelfCode']);
        }

        $getQuantityRes = $query->select([DB::raw('sum(pack_quantity) as pack_quantity'), DB::raw('sum(bulk_quantity) as bulk_quantity')])
                                ->first();

        return ['packQuantity' => $getQuantityRes['pack_quantity'] ?? 0, 'bulkQuantity' => $getQuantityRes['bulk_quantity'] ?? 0];
    }

    /**
     * 获取入库记录列表数据
     *
     * @param array $arrParams
     * @param int   $page
     * @param int   $pageSize
     *
     * @return array
     */
    public static function getAddStockRecordListData(array $arrParams, int $page = 1, int $pageSize = 10): array
    {
        // 必须指定医院查询
        if (empty($arrParams['hospitalId']))
        {
            return [];
        }

        // 医院ID
        $getWhere[] = ['stock_item_shelf_add.hospital_id', '=', $arrParams['hospitalId']];
        $getWhere[] = ['stock_item_shelf_add.status', '=', 1];

        // 基础查询条件
        $query = self::on()
                     ->select([
                                  'stock_item_shelf_add.*',
                                  'item.uid as item_uid',
                                  'item.pack_unit',
                                  'item.bulk_unit',
                                  'item.shelf_life_day',
                                  DB::raw(ItemModel::getItemDisplayNameField()),
                                  'user.uid as user_uid',
                                  'user.name as user_name',
                              ])
                     ->leftJoin(ItemModel::table() . ' as item', 'stock_item_shelf_add.item_id', '=', 'item.id')
                     ->leftJoin(UsersModel::table() . ' as user', 'stock_item_shelf_add.created_by', '=', 'user.id')
                     ->where($getWhere);

        // 商品条码或商品名称关键词搜索
        if (!empty($arrParams['keywords']))
        {
            $keywords = $arrParams['keywords'];
            $query->where(function ($subQuery) use ($keywords) {
                $subQuery->where('stock_item_shelf_add.item_barcode', 'like', '%' . $keywords . '%');
                ItemModel::getItemNameSearchCondition($subQuery, $keywords);
            });
        }

        // 出库关联业务单号
        if (!empty($arrParams['orderCode']))
        {
            $query->where('stock_item_shelf_add.relation_code', 'like', '%' . $arrParams['orderCode'] . '%');
        }

        // 出库类型
        if (isset($arrParams['type']) && is_numeric($arrParams['type']))
        {
            $query->where('stock_item_shelf_add.type', $arrParams['type']);
        }

        // 出库日期范围
        if (!empty($arrParams['startDate']))
        {
            $query->where('stock_item_shelf_add.created_at', '>=', $arrParams['startDate'] . ' 00:00:00');
        }
        if (!empty($arrParams['endDate']))
        {
            $query->where('stock_item_shelf_add.created_at', '<=', $arrParams['endDate'] . ' 23:59:59');
        }

        // 出库人
        if (!empty($arrParams['createUserId']) || !empty($arrParams['createUserUid']))
        {
            if (!empty($arrParams['createUserId']))
            {
                $query->where('user.id', '=', $arrParams['createUserId']);
            }
            else
            {
                $query->where('user.uid', '=', $arrParams['createUserUid']);
            }
        }

        // 获取总数
        $totalCount = $query->count();
        if ($totalCount <= 0)
        {
            return [];
        }

        // 分页查询
        if ($page > 0 && $pageSize > 0)
        {
            $query->offset(($page - 1) * $pageSize)
                  ->limit($pageSize);
        }

        // 排序
        $query->orderBy('stock_item_shelf_add.created_at', 'desc');

        // 获取数据
        $stockAddRecordList = $query->get()
                                    ->toArray();

        return ['total' => $totalCount, 'data' => $stockAddRecordList];
    }
}
