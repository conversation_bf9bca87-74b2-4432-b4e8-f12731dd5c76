<?php

namespace App\Models\Traits;

use Illuminate\Support\Facades\DB;
use App\Enums\SheetStatusEnum;

trait SheetStatusConvertTrait
{
    /**
     * 转换 sheet 状态为当前表查询条件[通用]
     *
     * 适用于：
     * status:0 作废/删除, 1...N 有效
     * pay_status:0 未支付, 1 已支付, 2 支付中
     *
     * @param int    $sheetStatus
     * @param string $statusColumn    业务状态列名
     * @param string $payStatusColumn 支付状态列名
     *
     * @return array
     */
    private static function CommonConvertSheetStatusToWhere(
        int    $sheetStatus,
        string $statusColumn = 'status',
        string $payStatusColumn = 'pay_status'
    ): array
    {
        return match ($sheetStatus)
        {
            SheetStatusEnum::Cancelled->value => [[$statusColumn, '=', 0]],
            SheetStatusEnum::Unpaid->value => [[$statusColumn, '>', 0], [$payStatusColumn, '=', 0]],
            SheetStatusEnum::Paying->value => [[$statusColumn, '>', 0], [$payStatusColumn, '=', 2]],
            SheetStatusEnum::Paid->value => [[$statusColumn, '>', 0], [$payStatusColumn, '=', 1]],
            default => [],
        };
    }

    /**
     * 转换当前表的状态为 sheet 状态[通用]
     *
     * 适用于：
     * status:0 作废/删除, 1...N 有效
     * pay_status:0 未支付, 1 已支付, 2 支付中
     *
     * @param string $statusColumn
     * @param string $payStatusColumn
     *
     * @return \Illuminate\Contracts\Database\Query\Expression|\Illuminate\Database\Query\Expression
     */
    private static function CommonConvertStatusToSheetSelectField(
        string $statusColumn = 'status',
        string $payStatusColumn = 'pay_status'
    ): \Illuminate\Contracts\Database\Query\Expression|\Illuminate\Database\Query\Expression
    {
        return DB::raw("
                CASE
                    WHEN $statusColumn = 0 THEN 0
                    WHEN $statusColumn > 0 AND $payStatusColumn = 0 THEN 1
                    WHEN $statusColumn > 0 AND $payStatusColumn = 2 THEN 2
                    WHEN $statusColumn > 0 AND $payStatusColumn = 1 THEN 3
                END AS status
            ");
    }
}
