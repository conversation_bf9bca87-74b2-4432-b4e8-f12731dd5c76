<?php

namespace App\Models\Traits;

use App\Models\UsersModel;

trait CreateUserOptionsTrait
{
    /**
     * 获取表的所有创建人
     *
     * @param int    $hospitalId
     * @param string $createByField
     *
     * @return array
     */
    public static function GetCreateUsersOptions(int $hospitalId, string $createByField = 'created_by'): array
    {
        if (empty($hospitalId))
        {
            return [];
        }

        $userIds = self::on()
                       ->select($createByField)
                       ->where(['hospital_id' => $hospitalId])
                       ->distinct()
                       ->orderByDesc('id')
                       ->pluck($createByField)
                       ->toArray();

        if (!empty($userIds))
        {
            $users = UsersModel::getUserByIds($userIds);
            if ($users->isEmpty())
            {
                return [];
            }

            return $users->map(function ($user) {
                return [
                    'uid'  => $user['uid'],
                    'name' => $user['name'],
                ];
            })
                         ->values()
                         ->toArray();
        }

        return [];
    }
}
