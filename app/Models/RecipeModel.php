<?php

namespace App\Models;

use Throwable;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Database\Query\Builder;
use App\Enums\SheetStatusEnum;
use App\Models\Interfaces\SheetUnionInterface;
use App\Models\Traits\SheetStatusConvertTrait;

/**
 * 处方表
 */
class RecipeModel extends Model implements SheetUnionInterface
{
    use SheetStatusConvertTrait;

    protected $table = 'his_recipes';

    /**
     * 完整查询字段
     *
     * @return array
     */
    public static function getSelectFields(): array
    {
        return [
            'id',
            DB::raw("recipe_code as sheet_code"),
            DB::raw("'recipe' as business_type"),
            'hospital_id',
            'member_id',
            'pet_id',
            'price',
            self::CommonConvertStatusToSheetSelectField(payStatusColumn: 'is_paid'),
            DB::raw("doctor_id as created_by"),
            DB::raw("doctor_id as sold_by"),
            DB::raw("0 as cancel_by"),
            DB::raw("created_at as order_time"),
            DB::raw("'' as paid_at"),
            DB::raw("'' as cancel_at"),
            'created_at',
            'updated_at',
        ];
    }

    /**
     * 获取当前业务的待付款 SQL 构造器
     *
     * @param int         $hospitalId
     * @param int|null    $memberId
     * @param int|null    $createdBy
     * @param string|null $createdStartTime
     * @param string|null $createdEndTime
     *
     * @return Builder
     */
    public static function getUnpaidQuery(int $hospitalId, ?int $memberId = null, ?int $createdBy = null, ?string $createdStartTime = null, ?string $createdEndTime = null): Builder
    {

        return DB::table(self::table())
                 ->select(self::getSelectFields())
                 ->where([
                             'hospital_id' => $hospitalId,
                         ])
                 ->where(self::CommonConvertSheetStatusToWhere(sheetStatus    : SheetStatusEnum::Unpaid->value,
                                                               payStatusColumn: 'is_paid'))
                 ->when($memberId, function ($query) use ($memberId) {
                     $query->where('member_id', $memberId);
                 })
                 ->when($createdBy, function ($query) use ($createdBy) {
                     $query->where('doctor_id', $createdBy);
                 })
                 ->when($createdStartTime, function ($query) use ($createdStartTime) {
                     $query->where('created_at', '>=', $createdStartTime);
                 })
                 ->when($createdEndTime, function ($query) use ($createdEndTime) {
                     $query->where('created_at', '<=', $createdEndTime);
                 });
    }

    /**
     * 批量获取处方单
     *
     * @param array $recipeCodes
     * @param int   $hospitalId
     * @param bool  $keyByCode
     * @param bool  $useWritePdo
     *
     * @return array
     */
    public static function GetRecipeByRecipeCodes(array $recipeCodes, int $hospitalId, bool $keyByCode = true, bool $useWritePdo = false): array
    {
        if (empty($recipeCodes) || empty($hospitalId))
        {
            return [];
        }

        return self::on()
                   ->where(['hospital_id' => $hospitalId])
                   ->whereIn('recipe_code', $recipeCodes)
                   ->when($useWritePdo, function ($query) {
                       $query->useWritePdo();
                   })
                   ->get()
                   ->when($keyByCode, function ($collection) {
                       return $collection->keyBy('recipe_code');
                   })
                   ->toArray();
    }

    /**
     * 获取病历下指定支付状态处方
     *
     * @param int        $caseId
     * @param int        $sourceType
     * @param int        $sourceRelationId
     * @param array|null $paidStatus
     *
     * @return array
     */
    public static function getWithPaidStatusRecipeByCaseId(int $caseId, int $sourceType, int $sourceRelationId, ?array $paidStatus = []): array
    {
        if (empty($caseId) || empty($sourceType) || empty($sourceRelationId))
        {
            return [];
        }

        return self::on()
                   ->where([
                               'case_id'            => $caseId,
                               'source_type'        => $sourceType,
                               'source_relation_id' => $sourceRelationId,
                               'status'             => 1,
                           ])
                   ->when(is_array($paidStatus), function ($query) use ($paidStatus) {
                       $query->whereIn('is_paid', $paidStatus);
                   })
                   ->get()
                   ->toArray();
    }

    /**
     * 删除处方
     *
     * @param int $recipeId
     * @param int $deletedBy
     *
     * @return bool
     * @throws Throwable
     */
    public static function deleteRecipe(int $recipeId, int $deletedBy = 0): bool
    {
        if (empty($recipeId) || empty($deletedBy))
        {
            return false;
        }

        try
        {
            DB::beginTransaction();

            // 删除处方主表
            self::on()
                ->where(['id' => $recipeId])
                ->update(['status' => 0]);

            // 删除处方明细
            $recipeWhere = ['recipe_id' => $recipeId];
            RecipeItemModel::on()
                           ->where($recipeWhere)
                           ->update(['status' => 0]);

            // 删除处方化验
            TestModel::on()
                     ->where($recipeWhere)
                     ->update(['status' => 0, 'deleted_by' => $deletedBy, 'deleted_at' => getCurrentTimeWithMilliseconds()]);

            // 删除处方影像
            ImagesModel::on()
                       ->where($recipeWhere)
                       ->update(['status' => 0, 'deleted_by' => $deletedBy, 'deleted_at' => getCurrentTimeWithMilliseconds()]);

            // 删除处方处置
            NurseModel::on()
                      ->where($recipeWhere)
                      ->update(['status' => 0, 'deleted_by' => $deletedBy, 'deleted_at' => getCurrentTimeWithMilliseconds()]);

            DB::commit();

            return true;
        } catch (Throwable $throwable)
        {
            DB::rollBack();

            Log::error(__CLASS__ . '::' . __METHOD__ . ' 删除处方异常', [
                'code'    => $throwable->getCode(),
                'message' => $throwable->getMessage(),
                'file'    => $throwable->getFile(),
                'line'    => $throwable->getLine(),
                'trace'   => $throwable->getTraceAsString(),
            ]);

            return false;
        }
    }

    /**
     * 获取病历下处方汇总
     *
     * @param int $caseId
     *
     * @return array
     */
    public static function getRecipeSummaryByCaseId(int $caseId): array
    {
        if (empty($caseId))
        {
            return [];
        }

        $getRecipeSummaryRes = self::on()
                                   ->select([
                                                DB::raw('COUNT(*) as recipe_count'),
                                                DB::raw('SUM(price) as total_price'),
                                                DB::raw('SUM(CASE WHEN is_paid = 1 THEN price ELSE 0 END) as paid_price'),
                                                DB::raw('SUM(CASE WHEN (is_paid = 0 OR is_paid = 2) THEN price ELSE 0 END) as unpaid_price'),
                                            ])
                                   ->where(['case_id' => $caseId, 'status' => 1])
                                   ->first();

        return $getRecipeSummaryRes ? $getRecipeSummaryRes->toArray() : [];
    }
}
