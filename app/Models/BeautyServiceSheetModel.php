<?php

namespace App\Models;

use Illuminate\Support\Collection;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Database\Query\Builder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Enums\SheetStatusEnum;
use App\Models\Traits\KeywordSearchMemberPet;
use App\Models\Interfaces\SheetUnionInterface;
use Throwable;

class BeautyServiceSheetModel extends Model implements SheetUnionInterface
{
    use KeywordSearchMemberPet;

    protected $table = 'beauty_service_sheet';

    /**
     * 完整查询字段
     *
     * @return array
     */
    public static function getSelectFields(): array
    {
        return [
            'id',
            'sheet_code',
            DB::raw("'beauty' as business_type"),
            'hospital_id',
            'member_id',
            'pet_id',
            'price',
            'status',
            'created_by',
            'sold_by',
            'cancel_by',
            'order_time',
            'paid_at',
            'cancel_at',
            'created_at',
            'updated_at',
        ];
    }

    /**
     * 获取当前业务的待付款 SQL 构造器
     *
     * @param int         $hospitalId
     * @param int|null    $memberId
     * @param int|null    $createdBy
     * @param string|null $createdStartTime
     * @param string|null $createdEndTime
     *
     * @return Builder
     */
    public static function getUnpaidQuery(
        int     $hospitalId,
        ?int    $memberId = null,
        ?int    $createdBy = null,
        ?string $createdStartTime = null,
        ?string $createdEndTime = null
    ): Builder
    {

        return DB::table(self::table())
                 ->select(self::getSelectFields())
                 ->where([
                             'hospital_id' => $hospitalId,
                             'status'      => SheetStatusEnum::Unpaid->value,
                         ])
                 ->when($memberId, function ($query) use ($memberId) {
                     $query->where('member_id', $memberId);
                 })
                 ->when($createdBy, function ($query) use ($createdBy) {
                     $query->where('created_by', $createdBy);
                 })
                 ->when($createdStartTime, function ($query) use ($createdStartTime) {
                     $query->where('created_at', '>=', $createdStartTime);
                 })
                 ->when($createdEndTime, function ($query) use ($createdEndTime) {
                     $query->where('created_at', '<=', $createdEndTime);
                 });
    }

    /**
     * 获取洗美服务单
     *
     * @param string $sheetCode
     * @param int    $sheetId
     * @param int    $hospitalId
     *
     * @return object|null
     */
    public static function GetSheetByCodeOrId(
        string $sheetCode = '',
        int    $sheetId = 0,
        int    $hospitalId = 0
    ): object|null
    {
        if (empty($sheetCode) && empty($sheetId))
        {
            return null;
        }

        return self::on()
                   ->when(!empty($sheetCode), function ($query) use ($sheetCode) {
                       $query->where(['sheet_code' => $sheetCode]);
                   })
                   ->when(!empty($sheetId), function ($query) use ($sheetId) {
                       $query->where(['id' => $sheetId]);
                   })
                   ->when(!empty($hospitalId), function ($query) use ($hospitalId) {
                       $query->where(['hospital_id' => $hospitalId]);
                   })
                   ->first();
    }

    /**
     * 批量获取洗美服务单[通过服务单号]
     *
     * @param array $sheetCodes
     * @param int   $hospitalId
     * @param bool  $keyByCode
     * @param bool  $useWritePdo
     *
     * @return array
     */
    public static function GetSheetBySheetCodes(
        array $sheetCodes, int $hospitalId, bool $keyByCode = true, bool $useWritePdo = false
    ): array
    {
        if (empty($sheetCodes) || empty($hospitalId))
        {
            return [];
        }

        return self::on()
                   ->where(['hospital_id' => $hospitalId])
                   ->whereIn('sheet_code', $sheetCodes)
                   ->when($useWritePdo, function ($query) {
                       $query->useWritePdo();
                   })
                   ->get()
                   ->when($keyByCode, function ($collection) {
                       return $collection->keyBy('sheet_code');
                   })
                   ->toArray();
    }

    /**
     * 创建洗美服务单
     *
     * @param array $sheetData
     * @param array $sheetItemsData
     *
     * @return int|null
     * @throws Throwable
     */
    public static function DoCreateSheet(array $sheetData, array $sheetItemsData): int|null
    {
        if (empty($sheetData) || empty($sheetItemsData))
        {
            return null;
        }

        try
        {
            DB::beginTransaction();

            $sheetId = self::insertOne($sheetData);
            if (empty($sheetId))
            {
                DB::rollBack();

                return null;
            }

            foreach ($sheetItemsData as $key => $itemInfo)
            {
                $sheetItemsData[$key]['sheet_id'] = $sheetId;
            }

            $insertItemRes = BeautyServiceSheetItemModel::insert($sheetItemsData);
            if (empty($insertItemRes))
            {
                DB::rollBack();

                return null;
            }

            DB::commit();

            return $sheetId;

        } catch (Throwable $throwable)
        {
            DB::rollBack();

            Log::error(__CLASS__ . '::' . __METHOD__ . ' 创建洗美服务单异常', [
                'code'    => $throwable->getCode(),
                'message' => $throwable->getMessage(),
                'file'    => $throwable->getFile(),
                'line'    => $throwable->getLine(),
                'trace'   => $throwable->getTraceAsString(),
            ]);

            return null;
        }
    }

    /**
     * 编辑洗美服务单
     *
     * @param int   $sheetId
     * @param array $sheetData
     * @param array $insertSheetItemsData
     * @param array $updateSheetItemsData
     * @param array $deleteSheetUids
     * @param int   $deleteBy
     *
     * @return bool
     * @throws Throwable
     */
    public static function DoEditSheet(
        int   $sheetId,
        array $sheetData,
        array $insertSheetItemsData,
        array $updateSheetItemsData,
        array $deleteSheetUids,
        int   $deleteBy
    ): bool
    {
        if (empty($sheetId))
        {
            return false;
        }

        if (empty($sheetData) && empty($insertSheetItemsData) && empty($updateSheetItemsData) && empty($deleteSheetUids))
        {
            return true;
        }

        if (!empty($deleteSheetUids) && empty($deleteBy))
        {
            return false;
        }

        try
        {
            DB::beginTransaction();

            // 存在新增数据
            if (!empty($insertSheetItemsData))
            {
                foreach ($insertSheetItemsData as $key => $itemInfo)
                {
                    $insertSheetItemsData[$key]['sheet_id'] = $sheetId;
                }

                $insertRes = BeautyServiceSheetItemModel::insert($insertSheetItemsData);
                if (empty($insertRes))
                {
                    DB::rollBack();

                    return false;
                }
            }

            // 存在更新数据
            if (!empty($updateSheetItemsData))
            {
                foreach ($updateSheetItemsData as $itemInfo)
                {
                    $curDataUid = $itemInfo['uid'];
                    unset($itemInfo['uid']);

                    $updateRes = BeautyServiceSheetItemModel::updateOneByUid($curDataUid, $itemInfo);
                    if (empty($updateRes))
                    {
                        DB::rollBack();

                        return false;
                    }
                }
            }

            // 存在删除数据
            if (!empty($deleteSheetUids))
            {
                $deleteRes = BeautyServiceSheetItemModel::on()
                                                        ->where(['sheet_id' => $sheetId])
                                                        ->whereIn('uid', $deleteSheetUids)
                                                        ->update([
                                                                     'status'     => 0,
                                                                     'deleted_by' => $deleteBy,
                                                                     'deleted_at' => getCurrentTimeWithMilliseconds()
                                                                 ]);

                if (empty($deleteRes))
                {
                    DB::rollBack();

                    return false;
                }
            }

            // 存在主信息更新
            if (!empty($sheetData))
            {
                self::updateOne($sheetId, $sheetData);
            }

            DB::commit();

            return true;

        } catch (Throwable $throwable)
        {
            DB::rollBack();

            Log::error(__CLASS__ . '::' . __METHOD__ . ' 编辑洗美服务单异常', [
                'code'    => $throwable->getCode(),
                'message' => $throwable->getMessage(),
                'file'    => $throwable->getFile(),
                'line'    => $throwable->getLine(),
                'trace'   => $throwable->getTraceAsString(),
            ]);

            return false;
        }
    }

    /**
     * 软删除服务单
     *
     * @param int   $sheetId
     * @param int   $updateStatus
     * @param int   $beforeStatus
     * @param array $updateData
     *
     * @return int
     */
    public static function DoSoftDelete(
        int   $sheetId,
        int   $updateStatus,
        int   $beforeStatus,
        array $updateData = []
    ): int
    {
        if (empty($sheetId))
        {
            return 0;
        }

        $updateData = array_merge(['status' => $updateStatus], $updateData);

        return self::on()
                   ->where(['id' => $sheetId, 'status' => $beforeStatus])
                   ->update($updateData);
    }

    /**
     * 获取服务单的所有会员
     *
     * @param int      $hospitalId
     * @param int|null $status
     *
     * @return Collection
     */
    public static function GetSheetMemberOptions(int $hospitalId, ?int $status = null): Collection
    {
        $subQuery = DB::table(self::table())
                      ->when($status !== null, function ($query) use ($status) {
                          return $query->where('status', $status);
                      })
                      ->where('hospital_id', $hospitalId)
                      ->select(
                          'member_id',
                          'updated_at',
                          DB::raw('ROW_NUMBER() OVER (PARTITION BY member_id ORDER BY updated_at DESC) AS rn')
                      );

        return DB::table(DB::raw("({$subQuery->toSql()}) as s"))
                 ->mergeBindings($subQuery)
                 ->where('rn', 1)
                 ->orderBy('updated_at', 'desc')
                 ->get();
    }

    /**
     * 获取创建服务单的用户
     *
     * @param int      $hospitalId
     * @param int|null $status
     *
     * @return Collection
     */
    public static function GetSheetCreateUsersOptions(int $hospitalId, ?int $status = null): Collection
    {
        return self::on()
                   ->select(DB::raw('DISTINCT created_by'), 'updated_at')
                   ->when($status !== null, function ($query) use ($status) {
                       return $query->where('status', $status);
                   })
                   ->where('hospital_id', $hospitalId)
                   ->orderBy('updated_at', 'desc')
                   ->get();
    }

    /**
     * 搜索服务单
     *
     * @param string $keywords
     * @param array  $where
     * @param array  $orderBy
     * @param int    $page
     * @param int    $count
     *
     * @return LengthAwarePaginator
     */
    public static function SearchSheet(
        string $keywords = '', array $where = [], array $orderBy = ['s.id' => 'asc'], int $page = 1, int $count = 10
    ): LengthAwarePaginator
    {
        return self::on()
                   ->select(['s.*'])
                   ->from(self::table() . ' as s')
                   ->searchMemberPet(keywords: $keywords, driverTable: 's')
                   ->where($where)
                   ->when(!empty($orderBy), function ($query) use ($orderBy) {
                       foreach ($orderBy as $column => $direction)
                       {
                           $query->orderBy($column, $direction);
                       }
                   })
                   ->paginate(perPage: $count, page: $page);

    }

    /**
     * 执行
     *
     * @param int   $sheetId
     * @param array $updateSheetData
     * @param array $insertExecutorData
     * @param array $updateExecutorData
     *
     * @return bool
     * @throws Throwable
     */
    public static function FollowExecutor(
        int $sheetId, array $updateSheetData, array $insertExecutorData, array $updateExecutorData
    ): bool
    {
        if (empty($sheetId) && empty($updateSheetData) && empty($insertExecutorData) && empty($updateExecutorData))
        {
            return true;
        }

        try
        {
            DB::beginTransaction();

            if (!empty($updateSheetData))
            {
                self::updateOne($sheetId, $updateSheetData);
            }

            if (!empty($insertExecutorData))
            {
                BeautyServiceExecutorModel::insert($insertExecutorData);
            }

            if (!empty($updateExecutorData))
            {
                foreach ($updateExecutorData as $updateItem)
                {
                    BeautyServiceExecutorModel::updateOne($updateItem['id'], $updateItem);
                }
            }

            DB::commit();

            return true;
        } catch (Throwable $throwable)
        {
            DB::rollBack();

            Log::error(__CLASS__ . '::' . __METHOD__ . ' 执行服务单异常', [
                'code'    => $throwable->getCode(),
                'message' => $throwable->getMessage(),
                'file'    => $throwable->getFile(),
                'line'    => $throwable->getLine(),
                'trace'   => $throwable->getTraceAsString(),
            ]);

            return false;
        }
    }
}
