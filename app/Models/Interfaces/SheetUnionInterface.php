<?php

namespace App\Models\Interfaces;

use Illuminate\Database\Query\Builder;

/**
 * 购买单统一接口
 *
 * 实现该接口的业务模型，需要支持合并付款的特征
 *
 */
interface SheetUnionInterface
{
    /**
     * 完整查询字段
     *
     * 必须返回统一的字段，确保 union 后的结果一致
     * 建议所有表的字段在这里保持相同别名
     * 例如：
     *  - id
     *  - sheet_code
     *  - business_type : beauty retail registration recipe
     *  - hospital_id
     *  - member_id
     *  - pet_id
     *  - price
     *  - status
     *  - crated_by
     *  - sold_by
     *  - cancel_by
     *  - order_time
     *  - paid_at
     *  - cancel_at
     *  - created_at
     *  - updated_at
     *
     * @return array
     */
    public static function getSelectFields(): array;

    /**
     * 获取当前业务的待付款 SQL 构造器
     *
     * @param int         $hospitalId
     * @param int|null    $memberId
     * @param int|null    $createdBy
     * @param string|null $createdStartTime
     * @param string|null $createdEndTime
     *
     * @return Builder
     */
    public static function getUnpaidQuery(
        int     $hospitalId,
        ?int    $memberId = null,
        ?int    $createdBy = null,
        ?string $createdStartTime = null,
        ?string $createdEndTime = null
    ): Builder;
}
