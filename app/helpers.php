<?php

use App\Enums\UserSmsSendTypeEnum;
use Illuminate\Support\Carbon;

if (!function_exists('addRequestReservedParameters'))
{
    /**
     * 添加保留参数
     *
     * @param string|array $key   单个键名或多个键值对数组
     * @param mixed        $value 如果是单个键名，这里是对应的值；如果 $key 是数组，此项将被忽略
     * @param string       $group 指定的 attributes 命名空间，默认为 reserved_parameters
     */
    function addReservedParameter(string|array $key, mixed $value = null, string $group = 'reserved_parameters'): void
    {
        $reserved = request()->attributes->get($group, []);

        if (is_array($key))
        {
            $reserved = array_merge($reserved, $key);
        }
        elseif (is_string($key))
        {
            $reserved[$key] = $value;
        }

        request()->attributes->set($group, $reserved);
    }
}

if (!function_exists('getRequestReservedParameters'))
{
    /**
     * 获取系统保留的参数值(即公共参数值)
     *
     * @param string|array $keys 为字符串，则返回单个匹配的值,为数组且不为空，则返回多个匹配的值,为数组且为空，则返回所有的值
     * @param array        $exceptKeys
     * @param string       $group
     *
     * @return string|array|null
     */
    function getRequestReservedParameters(string|array $keys = [], array $exceptKeys = [
        'payload',
        'signature',
        'nonce',
        'timestamp',
    ], string                                          $group = 'reserved_parameters'): string|array|null
    {
        $reserved = request()->attributes->get($group, []);

        if (!is_array($reserved))
        {
            return null;
        }

        if (is_string($keys))
        {
            if ($keys === '' || !array_key_exists($keys, $reserved) || in_array($keys, $exceptKeys, true))
            {
                return null;
            }

            return $reserved[$keys];
        }

        if (is_array($keys))
        {
            if (empty($keys))
            {
                $usedKeys = array_diff(array_keys($reserved), $exceptKeys);
            }
            else
            {
                $usedKeys = array_diff(array_intersect(array_keys($reserved), $keys), $exceptKeys);
            }

            return array_intersect_key($reserved, array_flip($usedKeys));
        }

        return null;
    }
}

if (!function_exists('outputJsonError'))
{

    /**
     * 输出JSON错误结果
     *
     * @param int        $code
     * @param string     $message
     * @param array|null $errors
     * @param array|null $content
     * @param array      $headers
     * @param int        $statusCode
     *
     * @return Illuminate\Http\JsonResponse
     */
    function outputJsonError(int $code = 500, string $message = '', ?array $errors = null, ?array $content = null, array $headers = [], int $statusCode = 200): Illuminate\Http\JsonResponse
    {
        if ($message == '')
        {
            $message = __('messages.' . $code);
        }

        $result = ['code' => $code, 'message' => $message];

        if (is_array($content))
        {
            $result['content'] = $content;
        }

        //开启调试模式，返回错误详情
        if (config('app.debug'))
        {
            $result = array_merge($result, ['errors' => $errors]);
        }

        $headers = array_merge($headers, ['API-Response-Code' => $code, 'API-Response-Msg' => urlencode($message)]);

        return response()
            ->json($result)
            ->setStatusCode($statusCode)
            ->withHeaders($headers);
    }

}

if (!function_exists('outputJsonResult'))
{

    /**
     * 输出JSON正常结果
     *
     * @param array|null $content
     * @param string     $message
     * @param int        $code
     * @param array      $headers
     *
     * @return Illuminate\Http\JsonResponse
     */
    function outputJsonResult(?array $content = null, string $message = '', int $code = 200, array $headers = []): Illuminate\Http\JsonResponse
    {
        if ($message == '')
        {
            $message = __('messages.' . $code);
        }

        $result = ['code' => $code, 'message' => $message, 'content' => $content];

        $headers = array_merge($headers, ['API-Response-Code' => $code, 'API-Response-Msg' => urlencode($message)]);

        return response()
            ->json($result)
            ->withHeaders($headers);
    }

}

if (!function_exists('checkValidCellphone'))
{

    /**
     * 是否有效手机号
     *
     * @param string $cellphone
     *
     * @return bool
     */
    function checkValidCellphone(string $cellphone): bool
    {
        if (preg_match("/^(1[3-9][0-9])\d{8}$/", $cellphone))
        {
            return true;
        }

        return false;
    }

}

if (!function_exists('secretCellphone'))
{

    /**
     * 隐藏手机号中间4位
     *
     * @param string $cellphone
     * @param bool   $effect
     *
     * @return string
     */
    function secretCellphone(string $cellphone, bool $effect = true): string
    {
        if (!$effect)
        {
            return $cellphone;
        }

        if (strlen($cellphone) > 8)
        {
            return substr_replace($cellphone, '****', 3, 4);
        }
        else
        {
            return $cellphone;
        }
    }

}
if (!function_exists('isLikelyBarcode'))
{
    /**
     * 判断是否可能是条码
     *
     * @param string $keywords
     *
     * @return bool
     */
    function isLikelyBarcode(string $keywords): bool
    {
        $keywords = trim($keywords);

        // 1. 至少包含连续5位数字
        if (preg_match('/\d{5,}/', $keywords))
        {
            return true;
        }

        // 2. 包含数字和字母的组合（至少一位数字和一位字母）
        if (preg_match('/(?=.*[a-zA-Z])(?=.*\d)/', $keywords))
        {
            return true;
        }

        // 3. 允许包含短横线分隔符的情况
        // 只要整体符合包含5个数字以上即可，已被第1条涵盖

        // 4. 其他不匹配
        return false;
    }
}

if (!function_exists('generateRandomNumber'))
{

    /**
     * 获取数字随机码
     *
     * @param int  $length
     * @param bool $startWithoutZero 第一位不为0
     *
     * @return string
     */
    function generateRandomNumber(int $length = 6, bool $startWithoutZero = false): string
    {
        $str = '';
        try
        {
            for ($i = 0; $i < $length; $i ++)
            {
                if ($i == 1)
                {
                    $str .= random_int($startWithoutZero ? 1 : 0, 9);
                }
                else
                {
                    $str .= random_int(0, 9);
                }
            }
        } catch (Exception)
        {
            return '';
        }

        return $str;
    }

}

if (!function_exists('generateUUID'))
{
    /**
     * 生成UUID
     *
     * 雪花算法+base62
     *
     * @return string
     */
    function generateUUID(): string
    {
        return (string) \Base62\Facades\Base62::encode(\App\Facades\SnowflakeFacade::generateId());
    }
}

if (!function_exists('getCurrentTimeWithMilliseconds'))
{
    /**
     * 获取含有毫秒的日期时间
     *
     * 输出类似：2025-02-17 15:30:45.123
     *
     * @return string
     */
    function getCurrentTimeWithMilliseconds(): string
    {
        return new DateTime()->format('Y-m-d H:i:s.v');
    }
}

if (!function_exists('smsSend'))
{
    /**
     * 发送短信
     *
     * @param string                  $phone      目标手机号
     * @param int|UserSmsSendTypeEnum $smsType    短信发送方式
     * @param string                  $templateId 短信模板ID：某些平台需要模板ID
     * @param string                  $content    短信内容：模板ID方式式，为模板内容替换字符串
     * @param array                   $config     配置：origin为发送源，用于统计
     *
     * @return bool
     */
    function smsSend(string $phone, int|UserSmsSendTypeEnum $smsType, string $templateId = '', string $content = '', array $config = []): bool
    {
        if ($phone == '' || !checkValidCellphone($phone))
        {
            return false;
        }

        if (is_int($smsType) && !UserSmsSendTypeEnum::exists($smsType))
        {
            return false;
        }

        if ($templateId == '' && $content == '')
        {
            return false;
        }

        {
            return match (UserSmsSendTypeEnum::valueToCase($smsType))
            {
                //TODO:发送短信的具体实现，待完成
                UserSmsSendTypeEnum::Sms => true,
                UserSmsSendTypeEnum::voice => false,
            };
        }
    }
}

if (!function_exists('is_enum'))
{
    /**
     * 检查变量是否为Enum
     *
     * @see https://www.php.net/manual/zh/class.unitenum.php
     *
     * @param mixed $variable
     *
     * @return bool
     */
    function is_enum(mixed $variable): bool
    {
        return $variable instanceof UnitEnum;
    }
}

if (!function_exists('calculatePersonAge'))
{
    /**
     * 根据出生日期计算人年龄
     *
     * @param string $birthDate 出生日期，格式为 Y-m-d
     * @param string $toDate    计算至此日期，默认为当前日期
     *
     * @return int
     * @throws Exception
     */
    function calculatePersonAge(string $birthDate, string $toDate = ''): int
    {
        if (empty($birthDate) || strtotime($birthDate) === false)
        {
            return 0;
        }

        $birthDateTime = new DateTime($birthDate);
        $toDateTime    = empty($toDate) ? new DateTime() : new DateTime($toDate);

        // 如果出生日期大于当前日期，返回0
        if ($birthDateTime > $toDateTime)
        {
            return 0;
        }

        $interval = $birthDateTime->diff($toDateTime);

        return $interval->y;
    }
}

if (!function_exists('calculatePetAge'))
{
    /**
     * 计算宠物年龄
     *
     * @param string|null $birthday 宠物出生日期，格式为 Y-m-d
     * @param string|null $toDate   计算至此日期，默认为当前日期
     *
     * @return string 格式化后的年龄字符串，如"2岁3个月"
     */
    function calculatePetAge(?string $birthday, ?string $toDate = null): string
    {
        // 检查生日是否有效
        if (empty($birthday) || $birthday === '0000-00-00' || strtotime($birthday) === false)
        {
            return '未知';
        }

        try
        {
            // 解析出生日期
            $birthdayDate = new DateTime($birthday);

            // 解析目标日期（默认为当前日期）
            $toDateTime = empty($toDate) ? new DateTime() : new DateTime($toDate);

            // 如果出生日期大于目标日期，返回"1个月"
            if ($birthdayDate > $toDateTime)
            {
                return '1个月';
            }

            // 计算年龄差异
            $interval = $birthdayDate->diff($toDateTime);

            // 年龄太大（超过30岁）
            if ($interval->y > 30)
            {
                return '未知';
            }

            // 不满一个月，默认为一个月
            if ($interval->y == 0 && $interval->m == 0)
            {
                return '1个月';
            }

            // 格式化年龄字符串
            $ageString = '';
            if ($interval->y > 0)
            {
                $ageString .= $interval->y . '岁';
            }
            if ($interval->m > 0)
            {
                $ageString .= $interval->m . '个月';
            }

            return $ageString;
        } catch (Exception $e)
        {
            return '未知';
        }

    }
}

if (!function_exists('generatePetRecordNumber'))
{
    /**
     * 生成宠物病历号
     *
     * @return string 格式为"PN+数字"的10位病历号
     */
    function generatePetRecordNumber(): string
    {
        // 前缀为"PN"
        $prefix = 'PN';

        // 拼接前缀和数字部分
        return $prefix . generateShortNumberBySnowId();
    }
}

if (!function_exists('getUserRedisConnect'))
{
    /**
     * 获取用户Redis连接
     *
     * @return Illuminate\Redis\Connections\Connection
     * @throws Exception
     */
    function getUserRedisConnect(): Illuminate\Redis\Connections\Connection
    {
        return getRedisConnect(config('setting.user.redis_connection', 'user'));
    }
}

if (!function_exists('getRedisConnect'))
{
    /**
     * 获取Redis连接
     *
     * @param string $connectionName
     *
     * @return Illuminate\Redis\Connections\Connection
     * @throws Exception
     */
    function getRedisConnect(string $connectionName = 'default'): Illuminate\Redis\Connections\Connection
    {
        if (!in_array($connectionName, array_keys(config('database.redis'))))
        {
            throw new Exception('Redis connection name not found: ' . $connectionName);
        }

        return Illuminate\Support\Facades\Redis::connection($connectionName);
    }
}

if (!function_exists('formatDisplayDateTime'))
{
    /**
     * 格式化展示的时间，去掉时间中的毫秒
     *
     * @param null|string $dateTime
     * @param string      $format
     *
     * @return string
     */
    function formatDisplayDateTime(?string $dateTime, string $format = 'Y-m-d H:i'): string
    {
        if (empty($dateTime))
        {
            return '';
        }

        return date($format, strtotime($dateTime));
    }
}

if (!function_exists('generateBusinessCodeNumber'))
{
    /**
     * 生成带有前缀的业务编码
     *
     *
     * @param App\Enums\BusinessCodePrefixEnum $prefix
     *
     * @return string
     * @noinspection PhpUnused
     */
    function generateBusinessCodeNumber(App\Enums\BusinessCodePrefixEnum $prefix): string
    {
        return $prefix->value . generateShortNumberBySnowId();
    }
}

if (!function_exists('generateShortNumberBySnowId'))
{
    /**
     * 生成短数字，用于生成短ID
     *
     *
     * @param int $withNumber
     *
     * @return string
     * @noinspection PhpUnused
     */
    function generateShortNumberBySnowId(int $withNumber = 10): string
    {
        $snowflakeId = App\Facades\SnowflakeFacade::generateId();

        $hash = crc32($snowflakeId);

        return substr(str_pad((string) $hash, $withNumber, '0', STR_PAD_LEFT), 0, $withNumber);
    }
}

if (!function_exists('getPublicParamsHospitalOrgId'))
{
    /**
     * 从请求中的公共参数获取医院组织ID
     *
     * @param string $defaultKey
     *
     * @return string
     */
    function getPublicParamsHospitalOrgId(string $defaultKey = '_hospitalOrgId'): string
    {
        return getRequestReservedParameters($defaultKey);
    }
}

if (!function_exists('getPublicParamsHospitalId'))
{
    /**
     * 从请求中的公共参数获取医院ID
     *
     * @param string $defaultKey
     *
     * @return string
     */
    function getPublicParamsHospitalId(string $defaultKey = '_hospitalId'): string|int
    {
        return getRequestReservedParameters($defaultKey);
    }
}

if (!function_exists('getPublicParamsHospitalUserId'))
{
    /**
     * 从请求中的公共参数获取医生ID
     *
     * @param string $defaultKey
     *
     * @return string
     */
    function getPublicParamsHospitalUserId(string $defaultKey = '_userId'): string
    {
        return getRequestReservedParameters($defaultKey);
    }
}

if (!function_exists('formatTimeInterval'))
{
    /**
     * 格式化时间间隔为友好的描述
     *
     * 根据时间间隔返回不同格式的描述：
     * - 小于1分钟：返回"1分钟前"
     * - 1分钟到1小时：返回"X分钟前"
     * - 1小时到1天：返回"X小时前"
     * - 大于1天：返回"X天Y小时前"或"X天Z分钟前"
     *
     * @param DateTime|int|string      $startTime 开始时间（时间戳、日期时间字符串或DateTime对象）
     * @param DateTime|int|string|null $endTime   结束时间（默认为当前时间）
     *
     * @return string 格式化后的时间描述
     */
    function formatTimeInterval(DateTime|int|string $startTime, DateTime|int|string|null $endTime = null): string
    {
        // 处理开始时间
        if ($startTime instanceof DateTime)
        {
            $startDateTime = $startTime;
        }
        elseif (is_numeric($startTime))
        {
            $startDateTime = new DateTime();
            $startDateTime->setTimestamp($startTime);
        }
        else
        {
            try
            {
                $startDateTime = new DateTime($startTime);
            } catch (Exception $e)
            {
                return '1分钟前'; // 无效的开始时间
            }
        }

        // 处理结束时间，默认为当前时间
        if ($endTime === null)
        {
            $endDateTime = new DateTime();
        }
        elseif ($endTime instanceof DateTime)
        {
            $endDateTime = $endTime;
        }
        elseif (is_numeric($endTime))
        {
            $endDateTime = new DateTime();
            $endDateTime->setTimestamp($endTime);
        }
        else
        {
            try
            {
                $endDateTime = new DateTime($endTime);
            } catch (Exception $e)
            {
                $endDateTime = new DateTime(); // 无效的结束时间，使用当前时间
            }
        }

        // 计算时间差（秒）
        $seconds = $endDateTime->getTimestamp() - $startDateTime->getTimestamp();

        // 如果时间为负或零，返回默认值
        if ($seconds <= 0)
        {
            return '1分钟前';
        }

        // 小于1分钟
        if ($seconds < 60)
        {
            return '1分钟前';
        }

        // 1分钟到1小时
        if ($seconds < 3600)
        {
            $minutes = ceil($seconds / 60);

            return $minutes . '分钟前';
        }

        // 1小时到1天
        if ($seconds < 86400)
        {
            $hours = floor($seconds / 3600);

            return $hours . '小时前';
        }

        // 大于1天
        $days             = floor($seconds / 86400);
        $remainingSeconds = $seconds % 86400;

        if ($remainingSeconds >= 3600)
        {
            // 如果剩余时间超过1小时，显示小时
            $hours = floor($remainingSeconds / 3600);

            return $days . '天' . $hours . '小时前';
        }
        else
        {
            // 如果剩余时间不足1小时，显示分钟
            $minutes = ceil($remainingSeconds / 60);
            if ($minutes > 0)
            {
                return $days . '天' . $minutes . '分钟前';
            }
            else
            {
                return $days . '天前';
            }
        }
    }
}


if (!function_exists('getNowDateTime'))
{
    /**
     * 获取当前时间（基于 Carbon）
     *
     * @param string|null $format    时间格式，默认 null 返回 Carbon 对象
     * @param int|null    $timestamp 时间戳，默认当前时间
     * @param bool        $asObject  是否返回 Carbon 对象，默认 false 返回格式化字符串
     *
     * @return Carbon|string
     */
    function getNowDateTime(?string $format = 'Y-m-d H:i:s', ?int $timestamp = null, bool $asObject = false): Carbon|string
    {
        // 使用指定时间戳或当前时间创建 Carbon 实例
        $date = $timestamp ? Carbon::createFromTimestamp($timestamp) : Carbon::now();

        if ($asObject)
        {
            return $date;
        }

        // 默认格式输出
        return $date->format($format ?: 'Y-m-d H:i:s');
    }
}

if (!function_exists('getRecentDateRange'))
{
    /**
     * 获取某个时间往前推 N 天的时间范围（默认从今天往前 7 天）
     *
     * @param string|null $endDate 结束日期（默认今天，包含今天）
     * @param int         $days    往前推的天数（默认 7天）
     *
     * @return array{ start_date: string, end_date: string }
     */
    function getRecentDateRange(?string $endDate = null, int $days = 7): array
    {
        // 获取结束时间（字符串转 Carbon）
        $end = Carbon::parse($endDate ?: getNowDateTime());

        // 往前推 $days 天，作为开始时间
        $start = (clone $end)->subDays($days - 1);

        return [
            'startDate' => $start->format('Y-m-d 00:00:00'),
            'endDate'   => $end->format('Y-m-d 23:59:59'),
        ];
    }
}

if (!function_exists('normalize_datetime'))
{
    /**
     * 将无效或无法解析的日期时间字符串转换为 null。
     *
     * @param string|null $datetime
     *
     * @return string|null
     */
    function normalizeDatetime(?string $datetime): ?string
    {
        $value = trim((string) $datetime);

        // 明确判断几种常见无效值
        $invalids = [
            '',
            'null',
            'NULL',
            '0000-00-00',
            '0000-00-00 00:00:00',
            '0000-00-00 00:00',
            '--',
            'NaN',
        ];

        if (in_array($value, $invalids, true))
        {
            return null;
        }

        try
        {
            $dt = \Carbon\Carbon::parse($datetime);

            // 判断粒度（格式）
            if (preg_match('/^\d{4}-\d{2}-\d{2}$/', $datetime))
            {
                return $dt->format('Y-m-d');
            }

            if (preg_match('/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}$/', $datetime))
            {
                return $dt->format('Y-m-d H:i');
            }

            // 默认：返回完整时间
            return $dt->format('Y-m-d H:i:s');
        } catch (\Exception $e)
        {
            return null;
        }
    }
}

if (!function_exists('checkDateIsValid'))
{
    /**
     * 校验日期格式与有效性
     *
     * @param string $date    待校验的日期字符串
     * @param array  $formats 可接受的格式列表
     *
     * @return bool
     */
    function checkDateIsValid(string $date, array $formats = ["Y-m-d", "Y/m/d", "Ymd"]): bool
    {
        $invalidDates = ['0000-00-00', '00-00-00', '0000/00/00', '00/00/00', '', '--', null];

        if (in_array($date, $invalidDates, true))
        {
            return false;
        }

        foreach ($formats as $format)
        {
            $dt = DateTime::createFromFormat($format, $date);
            if ($dt && $dt->format($format) === $date)
            {
                return true;
            }
        }

        return false;
    }
}

if (!function_exists('removeAllWhitespace'))
{
    /**
     * 去除字符串或数组中的所有空白字符（包括空格、中文空格、制表符、换行等）
     *
     * @param array|string $val
     * @param array        $replaceChars 要移除的字符数组，默认包括常见空白字符
     *
     * @return string|array
     */
    function removeAllWhitespace(array|string $val, array $replaceChars = [" ", "　", "\t", "\n", "\r"]): array|string
    {
        if (is_array($val))
        {
            return array_map(function ($v) use ($replaceChars) {
                return removeAllWhitespace($v, $replaceChars);
            }, $val);
        }

        return str_replace($replaceChars, '', $val);
    }
}

if (!function_exists('trimWhitespace'))
{
    /**
     * 去除字符串或数组两端的空白字符（包括空格、换行等）
     *
     * @param array|string|null $val
     *
     * @return string|array
     */
    function trimWhitespace(array|string|null $val): array|string
    {
        if (is_null($val))
        {
            return '';
        }
        if (is_array($val))
        {
            return array_map('trimWhitespace', $val);
        }

        return trim($val);
    }
}

if (!function_exists('formatDisplayNumber'))
{
    /**
     * 格式化数字用于展示：
     * - 默认保留指定小数位数（如 2 位：1 → 1.00）
     * - 可选去除小数点后的多余零（如 12.00 → 12，12.10 → 12.1）
     *
     * @param int|string $number            数字
     * @param int        $precision         保留小数位数，默认 2
     * @param bool       $trimTrailingZeros 是否去除多余的0（展示场景）
     *
     * @return string
     */
    function formatDisplayNumber(int|string $number, int $precision = 2, bool $trimTrailingZeros = true): string
    {
        $number = (string) $number;

        if ($number < 0)
        {
            return '0';
        }

        $formatted = number_format($number, $precision, '.', '');

        if ($trimTrailingZeros)
        {
            $formatted = rtrim(rtrim($formatted, '0'), '.');
        }

        return $formatted;
    }
}

if (!function_exists('numberAdd'))
{
    /**
     * 高精度相加，支持多个数值（数组或单个），默认保留 2 位小数。
     * 加法顺序：从左到右依次相加
     *
     * @param array $numbers 数值数组，支持嵌套或混合格式
     * @param int   $scale   精度（保留小数位数）
     *
     * @return string
     */
    function numberAdd(array $numbers, int $scale = 2): string
    {
        $flattened = [];

        // 展平数组，过滤非数字
        array_walk_recursive($numbers, function ($value) use (&$flattened) {
            if (is_numeric($value))
            {
                $flattened[] = (string) $value;
            }
        });

        $sum = '0';
        foreach ($flattened as $n)
        {
            $sum = bcadd($sum, $n, $scale);
        }

        return $sum;
    }
}

if (!function_exists('numberMul'))
{
    /**
     * 高精度相乘，支持多个数值（数组），默认保留 2 位小数。
     * 乘法顺序：从左到右依次相乘
     *
     * @param array $numbers
     * @param int   $scale
     *
     * @return string
     */
    function numberMul(array $numbers, int $scale = 2): string
    {
        $flattened = [];

        array_walk_recursive($numbers, function ($value) use (&$flattened) {
            if (is_numeric($value))
            {
                $flattened[] = (string) $value;
            }
        });

        if (empty($flattened))
        {
            return '0';
        }

        $result = array_shift($flattened);
        foreach ($flattened as $n)
        {
            $result = bcmul($result, $n, $scale);
        }

        return $result;
    }
}


if (!function_exists('numberSub'))
{
    /**
     *  高精度相减，支持多个数值（数组），默认保留 2 位小数。
     *  减法顺序：第一个数依次减去其余数
     *
     * @param array $numbers
     * @param int   $scale
     *
     * @return string
     */
    function numberSub(array $numbers, int $scale = 2): string
    {
        $flattened = [];

        array_walk_recursive($numbers, function ($value) use (&$flattened) {
            if (is_numeric($value))
            {
                $flattened[] = (string) $value;
            }
        });

        if (empty($flattened))
        {
            return '0';
        }

        $result = array_shift($flattened);
        foreach ($flattened as $n)
        {
            $result = bcsub($result, $n, $scale);
        }

        return $result;
    }
}

if (!function_exists('numberDiv'))
{
    /**
     * 高精度相除，支持多个数值（数组），默认保留 2 位小数。
     * 除法顺序：第一个数依次除以后续数，遇 0 则返回 '0'
     *
     * @param     $numbers
     * @param int $scale
     *
     * @return string
     */
    function numberDiv(array $numbers, int $scale = 2): string
    {
        $flattened = [];

        array_walk_recursive($numbers, function ($value) use (&$flattened) {
            if (is_numeric($value))
            {
                $flattened[] = (string) $value;
            }
        });

        if (empty($flattened))
        {
            return '0';
        }

        $result = array_shift($flattened);
        foreach ($flattened as $n)
        {
            if (bccomp($n, '0', $scale) === 0)
            {
                return '0'; // 避免除以 0
            }
            $result = bcdiv($result, $n, $scale);
        }

        return $result;
    }
}

if (!function_exists('realPicturePath'))
{

    /**
     * 返回图片真实路径
     *
     * 【多图】直接循环使用即可，不产生网络资源
     *
     * 1.自动加服务器头，2.其他服务器图片直接返回, 3.为空直接返回
     * -------------------------------------------------------------------------
     * 缩放选项举例 ['width' => 100, 'height' => 100, 'ratio' => true]
     * 裁剪选项举例 ['start_x' => 10, 'end_x' => 150, 'start_y' => 20, 'end_y' => 150]
     * -------------------------------------------------------------------------
     *
     * @param       $picturePath string
     * @param array $options     缩放或裁剪选项
     * @param bool  $outputJpg   返回jpg格式 【$options和$outputJpg不可同时使用】
     *
     * @return string
     */
    function realPicturePath(string $picturePath, array $options = [], bool $outputJpg = false): string
    {
        if ($picturePath == '')
        {
            return $picturePath;
        }
        elseif (str_starts_with($picturePath, 'http'))
        {
            return $picturePath;
        }
        else
        {
            return config('image.img_server', '') . $picturePath;
        }
    }
}

